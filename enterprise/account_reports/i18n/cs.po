# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <jan.ho<PERSON><PERSON><PERSON>@centrum.cz>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "\" account balance is affected by"
msgstr "\"zůstatek účtu je ovlivněn"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "%(journal)s - %(account)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and %(remaining)s others"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and one other"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "%(report_label)s: %(period)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%s is not a numeric value"
msgstr "%s není číselnou hodnotou"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "%s selected"
msgstr "%s vybráno"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'Open General Ledger' caret option is only available form report lines "
"targetting accounts."
msgstr ""
"Možnost 'Otevřít hlavní knihu' je dostupná pouze u řádků propojených s účty."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'View Bank Statement' caret option is only available for report lines "
"targeting bank statements."
msgstr ""
"Možnost 'Zobrazit bankovní výpis' je dostupá pouze u řádků propojené na "
"bankovní výpisy."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'external' engine does not support groupby, limit nor offset."
msgstr "'externí' metoda výpočtu nepodporuje seskupení, limit ani posun."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(%s lines)"
msgstr "(%s položek)"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_receipts
msgid "(+) Outstanding Receipts"
msgstr "(+) Neuhrazené příjmy"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_payments
msgid "(-) Outstanding Payments"
msgstr "(-) Neuhrazené platby"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(1 line)"
msgstr "(1 položka)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "(No %s)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(No Group)"
msgstr "(žádná skupina)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid ", leading to an unexplained difference of"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Obnovit"

#. module: account_reports
#: model:mail.template,body_html:account_reports.email_template_customer_statement
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px;\">\n"
"                    <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                    <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                    <br/>\n"
"                    Please find enclosed the statement of your account.\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any questions.\n"
"                    <br/>\n"
"                    Sincerely,\n"
"                    <br/>\n"
"\t                <t t-out=\"object._get_followup_responsible().name if is_html_empty(object._get_followup_responsible().signature) else object._get_followup_responsible().signature\"/>\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"The email address is unknown on the partner\" invisible=\"not "
"send_mail_readonly\"/>"
msgstr ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"Nezadaná e-mail adresa partnera\" invisible=\"not "
"send_mail_readonly\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid ""
"<i>Errors marked with <i class=\"fa fa-warning\"/> are critical and prevent "
"the file generation.</i>"
msgstr ""
"<i>Chyby označené <i class=\"fa fa-warning\"/> jsou kritické a blokují "
"vytvoření souboru.</i>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr "<span role=\"separator\">Párování</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "<span>One or more error(s) occurred during file generation:</span>"
msgstr ""
"<span>Jedna nebo více chyb, které nastaly při vytváření souboru:</span>"

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_report_horizontal_group_name_uniq
msgid "A horizontal group with the same name already exists."
msgstr "Horizontální skupina s tímto názvem již existuje."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid "A line with a 'Group By' value cannot have children."
msgstr "Řádek s 'Seskupit dle' nemůže mít podřízené řádky."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""
"Daňovou jednotku lze vytvořit pouze mezi společnostmi sdílejícími stejnou "
"hlavní měnu."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit must contain a minimum of two companies. You might want to delete"
" the unit."
msgstr ""
"Daňová jednotka musí zahrnovat minimálně dvě společnosti. Možná chcete "
"jednotku smazat."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "AP %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "AR %s"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "MAJETEK"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_account_name
#: model:account.report.column,name:account_reports.aged_receivable_report_account_name
#: model:account.report.column,name:account_reports.partner_ledger_report_account_code
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__account_id
msgid "Account"
msgstr "Účet"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Šablona účtové osnovy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code"
msgstr "Kód účtu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code / Tag"
msgstr "Kód účtu / značka"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "Pole zástupce zobrazení účtu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Account Label"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_annotation
msgid "Account Report Annotation"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_custom_handler
msgid "Account Report Custom Handler"
msgstr "Vlastní zpracování účetního výkazu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_report_handler
msgid "Account Report Handler for Tax Reports"
msgstr "Zpracování účetního výkazu pro daňové výkazy"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_send
msgid "Account Report Send"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_reports_show_per_company_setting
msgid "Account Reports Show Per Company Setting"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "Společnost zastupovaná účtem"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "Deník přecenění účtu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_account_type.xml:0
msgid "Account:"
msgstr "Účet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "Účetní firma"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "Accounting Report"
msgstr "Účetní výkaz"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget
msgid "Accounting Report Budget"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget_item
msgid "Accounting Report Budget Item"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_expression
msgid "Accounting Report Expression"
msgstr "Vzorec výpočtu účetního výkazu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_line
msgid "Accounting Report Line"
msgstr "Položka účetního výkazu"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_tree
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Accounting Reports"
msgstr "Účetní výkazy"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Accounts"
msgstr "Účty"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Accounts Coverage Report"
msgstr "Výkaz o pokrytí účtů"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_to_adjust
msgid "Accounts To Adjust"
msgstr "Účty k úpravě"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Accounts coverage"
msgstr "Pokrytí účtů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Akce"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__actionable_errors
msgid "Actionable Errors"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Akce mohou vyvolat určité chování, jako je otevření zobrazení kalendáře, "
"nebo se automaticky označí jako provedené při nahrávání dokumentu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Aktivita"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Typ aktivity"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
msgid "Add a line"
msgstr "Přidat položku"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Add contacts to notify..."
msgstr "Přidat kontakty k notifikaci..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Přidat součty pod sekcemi"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_adjustment
msgid "Adjustment"
msgstr "Nastavení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Adjustment Entry"
msgstr "Zadání úpravy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance Payments received from customers"
msgstr "Zálohy přijaté od partnerů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance payments made to suppliers"
msgstr "Zálohy zaplacené dodavatelům"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Advanced"
msgstr "Pokročilý"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner_balance_report_handler
msgid "Aged Partner Balance Custom Handler"
msgstr "Vlastní zpracování saldokonta"

#. module: account_reports
#: model:account.report,name:account_reports.aged_payable_report
#: model:account.report.line,name:account_reports.aged_payable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
msgid "Aged Payable"
msgstr "Závazky po splatnosti"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_payable_report_handler
msgid "Aged Payable Custom Handler"
msgstr "Vlastní zpracování závazků po splatnosti"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Věkové závazky"

#. module: account_reports
#: model:account.report,name:account_reports.aged_receivable_report
#: model:account.report.line,name:account_reports.aged_receivable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
msgid "Aged Receivable"
msgstr "Pohledávky po splatnosti"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_receivable_report_handler
msgid "Aged Receivable Custom Handler"
msgstr "Vlastní zpracování pohledávek po splatnosti"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Věková struktura pohledávek"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "All"
msgstr "Vše"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Journals"
msgstr "Všechny deníky"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Payable"
msgstr "Všechny závazky"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Receivable"
msgstr "Všechny pohledávky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Report Variants"
msgstr "Všechny varianty výkazu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"All selected companies or branches do not share the same Tax ID. Please "
"check the Tax ID of the selected companies."
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_amount
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount
#: model:account.report.column,name:account_reports.partner_ledger_amount
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__amount
msgid "Amount"
msgstr "Částka"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_amount_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_amount_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_amount_currency
msgid "Amount Currency"
msgstr "Měna množství"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Amount in currency: %s"
msgstr "Částka v měně: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Lakhs"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Millions"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Thousands"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Analytic"
msgstr "Analytika"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__filter_analytic_groupby
msgid "Analytic Group By"
msgstr "Analytická skupina podle"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Analytic Simulations"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/line_name.xml:0
msgid "Annotate"
msgstr "Opatřit poznámkami"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
msgid "Annotation"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__annotations_ids
msgid "Annotations"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "As of %s"
msgstr "K %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Ascending"
msgstr "Vzestupně"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period0
#: model:account.report.column,name:account_reports.aged_receivable_report_period0
msgid "At Date"
msgstr "Ke dní"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Attach a file"
msgstr "Nahrát přílohu"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line_name.xml:0
msgid "Audit"
msgstr "Audit"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Auditní výkazy"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Průměrné dny věřitelů"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Průměrné dny dlužníků"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "B: %s"
msgstr "B: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.balance_sheet_balance
#: model:account.report.column,name:account_reports.cash_flow_report_balance
#: model:account.report.column,name:account_reports.executive_summary_column
#: model:account.report.column,name:account_reports.general_ledger_report_balance
#: model:account.report.column,name:account_reports.journal_report_balance
#: model:account.report.column,name:account_reports.partner_ledger_report_balance
#: model:account.report.column,name:account_reports.profit_and_loss_column
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance"
msgstr "Zůstatek"

#. module: account_reports
#: model:account.report,name:account_reports.balance_sheet
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_balance_sheet
msgid "Balance Sheet"
msgstr "Rozvaha"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_balance_sheet_report_handler
msgid "Balance Sheet Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_current
msgid "Balance at Current Rate"
msgstr "Zůstatek v aktuálním kurzu"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_operation
msgid "Balance at Operation Rate"
msgstr "Zůstatek v provozním kurzu"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_currency
msgid "Balance in Foreign Currency"
msgstr "Zůstatek v cizí měně"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Balance of '%s'"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.balance_bank
msgid "Balance of Bank"
msgstr "Zůstatek banky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax advance payment account"
msgstr "Účet zálohové platby zálohy na daň ze zůstatku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (payable)"
msgstr "Běžný daňový zůstatek (splatný)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (receivable)"
msgstr "Běžný účet se zůstatkovou daní (pohledávka)"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
msgid "Bank Reconciliation"
msgstr "Bankovní párování"

#. module: account_reports
#: model:account.report,name:account_reports.bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Zpráva o bankovním párování"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report_handler
msgid "Bank Reconciliation Report Custom Handler"
msgstr "Vlastní zpracování výkazu o odsouhlasení banky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Bankovní a hotovostní účty"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Base Amount"
msgstr "Základní částka"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Based on"
msgstr "Založeno na"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Before"
msgstr "Před"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__budget_id
msgid "Budget"
msgstr "Rozpočet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__budget_item_ids
msgid "Budget Item"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Items"
msgstr "Položky rozpočtu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Name"
msgstr "Název rozpočtu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Budget items can only be edited from account lines."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/redirectAction/redirectAction.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Cancel"
msgstr "Zrušit"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Cannot audit tax from another model than account.tax."
msgstr "Nelze auditovat daň z jiného modelu než account.tax."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Cannot generate carryover values for all fiscal positions at once!"
msgstr "Nelze generovat hodnoty přenosu pro všechny fiskální pozice najednou!"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "Carryover"
msgstr "Přenos"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover adjustment for tax unit"
msgstr "Přenosová úprava pro daňovou jednotku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover can only be generated for a single column group."
msgstr "Přenos lze vygenerovat pouze pro jednu skupinu sloupců."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover from %(date_from)s to %(date_to)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover lines for: %s"
msgstr "Přenesené položky z: %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Hotovost"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report_handler
msgid "Cash Flow Report Custom Handler"
msgstr "Vlastní zpracování výkazu peněžních toků"

#. module: account_reports
#: model:account.report,name:account_reports.cash_flow_report
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
msgid "Cash Flow Statement"
msgstr "Výkaz peněžních toků"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, beginning of period"
msgstr "Peněžní prostředky a peněžní ekvivalenty, začátek období"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, closing balance"
msgstr "Peněžní prostředky a peněžní ekvivalenty, závěrečný zůstatek"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from financing activities"
msgstr "Peněžní toky z finančních činností"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from investing & extraordinary activities"
msgstr "Peněžní toky z investičních a mimořádných činností"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from operating activities"
msgstr "Peněžní toky z provozní činnosti"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from unclassified activities"
msgstr "Peněžní toky z neklasifikovaných činností"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash in"
msgstr "Hotovost"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash out"
msgstr "Proplatit"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash paid for operating activities"
msgstr "Hotovost vyplácená za provozní činnosti"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Hotovost přijata"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash received from operating activities"
msgstr "Peníze přijaté z provozních činností"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Vynaložené peníze"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Hotovostní přebytek"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Změnit datum uzamčení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Check them"
msgstr "Zkontrolujte je"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Close"
msgstr "Zavřít"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Closing Entry"
msgstr "Záznam uzávěrky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Uzavření bankovního zůstatku"

#. module: account_reports
#: model:account.report.column,name:account_reports.journal_report_code
msgid "Code"
msgstr "Kód"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/filters/filter_code.xml:0
msgid "Codes:"
msgstr "Kódy:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Columns"
msgstr "Sloupce"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_communication
msgid "Communication"
msgstr "Komunikace"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Společnosti"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__company_id
msgid "Company"
msgstr "Společnost"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"Company %(company)s already belongs to a tax unit in %(country)s. A company "
"can at most be part of one tax unit per country."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Currency"
msgstr "Měna společnosti"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Company Only"
msgstr "Pouze společnost"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Settings"
msgstr "Nastavení společnosti"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Comparison"
msgstr "Porovnání"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure start dates"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Configure your TAX accounts - %s"
msgstr "Nakonfigurujte své daňové účty - %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_config_settings.py:0
msgid "Configure your start dates"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Nakonfigurovat své daňové účty"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_body
msgid "Contents"
msgstr "Obsah"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Náklady na výnosy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Could not expand term %(term)s while evaluating formula "
"%(unexpanded_formula)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Could not parse account_code formula from token '%s'"
msgstr "Nelze analyzovat vzorec account_code z tokenu '%s'"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Country"
msgstr "Země"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "Kód země"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Create"
msgstr "Vytvořit"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_composite_report_list
msgid "Create Composite Report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Vytvořit záznam"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_report_menu
msgid "Create Menu Item"
msgstr "Vytvořit položku nabídky"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_credit
#: model:account.report.column,name:account_reports.journal_report_credit
#: model:account.report.column,name:account_reports.partner_ledger_report_credit
#: model:account.report.column,name:account_reports.trial_balance_report_credit
msgid "Credit"
msgstr "Dal"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_currency
#: model:account.report.column,name:account_reports.general_ledger_report_amount_currency
msgid "Currency"
msgstr "Měna"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Currency Rates (%s)"
msgstr "Měnové kurzy (%s)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Currency:"
msgstr "Měna:"

#. module: account_reports
#: model:account.report.column,name:account_reports.deferred_expense_current
#: model:account.report.column,name:account_reports.deferred_revenue_current
msgid "Current"
msgstr "Aktuální"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Běžná aktiva"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Krátkodobé závazky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_1
msgid "Current Year Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Nerozdělený zisk za běžný rok"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Průběžný majetek do pasiv"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Custom Dates"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_id
msgid "Custom Handler Model"
msgstr "Model vlastního zpracování"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_name
msgid "Custom Handler Model Name"
msgstr "Název modelu vlastního zpracování"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid ""
"Custom engine _report_custom_engine_last_statement_balance_amount does not "
"support groupby"
msgstr ""

#. module: account_reports
#: model:mail.template,name:account_reports.email_template_customer_statement
msgid "Customer Statement"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_date
#: model:account.report.column,name:account_reports.general_ledger_report_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__date
msgid "Date"
msgstr "Datum"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Date cannot be empty"
msgstr "Datum nemůže být prázdné"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__date
msgid "Date considered as annotated by the annotation."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Days"
msgstr "Dny"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_debit
#: model:account.report.column,name:account_reports.journal_report_debit
#: model:account.report.column,name:account_reports.partner_ledger_report_debit
#: model:account.report.column,name:account_reports.trial_balance_report_debit
msgid "Debit"
msgstr "Má dáti"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Deductible"
msgstr "Odpočitatelná položka"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "Deferrals have already been generated."
msgstr "Náklady příštích období již byly vytvořeny."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Deferred Entries"
msgstr "Záznamy časového rozlišení"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_expense
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_expense
msgid "Deferred Expense"
msgstr "Náklad příštích období"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_expense_report_handler
msgid "Deferred Expense Custom Handler"
msgstr "Vlastní zpracování nákladu příštích období"

#. module: account_reports
#: model:account.report,name:account_reports.deferred_expense_report
msgid "Deferred Expense Report"
msgstr "Výkaz nákladů příštích období"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_report_handler
msgid "Deferred Expense Report Custom Handler"
msgstr "Vlastní zpracování výkazu nákladů příštích období"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_revenue
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_revenue
msgid "Deferred Revenue"
msgstr "Výnos příštích období"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_revenue_report_handler
msgid "Deferred Revenue Custom Handler"
msgstr "Vlastní zpracování výnosu příštích období"

#. module: account_reports
#: model:account.report,name:account_reports.deferred_revenue_report
msgid "Deferred Revenue Report"
msgstr "Výkaz výnosů příštích období"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Definition"
msgstr "Definice"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Jednotky prodlení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Depending moves"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Descending"
msgstr "Klesající"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Difference from rounding taxes"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_line__display_custom_groupby_warning
msgid "Display Custom Groupby Warning"
msgstr "Zobrazit upozornění na vlastní způsob seskupení"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_mail_composer
msgid "Display Mail Composer"
msgstr "Zobrazit editor zpráv"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Document"
msgstr "Dokument"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Název dokumentů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__domain
msgid "Domain"
msgstr "Doména"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Domestic"
msgstr "Domácí"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_download
msgid "Download"
msgstr "Stáhnout"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Download Anyway"
msgstr "Stáhnout i tak"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Download Excel"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Stáhněte si zprávu o kontrole neměnnosti dat"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Draft Entries"
msgstr "Návrhy záznamů"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Due"
msgstr "Do"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.partner_ledger_report_date_maturity
msgid "Due Date"
msgstr "Datum splatnosti"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
msgid "EC Sales List"
msgstr "Seznam prodejů ES"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_ec_sales_report_handler
msgid "EC Sales Report Custom Handler"
msgstr "Vlastní zpracování výkazu prodejů ES"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on non EC countries"
msgstr "Daň ES na země mimo ES"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on same country"
msgstr "Daň ES ve stejné zemi"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "AKCIE"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed in multivat setup when "
"displaying data from all fiscal positions."
msgstr ""
"Úprava položky manuálního výkazu není povolena v nastavení multivat při "
"zobrazení dat ze všech fiskálních pozic."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed when multiple companies are "
"selected."
msgstr ""
"Úprava položky manuálního výkazu není povolena, pokud je vybráno více "
"společností."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_send_mail
msgid "Email"
msgstr "Email "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_template_id
msgid "Email template"
msgstr "Emailová šablona"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_download
msgid "Enable Download"
msgstr "Povolit stahování"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Enable Sections"
msgstr "Povolit sekci"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_send_mail
msgid "Enable Send Mail"
msgstr "Povolit posílání poštou"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Enable more ..."
msgstr "Povolit více..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "End Balance"
msgstr "Konečný zůstatek"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Month"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Quarter"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Year"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Engine"
msgstr "Mechanismus"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Entries with partners with no VAT"
msgstr "Přihlášky s partnery bez DPH"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Error message"
msgstr "Chybové hlášení"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
msgid "Exchange Rates"
msgstr "Směnné kurzy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__exclude_bank_lines
msgid "Exclude Bank Lines"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Exclude Bank lines"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "Vyloučit měnu poskytování"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_excluded
msgid "Excluded Accounts"
msgstr "Vyloučené účty"

#. module: account_reports
#: model:account.report,name:account_reports.executive_summary
#: model:ir.actions.client,name:account_reports.action_account_report_exec_summary
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_exec_summary
msgid "Executive Summary"
msgstr "Manažerský výkaz"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense Account"
msgstr "Účet nákladů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Účet zajišťování výdajů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Expense Provision for %s"
msgstr "Zajišťování výdajů pro %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
msgid "Expenses"
msgstr "Náklady"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Export"
msgstr "Export"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Formát exportu pro účetní výkazy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Exportovat do"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Průvodce exportem pro účetní výkazy"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Expression"
msgstr "Vzorec"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Expression labelled '%(label)s' of line '%(line)s' is being overwritten when"
" computing the current report. Make sure the cross-report aggregations of "
"this report only reference terms belonging to other reports."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__field_name
msgid "Field"
msgstr "Pole"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s does not exist on account.move.line, and is not supported by this "
"report's custom handler."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s does not exist on account.move.line."
msgstr "Pole %s na account.move.line neexistuje."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s of account.move.line is not stored, and hence cannot be used in a "
"groupby expression"
msgstr ""
"Pole %s z account.move.line není uloženo, a proto jej nelze použít ve výrazu"
" groupby"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field 'Custom Handler Model' can only reference records inheriting from "
"[%s]."
msgstr ""
"Pole 'Model vlastního zpracování' může odkazovat pouze na záznamy zděděné z "
"[%s]."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_content
msgid "File Content"
msgstr "Obsah souboru"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "File Download Errors"
msgstr "Chyby stažení souboru"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_name
msgid "File Name"
msgstr "Název souboru"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Filters"
msgstr "Filtry"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_aml_ir_filters.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Filters:"
msgstr "Filtry:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_budget_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_budget_tree
msgid "Financial Budgets"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_fiscal_position
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskální pozice"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
msgid "Fiscal Position:"
msgstr "Fiskální pozice:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__fpos_synced
msgid "Fiscal Positions Synchronised"
msgstr "Fiskální pozice synchronizována"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid ""
"Fiscal Positions should apply to all companies of the tax unit. You may want"
" to"
msgstr ""
"Fiskální pozice by měla být použita na všechny společnosti daňové jednotky. "
"Možná chcete "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Vstup úpravy cizí měny k %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Formula"
msgstr "Vzorec"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"From %(date_from)s\n"
"to  %(date_to)s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_param
msgid "Function Parameter"
msgstr "Parametr funkce"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Funkce pro volání"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "G %s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
#: model:account.report,name:account_reports.general_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
msgid "General Ledger"
msgstr "Hlavní účetní kniha"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "Vlastní zpracování hlavní účetní knihy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Generate entry"
msgstr "Vytvořit záznam"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "Generated Documents"
msgstr "Vygenerované dokumenty"

#. module: account_reports
#: model:account.report,name:account_reports.generic_ec_sales_report
msgid "Generic EC Sales List"
msgstr "Generický seznam prodejů EC"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr "Vlastní zpracování generických daňových výkazů"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_account_tax
msgid "Generic Tax Report Custom Handler (Account -> Tax)"
msgstr "Vlastní zpracování generických daňových výkazů (Účet -> Daň)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_tax_account
msgid "Generic Tax Report Custom Handler (Tax -> Account)"
msgstr "Vlastní zpracování generických daňových výkazů (Daň -> Účet)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
msgid "Global Tax Summary"
msgstr "Celkové shrnutí daní"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Goods"
msgstr "Zboží"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Grid"
msgstr "Mřížka"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Hrubý zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Hrubý zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Hrubá zisková marže (hrubý zisk / provozní výnosy)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_horizontal_group_form
msgid "Group Name"
msgstr "Název Skupiny"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Group by"
msgstr "Skupina od"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Grouped Deferral Entry of %s"
msgstr "Seskupené záznamy časového rozlišení k %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Account"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Debit/Credit"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hide lines at 0"
msgstr "Skýt řádky s 0"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hierarchy and Subtotals"
msgstr "Hierarchie a mezisoučty"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__horizontal_group_id
msgid "Horizontal Group"
msgstr "Horizontální skupina"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
msgid "Horizontal Group:"
msgstr "Horizontální skupina:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_horizontal_groups
#: model:ir.model.fields,field_description:account_reports.field_account_report__horizontal_group_ids
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_horizontal_groups
msgid "Horizontal Groups"
msgstr "Horizontální skupiny"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group
msgid "Horizontal group for reports"
msgstr "Horizontální skupina pro výkazy"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group_rule
msgid "Horizontal group rule for reports"
msgstr "Pravidlo horizontální skupiny pro výkazy"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Horizontal:"
msgstr "Horizontální:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Jak často musí být podána daňová přiznání"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impact On Grid"
msgstr "Vliv na mřížku"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impacted Tax Grids"
msgstr "Ovlivněné daňové štítky"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "In %s"
msgstr "V %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Inactive"
msgstr "Neaktivní"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Include Payments"
msgstr "Zahrnout parametry"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Including Analytic Simulations"
msgstr "Včetně analytických simulací"

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_payments
#: model:account.report.line,name:account_reports.unreconciled_last_statement_payments
msgid "Including Unreconciled Payments"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_receipt
#: model:account.report.line,name:account_reports.unreconciled_last_statement_receipts
msgid "Including Unreconciled Receipts"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Příjmový účet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "Účet poskytování příjmů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Income Provision for %s"
msgstr "Poskytování příjmů pro %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Inconsistent Statements"
msgstr "Nekonzistentní prohlášení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent data: more than one external value at the same date for a "
"'most_recent' external line."
msgstr ""
"Nekonzistentní data: více než jedna externí hodnota ke stejnému datu pro "
"externí položku „most_recent“."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent report_id in options dictionary. Options says "
"%(options_report)s; report is %(report)s."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "Initial Balance"
msgstr "Počáteční zůstatek"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Integer Rounding"
msgstr "Zaokrouhlení celého čísla"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Intervals cannot be smaller than 1"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "Intra-community taxes are applied on"
msgstr "Uplatňují se intrakomunitární daně"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid domain formula in expression \"%(expression)s\" of line "
"\"%(line)s\": %(formula)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid method “%s”"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid subformula in expression \"%(expression)s\" of line \"%(line)s\": "
"%(subformula)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid token '%(token)s' in account_codes formula '%(formula)s'"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.aged_payable_report_invoice_date
#: model:account.report.column,name:account_reports.aged_receivable_report_invoice_date
#: model:account.report.column,name:account_reports.partner_ledger_report_invoicing_date
msgid "Invoice Date"
msgstr "Datum faktury"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Invoice lines"
msgstr "Položky faktury"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__is_account_coverage_report_available
msgid "Is Account Coverage Report Available"
msgstr "Dostupný přehled o pokrytí účtů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "It seems there is some depending closing move to be posted"
msgstr ""
"Zdá se, že je zde nějaký závislý uzavírací krok, který má být zveřejněn"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a budget with the horizontal group feature."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a horizontal group with the budget feature."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__item_ids
msgid "Items"
msgstr "Položky"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_journal_code
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Účetní deník"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_ja
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_ja
msgid "Journal Audit"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Účetní záznam"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Účetní položka"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#: code:addons/account_reports/static/src/components/general_ledger/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Journal Items"
msgstr "Účetní položky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Journal Items for Tax Audit"
msgstr "Položky účetního deníku pro daňový audit"

#. module: account_reports
#: model:account.report,name:account_reports.journal_report
msgid "Journal Report"
msgstr "Výkaz účetních deníků"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal_report_handler
msgid "Journal Report Custom Handler"
msgstr "Vlastní zpracování výkazu účetních deníků"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Journal items with archived tax tags"
msgstr "Položky deníku se štítky archivovaných daní"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Journals"
msgstr "Účetní deníky"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Journals:"
msgstr "Účetní deníky:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "PASIVA"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "PASIVA + VLASTNÍ KAPITÁL"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_label
msgid "Label"
msgstr "Popis"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_lang
msgid "Lang"
msgstr "Jazyk"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement balance + Transactions since statement"
msgstr "Zůstatek posledního výpisu + transakce od výpisu"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: account_reports
#: model:account.report.line,name:account_reports.last_statement_balance
msgid "Last statement balance"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Later"
msgstr "Později"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Ledger"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_cost_sales0
msgid "Less Costs of Revenue"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_expense0
msgid "Less Operating Expenses"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Less Other Expenses"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__line_id
msgid "Line"
msgstr "Řádek"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Lines"
msgstr "Řádky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Load more..."
msgstr "Načíst další..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_attachments_widget
msgid "Mail Attachments Widget"
msgstr "Widget emailových příloh"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Hlavní společnost"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""
"Hlavní společnost této jednotky; ta, která daně skutečně hlásí a platí."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Make Adjustment Entry"
msgstr "Proveďte zadání úpravy"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_file_download_error_wizard
msgid "Manage the file generation errors from report exports."
msgstr "Spravujte chyby generování souboru z exportu výkazů."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual value"
msgstr "Manuální hodnota"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual values"
msgstr "Manuální hodnoty"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_matching_number
msgid "Matching"
msgstr "Spárováno"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "Členové této jednotky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Method '%(method_name)s' must start with the '%(prefix)s' prefix."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.misc_operations
msgid "Misc. operations"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mode
msgid "Mode"
msgstr "Režim"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__res_model_name
msgid "Model"
msgstr "Model"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Month"
msgstr "Měsíc"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Months"
msgstr "měsíců"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Multi-Ledger:"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Multi-ledger"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_report_handler
msgid "Multicurrency Revaluation Report Custom Handler"
msgstr "Vlastní zpracování výkazu přecenění cizích měn"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "Průvodce přeceněním cizích měn"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__multi
msgid "Multiple Recipients"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for fiscal position %(position)s after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:account.report.line,name:account_reports.journal_report_line
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Name"
msgstr "Název"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "Název, který se má generovaným dokumentům dát."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Čistý zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Čistý majetek"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Net increase in cash and cash equivalents"
msgstr "Čistý nárůst peněžních prostředků a peněžních ekvivalentů"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "Čisté ziskové rozpětí (čistý zisk / zisk)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Never miss a tax deadline."
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "No"
msgstr "Ne"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "No Comparison"
msgstr "Žádné srovnání"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No Journal"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No VAT number associated with your company. Please define one."
msgstr "K vaší společnosti není přiřazeno žádné DIČ. Definujte prosím jedno."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No adjustment needed"
msgstr "Není nutná žádná úprava"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "No data to display !"
msgstr "Žádná data k zobrazení!"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/chart_template.py:0
msgid "No default miscellaneous journal could be found for the active company"
msgstr "V aktivní společnosti nelze nalézt výchozí deník pro ostatní operace."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "No entry to generate."
msgstr "Žádný záznam k vytvoření."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No provision needed was found."
msgstr "Nebylo nalezeno žádné potřebné opatření."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Non Trade Partners"
msgstr "Partneři mimo obchodní styk"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Payable"
msgstr "Závazky mimo obchodní styk"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Receivable"
msgstr "Pohledávky mimo obchodní styk"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Non-Deductible"
msgstr "Neodpočitatelná položka"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "None"
msgstr "Žádné"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Not Started"
msgstr "Nezahájené"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Number of periods cannot be smaller than 1"
msgstr "Počet období nemůže být menší než 1"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "PODROZVAHOVÉ ÚČTY"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Odoo Warning"
msgstr "Odoo varování"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period5
#: model:account.report.column,name:account_reports.aged_receivable_report_period5
msgid "Older"
msgstr "Starší"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "Jeden z vybraných formátů nelze exportovat do DMS"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Změnit data uzamčení mohou pouze správci fakturace!"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_account_reports_customer_statements
msgid "Open Customer Statements"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Počáteční zůstatek finančního roku"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_operating_income0
msgid "Operating Income (or Loss)"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Options"
msgstr "Možnosti"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Options:"
msgstr "Možnosti:"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding
msgid "Outstanding Receipts/Payments"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "ID nadřazené zprávy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Nadřazený průvodce"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#: model:account.report.column,name:account_reports.general_ledger_report_partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__partner_ids
msgid "Partner"
msgstr "Partner"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Partner Categories"
msgstr "Kategorie partnerů"

#. module: account_reports
#: model:account.report,name:account_reports.partner_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
msgid "Partner Ledger"
msgstr "Saldokonto"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_partner_ledger_report_handler
msgid "Partner Ledger Custom Handler"
msgstr "Vlastní zpracování saldokonta"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is bad"
msgstr "Partner je špatný"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is good"
msgstr "Partner je dobrý"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Partner(s) should have an email address."
msgstr "Partneři by měli mít e-mailovou adresu."

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Partners"
msgstr "Partneři"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners Categories:"
msgstr "Kategorie partnerů:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners:"
msgstr "Partneři:"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_pay
msgid "Pay Tax"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Pay tax: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Payable"
msgstr "Závazky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Payable tax amount"
msgstr "Splatná částka daně"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "Závazky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Efektivita skladového hospodářství"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Period"
msgstr "Období"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period1
#: model:account.report.column,name:account_reports.aged_receivable_report_period1
msgid "Period 1"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period2
#: model:account.report.column,name:account_reports.aged_receivable_report_period2
msgid "Period 2"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period3
#: model:account.report.column,name:account_reports.aged_receivable_report_period3
msgid "Period 3"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period4
#: model:account.report.column,name:account_reports.aged_receivable_report_period4
msgid "Period 4"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Period order"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Periodicity"
msgstr "Periodicita"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Periodicita v měsíci"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Periods"
msgstr "Období"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Plans"
msgstr "Plány"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Please enter a valid budget name."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Please select a mail template to send multiple statements."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Please select the main company and its branches in the company selector to "
"proceed."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr "Prosím nastavte účty časového rozlišení v nastavení účetnictví."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr "Prosím nastavte deník časového rozlišení v nastavení účetnictví."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Please specify the accounts necessary for the Tax Closing Entry."
msgstr "Uveďte prosím účty potřebné pro záznam daňové závěrky."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Plus fixný majetek"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Plus neaktuálny majetek"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Plus Dlouhodobé závazky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_other_income0
msgid "Plus Other Income"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Pozice"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Post"
msgstr "Zaúčtovat"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Posted Entries"
msgstr "Zaúčtované položky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Zálohy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Náhled dat"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Period"
msgstr "Předchozí období"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_2
msgid "Previous Years Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Nerozdělené příjmy předchozích let"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Print & Send"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Proceed"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr "Postupujte opatrně, protože pro toto období může existovat úprava ("

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product"
msgstr "Produkt"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product Category"
msgstr "Produktová kategorie"

#. module: account_reports
#: model:account.report,name:account_reports.profit_and_loss
#: model:ir.actions.client,name:account_reports.action_account_report_pl
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_profit_and_loss
msgid "Profit and Loss"
msgstr "Výsledovka"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Ziskovost"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "Návrh na daňové uzavření deníkového záznamu."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Provision for %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"
msgstr "Změna %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Quarter"
msgstr "Kvartál"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
msgid "Rates"
msgstr "Kurzy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Receivable"
msgstr "Pohledávky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Receivable tax amount"
msgstr "Částka splatné daně"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Pohledávky"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_partner_ids
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Recipients"
msgstr "Příjemci"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Zpráva o párování"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Připomínka"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__account_report_id
msgid "Report"
msgstr "Výkaz"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Report Line"
msgstr "Řádek výkazu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Report Name"
msgstr "Název výkazu"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__report_options
msgid "Report Options"
msgstr "Volby sestavy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Report lines mentioning the account code"
msgstr "Položky výkazu s kódem účtu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Report:"
msgstr "Sestava"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Výkazy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__report_ids
msgid "Reports"
msgstr "Sestavy"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Reset to Standard"
msgstr "Obnovit standard"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Nerozdělený zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Návratnost investic (čistý zisk / majetek)"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.report.line,name:account_reports.account_financial_report_revenue0
msgid "Revenue"
msgstr "Výnos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Datum opravného záznamu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Reversal of Grouped Deferral Entry of %s"
msgstr "Storno souhrného dokladu časového rozlišení %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Reversal of: %s"
msgstr "Oprava záznamu: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Root Report"
msgstr "Výkaz předčíslí účtů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__rule_ids
msgid "Rules"
msgstr "Pravidla"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Same Period Last Year"
msgstr "Stejné období minulého roku"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
msgid "Search..."
msgstr "Hledat..."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Sections"
msgstr "Sekce"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send"
msgstr "Odchozí"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__send_and_print_values
msgid "Send And Print Values"
msgstr "Uložit a vytisknout hodnoty"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__send_mail_readonly
msgid "Send Mail Readonly"
msgstr "Poslat email pro čtení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send Partner Ledgers"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.ir_cron_account_report_send_ir_actions_server
msgid "Send account reports automatically"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Send tax report: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Sending statements"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Services"
msgstr "Servisní služby"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "Set as Checked"
msgstr "Nastavit jako zkontrolováno"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Krátkodobá hotovostní předpověď"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Account"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Show All Accounts"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Currency"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Zobrazit varovný pohyb"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__single
msgid "Single Recipient"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "Some"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Some journal items appear to point to obsolete report lines."
msgstr ""
"Zdá se, že některé položky deníku ukazují na zastaralé položky zprávy."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Specific Date"
msgstr "Konkrétní datum"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""
"Zadejte účetní firmu, která bude při exportu výkazů vystupovat jako "
"zástupce."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Split Horizontally"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__tax_closing_start_date
msgid "Start Date"
msgstr "Počáteční datum"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Začít od"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Starting Balance"
msgstr "Počáteční zůstatek"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Statements are being sent in the background."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Subformula"
msgstr "Podřazený vzorec"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_subject
msgid "Subject"
msgstr "Předmět"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Subject..."
msgstr "Předmět..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "T: %s"
msgstr "T: %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Tags"
msgstr "Štítky"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Amount"
msgstr "Výše daně"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Applied"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_alert
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_alert
msgid "Tax Closing Alert"
msgstr "Varování daňové závěrky"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_report_id
msgid "Tax Closing Report"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Tax Declaration"
msgstr "Přiznání k dani"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Tax Grids"
msgstr "Daňové štítky"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "DIČ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.company_information
msgid "Tax ID:"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Paid Adjustment"
msgstr "Úprava zaplacené daně"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/filters/filter_date.xml:0
msgid "Tax Period"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Received Adjustment"
msgstr "Úprava přijaté daně"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Tax Report"
msgstr "Daňový výkaz"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax Report Ready"
msgstr "Připraven daňový výkaz "

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
msgid "Tax Return"
msgstr "Daňové přiznání"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Tax Return Periodicity"
msgstr "Perioda daňového přiznání"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "Daňová entita"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
msgid "Tax Unit:"
msgstr "Daňová jednotka:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "Daňové entity"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity__account_tax_closing_params
msgid "Tax closing additional params"
msgstr ""

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_pay
msgid "Tax is ready to be paid"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Daňové přiznání"

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax report is ready to be sent to the administration"
msgstr "Přiznání k dani je připraveno k odeslání na příslušný úřad"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "Tax return"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Taxes"
msgstr "Daně"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "Taxes Applied"
msgstr "Daně aplikovány"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__fpos_synced
msgid ""
"Technical field indicating whether Fiscal Positions exist for all companies "
"in the unit"
msgstr ""
"Technické pole indikující, zda fiskální pozice existuje pro všechny "
"společnosti v jednotce"

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "Technický model pro stahování účetních výkazů"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/ellipsis/ellipsis.js:0
msgid "Text copied"
msgstr "Text zkopírován"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The Accounts Coverage Report is not available for this report."
msgstr "Přehled o pokrytí účtů není pro tento výkaz dostupný."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover_line/annotation_popover_line.js:0
msgid "The annotation shouldn't have an empty value."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__text
msgid "The annotation's content."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"The attachments of the tax report can be found on the <a href='#' data-oe-"
"model='account.move' data-oe-id='%s'>closing entry</a> of the representative"
" company."
msgstr ""
"Přílohy daňového hlášení najdete na <a href='#' data-oe-model='account.move'"
" data-oe-id='%s'>závěrce</a> zastupující firmy."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "The column '%s' is not available for this report."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"The country detected for this VAT number does not match the one set on this "
"Tax Unit."
msgstr ""
"Země určená pro toto DIČ neodpovídá zemi nastavené na Daňové jednotce."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""
"Země, ve které se tato daňová jednotka používá k seskupení prohlášení o "
"daňových výkazech vašich společností."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "The currency rate cannot be equal to zero"
msgstr "Kurz měny se nemůže rovnat nule"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "The current balance in the"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"The currently selected dates don't match a tax period. The closing entry "
"will be created for the closest-matching period according to your "
"periodicity setup."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "The entry that will be generated will take them into account."
msgstr "Doklad, který bude vytvořen, je vezme v potaz."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__fiscal_position_id
msgid "The fiscal position used while annotating."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__line_id
msgid "The id of the annotated line."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__report_id
msgid "The id of the annotated report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""
"Identifikátor, který se má použít při odesílání výkazu pro tuto jednotku."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "The main company of a tax unit has to be part of it."
msgstr "Hlavní společnost daňové jednotky musí být její součástí."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "Daňové jednotky, do kterých tato jednotka patří."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The used operator is not supported for this expression."
msgstr "Použitý operátor není pro tento výraz podporován."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "There are"
msgstr "Existují"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid ""
"There are currently reports waiting to be sent, please try again later."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "There is no data to display for the given filters."
msgstr "Pro zadaný filtr není co zobrazit."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account exists in the Chart of Accounts but is not mentioned in any "
"line of the report"
msgstr ""
"Tento účet existuje v účtové osnově, ale není uveden v žádné položce výkazu."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account is reported in a line of the report but does not exist in the "
"Chart of Accounts"
msgstr "Tento účet je uveden v položce výkazu, ale v účtové osnově neexistuje"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported in multiple lines of the report"
msgstr "Tento účet je uveden na více položkách výkazu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported multiple times on the same line of the report"
msgstr "Tento účet je uveden vícekrát ve stejné položce výkazu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr "To vám umožní zvolit pozici součtů ve vašich finančních výkazech."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This company is part of a tax unit. You're currently not viewing the whole "
"unit."
msgstr ""
"Tato společnost je součástí daňové jednotky. V tuto chvíli nezobrazujete "
"kompletní jednotku."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid ""
"This line and all its children will be deleted. Are you sure you want to "
"proceed?"
msgstr ""
"Tento řádek a všechny jeho podřádky budou smazány. Opravdu chcete "
"pokračovat?"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "This line uses a custom user-defined 'Group By' value."
msgstr "Tento řádek používá vlastní hodnotu pro seskupení."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "This option hides lines with a value of 0"
msgstr "Tato možnost skryje řádky s hodnotou 0"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This report already has a menuitem."
msgstr "Tento výkaz je již zařazen do menu."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"This report contains inconsistencies. The affected lines are marked with a "
"warning."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "This report only displays the data of the active company."
msgstr "Tento výjaz zobrazuje data pouze z aktivní společnosti."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid ""
"This report uses report-specific code.\n"
"                        You can customize it manually, but any change in the parameters used for its computation could lead to errors."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This report uses the CTA conversion method to consolidate multiple companies using different currencies,\n"
"        which can lead the report to be unbalanced."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This subformula references an unknown expression: %s"
msgstr "Tento podvzorec odkazuje na neznámý výraz: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This tag is reported in a line of the report but is not linked to any "
"account of the Chart of Accounts"
msgstr ""
"Tato položka je uvedena v řádku výkazu, ale není spojena s žádným účtem "
"účtové osnovy."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid ""
"This tax closing entry is posted, but the tax lock date is earlier than the "
"covered period's last day. You might need to reset it to draft and refresh "
"its content, in case other entries using taxes have been posted in the "
"meantime."
msgstr ""
"Tento doklad k vyúčtování daně je zaúčtován, ale datum daňového uzamčení je "
"dřívější, než poslední datum daňového období. Možná jej budete potřebovat "
"vrátit do stavu 'Návrh' a aktualizovat obsah, a to v případě, že mezitím "
"byly zaúčtovány další doklady s daní."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Today"
msgstr "Dnes"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_total
#: model:account.report.column,name:account_reports.aged_receivable_report_total
msgid "Total"
msgstr "Celkem"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Total %s"
msgstr "Celkový %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Trade Partners"
msgstr "Obchodní partneři"

#. module: account_reports
#: model:account.report.line,name:account_reports.transaction_without_statement
msgid "Transactions without statement"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.trial_balance_report
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr "Předvaha"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr "Vlastní 'handler' hlavní knihy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Triangular"
msgstr "Trojúhelníkový"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to dispatch an action on a report not compatible with the provided "
"options."
msgstr ""
"Pokus o spuštění akce na výkazu, který není kompatibilní se zadanými "
"parametry."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to expand a group for a line which was not generated by a report "
"line: %s"
msgstr ""
"Pokus o rozšíření skupiny pro položku, která nebyla vygenerována položkou "
"výkazu: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand a line without an expansion function."
msgstr "Pokus o rozšíření položky bez rozšiřující funkce."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand groupby results on lines without a groupby value."
msgstr ""
"Pokus o rozšíření výsledků po skupinách v položkách bez hodnoty podle "
"skupiny."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Nepřiřazené příjmy"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Unfold All"
msgstr "Rozbalit vše"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown"
msgstr "Neznámá"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Unknown Partner"
msgstr "Neznámý partner"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown bound criterium: %s"
msgstr "Neznámé vázací kritérium: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown date scope: %s"
msgstr "Neznámý rozsah data: %s"

#. module: account_reports
#: model:account.report,name:account_reports.multicurrency_revaluation_report
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
msgid "Unrealized Currency Gains/Losses"
msgstr "Nezrealizované kurzovní rozdíly"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Unreconciled Entries"
msgstr "Nespárované záznamy"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr "DIČ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "VAT Periodicity"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Value"
msgstr "Hodnota"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Vat closing from %(date_from)s to %(date_to)s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View"
msgstr "Zobrazení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Bank Statement"
msgstr "Zobrazení bankovního výpisu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "View Carryover Lines"
msgstr "Zobrazit položky přenosu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Journal Entry"
msgstr "Zobrazit zápis do deníku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View Partner"
msgstr "Zobrazit partnera"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "View Partner(s)"
msgstr "Zobrazit Partner(y)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Payment"
msgstr "Zobrazit platbu"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__warnings
msgid "Warnings"
msgstr "Varování"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr "Po zaškrtnutí se součty a mezisoučty objeví pod částmi zprávy"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""
"Pokud je zaškrtnuto, pod částmi přehledu se zobrazí součty a mezisoučty."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr "Bez ohledu na to, zda musíme provést rezervy pro vybrané cizí měny."

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "With Draft Entries"
msgstr "S koncepty"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Wrong ID for general ledger line to expand: %s"
msgstr "Špatné ID pro rozbalení položky hlavní účetní knihy: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Wrong ID for partner ledger line to expand: %s"
msgstr "Špatné ID pro rozbalení položky saldokonta: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Wrong format for if_other_expr_above/if_other_expr_below formula: %s"
msgstr ""
"Nesprávný formát pro vzorec if_other_expr_above/if_other_expr_below: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Year"
msgstr "Rok"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Yes"
msgstr "Ano"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "You are using custom exchange rates."
msgstr "Používáte vlastní směnné kurzy."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "You can't open a tax report from a move without a VAT closing date."
msgstr "Nelze otevřít daňový výkaz z dokladu bez uzávěrky DPH."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move_line.py:0
msgid "You cannot add taxes on a tax closing move line."
msgstr "Nemůžete přidat daně na zúčtovací doklad daňové závěrky."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid ""
"You cannot generate entries for a period that does not end at the end of the"
" month."
msgstr "Nelze vytvářet záznamy pro období, které nekončí na konci měsíce."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "You cannot generate entries for a period that is locked."
msgstr "Nelze vytvářet záznamy do uzavřeného období."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as another closing entry has "
"been posted at a later date."
msgstr ""
"Tento závěrečný záznam nemůžete resetovat na koncept, protože jiný závěrečný"
" záznam byl zaúčtován později."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as it would delete carryover "
"values impacting the tax report of a locked period. To do this, you first "
"need to modify you tax return lock date."
msgstr ""
"Tento závěrečný záznam nemůžete resetovat na koncept, protože by došlo k "
"odstranění přenesených hodnot ovlivňujících daňový výkaz zamknutého období. "
"Chcete-li to provést, musíte nejprve upravit datum uzamčení daňového "
"přiznání."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "You need to activate more than one currency to access this report."
msgstr "Pro přístup k tomuto výkazu musíte aktivovat více než jednu měnu."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"You're about the generate the closing entries of multiple companies at once."
" Each of them will be created in accordance with its company tax "
"periodicity."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "[Draft]"
msgstr "[Návrh]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "addressed to"
msgstr "adresováno"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "and correct their tax tags if necessary."
msgstr "a opravte jejich štítky daní, pokud je potřeba."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "každoročně"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "dny po období"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "doesn't match the balance of your"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "každé 2 měsíce"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "každé 4 měsíce"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "have a starting balance different from the previous ending balance."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "in the next period."
msgstr "v dalším období."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "invoices"
msgstr "faktury"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "journal items"
msgstr "Účetní deník"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "last bank statement"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "měsíčně"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "n/a"
msgstr "n/a"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "partners"
msgstr "partneři"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "prior or included in this period."
msgstr "před nebo v rámci tohoto období."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "kvartálně"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "pololetně"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "statements"
msgstr "výpisy"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "synchronize fiscal positions"
msgstr "synchronizovat fiskální pozice"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "tax unit [%s]"
msgstr "daňová jednotka [%s]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "that are not established abroad."
msgstr "které nejsou zřízené v zahraničí."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "to"
msgstr "k"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "unposted Journal Entries"
msgstr "neodeslané vnitřní účetní doklady"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "were carried over to this line from previous period."
msgstr "byly na tuto položku přeneseny z předchozího období."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "which don't originate from a bank statement nor payment."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "who are not established in any of the EC countries."
msgstr "kteří nejsou zřízení v žádné ze zemí ES."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to"
msgstr "budou převedeny do"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to this line in the next period."
msgstr "budou převedeny na tuto položku v dalším období."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "without a valid intra-community VAT number."
msgstr "bez platného intrakomunitárního DIČ."

#. module: account_reports
#: model:mail.template,subject:account_reports.email_template_customer_statement
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Statement - {{ object.commercial_company_name }}"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Obnovit rychlost Odoo"
