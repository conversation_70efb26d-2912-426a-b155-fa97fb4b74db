# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "\" account balance is affected by"
msgstr "\" يتأثر رصيد الحساب بـ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "%(journal)s - %(account)s"
msgstr "%(journal)s - %(account)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and %(remaining)s others"
msgstr "%(names)s و%(remaining)s آخرين "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and one other"
msgstr "%(names)s وشخص آخر "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "%(report_label)s: %(period)s"
msgstr "%(report_label)s: %(period)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%s is not a numeric value"
msgstr "%s ليست قيمة عددية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "%s selected"
msgstr "تم تحديد %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'Open General Ledger' caret option is only available form report lines "
"targetting accounts."
msgstr ""
"خيار علامة إقحام 'فتح دفتر الأستاذ العام' متاح فقط من بنود التقرير التي "
"تستهدف الحسابات. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'View Bank Statement' caret option is only available for report lines "
"targeting bank statements."
msgstr ""
"خيار علامة إقحام 'عرض كشف الحساب البنكي' متاح فقط من بنود التقرير التي "
"تستهدف كشوف الحسابت البنكية. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'external' engine does not support groupby, limit nor offset."
msgstr "لا يدعم المحرك 'الخارجي' عمليات التجميع حسب، أو التقييد، أو الإزاحة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(%s lines)"
msgstr "(%s بنود) "

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_receipts
msgid "(+) Outstanding Receipts"
msgstr "(+) الإيصالات المستحقة "

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_payments
msgid "(-) Outstanding Payments"
msgstr "(-) المدفوعات المستحقة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(1 line)"
msgstr "(بند 1) "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "(No %s)"
msgstr "(لا %s) "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(No Group)"
msgstr "(بلا تجميع)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid ", leading to an unexplained difference of"
msgstr "، والذي يؤدي إلى فريق لا يمكن تفسيره في "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> تحديث"

#. module: account_reports
#: model:mail.template,body_html:account_reports.email_template_customer_statement
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px;\">\n"
"                    <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                    <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                    <br/>\n"
"                    Please find enclosed the statement of your account.\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any questions.\n"
"                    <br/>\n"
"                    Sincerely,\n"
"                    <br/>\n"
"\t                <t t-out=\"object._get_followup_responsible().name if is_html_empty(object._get_followup_responsible().signature) else object._get_followup_responsible().signature\"/>\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"The email address is unknown on the partner\" invisible=\"not "
"send_mail_readonly\"/>"
msgstr ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"عنوان البريد الإلكتروني غير معرف لدى الشريك \" invisible=\"not "
"send_mail_readonly\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid ""
"<i>Errors marked with <i class=\"fa fa-warning\"/> are critical and prevent "
"the file generation.</i>"
msgstr ""
"<i>تعتبر الأخطاء التي تم وضع علامة <i class=\"fa fa-warning\"/> عليها خطيرة "
"وتمنع إنشاء الملف.</i>   "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr "<span role=\"separator\">التسوية</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "<span>One or more error(s) occurred during file generation:</span>"
msgstr "<span>وقع خطأ واحد أو أكثر أثناء عملية إنشاء الملف:</span> "

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_report_horizontal_group_name_uniq
msgid "A horizontal group with the same name already exists."
msgstr "توجد مجموعة أفقية بنفس الاسم بالفعل. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid "A line with a 'Group By' value cannot have children."
msgstr "البند الذي به قيمة 'التجميع حسب' لا يمكن أن يكون له توابع. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr "يمكن إنشاء الوحدة الضريبية فقط بين الشركات التي تتشارك نفس العملة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit must contain a minimum of two companies. You might want to delete"
" the unit."
msgstr ""
"يجب أن تحتوي وحدة الضريبة على شركتين كحد أدنى. قد ترغب في حذف الوحدة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "AP %s"
msgstr "AP %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "AR %s"
msgstr "AR %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "الأصول"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_account_name
#: model:account.report.column,name:account_reports.aged_receivable_report_account_name
#: model:account.report.column,name:account_reports.partner_ledger_report_account_code
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__account_id
msgid "Account"
msgstr "الحساب "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "نموذج مخطط الحساب "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code"
msgstr "كود الحساب "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code / Tag"
msgstr "علامة تصنيف / كود الحساب "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "حقل ممثل لعرض الحساب "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Account Label"
msgstr "عنوان الحساب "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_annotation
msgid "Account Report Annotation"
msgstr "شرح تقرير الحساب "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_custom_handler
msgid "Account Report Custom Handler"
msgstr "المعالج المخصص لتقارير الحساب "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_report_handler
msgid "Account Report Handler for Tax Reports"
msgstr "معالج تقرير الحساب للتقارير الضريبية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_send
msgid "Account Report Send"
msgstr "إرسال التقرير المحاسبي "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_reports_show_per_company_setting
msgid "Account Reports Show Per Company Setting"
msgstr "عرض تقارير الحساب حسب إعدادات الشركة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "شركة ممثَّلة في الحساب "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "يومية إعادة تقييم الحساب "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_account_type.xml:0
msgid "Account:"
msgstr "الحساب: "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "مؤسسة محاسبية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "Accounting Report"
msgstr "تقرير المحاسبة "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget
msgid "Accounting Report Budget"
msgstr "ميزانية التقرير المحاسبي "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget_item
msgid "Accounting Report Budget Item"
msgstr "عنصر ميزانية التقرير المحاسبي "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_expression
msgid "Accounting Report Expression"
msgstr "تعبير التقرير المحاسبي "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_line
msgid "Accounting Report Line"
msgstr "بند التقرير المحاسبي "

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_tree
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Accounting Reports"
msgstr "التقارير المحاسبية"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Accounts"
msgstr "الحسابات"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Accounts Coverage Report"
msgstr "تقرير تغطية الحسابات "

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_to_adjust
msgid "Accounts To Adjust"
msgstr "الحسابات بانتظار التعديل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Accounts coverage"
msgstr "تغطية الحسابات "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "إجراء"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__actionable_errors
msgid "Actionable Errors"
msgstr "أخطاء قابلة للتنفيذ "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"قد تؤدي الإجراءات إلى سلوك معين مثل فتح طريقة عرض التقويم أو وضع علامة "
"\"تم\" تلقائياً عند تحميل مستند "

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "النشاط"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "نوع النشاط"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
msgid "Add a line"
msgstr "إضافة بند"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Add contacts to notify..."
msgstr "إضافة جهات اتصال لإشعارهم..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "إضافة إجمالي تحت الأقسام"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_adjustment
msgid "Adjustment"
msgstr "التعديلات"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Adjustment Entry"
msgstr "تعديل القيد"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance Payments received from customers"
msgstr "المدفوعات المدفوعة مقدماً من قِبَل العملاء "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance payments made to suppliers"
msgstr "المدفوعات المدفوعة مقدمًا للموردين"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Advanced"
msgstr "متقدم"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner_balance_report_handler
msgid "Aged Partner Balance Custom Handler"
msgstr "المعالج المخصص لأعمار ديون الشريك "

#. module: account_reports
#: model:account.report,name:account_reports.aged_payable_report
#: model:account.report.line,name:account_reports.aged_payable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
msgid "Aged Payable"
msgstr "حسابات دائنة مستحقة متأخرة "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_payable_report_handler
msgid "Aged Payable Custom Handler"
msgstr "المعالج المخصص للحسابات الدائنة المستحقة "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "حساب دائن مستحق متأخر "

#. module: account_reports
#: model:account.report,name:account_reports.aged_receivable_report
#: model:account.report.line,name:account_reports.aged_receivable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
msgid "Aged Receivable"
msgstr "المتأخر المدين"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_receivable_report_handler
msgid "Aged Receivable Custom Handler"
msgstr "المعالج المخصص للحسابات المدينة المستحقة "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "المتأخرات المدينة"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "All"
msgstr "الكل"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Journals"
msgstr "كافة اليوميات "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Payable"
msgstr "جميع الذمم مستحقة الدفع "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Receivable"
msgstr "جميع الذمم المدينة"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Report Variants"
msgstr "كافة متغيرات التقرير "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"All selected companies or branches do not share the same Tax ID. Please "
"check the Tax ID of the selected companies."
msgstr ""
"لا تتشارك كل الشركات أو الفروع المحددة في نفس المُعرِّف الضريبي. يُرجى "
"التحقق من المُعرِّف الضريبي للشركات المحددة. "

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_amount
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount
#: model:account.report.column,name:account_reports.partner_ledger_amount
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__amount
msgid "Amount"
msgstr "مبلغ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_amount_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_amount_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_amount_currency
msgid "Amount Currency"
msgstr "عملة المبلغ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Amount in currency: %s"
msgstr "المبلغ بالعملة: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Lakhs"
msgstr "المبلغ باللاكس "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Millions"
msgstr "المبالغ بالملايين "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Thousands"
msgstr "المبالغ بالآلاف "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Analytic"
msgstr "تحليلي "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__filter_analytic_groupby
msgid "Analytic Group By"
msgstr "التجميع التحليلي حسب "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Analytic Simulations"
msgstr "عمليات المحاكاة التحليلية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/line_name.xml:0
msgid "Annotate"
msgstr "تعليق"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
msgid "Annotation"
msgstr "الشرح "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__annotations_ids
msgid "Annotations"
msgstr "الشرح "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "As of %s"
msgstr "اعتبارًا من %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Ascending"
msgstr "تصاعدي "

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period0
#: model:account.report.column,name:account_reports.aged_receivable_report_period0
msgid "At Date"
msgstr "بتاريخ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Attach a file"
msgstr "إرفاق ملف"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line_name.xml:0
msgid "Audit"
msgstr "تدقيق"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "تقارير التدقيق"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "متوسط أيام الدائنين"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "متوسط أيام المدينين"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "B: %s"
msgstr "B: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.balance_sheet_balance
#: model:account.report.column,name:account_reports.cash_flow_report_balance
#: model:account.report.column,name:account_reports.executive_summary_column
#: model:account.report.column,name:account_reports.general_ledger_report_balance
#: model:account.report.column,name:account_reports.journal_report_balance
#: model:account.report.column,name:account_reports.partner_ledger_report_balance
#: model:account.report.column,name:account_reports.profit_and_loss_column
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance"
msgstr "الرصيد"

#. module: account_reports
#: model:account.report,name:account_reports.balance_sheet
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_balance_sheet
msgid "Balance Sheet"
msgstr "الميزانية العمومية"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_balance_sheet_report_handler
msgid "Balance Sheet Custom Handler"
msgstr "المعالج المخصص للميزانية العمومية "

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_current
msgid "Balance at Current Rate"
msgstr "الرصيد بسعر الصرف الحالي "

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_operation
msgid "Balance at Operation Rate"
msgstr "الرصيد بسعر العملية "

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_currency
msgid "Balance in Foreign Currency"
msgstr "الرصيد بالعملة الأجنبية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Balance of '%s'"
msgstr "رصيد '%s' "

#. module: account_reports
#: model:account.report.line,name:account_reports.balance_bank
msgid "Balance of Bank"
msgstr "رصيد البنك "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax advance payment account"
msgstr "حساب رصيد الضريبة مسبقة الدفع "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (payable)"
msgstr "حساب رصيد الضريبة مسبقة الدفع (الدائن)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (receivable)"
msgstr "حساب رصيد الضريبة مسبقة الدفع (المدين)"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
msgid "Bank Reconciliation"
msgstr "التسوية البنكية"

#. module: account_reports
#: model:account.report,name:account_reports.bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "تقرير التسوية البنكية"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report_handler
msgid "Bank Reconciliation Report Custom Handler"
msgstr "المعالج المخصص لتقارير تسوية البنك "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "الحسابات البنكية والنقدية "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Base Amount"
msgstr "المبلغ الأساسي"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Based on"
msgstr "بناءً على"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Before"
msgstr "قبل"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__budget_id
msgid "Budget"
msgstr "الميزانية "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__budget_item_ids
msgid "Budget Item"
msgstr "عنصر الميزانية "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Items"
msgstr "عناصر الميزانية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Name"
msgstr "اسم الميزانية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Budget items can only be edited from account lines."
msgstr "يمكن تحرير عناصر الميزانية فقط من بنود الحساب. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/redirectAction/redirectAction.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Cancel"
msgstr "إلغاء"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Cannot audit tax from another model than account.tax."
msgstr "لا يمكن تدقيق الضريبة من نموذج آخر غير account.tax. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Cannot generate carryover values for all fiscal positions at once!"
msgstr "لا يمكن إنشاء قيم الترحيل لكافة الأوضاع المالية في آن واحد! "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "Carryover"
msgstr "الترحيل"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover adjustment for tax unit"
msgstr "تعديل الترحيل لوحدة الضريبة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover can only be generated for a single column group."
msgstr "يمكن إنشاء الترحيل فقط لمجموعة عمود واحدة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover from %(date_from)s to %(date_to)s"
msgstr "ترحيل من %(date_from)s إلى %(date_to)s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover lines for: %s"
msgstr "ترحيل القيود لـ: %s "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "نقدي"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report_handler
msgid "Cash Flow Report Custom Handler"
msgstr "المعالج المخصص لتقارير التدفق النقدي "

#. module: account_reports
#: model:account.report,name:account_reports.cash_flow_report
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
msgid "Cash Flow Statement"
msgstr "كشف التدفقات النقدية"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, beginning of period"
msgstr "النقد ومعادِلات النقد، بداية الفترة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, closing balance"
msgstr "النقد وومعادِلات النقد، رصيد الإقفال "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from financing activities"
msgstr "التدفقات النقدية من الأنشطة المالية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from investing & extraordinary activities"
msgstr "التدفقات النقدية من الأنشطة الاستثمارية وغير العادية"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from operating activities"
msgstr "التدفقات النقدية من الأنشطة التشغيلية"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from unclassified activities"
msgstr "التدفقات النقدية من الأنشطة غير المصنفة"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash in"
msgstr "إيرادات"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash out"
msgstr "نفقات "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash paid for operating activities"
msgstr "نفقات الأنشطة التشغيلية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "الأرباح النقدية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash received from operating activities"
msgstr "أرباح الأنشطة التشغيلية"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "المبلغ المُنفَق "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "الفائض النقدي"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "تغيير تاريخ الإقفال"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr "تحقق من عناوين البريد الإلكتروني للشركاء "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Check them"
msgstr "تفقدهم "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Close"
msgstr "إغلاق"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Closing Entry"
msgstr "قيد الإقفال "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "رصيد الإقفال البنكي "

#. module: account_reports
#: model:account.report.column,name:account_reports.journal_report_code
msgid "Code"
msgstr "رمز "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/filters/filter_code.xml:0
msgid "Codes:"
msgstr "الأكواد: "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Columns"
msgstr "الأعمدة"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_communication
msgid "Communication"
msgstr "التواصل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "الشركات"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__company_id
msgid "Company"
msgstr "الشركة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"Company %(company)s already belongs to a tax unit in %(country)s. A company "
"can at most be part of one tax unit per country."
msgstr ""
"الشركة %(company)s تنتمي بالفعل إلى وحدة ضريبية في %(country)s. يمكن أن تكون"
" الشركة الواحدة جزءاً من وحدة ضريبية واحدة كحد أقصى لكل دولة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Currency"
msgstr "عملة الشركة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Company Only"
msgstr "الشركة فقط "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Settings"
msgstr "إعدادات الشركة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Comparison"
msgstr "مقارنة"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure start dates"
msgstr "تهيئة تواريخ البدء "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Configure your TAX accounts - %s"
msgstr "قم بتهيئة حساباتك الضريبية - %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_config_settings.py:0
msgid "Configure your start dates"
msgstr "قم بتهيئة تواريخ البدء "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "قم بتهيئة حساباتك الضريبية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_body
msgid "Contents"
msgstr "المحتويات"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "تكاليف الإيرادات "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Could not expand term %(term)s while evaluating formula "
"%(unexpanded_formula)s"
msgstr "hello "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Could not parse account_code formula from token '%s'"
msgstr "تعذر تحليل صيغة account_code من الرمز '%s' "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Country"
msgstr "الدولة"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "رمز الدولة"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Create"
msgstr "إنشاء "

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_composite_report_list
msgid "Create Composite Report"
msgstr "إنشاء تقرير مُركّب "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "إنشاء قيد "

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_report_menu
msgid "Create Menu Item"
msgstr "إنشاء عنصر قائمة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_credit
#: model:account.report.column,name:account_reports.journal_report_credit
#: model:account.report.column,name:account_reports.partner_ledger_report_credit
#: model:account.report.column,name:account_reports.trial_balance_report_credit
msgid "Credit"
msgstr "الدائن"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_currency
#: model:account.report.column,name:account_reports.general_ledger_report_amount_currency
msgid "Currency"
msgstr "العملة"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Currency Rates (%s)"
msgstr "أسعار صرف العملة (%s)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Currency:"
msgstr "العملة: "

#. module: account_reports
#: model:account.report.column,name:account_reports.deferred_expense_current
#: model:account.report.column,name:account_reports.deferred_revenue_current
msgid "Current"
msgstr "الحالي "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "الأصول المتداولة"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "الالتزامات الجارية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_1
msgid "Current Year Retained Earnings"
msgstr "الأرباح المحتجزة للسنة الجارية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "الأرباح غير المخصصة للسنة الجارية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "نسبة الأصول المتداولة إلى الالتزامات"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Custom Dates"
msgstr "التواريخ المخصصة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_id
msgid "Custom Handler Model"
msgstr "نموذج للمعالج المخصص "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_name
msgid "Custom Handler Model Name"
msgstr "اسم نموذج المعالج المخصص "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid ""
"Custom engine _report_custom_engine_last_statement_balance_amount does not "
"support groupby"
msgstr ""
"المحرك المخصص _report_custom_engine_last_statement_balance_amount لا يدعم "
"خاصية groupby "

#. module: account_reports
#: model:mail.template,name:account_reports.email_template_customer_statement
msgid "Customer Statement"
msgstr "كشف حساب العميل "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_date
#: model:account.report.column,name:account_reports.general_ledger_report_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__date
msgid "Date"
msgstr "التاريخ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Date cannot be empty"
msgstr "لا يمكن ترك خانة التاريخ فارغة"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__date
msgid "Date considered as annotated by the annotation."
msgstr "يُعتَبَر التاريخ مشروحاً بواسطة الشرح. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Days"
msgstr "أيام "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_debit
#: model:account.report.column,name:account_reports.journal_report_debit
#: model:account.report.column,name:account_reports.partner_ledger_report_debit
#: model:account.report.column,name:account_reports.trial_balance_report_debit
msgid "Debit"
msgstr "المدين"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Deductible"
msgstr "قابل للاستقطاع "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "Deferrals have already been generated."
msgstr "تم إنشاء الدفعة المؤجلة بالفعل. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Deferred Entries"
msgstr "القيم المؤجلة "

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_expense
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_expense
msgid "Deferred Expense"
msgstr "النفقات المؤجلة "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_expense_report_handler
msgid "Deferred Expense Custom Handler"
msgstr "أداة مخصصة لمعالجة النفقات المؤجلة "

#. module: account_reports
#: model:account.report,name:account_reports.deferred_expense_report
msgid "Deferred Expense Report"
msgstr "تقرير النفقات المؤجلة "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_report_handler
msgid "Deferred Expense Report Custom Handler"
msgstr "أداة مخصصة لمعالجة تقرير النفقات المؤجلة "

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_revenue
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_revenue
msgid "Deferred Revenue"
msgstr "الإيرادات المؤجلة "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_revenue_report_handler
msgid "Deferred Revenue Custom Handler"
msgstr "أداة مخصصة لمعالجة الإيرادات المؤجلة "

#. module: account_reports
#: model:account.report,name:account_reports.deferred_revenue_report
msgid "Deferred Revenue Report"
msgstr "تقرير الإيرادات المؤجلة "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Definition"
msgstr "تعريف"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "وحدات التأخير"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Depending moves"
msgstr "الحركات المعتمدة على غيرها "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Descending"
msgstr "تنازلي "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Difference from rounding taxes"
msgstr "الفرق من تقريب الضرائب "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_line__display_custom_groupby_warning
msgid "Display Custom Groupby Warning"
msgstr "عرض تحذير \"التجميع حسب\" المخصص "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_mail_composer
msgid "Display Mail Composer"
msgstr "عرض أداة إنشاء البريد الإلكتروني "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Document"
msgstr "المستند "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "اسم المستندات "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__domain
msgid "Domain"
msgstr "النطاق"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Domestic"
msgstr "محلي"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_download
msgid "Download"
msgstr "تنزيل "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Download Anyway"
msgstr "التنزيل بأي حال "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Download Excel"
msgstr "تنزيل إكسل "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "تحميل تقرير التحقق من بيانات عدم قابلية التغيير "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Draft Entries"
msgstr "القيود في حالة المسودة "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Due"
msgstr "مستحق"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.partner_ledger_report_date_maturity
msgid "Due Date"
msgstr "موعد إجراء المكالمة "

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
msgid "EC Sales List"
msgstr "قائمة مبيعات EC"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_ec_sales_report_handler
msgid "EC Sales Report Custom Handler"
msgstr "المعالج المخصص لتقارير مبيعات العمولة الأوروبية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on non EC countries"
msgstr "ضريبة الاتحاد الأوروبي للدول خارج الاتحاد الأوروبي "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on same country"
msgstr "ضريبة الاتحاد الأوروبي في نفس الدولة "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "رأس المال "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed in multivat setup when "
"displaying data from all fiscal positions."
msgstr ""
"لا يُسمح بتحرير تقرير يدوي في بيئة متعددة الضرائب عند عرض البيانات من كافة "
"الأوضاع المالية. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed when multiple companies are "
"selected."
msgstr "لا يُسمح بتحرير تقرير يدوي عندما يتم تحديد عدة شركات. "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_send_mail
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_template_id
msgid "Email template"
msgstr "قالب البريد الإلكتروني "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_download
msgid "Enable Download"
msgstr "تمكين التنزيل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Enable Sections"
msgstr "تمكين الأجزاء "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_send_mail
msgid "Enable Send Mail"
msgstr "تمكين إرسال البريد الإلكتروني "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Enable more ..."
msgstr "تمكين المزيد... "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "End Balance"
msgstr "الرصيد النهائي "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Month"
msgstr "نهاية الشهر "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Quarter"
msgstr "نهاية ربع السنة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Year"
msgstr "نهاية العام "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Engine"
msgstr "المحرك "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Entries with partners with no VAT"
msgstr "القيود التي بها شركاء بلا ضريبة القيمة المضافة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Error message"
msgstr "رسالة خطأ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
msgid "Exchange Rates"
msgstr "أسعار الصرف "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__exclude_bank_lines
msgid "Exclude Bank Lines"
msgstr "استثناء بنود البنك "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Exclude Bank lines"
msgstr "استثناء بنود البنك "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "استثناء عملة الحكم "

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_excluded
msgid "Excluded Accounts"
msgstr "الحسابات المستثناة "

#. module: account_reports
#: model:account.report,name:account_reports.executive_summary
#: model:ir.actions.client,name:account_reports.action_account_report_exec_summary
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_exec_summary
msgid "Executive Summary"
msgstr "الملخص التنفيذي"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense Account"
msgstr "حساب النفقات "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "حساب أحكام النفقات "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Expense Provision for %s"
msgstr "حكم النفقات لـ %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
msgid "Expenses"
msgstr "النفقات "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Export"
msgstr "تصدير"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "صيغة التصدير للتقارير المحاسبية "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "التصدير إلى "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "مُعالِج التصدير للتقارير المحاسبية "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Expression"
msgstr "تعبير "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Expression labelled '%(label)s' of line '%(line)s' is being overwritten when"
" computing the current report. Make sure the cross-report aggregations of "
"this report only reference terms belonging to other reports."
msgstr ""
"تتم الكتابة فوق التعبير الذي عنوانه \"%(label)s\" من البند \"%(line)s\" عند "
"احتساب التقرير الحالي. تأكد من أن مجموعات التقارير التبادلية لهذا التقرير "
"تشير فقط إلى الشروط التي تنتمي إلى تقارير أخرى. "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__field_name
msgid "Field"
msgstr "حقل"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s does not exist on account.move.line, and is not supported by this "
"report's custom handler."
msgstr ""
"الحقل %s غير موجود في account.move.line، وهو غير مدعوم في أداة معالجة "
"التقارير هذه. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s does not exist on account.move.line."
msgstr "الحقل %s غير موجود في account.move.line. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s of account.move.line is not stored, and hence cannot be used in a "
"groupby expression"
msgstr ""
"الحقل %s لـ account.move.line غير مخزن، وبالتالي لا يمكن استخدامه في تعبير "
"\"groupby\" "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field 'Custom Handler Model' can only reference records inheriting from "
"[%s]."
msgstr ""
"الحقل 'نموذج المعالج المخصص' يمكنه فقط الإشارة إلى السجلات التي ترث من [%s]."
" "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_content
msgid "File Content"
msgstr "محتوى الملف "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "File Download Errors"
msgstr "أخطاء تنزيل الملف "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_name
msgid "File Name"
msgstr "اسم الملف"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Filters"
msgstr "عوامل التصفية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_aml_ir_filters.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Filters:"
msgstr "عوامل التصفية: "

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_budget_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_budget_tree
msgid "Financial Budgets"
msgstr "الميزانيات المالية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_fiscal_position
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__fiscal_position_id
msgid "Fiscal Position"
msgstr "الوضع المالي "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
msgid "Fiscal Position:"
msgstr "الوضع المالي: "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__fpos_synced
msgid "Fiscal Positions Synchronised"
msgstr "الأوضاع المالية المتزامنة "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid ""
"Fiscal Positions should apply to all companies of the tax unit. You may want"
" to"
msgstr ""
"يجب أن تنطبق الأوضاع المالية على كافة الشركات لوحدة الضريبة. ربما عليك "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Foreign currencies adjustment entry as of %s"
msgstr "قيد تعديل العملات الأجنبية اعتباراً من %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Formula"
msgstr "الصيغة"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"From %(date_from)s\n"
"to  %(date_to)s"
msgstr ""
"من %(date_from)s\n"
"إلى  %(date_to)s "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_param
msgid "Function Parameter"
msgstr "معايير الوظيفة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "الوظيفة لاستدعائها "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "G %s"
msgstr "G %s"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
#: model:account.report,name:account_reports.general_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
msgid "General Ledger"
msgstr "دفتر الأستاذ العام"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "المعالج المخصص لدفتر الأستاذ العام "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Generate entry"
msgstr "إنشاء قيد "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "Generated Documents"
msgstr "إنشاء المستندات "

#. module: account_reports
#: model:account.report,name:account_reports.generic_ec_sales_report
msgid "Generic EC Sales List"
msgstr "قائمة مبيعات EC عامة "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr "معالج مخصص للتقرير الضريبي العام "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_account_tax
msgid "Generic Tax Report Custom Handler (Account -> Tax)"
msgstr "معالج مخصص للتقرير الضريبي العام (الحساب -> الضريبة) "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_tax_account
msgid "Generic Tax Report Custom Handler (Tax -> Account)"
msgstr "معالج مخصص للتقرير الضريبي العام (الضريبة -> الحساب) "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
msgid "Global Tax Summary"
msgstr "الملخص الشامل للضريبة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Goods"
msgstr "البضائع "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Grid"
msgstr "الشبكة"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "إجمالي الربح"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "إجمالي الربح"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "إجمالي هامش الربح (إجمالي الربح / الدخل التشغيلي)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_horizontal_group_form
msgid "Group Name"
msgstr "اسم المجموعة"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Group by"
msgstr "التجميع حسب "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Grouped Deferral Entry of %s"
msgstr "القيد المؤجل المجمع لـ %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Account"
msgstr "إخفاء الحساب "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Debit/Credit"
msgstr "إخفاء الخصم / الائتمان "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hide lines at 0"
msgstr "إخفاء البنود في 0 "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hierarchy and Subtotals"
msgstr "التسلسل الهرمي والمجاميع الفرعية"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__horizontal_group_id
msgid "Horizontal Group"
msgstr "المجموعة الأفقية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
msgid "Horizontal Group:"
msgstr "المجموعة الأفقية: "

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_horizontal_groups
#: model:ir.model.fields,field_description:account_reports.field_account_report__horizontal_group_ids
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_horizontal_groups
msgid "Horizontal Groups"
msgstr "المجموعات الأفقية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group
msgid "Horizontal group for reports"
msgstr "المجموعة الأفقية للتقارير "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group_rule
msgid "Horizontal group rule for reports"
msgstr "قاعدة المجموعة الأفقية للتقارير "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Horizontal:"
msgstr "أفقي: "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "مدى تواتر الإقرارات الضريبية "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "المُعرف"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impact On Grid"
msgstr "التأثير على الشبكة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impacted Tax Grids"
msgstr "شبكات الضرائب المتأثرة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "In %s"
msgstr "في %s "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Inactive"
msgstr "غير نشط "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Include Payments"
msgstr "تضمين المدفوعات "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Including Analytic Simulations"
msgstr "شاملة عمليات المحاكات التحليلية "

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_payments
#: model:account.report.line,name:account_reports.unreconciled_last_statement_payments
msgid "Including Unreconciled Payments"
msgstr "شاملة المدفوعات غير المسواة "

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_receipt
#: model:account.report.line,name:account_reports.unreconciled_last_statement_receipts
msgid "Including Unreconciled Receipts"
msgstr "شاملة الإيصالات غير المسواة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "حساب الدخل"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "حساب أحكام الدخل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Income Provision for %s"
msgstr "حكم الدخل لـ %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Inconsistent Statements"
msgstr "كشوفات الحساب غير المتسقة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent data: more than one external value at the same date for a "
"'most_recent' external line."
msgstr ""
"Inconsistent data: more than one external value at the same date for a "
"'most_recent' external line."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent report_id in options dictionary. Options says "
"%(options_report)s; report is %(report)s."
msgstr ""
"report_id غير متسق في دليل الخيارات. يقول الخيار %(options_report)s؛ التقرير"
" %(report)s.  "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "Initial Balance"
msgstr "الرصيد الافتتاحي"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Integer Rounding"
msgstr "تقريب العدد الصحيح "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Intervals cannot be smaller than 1"
msgstr "لا يمكن أن تكون الفترات الزمنية أقل من 1 "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "Intra-community taxes are applied on"
msgstr "الضرائب بين المجتمعات مطبقة على "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid domain formula in expression \"%(expression)s\" of line "
"\"%(line)s\": %(formula)s"
msgstr ""
"صيغة النطاق غير صالحة في التعبير \"%(expression)s\" في البند \"%(line)s\": "
"%(formula)s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid method “%s”"
msgstr "الطريقة غير صحيحة \"%s\" "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid subformula in expression \"%(expression)s\" of line \"%(line)s\": "
"%(subformula)s"
msgstr ""
"الصيغة الفرعية غير صالحة في التعبير \"%(expression)s\" في البند "
"\"%(line)s\": %(subformula)s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid token '%(token)s' in account_codes formula '%(formula)s'"
msgstr "الرمز غير صالح '%(token)s' في صيغة account_codes '%(formula)s' "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.aged_payable_report_invoice_date
#: model:account.report.column,name:account_reports.aged_receivable_report_invoice_date
#: model:account.report.column,name:account_reports.partner_ledger_report_invoicing_date
msgid "Invoice Date"
msgstr "تاريخ الفاتورة"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Invoice lines"
msgstr "بنود الفاتورة"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__is_account_coverage_report_available
msgid "Is Account Coverage Report Available"
msgstr "تقرير تغطية الحسابات متاح "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "It seems there is some depending closing move to be posted"
msgstr "يبدو أنه توجد حركة إغلاق معتمدة على غيرها يجب أن يتم ترحيلها. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a budget with the horizontal group feature."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a horizontal group with the budget feature."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__item_ids
msgid "Items"
msgstr "العناصر "

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_journal_code
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "دفتر اليومية"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_ja
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_ja
msgid "Journal Audit"
msgstr "تدقيق دفتر اليومية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#: code:addons/account_reports/static/src/components/general_ledger/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Journal Items for Tax Audit"
msgstr "عناصر اليومية للتدقيق الضريبي"

#. module: account_reports
#: model:account.report,name:account_reports.journal_report
msgid "Journal Report"
msgstr "تقرير اليومية "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal_report_handler
msgid "Journal Report Custom Handler"
msgstr "المعالج المخصص لتقرير اليومية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Journal items with archived tax tags"
msgstr "عناصر دفتر اليومية مع علامات تصنيف مؤرشفة للضريبة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Journals"
msgstr "دفاتر اليومية"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Journals:"
msgstr "دفاتر اليومية:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "التزامات"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "الالتزامات + رأس المال "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_label
msgid "Label"
msgstr "بطاقة عنوان"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_lang
msgid "Lang"
msgstr "اللغة "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement balance + Transactions since statement"
msgstr "رصيد آخر كشف حساب + المعاملات منذ إصدار كشف الحساب "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_reports
#: model:account.report.line,name:account_reports.last_statement_balance
msgid "Last statement balance"
msgstr "رصيد آخر كشف حساب "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Later"
msgstr "لاحقاً"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Ledger"
msgstr "دفاتر الأستاذ "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_cost_sales0
msgid "Less Costs of Revenue"
msgstr "تكاليف إيرادات أقل "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_expense0
msgid "Less Operating Expenses"
msgstr "نفقات تشغيلية أقل "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Less Other Expenses"
msgstr "نفقات أخرى أقل "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__line_id
msgid "Line"
msgstr "البند "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Lines"
msgstr "البنود"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Load more..."
msgstr "تحميل المزيد... "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_attachments_widget
msgid "Mail Attachments Widget"
msgstr "أداة مرفقات البريد الإلكتروني "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "الشركة الرئيسية "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""
"الشركة الرئيسية لهذه الوحدة؛ الشركة التي تقوم بإصدار التقارير ودفع الضرائب. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Make Adjustment Entry"
msgstr "إنشاء قيد تعديل "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_file_download_error_wizard
msgid "Manage the file generation errors from report exports."
msgstr "قم بإدارة أخطاء إنشاء الملفات من عمليات تصدير التقارير. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual value"
msgstr "القيمة اليدوية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual values"
msgstr "القيم اليدوية "

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_matching_number
msgid "Matching"
msgstr "مطابقة"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "أعضاء هذه الوحدة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Method '%(method_name)s' must start with the '%(prefix)s' prefix."
msgstr "يجب أن تبدأ الطريقة '%(method_name)s' بالبادئة '%(prefix)s'. "

#. module: account_reports
#: model:account.report.line,name:account_reports.misc_operations
msgid "Misc. operations"
msgstr "العمليات المتنوعة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mode
msgid "Mode"
msgstr "الوضع"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__res_model_name
msgid "Model"
msgstr "النموذج "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Month"
msgstr "الشهر"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Months"
msgstr "شهور"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Multi-Ledger:"
msgstr "دفتر الأستاذ المتعدد "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Multi-ledger"
msgstr "دفتر الأستاذ المتعدد "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_report_handler
msgid "Multicurrency Revaluation Report Custom Handler"
msgstr "المعالج المخصص لتقرير إعادة التقييم متعدد العملات "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "مُعالج إعادة التقييم متعدد العملات "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__multi
msgid "Multiple Recipients"
msgstr "عدة مستلمين "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for fiscal position %(position)s after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""
"توجد عدة قيود إقفال الضريبة بحالة المسودة في الوضع المالي %(position)s بعد %(period_start)s. يجب أن يكون هناك واحد فقط كحد أقصى. \n"
" %(closing_entries)s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""
"توجد عدة قيود إقفال للضريبة بحالة المسودة في منطقتك بعد%(period_start)s. يجب أن يكون هناك واحد فقط كحد أقصى. \n"
" %(closing_entries)s "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:account.report.line,name:account_reports.journal_report_line
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Name"
msgstr "الاسم"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "الاسم لمنحه للمستندات المنشأة. "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "صافي الربح"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "صافي الأصول"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Net increase in cash and cash equivalents"
msgstr "صافي الزيادة في النقد وما يعادل النقد "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "صافي هامش الربح (صافي الربح / الدخل)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Never miss a tax deadline."
msgstr "لا تفوت أي موعد نهائي بعد الآن. "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "No"
msgstr "لا"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "No Comparison"
msgstr "بلا مقارنة"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No Journal"
msgstr "لا يوجد دفتر يومية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No VAT number associated with your company. Please define one."
msgstr "لا يوجد رقم ضريبة مرتبط بشركتك. الرجاء تحديد واحد. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No adjustment needed"
msgstr "لا حاجة لإجراء أي تعديل "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "No data to display !"
msgstr "لا توجد بيانات لعرضها! "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/chart_template.py:0
msgid "No default miscellaneous journal could be found for the active company"
msgstr "لم يتم العثور على دفتر يومية افتراضي للمتفرقات للشركة الفعالة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "No entry to generate."
msgstr "لا يوجد قيد لإنشائه. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No provision needed was found."
msgstr "لم يتم العثور على أي أحكام مطلوبة. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Non Trade Partners"
msgstr "الشركاء غير التجاريين. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Payable"
msgstr "حساب الدائنين غير التجاري "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Receivable"
msgstr "حساب المدينين غير التجاري "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Non-Deductible"
msgstr "غير قابلة للاقتطاع "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "None"
msgstr "لا شيء"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Not Started"
msgstr "لم يبدأ بعد "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Number of periods cannot be smaller than 1"
msgstr "لا يمكن أن يكون عدد الفترات أقل من 1 "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "الحسابات خارج الميزانية العمومية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Odoo Warning"
msgstr "تحذير من أودو"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period5
#: model:account.report.column,name:account_reports.aged_receivable_report_period5
msgid "Older"
msgstr "أقدم"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "إحدى الصيغ التي اخترتها لا يمكن تصديرها في برنامج إدارة المستندات "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "مديرو الفوترة وحدهم المصرح لهم بتغيير تواريخ الإقفال! "

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_account_reports_customer_statements
msgid "Open Customer Statements"
msgstr "فتح كشوفات العملاء "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "الرصيد الافتتاحي للسنة المالية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_operating_income0
msgid "Operating Income (or Loss)"
msgstr "الدخل التشغيلي (أو الخسائر التشغيلية) "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Options"
msgstr "الخيارات"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Options:"
msgstr "الخيارات: "

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding
msgid "Outstanding Receipts/Payments"
msgstr "المدفوعات/الإيصالات المستحقة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "معرف التقرير الأساسي "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "المعالج الأساسي "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#: model:account.report.column,name:account_reports.general_ledger_report_partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__partner_ids
msgid "Partner"
msgstr "الشريك"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Partner Categories"
msgstr "فئات الشركاء "

#. module: account_reports
#: model:account.report,name:account_reports.partner_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
msgid "Partner Ledger"
msgstr "دفتر الأستاذ العام للشركاء"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_partner_ledger_report_handler
msgid "Partner Ledger Custom Handler"
msgstr "المعالج المخصص لدفتر الأستاذ العام الخاص بالشريك "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is bad"
msgstr "الشريك سيء "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is good"
msgstr "الشريك جيد "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Partner(s) should have an email address."
msgstr "يجب أن يكون للوكلاء عنوان بريد إلكتروني. "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Partners"
msgstr "الشركاء"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners Categories:"
msgstr "فئات الشركاء: "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners:"
msgstr "الشركاء:"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_pay
msgid "Pay Tax"
msgstr "دفع الضريبة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Pay tax: %s"
msgstr "دفع الضريبة: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Payable"
msgstr "الدائن"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Payable tax amount"
msgstr "مبلغ الضريبة الدائن "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "الدائنون"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "الأداء"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Period"
msgstr "الفترة"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period1
#: model:account.report.column,name:account_reports.aged_receivable_report_period1
msgid "Period 1"
msgstr "الفترة 1 "

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period2
#: model:account.report.column,name:account_reports.aged_receivable_report_period2
msgid "Period 2"
msgstr "الفترة 2 "

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period3
#: model:account.report.column,name:account_reports.aged_receivable_report_period3
msgid "Period 3"
msgstr "الفترة 3 "

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period4
#: model:account.report.column,name:account_reports.aged_receivable_report_period4
msgid "Period 4"
msgstr "الفترة 4 "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Period order"
msgstr "طلب المدة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Periodicity"
msgstr "الوتيرة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "الوتيرة في الشهر "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Periods"
msgstr "الفترات "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Plans"
msgstr "الخطط "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Please enter a valid budget name."
msgstr "يُرجى إدخال اسم ميزانية صالح. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Please select a mail template to send multiple statements."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Please select the main company and its branches in the company selector to "
"proceed."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr "يرجى تعيين الحسابات المؤجلة في إعدادات المحاسبة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr "يرجى إعداد دفتر اليومية المؤجل في إعدادات المحاسبة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Please specify the accounts necessary for the Tax Closing Entry."
msgstr "يرجى تحديد الحسابات الضرورية للقيد الختامي للضريبة. "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "بالإضافة الى الأصول الثابتة"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "بالإضافة للأصول غير المتداولة"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "بالإضافة للالتزامات غير الجارية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_other_income0
msgid "Plus Other Income"
msgstr "بالإضافة إلى دخل آخر "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "المنصب الوظيفي "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Post"
msgstr "منشور "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Posted Entries"
msgstr "القيود المُرحّلة "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "المدفوعات المسددة مقدمًا "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "معاينة البيانات "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Period"
msgstr "الفترة السابقة"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_2
msgid "Previous Years Retained Earnings"
msgstr "الأرباح المحتجزة للسنة الماضية "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "أرباح السنين الماضية غير المخصصة"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Print & Send"
msgstr "طباعة وإرسال "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Proceed"
msgstr "استمرار "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr "الاستمرار بحذر لأنه قد تكون هناك تعديلات لهذه الفترة ("

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product"
msgstr "المنتج "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product Category"
msgstr "فئة المنتج "

#. module: account_reports
#: model:account.report,name:account_reports.profit_and_loss
#: model:ir.actions.client,name:account_reports.action_account_report_pl
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_profit_and_loss
msgid "Profit and Loss"
msgstr "الربح والخسارة "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "الربحية "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "مقترح قيد يومية إقفال الضريبة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Provision for %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"
msgstr "الحكل لـ %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s) "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Quarter"
msgstr "ربع السنة"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
msgid "Rates"
msgstr "الأسعار"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Receivable"
msgstr "المدين"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Receivable tax amount"
msgstr "مبلغ الضريبة المدين "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "المدينين"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_partner_ids
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Recipients"
msgstr "المستلمين"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "تقرير التسوية "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "تذكير"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__account_report_id
msgid "Report"
msgstr "التقرير"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Report Line"
msgstr "بند التقرير"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Report Name"
msgstr "اسم التقرير"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__report_options
msgid "Report Options"
msgstr "خيارات التقرير"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Report lines mentioning the account code"
msgstr "بنود التقرير التي تحتوي على كود الحساب "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Report:"
msgstr "التقرير:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__report_ids
msgid "Reports"
msgstr "التقارير"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Reset to Standard"
msgstr "إعادة التعيين إلى الوضع القياسي "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "الأرباح المحتجزة"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "العائد على الاستثمار (صافي الربح / الأصول)"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.report.line,name:account_reports.account_financial_report_revenue0
msgid "Revenue"
msgstr "الإيرادات "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "تاريخ الانعكاس"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Reversal of Grouped Deferral Entry of %s"
msgstr "عكس القيد المؤجل المجمع لـ %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Reversal of: %s"
msgstr "عكس: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Root Report"
msgstr "تقرير الجذر "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__rule_ids
msgid "Rules"
msgstr "القواعد"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Same Period Last Year"
msgstr "نفس الفترة العام الماضي"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
msgid "Search..."
msgstr "بحث..."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Sections"
msgstr "الأقسام "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send"
msgstr "إرسال"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__send_and_print_values
msgid "Send And Print Values"
msgstr "إرسال وطباعة القيم "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__send_mail_readonly
msgid "Send Mail Readonly"
msgstr "إرسال بريد إلكتروني للقراءة فقط "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send Partner Ledgers"
msgstr "إرسال دفاتر الأستاذ العام للشركاء "

#. module: account_reports
#: model:ir.actions.server,name:account_reports.ir_cron_account_report_send_ir_actions_server
msgid "Send account reports automatically"
msgstr "إرسال تقارير الحسابات تلقائياً "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Send tax report: %s"
msgstr "إرسال التقرير الضريبي: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Sending statements"
msgstr "جاري إرسال كشوفات الحساب "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Services"
msgstr "الخدمات"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "Set as Checked"
msgstr "التعيين كتمّ التحقق منه "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "توقعات النقد قصيرة الأجل"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Account"
msgstr "إظهار الحساب "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Show All Accounts"
msgstr "إظهار كافة الحسابات "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Currency"
msgstr "إظهار العملة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "إظهار حركة تحذير "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__single
msgid "Single Recipient"
msgstr "مستلم واحد "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "Some"
msgstr "بعض "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Some journal items appear to point to obsolete report lines."
msgstr "يبدو أن بعض عناصر دفتر اليومية تشير إلى بنود تقرير قديمة. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Specific Date"
msgstr "تاريخ محدد"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""
"قم بتحديد منشأة محاسبية التي سوف تؤدي دور الوكيل أو الممثل عند تصدير "
"التقارير.  "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Split Horizontally"
msgstr "التقسيم أفقياَ "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__tax_closing_start_date
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "البدء من "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Starting Balance"
msgstr "الرصيد الافتتاحي"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Statements are being sent in the background."
msgstr "يتم إرسال كشوف الحسابات في الخلفية. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Subformula"
msgstr "صيغة فرعية "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_subject
msgid "Subject"
msgstr "الموضوع "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Subject..."
msgstr "الموضوع..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "T: %s"
msgstr "T: %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Tags"
msgstr "علامات التصنيف "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Amount"
msgstr "مبلغ الضريبة "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Applied"
msgstr "تم تطبيق الضريبة "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_alert
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_alert
msgid "Tax Closing Alert"
msgstr "تنبيه الإقفال الضريبي "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_report_id
msgid "Tax Closing Report"
msgstr "تقرير الإقفال الضريبي "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Tax Declaration"
msgstr "الإقرار الضريبي "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Tax Grids"
msgstr "شبكات الضرائب"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "معرف الضريبة"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.company_information
msgid "Tax ID:"
msgstr "معرف الضريبة: "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Paid Adjustment"
msgstr "تعديل الضريبة المدفوعة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/filters/filter_date.xml:0
msgid "Tax Period"
msgstr "الفترة الضريبية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Received Adjustment"
msgstr "تعديل الضريبة المتلقاة "

#. module: account_reports
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Tax Report"
msgstr "التقرير الضريبي "

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax Report Ready"
msgstr "التقرير الضريبي جاهز "

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
msgid "Tax Return"
msgstr "الإقرار الضريبي "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Tax Return Periodicity"
msgstr "مدى دورية الإقرار الضريبي "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "الوحدة الضريبية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
msgid "Tax Unit:"
msgstr "وحدة الضريبة: "

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "الوحدات الضريبية "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity__account_tax_closing_params
msgid "Tax closing additional params"
msgstr "المعايير الإضافية للإقفال الضريبي "

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_pay
msgid "Tax is ready to be paid"
msgstr "الضريبة جاهزة ليتم دفعها "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "التقرير الضريبي "

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax report is ready to be sent to the administration"
msgstr "التقرير الضريبي جاهز لإرساله إلى الإدارة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "Tax return"
msgstr "الإقرار الضريبي "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Taxes"
msgstr "الضرائب"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "Taxes Applied"
msgstr "تم تطبيق الضرائب"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__fpos_synced
msgid ""
"Technical field indicating whether Fiscal Positions exist for all companies "
"in the unit"
msgstr ""
"حقل تقني يشير إلى ما إذا كانت الأوضاع المالية موجودة لكافة الشركات في الوحدة"
" أم لا "

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "نموذج تقني لتنزيلات التقارير المحاسبية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/ellipsis/ellipsis.js:0
msgid "Text copied"
msgstr "تم نسخ النص "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The Accounts Coverage Report is not available for this report."
msgstr "تقرير تغطية الحسابات غير متاح لهذا التقرير. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover_line/annotation_popover_line.js:0
msgid "The annotation shouldn't have an empty value."
msgstr "يجب ألا يحتوي الشرح على قيمة فارغة. "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__text
msgid "The annotation's content."
msgstr "محتوى الشرح. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"The attachments of the tax report can be found on the <a href='#' data-oe-"
"model='account.move' data-oe-id='%s'>closing entry</a> of the representative"
" company."
msgstr ""
"يمكن العثور على مرفقات التقرير الضريبي في <a href='#' data-oe-"
"model='account.move' data-oe-id='%s'>قيد الإقفال</a> للشركة الممثلة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "The column '%s' is not available for this report."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"The country detected for this VAT number does not match the one set on this "
"Tax Unit."
msgstr ""
"لا تطابق الدولة التي تم رصدها لرقم الضريبة الدولة التي تم تعيينها لهذا الوضع"
" الضريبي. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""
"الدولة التي تُستخدَم فيها هذه الوحدة الضريبية لتجميع إقرارات التقارير "
"الضريبية لشركتك. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "The currency rate cannot be equal to zero"
msgstr "لا يمكن أن يكون سعر صرف العملة مساوياً لصفر "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "The current balance in the"
msgstr "الرصيد الحالي في "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"The currently selected dates don't match a tax period. The closing entry "
"will be created for the closest-matching period according to your "
"periodicity setup."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "The entry that will be generated will take them into account."
msgstr "سيقوم القيد الذي سيتم إنشاؤه باعتبارها. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__fiscal_position_id
msgid "The fiscal position used while annotating."
msgstr "الوضع المالي المُستَخدَم عند الشرح. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__line_id
msgid "The id of the annotated line."
msgstr "معرِّف البند الذي تم شرحه. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__report_id
msgid "The id of the annotated report."
msgstr "معرِّف التقرير الذي تم شرحه. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr "المعرف لاستخدامه عند تسليم تقرير لهذه الوحدة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "The main company of a tax unit has to be part of it."
msgstr "يجب أن تكون الشركة الرئيسية لوحدة ضريبية جزءاً منها. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "الوحدة الضريبية التي تنتمي إليها هذه الشركة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The used operator is not supported for this expression."
msgstr "المشغل المستخدَم غير مدعوم لهذا التعبير. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "There are"
msgstr "هناك "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid ""
"There are currently reports waiting to be sent, please try again later."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "There is no data to display for the given filters."
msgstr "لا توجد بيانات لعرضها لعناصر التصفية المحددة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account exists in the Chart of Accounts but is not mentioned in any "
"line of the report"
msgstr ""
"الحساب موجود في شجرة الحسابات ولكنه غير مذكور في أي بند من بنود التقرير "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account is reported in a line of the report but does not exist in the "
"Chart of Accounts"
msgstr ""
"تم إعداد تقرير حول هذا الحساب في بند من التقرير ولكنه غير موجود في شجرة "
"الحسابات "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported in multiple lines of the report"
msgstr "توجد عدة تقارير حول هذا الحساب في عدة بنود من التقرير "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported multiple times on the same line of the report"
msgstr "توجد عدة تقارير حول هذا الحساب في نفس البند من التقرير "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr "يسمح لك هذا باختيار مكان الإجمالي في تقاريرك المالية."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This company is part of a tax unit. You're currently not viewing the whole "
"unit."
msgstr "هذه الشركة هي جزء من وحدة ضريبية. لا تعرض حالياً الوحدة بأكملها. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid ""
"This line and all its children will be deleted. Are you sure you want to "
"proceed?"
msgstr ""
"سيتم حذف هذا البند وكافة توابعه. هل أنت متأكد من أنك ترغب بالاستمرار؟ "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "This line uses a custom user-defined 'Group By' value."
msgstr "يستخدم هذا البند قيمة \"التجميع حسب\" المخصصة المعرفة من قِبَل المستخدم. "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "This option hides lines with a value of 0"
msgstr "يقوم هذا الخيار بإخفاء البنود التي قيمتها 0 "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This report already has a menuitem."
msgstr "هذا التقرير يحتوي على عنصر قائمة بالفعل. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"This report contains inconsistencies. The affected lines are marked with a "
"warning."
msgstr ""
"يحتوي التقرير على بيانات غير متسقة. تم وضع علامة تحذير على البنود المتأثرة. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "This report only displays the data of the active company."
msgstr "هذا التقرير يعرض فقط بيانات الشركة النشطة. "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid ""
"This report uses report-specific code.\n"
"                        You can customize it manually, but any change in the parameters used for its computation could lead to errors."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This report uses the CTA conversion method to consolidate multiple companies using different currencies,\n"
"        which can lead the report to be unbalanced."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This subformula references an unknown expression: %s"
msgstr "تشير الصيغة الفرعية إلى تعبير غير معروف: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This tag is reported in a line of the report but is not linked to any "
"account of the Chart of Accounts"
msgstr ""
"تم إعداد تقرير حول علامة التصنيف هذه في بند من التقرير ولكنه غير مرتبط بأي "
"حساب في شجرة الحسابات "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid ""
"This tax closing entry is posted, but the tax lock date is earlier than the "
"covered period's last day. You might need to reset it to draft and refresh "
"its content, in case other entries using taxes have been posted in the "
"meantime."
msgstr ""
"تم ترحيل قيد الإقفال الضريبي هذا، ولكن تاريخ قفل الضريبة يسبق اليوم الأخير "
"للفترة المشمولة. قد تحتاج إلى إعادة تعيينه إلى حالة المسودة لتحديث محتواه، "
"في حال تم ترحيل قيود أخرى تستخدم الضرائب في هذه الأثناء. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Today"
msgstr "اليوم "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_total
#: model:account.report.column,name:account_reports.aged_receivable_report_total
msgid "Total"
msgstr "الإجمالي"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Total %s"
msgstr "الإجمالي %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Trade Partners"
msgstr "شركاء تجاريون "

#. module: account_reports
#: model:account.report.line,name:account_reports.transaction_without_statement
msgid "Transactions without statement"
msgstr "المعاملات التي ليس لها كشف حساب "

#. module: account_reports
#: model:account.report,name:account_reports.trial_balance_report
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr "ميزان المراجعة"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr "معالج مخصص لميزان المراجعة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Triangular"
msgstr "مثلث الشكل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to dispatch an action on a report not compatible with the provided "
"options."
msgstr "جاري محاولة إرسال إجراء في تقرير غير متوافق مع الخيارات المتوفرة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to expand a group for a line which was not generated by a report "
"line: %s"
msgstr "نحاول تفصيل مجموعة لبند لم يتم إنشاؤه بواسطة بند تقرير: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand a line without an expansion function."
msgstr "نحاول تفصيل بند دون استخدام خاصية التفصيل. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand groupby results on lines without a groupby value."
msgstr "نحاول تفصيل نتائج خاصية groupby دون قيمة groupby. "

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "أرباح غير مخصصة"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Unfold All"
msgstr "كشف الكل"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown"
msgstr "غير معروف"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Unknown Partner"
msgstr "شريك مجهول "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown bound criterium: %s"
msgstr "فئة ربط غير معروفة: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown date scope: %s"
msgstr "نطاق البيانات غير معروف: %s "

#. module: account_reports
#: model:account.report,name:account_reports.multicurrency_revaluation_report
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
msgid "Unrealized Currency Gains/Losses"
msgstr "الأرباح/الخسائر غير المُدرَكة للعملة "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Unreconciled Entries"
msgstr "القيود غير المسواة "

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr "رقم ضريبة القيمة المضافة"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "VAT Periodicity"
msgstr "وتيرة ضريبة القيمة المضافة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Value"
msgstr "القيمة"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Vat closing from %(date_from)s to %(date_to)s"
msgstr "إغلاق ضريبة القيمة المضافة من %(date_from)s إلى %(date_to)s "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View"
msgstr "أداة العرض"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Bank Statement"
msgstr "عرض كشف الحساب البنكي "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "View Carryover Lines"
msgstr "عرض بنود الترحيل "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Journal Entry"
msgstr "عرض قيد اليومية"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View Partner"
msgstr "عرض الشريك"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "View Partner(s)"
msgstr "View Partner(s)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Payment"
msgstr "عرض الدفع "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__warnings
msgid "Warnings"
msgstr "تحذيرات"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr "عند التحديد، سوف تظهر المجاميع الكلية والفرعية تحت أقسام التقرير "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr "عند التحديد، سوف تظهر المجاميع الكلية والفرعية تحت أقسام التقرير "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr "ما إذا كان علينا إنشاء أحكام للعملات الأجنبية المختارة أم لا. "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "With Draft Entries"
msgstr "مع القيود بحالة المسودة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Wrong ID for general ledger line to expand: %s"
msgstr "المعرّف غير صحيح لبند دفتر الأستاذ العام لتفصيله: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Wrong ID for partner ledger line to expand: %s"
msgstr "المعرّف غير صحيح لبند دفتر الأستاذ الخاص بالشريك لتفصيله: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Wrong format for if_other_expr_above/if_other_expr_below formula: %s"
msgstr "الصيغة غير صحيحة لمعادلة if_other_expr_above/if_other_expr_below: %s "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Year"
msgstr "السنة "

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Yes"
msgstr "نعم"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "You are using custom exchange rates."
msgstr "أنت تستخدم أسعار صرف مخصصة "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "You can't open a tax report from a move without a VAT closing date."
msgstr ""
"لا يمكنك فتح تقرير ضريبي من حركة دون تاريخ إقفال ضريبة القيمة المضافة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move_line.py:0
msgid "You cannot add taxes on a tax closing move line."
msgstr "لا يمكنك إضافة ضرائب في بند حركة إقفال ضريبي. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid ""
"You cannot generate entries for a period that does not end at the end of the"
" month."
msgstr "لا يمكنك إنشاء قيود لفترة لا تنتهي بنهاية الشهر. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "You cannot generate entries for a period that is locked."
msgstr "لا يمكنك إنشاء قيود لفترة مقفلة. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as another closing entry has "
"been posted at a later date."
msgstr ""
"لا يمكنك إعادة تعيين قيد الإقفال هذا إلى حالة المسودة، لأنه قد تم ترحيل قيد "
"إقفال آخر لإي تاريخ آخر. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as it would delete carryover "
"values impacting the tax report of a locked period. To do this, you first "
"need to modify you tax return lock date."
msgstr ""
"لا يمكنك إعادة تعيين قيد الإقفال هذا إلى حالة المسودة، حيث إنه سيقوم بحذف "
"قيم الترحيل التي تؤثر على التقرير الضريبي للفترة المقفلة. للقيام بذلك، عليك "
"أولاً تعديل تاريخ إقفال الإقرار الضريبي الخاص بك. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "You need to activate more than one currency to access this report."
msgstr "عليك تفعيل أكثر من عملة واحدة للوصول إلى هذا التقرير. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"You're about the generate the closing entries of multiple companies at once."
" Each of them will be created in accordance with its company tax "
"periodicity."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "[Draft]"
msgstr "[Draft]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "addressed to"
msgstr "موجهة إلى "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "and correct their tax tags if necessary."
msgstr "وتصحيح علامات تصنيف الضريبة إذا لزم الأمر. "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "سنوياً "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "أيام بعد الفترة "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "doesn't match the balance of your"
msgstr "لا يطابق رصيد "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "كل شهرين "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "كل 4 شهور "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "have a starting balance different from the previous ending balance."
msgstr "أن يكون الرصيد الافتتاحي مختلفاً عن الرصيد الختامي السابق "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "in the next period."
msgstr "في الفترة القادمة. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "invoices"
msgstr "فواتير العملاء "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "journal items"
msgstr "عناصر اليومية "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "last bank statement"
msgstr "آخر كشف حساب بنكي "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "شهرياً "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "n/a"
msgstr "غير منطبق "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "partners"
msgstr "الشركاء "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "prior or included in this period."
msgstr "قبل هذه الفترة أو مشمول فيها. "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "ربع سنوي "

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "شبه سنوي "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "statements"
msgstr "كشوفات الحسابات "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "synchronize fiscal positions"
msgstr "مزامنة الأوضاع المالية "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "tax unit [%s]"
msgstr "وحدة الضريبة [%s] "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "that are not established abroad."
msgstr "التي لم يتم إنشاؤها في الخارج. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "to"
msgstr "إلى"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "unposted Journal Entries"
msgstr "قيود يومية غير مُرحلة"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "were carried over to this line from previous period."
msgstr "تم ترحيلها إلى هذا البند من الفترة السابقة. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "which don't originate from a bank statement nor payment."
msgstr "والذي لا ينتج عن كشف حساب بنكي أو عملية دفع. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "who are not established in any of the EC countries."
msgstr "غير المنشئين في أي من دول الاتحاد الأوروبي. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to"
msgstr "سيتم ترحيلها إلى "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to this line in the next period."
msgstr "سيتم ترحيلها إلى هذا البند في الفترة التالية. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "without a valid intra-community VAT number."
msgstr "دون رقم ضريبي صالح لبين المجتمعات. "

#. module: account_reports
#: model:mail.template,subject:account_reports.email_template_customer_statement
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Statement - {{ object.commercial_company_name }}"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "⇒ Reset to Odoo’s Rate"
msgstr "-> إعادة التعيين لأسعار أودو "
