# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auto_backup
#
# <PERSON><PERSON><PERSON>. <<EMAIL>>, 2015.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 8.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-03-26 14:17+0000\n"
"PO-Revision-Date: 2015-12-13 10:46+0300\n"
"Last-Translator: Sa<PERSON>i <PERSON>. <<EMAIL>>\n"
"Language-Team: <EMAIL>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"



#. module: auto_backup
#: code:addons/auto_backup/backup_scheduler.py:137
#, python-format
msgid "%s"
msgstr "%s"

#. module: auto_backup
#: help:db.backup,bkp_dir:0
msgid "Absolute path for storing the backups"
msgstr "المسار الكامل لحفظ النسخ الاحتياطي"

#. module: auto_backup
#: field:db.backup,sendmailsftpfail:0
msgid "Auto. E-mail on backup fail"
msgstr "إرسال بريد إلكتروني تلقائياً في حالة فشل النسخ الاحتياطي"

#. module: auto_backup
#: field:db.backup,autoremove:0
msgid "Auto. Remove Backups"
msgstr "إزالة النسخ الاحتياطية تلقائياً"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Automatic backups of the database can be scheduled as follows:"
msgstr "النسخ الاحتياطي التلقائي لقاعدة البيانات يمكن جدولته كالتالي"

#. module: auto_backup
#: field:db.backup,bkp_dir:0
msgid "Backup Directory"
msgstr "دليل النسخ الاحتياطي"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_tree
msgid "Backups"
msgstr "النسخ الاحتياطية"

#. module: auto_backup
#: help:db.backup,daystokeepsftp:0
msgid "Choose after how many days the backup should be deleted from the FTP server. For example:\n"
"If you fill in 5 the backups will be removed after 5 days from the FTP server."
msgstr ""
"اختر بعد كم من الأيام سيتم حذف النسخ الاحتياطي من خادم FTP مثلاً :\n إذا أدخلت "
"5 فإن النسخ الاحتياطية سيتم إزالتها من خادم FTP بعد 5 أيام."

#. module: auto_backup
#: help:db.backup,daystokeep:0
msgid "Choose after how many days the backup should be deleted. For example:\n"
"If you fill in 5 the backups will be removed after 5 days."
msgstr ""
"اختر بعد كم من الأيام سيتم حذف النسخ الاحتياطي مثلاً :\n إذا أدخلت 5 فإن النسخ "
"الاحتياطية سيتم إزالتها بعد 5 أيام."

#. module: auto_backup
#: model:ir.actions.act_window,name:auto_backup.action_backup_conf_form
#: model:ir.ui.menu,name:auto_backup.backup_conf_menu
msgid "Configure Backup"
msgstr "إعدادات النسخ الإحتياطي"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Contact us!"
msgstr "اتصل بنا!"

#. module: auto_backup
#: field:db.backup,create_uid:0
msgid "Created by"
msgstr "تم ألإنشاء بواسطة"

#. module: auto_backup
#: field:db.backup,create_date:0
msgid "Created on"
msgstr "تم ألإنشاء في"

#. module: auto_backup
#: field:db.backup,name:0
msgid "Database"
msgstr "قاعدة البيانات"

#. module: auto_backup
#: help:db.backup,name:0
msgid "Database you want to schedule backups for"
msgstr "قاعدة البيانات التي تريد جدولة النسخ الاحتياطي لها"

#. module: auto_backup
#: field:db.backup,emailtonotify:0
msgid "E-mail to notify"
msgstr "تنبيه البريد الإلكتروني"

#. module: auto_backup
#: code:addons/auto_backup/backup_scheduler.py:106
#: constraint:db.backup:0
#, python-format
msgid "Error ! No such database exists!"
msgstr "خطأ ! لا وجود لقاعدة البيانات هذه !"

#. module: auto_backup
#: help:db.backup,emailtonotify:0
msgid "Fill in the e-mail where you want to be notified that the backup failed on the FTP."
msgstr ""
"ادخل عنوان البريد الإلكتروني الذي تريد تنبيهك من خلاله عند فشل النسخ "
"الاحتياطي على FTP."

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "For example: /odoo/backups/"
msgstr "مثلاً : /odoo/backups/"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Go to Settings / Technical / Automation / Scheduled Actions."
msgstr "اذهب إلى الإعدادات / التقني / ألأتمته / جدولة الإخزاءات."

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Help"
msgstr "المساعدة"

#. module: auto_backup
#: field:db.backup,host:0
msgid "Host"
msgstr "المضيف"

#. module: auto_backup
#: field:db.backup,id:0
msgid "ID"
msgstr "المعرف"

#. module: auto_backup
#: field:db.backup,sftpip:0
msgid "IP Address SFTP Server"
msgstr "عنوان بروتوكول الأنترنت لخادم SFTP"

#. module: auto_backup
#: help:db.backup,sendmailsftpfail:0
msgid "If you check this option you can choose to automaticly get e-mailed when the backup to the external server failed."
msgstr ""
"إذا قمت بتأشير هذا الخيار ستستطيع اختيار استلام البريد الإلكتروني تلقائياً "
"عند فشل النسخ الاحتياطي للخادوم الخارجي."

#. module: auto_backup
#: help:db.backup,autoremove:0
msgid "If you check this option you can choose to automaticly remove the backup after xx days"
msgstr ""
"إذا قمت بتأشير هذا الخيار ستستطيع اختيار الإزالة التلقائية للنسخ الاحتياطي "
"بعد س من الأيام"

#. module: auto_backup
#: help:db.backup,sftpwrite:0
msgid "If you check this option you can specify the details needed to write to a remote server with SFTP."
msgstr ""
"إذا قمت بتأشير هذا الخيار ستستطيع تحديد التفاصيل المطلوبة للكتابة على الخادم "
"البعيد من خلال SFTP."

#. module: auto_backup
#: field:db.backup,write_uid:0
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: auto_backup
#: field:db.backup,write_date:0
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Local backup configuration"
msgstr "إعدادات النسخ الاحتياطي المحلي"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Need more help?"
msgstr "هل تحتاج لمزيد من المساعدة ؟"

#. module: auto_backup
#: field:db.backup,sftppassword:0
msgid "Password User SFTP Server"
msgstr "كلمة المرور لمستخدم خادم SFTP"

#. module: auto_backup
#: field:db.backup,sftppath:0
msgid "Path external server"
msgstr "المسار الخارجي للخادم"

#. module: auto_backup
#: field:db.backup,port:0
msgid "Port"
msgstr "المنفذ"

#. module: auto_backup
#: field:db.backup,daystokeepsftp:0
msgid "Remove SFTP after x days"
msgstr "الإزالة من خادم SFTP بعد س من الأيام"

#. module: auto_backup
#: field:db.backup,daystokeep:0
msgid "Remove after x days"
msgstr "الإزالة بعد س من الأيام"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "SFTP"
msgstr "SFTP"

#. module: auto_backup
#: field:db.backup,sftpport:0
msgid "SFTP Port"
msgstr "منفذ SFTP"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_search
msgid "Search options"
msgstr "خيارات البحث"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Search the action named 'Backup scheduler'."
msgstr "ابحث عن الأجزاء المسمى 'Backup scheduler'."

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Set the scheduler to active and fill in how often you want backups generated."
msgstr "قم بتفعيل الجدولة واملأ كم تربد عادة توليد النسخ الاحتياطي."

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Test"
msgstr "اختبار"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Test SFTP Connection"
msgstr "اختبار توصيل SFTP"

#. module: auto_backup
#: help:db.backup,sftpip:0
msgid "The IP address from your remote server. For example ***********"
msgstr "عنوان بروتوكول الإنترنت من خادومك البعيد. مثلاً ***********"

#. module: auto_backup
#: help:db.backup,sftppath:0
msgid "The location to the folder where the dumps should be written to. For example /odoo/backups/.\n"
"Files will then be written to /odoo/backups/ on your remote server."
msgstr ""
"موقع المجلد التي يجب كتابة ملفات النسخ عليه. مثلاً /odoo/backups/.\n"
"وحينها سيتم كتابة الملفات إلى /odoo/backups/ على خادومك البعيد."

#. module: auto_backup
#: help:db.backup,sftppassword:0
msgid "The password from the user where the SFTP connection should be made with. This is the password from the user on the external server."
msgstr ""
"كلمة المرور الذي من المفترض عمل اتصال SFTP من بها. هذا هي كلمة المرور على "
"الخادم الخارجي."

#. module: auto_backup
#: help:db.backup,sftpport:0
msgid "The port on the FTP server that accepts SSH/SFTP calls."
msgstr "المنفذ على خادم FTP الذي يقبل طلبات SSH/SFTP."

#. module: auto_backup
#: help:db.backup,sftpusername:0
msgid "The username where the SFTP connection should be made with. This is the user on the external server."
msgstr ""
"اسم المستخدم الذي من المفترض عمل اتصال SFTP من به. هذا هو المستخدم على "
"الخادم الخارجي."

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "This configures the scheduler for automatic backup of the given database running on given host at given port on regular intervals."
msgstr ""
"هذا الإعداد سيجدول النسخ الاحتياطي التلقائي لقاعدة بيانات معينة وسينفذ على "
"مضيف معين بمنفذ معين خلال فترات متتابعة. "

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Use SFTP with caution! This writes files to external servers under the path you specify."
msgstr ""
"استخدم SFTP بحذر ! لأن هذا يؤدي لكتابة ملفات لخوادم خارجية على المسار الذي "
"حددته."

#. module: auto_backup
#: field:db.backup,sftpusername:0
msgid "Username SFTP Server"
msgstr "أسم المستخدم لخادم SFTP"

#. module: auto_backup
#: view:db.backup:auto_backup.view_backup_conf_form
msgid "Warning:"
msgstr "تحذير :"

#. module: auto_backup
#: field:db.backup,sftpwrite:0
msgid "Write to external server with sftp"
msgstr "الكتابة على خادم خارجي من خلال sftp"
