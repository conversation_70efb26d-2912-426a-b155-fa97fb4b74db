# Polish translation for openobject-addons
# Copyright (c) 2010 Rosetta Contributors and Canonical Ltd 2010
# This file is distributed under the same license as the openobject-addons package.
# <AUTHOR> <EMAIL>, 2010.
#
msgid ""
msgstr ""
"Project-Id-Version: openobject-addons\n"
"Report-Msgid-Bugs-To: FULL NAME <EMAIL@ADDRESS>\n"
"POT-Creation-Date: 2009-11-24 13:49+0000\n"
"PO-Revision-Date: 2011-02-15 15:01+0000\n"
"Last-Translator: OpenERP Administrators <Unknown>\n"
"Language-Team: Polish <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Launchpad-Export-Date: 2013-04-20 05:35+0000\n"
"X-Generator: Launchpad (build 16567)\n"

#. module: auto_backup
#: help:db.backup,name:0
msgid "Database you want to schedule backups for"
msgstr "<PERSON><PERSON> da<PERSON>, dla k<PERSON><PERSON><PERSON>j ch<PERSON> robienie kopii zapasowej"

#. module: auto_backup
#: constraint:ir.model:0
msgid ""
"The Object name must start with x_ and not contain any special character !"
msgstr ""
"Nazwa obiektu musi zaczynać się od x_ oraz nie może zawierać znaków "
"specjalnych !"

#. module: auto_backup
#: constraint:ir.actions.act_window:0
msgid "Invalid model name in the action definition."
msgstr "Nieprawidłowa nazwa modelu w definicji akcji."

#. module: auto_backup
#: model:ir.model,name:auto_backup.model_db_backup
msgid "db.backup"
msgstr ""

#. module: auto_backup
#: view:db.backup:0
msgid ""
"1) Go to Administration / Configuration / Scheduler / Scheduled Actions"
msgstr "1) Idź do Administracja / Konfirguracja / Planista / Planowane akcje"

#. module: auto_backup
#: model:ir.actions.act_window,name:auto_backup.action_backup_conf_form
#: model:ir.ui.menu,name:auto_backup.backup_conf_menu
msgid "Configure Backup"
msgstr "Konfiguruj kopie zapasowe"

#. module: auto_backup
#: view:db.backup:0
msgid "Test"
msgstr "Przetestuj"

#. module: auto_backup
#: view:db.backup:0
msgid "IP Configuration"
msgstr "Konfiguracja adresu IP"

#. module: auto_backup
#: help:db.backup,bkp_dir:0
msgid "Absolute path for storing the backups"
msgstr "Pełna ścieżka  dla kopii zapasowych"

#. module: auto_backup
#: model:ir.module.module,shortdesc:auto_backup.module_meta_information
msgid "Database Auto-Backup"
msgstr "Automatyczne kopie zapasowe bazy danych"

#. module: auto_backup
#: view:db.backup:0
msgid "Database Configuration"
msgstr "Konfiguracja bazy danych"

#. module: auto_backup
#: view:db.backup:0
msgid "4) Set other values as per your preference"
msgstr "4) Ustaw inne dane według uznania"

#. module: auto_backup
#: field:db.backup,host:0
msgid "Host"
msgstr "Host"

#. module: auto_backup
#: view:db.backup:0
msgid ""
"Automatic backup of all the databases under this can be scheduled as "
"follows: "
msgstr ""
"Automatyczne kopie wszystkich baz danych mogą być zaplanowane następująco: "

#. module: auto_backup
#: constraint:ir.ui.view:0
msgid "Invalid XML for View Architecture!"
msgstr "XML niewłaściwy dla tej architektury wyświetlania!"

#. module: auto_backup
#: field:db.backup,bkp_dir:0
msgid "Backup Directory"
msgstr "Katalog kopii zapasowych"

#. module: auto_backup
#: field:db.backup,name:0
msgid "Database"
msgstr "Baza danych"

#. module: auto_backup
#: view:db.backup:0
msgid "2) Schedule new action(create a new record)"
msgstr "2) Zaplanuj nową akcję (utwórz nowy rekord)"

#. module: auto_backup
#: model:ir.module.module,description:auto_backup.module_meta_information
msgid ""
"The generic Open ERP Database Auto-Backup system enables the user to make "
"configurations for the automatic backup of the database.\n"
"User simply requires to specify host & port under IP Configuration & "
"database(on specified host running at specified port) and backup "
"directory(in which all the backups of the specified database will be stored) "
"under Database Configuration.\n"
"\n"
"Automatic backup for all such configured databases under this can then be "
"scheduled as follows:  \n"
"                      \n"
"1) Go to Administration / Configuration / Scheduler / Scheduled Actions\n"
"2) Schedule new action(create a new record)\n"
"3) Set 'Object' to 'db.backup' and 'Function' to 'schedule_backup' under "
"page 'Technical Data'\n"
"4) Set other values as per your preference"
msgstr ""
"System Open ERP Database Auto-Backup pozwala użytkownikowi utworzyć "
"konfiguracje dla automatycznego zachowywania kopii zapasowych baz danych.\n"
"Użytkownik musi tylko podać host i port w Konfiguracji IP  i bazę danych "
"oraz katalog dla kopii zapasowej Konfiguracji bazy danych.\n"
"\n"
"Automatyczne kopie wszystkich baz danych mogą być zaplanowane następująco:   "
"\n"
"                      \n"
"1) Idź do Administracja / Konfirguracja / Planista / Planowane akcje\n"
"2) Zaplanuj nową akcję (utwórz nowy rekord)\n"
"3) Ustaw 'Obiekt' na 'db.backup' i 'Funkcja' na 'schedule_backup' na stronie "
"'Dane techniczne'.\n"
"4) Ustaw inne dane według uznania"

#. module: auto_backup
#: view:db.backup:0
msgid ""
"3) Set 'Object' to 'db.backup' and 'Function' to 'schedule_backup' under "
"page 'Technical Data'"
msgstr ""
"3) Ustaw 'Obiekt' na 'db.backup' i 'Funkcja' na 'schedule_backup' na stronie "
"'Dane techniczne'."

#. module: auto_backup
#: view:db.backup:0
msgid "Help"
msgstr "Pomoc"

#. module: auto_backup
#: view:db.backup:0
msgid ""
"This configures the scheduler for automatic backup of the given database "
"running on given host at given port on regular intervals."
msgstr ""
"Tu konfigurujesz planistę do automatycznego zapisywania kopii zapasowych dla "
"określonej bazy danych na na określonym hoście (komputerze) na określonym "
"porcie w regularnych terminach."

#. module: auto_backup
#: field:db.backup,port:0
msgid "Port"
msgstr "Port"
