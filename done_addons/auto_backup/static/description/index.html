<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
            <h2 class="oe_slogan">Automated backups</h2>
            <h3 class="oe_slogan">A tool for all your back-ups, internal and external!</h3>
        </div>
	<div class="oe_span6">
            <div class="oe_demo oe_picture oe_screenshot">
                    <img src="overview.png">
            </div>
        </div>
	<div class="oe_span6">
            <p class="oe_mt32">
		Keep your Odoo data safe with this module. Take automated back-ups, remove them automatically 
		and even write them to an external server through an encrypted tunnel.
		You can even specify how long local backups and external backups should be kept, automatically!
            </p>
            <div class="oe_centeralign oe_websiteonly">
                <a href="http://www.openerp.com/start?app=mail" class="oe_button oe_big oe_tacky">Start your <span class="oe_emph">free</span> trial</a>
            </div>
        </div>
    </div>
</section>
<!-- Second block -->
<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan">Connect with an FTP Server</h2>
        <h3 class="oe_slogan">Keep your data safe, through an SSH tunnel!</h3>
        <div class="oe_span6">
            <p class="oe_mt32">
		Want to go even further and write your backups to an external server?
		You can with this module! Specify the credentials to the server, specify a path and 			everything will be backed up automatically. This is done through an SSH (encrypted) tunnel, 			thanks to pysftp, so your data is safe!

            </p>
        </div>
        <div class="oe_span6">
            <div class="oe_row_img oe_centered">
                <img class="oe_picture oe_screenshot" src="terminalssh.png">
            </div>
        </div>
    </div>
</section>
<!--Third block -->
<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
            <h2 class="oe_slogan">Test connection</h2>
            <h3 class="oe_slogan">Checks your credentials in one click</h3>
        </div>
	<div class="oe_span6">
            <div class="oe_demo oe_picture oe_screenshot">
                    <img src="testconnection.png">
		    <img src="testconnectionfailed.png">
            </div>
        </div>
	<div class="oe_span6">
            <p class="oe_mt32">
		Want to make sure if the connection details are correct and if Odoo can automatically write them to the remote server? Simply click on the 'Test SFTP Connection' button and you will get message telling you if everything is OK, or what is wrong!
            </p>
        </div>
    </div>
</section>
<!-- Fourth block -->
<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan">E-mail on backup failure</h2>
        <h3 class="oe_slogan">Stay informed of problems, automatically!</h3>
        <div class="oe_span6">
            <p class="oe_mt32">
		Do you want to know if the database backup failed? Check the checkbox 'Auto. E-mail on backup fail' and fill in your e-mail.
		Every time a backup fails you will get an e-mail in your mailbox with technical details.
            </p>
        </div>
        <div class="oe_span6">
            <div class="oe_row_img oe_centered">
                <img class="oe_picture oe_screenshot" src="emailnotification.png">
            </div>
        </div>
    </div>
</section>
<!--Fifth block -->
<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
            <h2 class="oe_slogan">Contact / Support</h2>
            <h3 class="oe_slogan">Need help or want extra features?</h3>
        </div>
	<div class="oe_span6">
            <p class="oe_mt32">
		Need help with the configuration or want this module to have more functionalities?
		Please create a bug report <a href="https://github.com/Yenthe666/auto_backup/issues">on the Github issue tracker</a>
            </p>
        </div>
    </div>
</section>
