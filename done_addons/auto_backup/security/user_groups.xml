<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.module.category" id="module_management">
            <field name="name">Auto backup access</field>
            <field name="description">User access level for this module</field>
            <field name="sequence">3</field>
        </record>

        <record id="group_manager" model="res.groups">
            <field name="name">Manager</field>
            <field name="category_id" ref="auto_backup.module_management"/>
        </record>
    </data>
</odoo>
