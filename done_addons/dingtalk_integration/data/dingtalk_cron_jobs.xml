<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- 钉钉员工自动同步定时任务 -->
        <record id="cron_dingtalk_sync_employees" model="ir.cron">
            <field name="name">DingTalk: Auto Sync Employees</field>
            <field name="model_id" ref="model_dingtalk_config"/>
            <field name="state">code</field>
            <field name="code">
# 钉钉员工自动同步
try:
    configs = model.search([('active', '=', True), ('auto_sync_employees', '=', True)])
    for config in configs:
        try:
            sync_service = env['dingtalk.sync.service']
            result = sync_service.sync_employees(config)
            
            # 更新配置状态
            config.write({
                'last_employee_sync': fields.Datetime.now(),
                'last_sync_status': 'success' if result.get('success') else 'error',
                'last_sync_message': result.get('message', ''),
                'total_employees_synced': result.get('synced_count', 0),
            })
            
            if result.get('success'):
                log.info(f'DingTalk employee sync completed for config {config.name}: {result.get("synced_count", 0)} employees synced')
            else:
                log.error(f'DingTalk employee sync failed for config {config.name}: {result.get("message", "")}')
                
        except Exception as e:
            log.error(f'DingTalk employee sync error for config {config.name}: {str(e)}')
            config.write({
                'last_employee_sync': fields.Datetime.now(),
                'last_sync_status': 'error',
                'last_sync_message': str(e),
            })
            
except Exception as e:
    log.error(f'DingTalk employee sync cron job error: {str(e)}')
            </field>
            <field name="interval_number">24</field>
            <field name="interval_type">hours</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="active" eval="True"/>
        </record>

        <!-- 钉钉考勤自动同步定时任务 -->
        <record id="cron_dingtalk_sync_attendance" model="ir.cron">
            <field name="name">DingTalk: Auto Sync Attendance</field>
            <field name="model_id" ref="model_dingtalk_config"/>
            <field name="state">code</field>
            <field name="code">
# 钉钉考勤自动同步
try:
    configs = model.search([('active', '=', True), ('auto_sync_attendance', '=', True)])
    for config in configs:
        try:
            sync_service = env['dingtalk.sync.service']
            result = sync_service.sync_attendance(config)
            
            # 更新配置状态
            config.write({
                'last_attendance_sync': fields.Datetime.now(),
                'last_sync_status': 'success' if result.get('success') else 'error',
                'last_sync_message': result.get('message', ''),
                'total_attendance_synced': result.get('synced_count', 0),
            })
            
            if result.get('success'):
                log.info(f'DingTalk attendance sync completed for config {config.name}: {result.get("synced_count", 0)} records synced')
            else:
                log.error(f'DingTalk attendance sync failed for config {config.name}: {result.get("message", "")}')
                
        except Exception as e:
            log.error(f'DingTalk attendance sync error for config {config.name}: {str(e)}')
            config.write({
                'last_attendance_sync': fields.Datetime.now(),
                'last_sync_status': 'error',
                'last_sync_message': str(e),
            })
            
except Exception as e:
    log.error(f'DingTalk attendance sync cron job error: {str(e)}')
            </field>
            <field name="interval_number">4</field>
            <field name="interval_type">hours</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="active" eval="True"/>
        </record>

        <!-- 清理旧同步日志定时任务 -->
        <record id="cron_dingtalk_cleanup_logs" model="ir.cron">
            <field name="name">DingTalk: Cleanup Old Sync Logs</field>
            <field name="model_id" ref="model_dingtalk_sync_log"/>
            <field name="state">code</field>
            <field name="code">
# 清理30天前的同步日志
try:
    count = model.cleanup_old_logs(30)
    log.info(f'DingTalk log cleanup completed: {count} old logs removed')
except Exception as e:
    log.error(f'DingTalk log cleanup error: {str(e)}')
            </field>
            <field name="interval_number">7</field>
            <field name="interval_type">days</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="active" eval="True"/>
        </record>

    </data>
</odoo>
