<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>DingTalk Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .feature { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; background: #f9f9f9; }
        .feature h3 { margin-top: 0; color: #007cba; }
        .screenshot { text-align: center; margin: 20px 0; }
        .screenshot img { max-width: 100%; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 DingTalk Integration</h1>
        <p>Complete DingTalk integration for Odoo 18</p>
        <p>钉钉企业应用完整集成解决方案</p>
    </div>

    <div class="feature">
        <h3>👥 Employee Synchronization</h3>
        <p>Automatically sync employee information from DingTalk to Odoo, including:</p>
        <ul>
            <li>Employee basic information</li>
            <li>Department assignments</li>
            <li>Contact details</li>
            <li>Job positions</li>
        </ul>
    </div>

    <div class="feature">
        <h3>⏰ Attendance Integration</h3>
        <p>Import attendance records from DingTalk with full details:</p>
        <ul>
            <li>Check-in/Check-out times</li>
            <li>Location information</li>
            <li>Attendance status (Normal, Late, Early, etc.)</li>
            <li>Exception handling</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🔄 Automated Synchronization</h3>
        <p>Set up automated data synchronization:</p>
        <ul>
            <li>Scheduled sync jobs</li>
            <li>Configurable intervals</li>
            <li>Error handling and retry</li>
            <li>Comprehensive logging</li>
        </ul>
    </div>

    <div class="feature">
        <h3>⚙️ Easy Configuration</h3>
        <p>Simple setup and management:</p>
        <ul>
            <li>User-friendly configuration interface</li>
            <li>Connection testing</li>
            <li>Flexible sync options</li>
            <li>Department-level control</li>
        </ul>
    </div>

    <div class="feature">
        <h3>📊 Monitoring & Logging</h3>
        <p>Complete visibility into sync operations:</p>
        <ul>
            <li>Detailed sync logs</li>
            <li>Success/failure statistics</li>
            <li>Error tracking</li>
            <li>Performance monitoring</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🔐 Security & Permissions</h3>
        <p>Robust security features:</p>
        <ul>
            <li>Role-based access control</li>
            <li>Secure API credential storage</li>
            <li>Data access rules</li>
            <li>Audit trails</li>
        </ul>
    </div>

    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e8f4f8;">
        <h3>Ready to integrate with DingTalk?</h3>
        <p>Install this module and connect your DingTalk enterprise application with Odoo in minutes!</p>
        <p><strong>支持钉钉企业版，快速集成员工和考勤数据！</strong></p>
    </div>
</body>
</html>
