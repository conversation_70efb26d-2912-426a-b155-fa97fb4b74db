<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 扩展HR员工表单视图 -->
    <record id="hr_employee_form_dingtalk_inherit" model="ir.ui.view">
        <field name="name">hr.employee.form.dingtalk.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <!-- 在按钮框中添加钉钉相关按钮 -->
            <div name="button_box" position="inside">
                <button name="action_view_dingtalk_binding" type="object" 
                        class="oe_stat_button" icon="fa-link">
                    <field name="dingtalk_sync_status" widget="statinfo" 
                           string="钉钉绑定"/>
                </button>
                <button name="action_view_attendance_from_dingtalk" type="object" 
                        class="oe_stat_button" icon="fa-clock-o">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">钉钉考勤</span>
                    </div>
                </button>
            </div>
            
            <!-- 在工作信息页面添加钉钉信息 -->
            <page name="hr_settings" position="after">
                <page string="钉钉集成" name="dingtalk_integration">
                    <group>
                        <group name="dingtalk_binding" string="钉钉绑定信息">
                            <field name="dingtalk_user_id" readonly="1"/>
                            <field name="dingtalk_name" readonly="1"/>
                            <field name="dingtalk_mobile" readonly="1"/>
                            <field name="dingtalk_sync_status" readonly="1"/>
                            <field name="last_dingtalk_sync" readonly="1"/>
                        </group>
                        <group name="dingtalk_settings" string="钉钉设置">
                            <field name="dingtalk_sync_enabled"/>
                        </group>
                    </group>
                    
                    <div class="oe_button_box mt-3">
                        <button name="action_create_dingtalk_binding" type="object" 
                                string="创建钉钉绑定" class="btn-primary" 
                                icon="fa-plus" invisible="dingtalk_binding_id"/>
                        <button name="action_view_dingtalk_binding" type="object" 
                                string="查看钉钉绑定" class="btn-secondary" 
                                icon="fa-eye" invisible="not dingtalk_binding_id"/>
                        <button name="action_sync_from_dingtalk" type="object" 
                                string="从钉钉同步" class="btn-secondary" 
                                icon="fa-refresh" invisible="not dingtalk_binding_id"/>
                    </div>
                </page>
            </page>
        </field>
    </record>

    <!-- 扩展HR员工列表视图 -->
    <record id="hr_employee_list_dingtalk_inherit" model="ir.ui.view">
        <field name="name">hr.employee.list.dingtalk.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <field name="work_email" position="after">
                <field name="dingtalk_sync_status" widget="badge" 
                       decoration-success="dingtalk_sync_status == 'bound'"
                       decoration-warning="dingtalk_sync_status == 'error'"
                       decoration-muted="dingtalk_sync_status == 'not_bound'"
                       optional="hide"/>
                <field name="dingtalk_user_id" optional="hide"/>
                <field name="last_dingtalk_sync" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- 扩展HR员工搜索视图 -->
    <record id="hr_employee_search_dingtalk_inherit" model="ir.ui.view">
        <field name="name">hr.employee.search.dingtalk.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="dingtalk_user_id"/>
                <field name="dingtalk_name"/>
            </field>
            
            <filter name="inactive" position="after">
                <separator/>
                <filter string="已绑定钉钉" name="dingtalk_bound" 
                        domain="[('dingtalk_sync_status', '=', 'bound')]"/>
                <filter string="未绑定钉钉" name="dingtalk_not_bound" 
                        domain="[('dingtalk_sync_status', '=', 'not_bound')]"/>
                <filter string="钉钉绑定错误" name="dingtalk_error" 
                        domain="[('dingtalk_sync_status', '=', 'error')]"/>
                <separator/>
                <filter string="启用钉钉同步" name="dingtalk_sync_enabled" 
                        domain="[('dingtalk_sync_enabled', '=', True)]"/>
            </filter>
            
            <filter name="group_manager" position="after">
                <filter string="钉钉同步状态" name="group_by_dingtalk_status" 
                        context="{'group_by': 'dingtalk_sync_status'}"/>
            </filter>
        </field>
    </record>

</odoo>
