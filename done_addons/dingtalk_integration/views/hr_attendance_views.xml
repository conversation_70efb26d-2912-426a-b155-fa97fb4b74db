<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 扩展HR考勤表单视图 -->
    <record id="hr_attendance_form_dingtalk_inherit" model="ir.ui.view">
        <field name="name">hr.attendance.form.dingtalk.inherit</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
        <field name="arch" type="xml">
            <!-- 在表单中添加钉钉相关信息 -->
            <field name="employee_id" position="after">
                <field name="synced_from_dingtalk" readonly="1" 
                       invisible="not synced_from_dingtalk"/>
                <field name="dingtalk_sync_time" readonly="1" 
                       invisible="not synced_from_dingtalk"/>
            </field>
            
            <!-- 添加钉钉详细信息页面 -->
            <sheet position="inside">
                <notebook invisible="not synced_from_dingtalk">
                    <page string="钉钉详情" name="dingtalk_details">
                        <group>
                            <group name="dingtalk_basic" string="钉钉基本信息">
                                <field name="dingtalk_record_id" readonly="1"/>
                                <field name="dingtalk_user_id" readonly="1"/>
                                <field name="dingtalk_check_type" readonly="1"/>
                                <field name="dingtalk_user_check_time" readonly="1"/>
                                <field name="dingtalk_base_check_time" readonly="1"/>
                            </group>
                            <group name="dingtalk_result" string="钉钉结果">
                                <field name="dingtalk_location_result" readonly="1"/>
                                <field name="dingtalk_time_result" readonly="1"/>
                                <field name="dingtalk_location_title" readonly="1"/>
                            </group>
                        </group>
                        <group name="dingtalk_location" string="位置详情" 
                               invisible="not dingtalk_location_detail">
                            <field name="dingtalk_location_detail" readonly="1" widget="text"/>
                        </group>
                    </page>
                </notebook>
            </sheet>
        </field>
    </record>

    <!-- 扩展HR考勤列表视图 -->
    <record id="hr_attendance_list_dingtalk_inherit" model="ir.ui.view">
        <field name="name">hr.attendance.list.dingtalk.inherit</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.view_attendance_tree"/>
        <field name="arch" type="xml">
            <field name="worked_hours" position="after">
                <field name="synced_from_dingtalk" widget="boolean_toggle" 
                       string="钉钉" optional="hide"/>
                <field name="dingtalk_time_result" widget="badge" 
                       decoration-success="dingtalk_time_result == 'Normal'"
                       decoration-warning="dingtalk_time_result in ['Early', 'Late']"
                       decoration-danger="dingtalk_time_result in ['SeriousLate', 'Absenteeism']"
                       decoration-muted="dingtalk_time_result == 'NotSigned'"
                       optional="hide"/>
                <field name="dingtalk_location_result" widget="badge" 
                       decoration-success="dingtalk_location_result == 'Normal'"
                       decoration-warning="dingtalk_location_result == 'Outside'"
                       decoration-muted="dingtalk_location_result == 'NotSigned'"
                       optional="hide"/>
            </field>
            
            <!-- 添加钉钉详情按钮 -->
            <field name="worked_hours" position="after">
                <button name="action_view_dingtalk_details" type="object" 
                        string="钉钉详情" icon="fa-info-circle" 
                        class="btn-link" invisible="not synced_from_dingtalk"/>
            </field>
        </field>
    </record>

    <!-- 扩展HR考勤搜索视图 -->
    <record id="hr_attendance_search_dingtalk_inherit" model="ir.ui.view">
        <field name="name">hr.attendance.search.dingtalk.inherit</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter"/>
        <field name="arch" type="xml">
            <field name="employee_id" position="after">
                <field name="dingtalk_user_id"/>
                <field name="dingtalk_record_id"/>
            </field>
            
            <filter name="check_in_filter" position="after">
                <separator/>
                <filter string="来自钉钉" name="from_dingtalk"
                        domain="[('synced_from_dingtalk', '=', True)]"/>
                <filter string="手动录入" name="manual_entry"
                        domain="[('synced_from_dingtalk', '=', False)]"/>
                <separator/>
                <filter string="正常打卡" name="normal_time"
                        domain="[('dingtalk_time_result', '=', 'Normal')]"/>
                <filter string="迟到" name="late"
                        domain="[('dingtalk_time_result', 'in', ['Late', 'SeriousLate'])]"/>
                <filter string="早退" name="early"
                        domain="[('dingtalk_time_result', '=', 'Early')]"/>
                <filter string="旷工" name="absenteeism"
                        domain="[('dingtalk_time_result', '=', 'Absenteeism')]"/>
                <separator/>
                <filter string="正常位置" name="normal_location"
                        domain="[('dingtalk_location_result', '=', 'Normal')]"/>
                <filter string="外勤" name="outside"
                        domain="[('dingtalk_location_result', '=', 'Outside')]"/>
            </filter>
            
            <filter name="employee" position="after">
                <filter string="钉钉时间结果" name="group_by_time_result"
                        context="{'group_by': 'dingtalk_time_result'}"/>
                <filter string="钉钉位置结果" name="group_by_location_result"
                        context="{'group_by': 'dingtalk_location_result'}"/>
                <filter string="数据来源" name="group_by_sync_source"
                        context="{'group_by': 'synced_from_dingtalk'}"/>
            </filter>
        </field>
    </record>

    <!-- 钉钉考勤记录专用视图 -->
    <record id="dingtalk_attendance_list_view" model="ir.ui.view">
        <field name="name">dingtalk.attendance.list</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <list string="钉钉考勤记录" default_order="check_in desc">
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <field name="worked_hours" widget="float_time"/>
                <field name="dingtalk_check_type"/>
                <field name="dingtalk_time_result" widget="badge" 
                       decoration-success="dingtalk_time_result == 'Normal'"
                       decoration-warning="dingtalk_time_result in ['Early', 'Late']"
                       decoration-danger="dingtalk_time_result in ['SeriousLate', 'Absenteeism']"/>
                <field name="dingtalk_location_result" widget="badge" 
                       decoration-success="dingtalk_location_result == 'Normal'"
                       decoration-warning="dingtalk_location_result == 'Outside'"/>
                <field name="dingtalk_location_title"/>
                <field name="dingtalk_sync_time"/>
                <button name="action_view_dingtalk_details" type="object" 
                        string="详情" icon="fa-eye" class="btn-link"/>
            </list>
        </field>
    </record>

    <!-- 钉钉考勤记录动作 -->
    <record id="dingtalk_attendance_action" model="ir.actions.act_window">
        <field name="name">钉钉考勤记录</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="dingtalk_attendance_list_view"/>
        <field name="domain">[('synced_from_dingtalk', '=', True)]</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                暂无钉钉考勤记录
            </p>
            <p>
                这里显示从钉钉同步过来的考勤打卡记录。
            </p>
        </field>
    </record>

</odoo>
