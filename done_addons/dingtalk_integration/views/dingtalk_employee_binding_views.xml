<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 钉钉员工绑定列表视图 -->
    <record id="dingtalk_employee_binding_list_view" model="ir.ui.view">
        <field name="name">dingtalk.employee.binding.list</field>
        <field name="model">dingtalk.employee.binding</field>
        <field name="arch" type="xml">
            <list string="钉钉员工绑定" default_order="employee_name">
                <field name="employee_name"/>
                <field name="dingtalk_user_id"/>
                <field name="dingtalk_name"/>
                <field name="dingtalk_mobile"/>
                <field name="dingtalk_email"/>
                <field name="binding_status" widget="badge" 
                       decoration-success="binding_status == 'active'"
                       decoration-warning="binding_status == 'pending'"
                       decoration-danger="binding_status == 'error'"
                       decoration-muted="binding_status == 'inactive'"/>
                <field name="sync_status" widget="badge" 
                       decoration-success="sync_status == 'success'"
                       decoration-warning="sync_status == 'warning'"
                       decoration-danger="sync_status == 'error'"/>
                <field name="last_sync_time"/>
                <field name="active" widget="boolean_toggle"/>
                <button name="action_test_sync" type="object" string="测试同步" 
                        icon="fa-refresh" class="btn-link"/>
            </list>
        </field>
    </record>

    <!-- 钉钉员工绑定表单视图 -->
    <record id="dingtalk_employee_binding_form_view" model="ir.ui.view">
        <field name="name">dingtalk.employee.binding.form</field>
        <field name="model">dingtalk.employee.binding</field>
        <field name="arch" type="xml">
            <form string="钉钉员工绑定">
                <header>
                    <button name="action_test_sync" type="object" string="测试同步" 
                            class="btn-primary" icon="fa-refresh"/>
                    <field name="binding_status" widget="statusbar" 
                           statusbar_visible="pending,active,error"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="已停用" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="display_name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="odoo_info" string="Odoo员工信息">
                            <field name="employee_id" options="{'no_create': True}"/>
                            <field name="employee_name" readonly="1"/>
                            <field name="employee_work_email" readonly="1"/>
                            <field name="employee_mobile_phone" readonly="1"/>
                            <field name="active"/>
                        </group>
                        <group name="dingtalk_info" string="钉钉用户信息">
                            <field name="dingtalk_user_id" required="1"/>
                            <field name="dingtalk_union_id"/>
                            <field name="dingtalk_name"/>
                            <field name="dingtalk_mobile"/>
                            <field name="dingtalk_email"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="钉钉详细信息" name="dingtalk_details">
                            <group>
                                <group>
                                    <field name="dingtalk_job_number"/>
                                    <field name="dingtalk_position"/>
                                    <field name="dingtalk_dept_ids" widget="text"/>
                                    <field name="dingtalk_avatar"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="同步状态" name="sync_status">
                            <group>
                                <group string="同步信息">
                                    <field name="last_sync_time" readonly="1"/>
                                    <field name="sync_status" readonly="1"/>
                                    <field name="sync_message" readonly="1" widget="text"/>
                                    <field name="created_by_sync" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- 钉钉员工绑定搜索视图 -->
    <record id="dingtalk_employee_binding_search_view" model="ir.ui.view">
        <field name="name">dingtalk.employee.binding.search</field>
        <field name="model">dingtalk.employee.binding</field>
        <field name="arch" type="xml">
            <search string="钉钉员工绑定">
                <field name="employee_name"/>
                <field name="dingtalk_user_id"/>
                <field name="dingtalk_name"/>
                <field name="dingtalk_mobile"/>
                <field name="dingtalk_email"/>
                <separator/>
                <filter string="激活" name="active" domain="[('active', '=', True)]"/>
                <filter string="未激活" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="正常绑定" name="binding_active" 
                        domain="[('binding_status', '=', 'active')]"/>
                <filter string="绑定错误" name="binding_error" 
                        domain="[('binding_status', '=', 'error')]"/>
                <filter string="待处理" name="binding_pending" 
                        domain="[('binding_status', '=', 'pending')]"/>
                <separator/>
                <filter string="同步成功" name="sync_success" 
                        domain="[('sync_status', '=', 'success')]"/>
                <filter string="同步错误" name="sync_error" 
                        domain="[('sync_status', '=', 'error')]"/>
                <separator/>
                <filter string="由同步创建" name="created_by_sync" 
                        domain="[('created_by_sync', '=', True)]"/>
                <group expand="0" string="分组">
                    <filter string="绑定状态" name="group_by_binding_status" 
                            context="{'group_by': 'binding_status'}"/>
                    <filter string="同步状态" name="group_by_sync_status" 
                            context="{'group_by': 'sync_status'}"/>
                    <filter string="员工" name="group_by_employee" 
                            context="{'group_by': 'employee_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 钉钉员工绑定动作 -->
    <record id="dingtalk_employee_binding_action" model="ir.actions.act_window">
        <field name="name">钉钉员工绑定</field>
        <field name="res_model">dingtalk.employee.binding</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="dingtalk_employee_binding_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                暂无钉钉员工绑定
            </p>
            <p>
                员工绑定将钉钉用户与Odoo员工关联，支持数据同步和考勤集成。
            </p>
        </field>
    </record>

</odoo>
