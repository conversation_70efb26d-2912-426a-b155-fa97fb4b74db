<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 钉钉同步日志列表视图 -->
    <record id="dingtalk_sync_log_list_view" model="ir.ui.view">
        <field name="name">dingtalk.sync.log.list</field>
        <field name="model">dingtalk.sync.log</field>
        <field name="arch" type="xml">
            <list string="钉钉同步日志" default_order="create_date desc">
                <field name="display_name"/>
                <field name="sync_type"/>
                <field name="sync_mode"/>
                <field name="status" widget="badge" 
                       decoration-info="status == 'running'"
                       decoration-success="status == 'success'"
                       decoration-warning="status == 'warning'"
                       decoration-danger="status == 'error'"
                       decoration-muted="status == 'cancelled'"/>
                <field name="start_time"/>
                <field name="end_time"/>
                <field name="duration" widget="float_time"/>
                <field name="total_records"/>
                <field name="success_records"/>
                <field name="error_records"/>
                <field name="success_rate" widget="percentage"/>
                <field name="config_name"/>
                <field name="user_id"/>
                <button name="action_view_details" type="object" string="详情" 
                        icon="fa-eye" class="btn-link"/>
                <button name="action_retry_sync" type="object" string="重试" 
                        icon="fa-repeat" class="btn-link" 
                        invisible="status not in ['error', 'cancelled']"/>
            </list>
        </field>
    </record>

    <!-- 钉钉同步日志表单视图 -->
    <record id="dingtalk_sync_log_form_view" model="ir.ui.view">
        <field name="name">dingtalk.sync.log.form</field>
        <field name="model">dingtalk.sync.log</field>
        <field name="arch" type="xml">
            <form string="钉钉同步日志" create="false" edit="false">
                <header>
                    <button name="action_retry_sync" type="object" string="重试同步" 
                            class="btn-primary" icon="fa-repeat" 
                            invisible="status not in ['error', 'cancelled']"/>
                    <field name="status" widget="statusbar" 
                           statusbar_visible="running,success,warning,error"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info" string="基本信息">
                            <field name="sync_type"/>
                            <field name="sync_mode"/>
                            <field name="config_id"/>
                            <field name="user_id"/>
                        </group>
                        <group name="time_info" string="时间信息">
                            <field name="start_time"/>
                            <field name="end_time"/>
                            <field name="duration" widget="float_time"/>
                        </group>
                    </group>
                    
                    <group name="statistics" string="统计信息">
                        <group>
                            <field name="total_records"/>
                            <field name="success_records"/>
                            <field name="error_records"/>
                            <field name="skipped_records"/>
                        </group>
                        <group>
                            <field name="success_rate" widget="percentage"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="消息" name="message">
                            <field name="message" widget="text" readonly="1"/>
                        </page>
                        
                        <page string="错误详情" name="error_details" 
                              invisible="not error_details">
                            <field name="error_details" widget="text" readonly="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 钉钉同步日志搜索视图 -->
    <record id="dingtalk_sync_log_search_view" model="ir.ui.view">
        <field name="name">dingtalk.sync.log.search</field>
        <field name="model">dingtalk.sync.log</field>
        <field name="arch" type="xml">
            <search string="钉钉同步日志">
                <field name="display_name"/>
                <field name="sync_type"/>
                <field name="config_name"/>
                <field name="user_id"/>
                <separator/>
                <filter string="运行中" name="running" domain="[('status', '=', 'running')]"/>
                <filter string="成功" name="success" domain="[('status', '=', 'success')]"/>
                <filter string="警告" name="warning" domain="[('status', '=', 'warning')]"/>
                <filter string="错误" name="error" domain="[('status', '=', 'error')]"/>
                <filter string="已取消" name="cancelled" domain="[('status', '=', 'cancelled')]"/>
                <separator/>
                <filter string="员工同步" name="employee_sync" domain="[('sync_type', '=', 'employee')]"/>
                <filter string="考勤同步" name="attendance_sync" domain="[('sync_type', '=', 'attendance')]"/>
                <filter string="连接测试" name="test_sync" domain="[('sync_type', '=', 'test')]"/>
                <separator/>
                <filter string="手动同步" name="manual_sync" domain="[('sync_mode', '=', 'manual')]"/>
                <filter string="自动同步" name="auto_sync" domain="[('sync_mode', '=', 'auto')]"/>
                <filter string="定时同步" name="cron_sync" domain="[('sync_mode', '=', 'cron')]"/>
                <separator/>
                <filter string="今天" name="today" domain="[('start_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('start_time', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                <filter string="本周" name="this_week" domain="[('start_time', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter string="本月" name="this_month" domain="[('start_time', '>=', context_today().strftime('%Y-%m-01'))]"/>
                <group expand="0" string="分组">
                    <filter string="状态" name="group_by_status" context="{'group_by': 'status'}"/>
                    <filter string="同步类型" name="group_by_sync_type" context="{'group_by': 'sync_type'}"/>
                    <filter string="同步模式" name="group_by_sync_mode" context="{'group_by': 'sync_mode'}"/>
                    <filter string="配置" name="group_by_config" context="{'group_by': 'config_id'}"/>
                    <filter string="日期" name="group_by_date" context="{'group_by': 'start_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 钉钉同步日志动作 -->
    <record id="dingtalk_sync_log_action" model="ir.actions.act_window">
        <field name="name">钉钉同步日志</field>
        <field name="res_model">dingtalk.sync.log</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="dingtalk_sync_log_search_view"/>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                暂无同步日志
            </p>
            <p>
                同步日志记录了钉钉数据同步的详细过程和结果。
            </p>
        </field>
    </record>

</odoo>
