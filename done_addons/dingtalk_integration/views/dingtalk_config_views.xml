<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 钉钉配置列表视图 -->
    <record id="dingtalk_config_list_view" model="ir.ui.view">
        <field name="name">dingtalk.config.list</field>
        <field name="model">dingtalk.config</field>
        <field name="arch" type="xml">
            <list string="钉钉配置" default_order="name">
                <field name="name"/>
                <field name="active" widget="boolean_toggle"/>
                <field name="app_key"/>
                <field name="auto_sync_employees" widget="boolean_toggle"/>
                <field name="auto_sync_attendance" widget="boolean_toggle"/>
                <field name="last_employee_sync"/>
                <field name="last_attendance_sync"/>
                <field name="last_sync_status" widget="badge" 
                       decoration-success="last_sync_status == 'success'"
                       decoration-warning="last_sync_status == 'warning'"
                       decoration-danger="last_sync_status == 'error'"/>
                <field name="total_employees_synced"/>
                <field name="total_attendance_synced"/>
            </list>
        </field>
    </record>

    <!-- 钉钉配置表单视图 -->
    <record id="dingtalk_config_form_view" model="ir.ui.view">
        <field name="name">dingtalk.config.form</field>
        <field name="model">dingtalk.config</field>
        <field name="arch" type="xml">
            <form string="钉钉配置">
                <header>
                    <button name="test_connection" type="object" string="测试连接" 
                            class="btn-primary" icon="fa-plug"/>
                    <button name="action_sync_employees" type="object" string="同步员工" 
                            class="btn-secondary" icon="fa-users"/>
                    <button name="action_sync_attendance" type="object" string="同步考勤" 
                            class="btn-secondary" icon="fa-clock-o"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(dingtalk_employee_binding_action)d" type="action" 
                                class="oe_stat_button" icon="fa-link">
                            <field name="total_employees_synced" widget="statinfo" 
                                   string="已绑定员工"/>
                        </button>
                        <button name="%(dingtalk_sync_log_action)d" type="action" 
                                class="oe_stat_button" icon="fa-list-alt">
                            <field name="total_attendance_synced" widget="statinfo" 
                                   string="同步记录"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="已停用" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="配置名称"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info" string="基本信息">
                            <field name="active"/>
                            <field name="app_key" password="True"/>
                            <field name="app_secret" password="True"/>
                            <field name="corp_id"/>
                            <field name="api_base_url"/>
                        </group>
                        <group name="sync_config" string="同步配置">
                            <field name="auto_sync_employees"/>
                            <field name="auto_sync_attendance"/>
                            <field name="sync_interval_hours"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="员工同步配置" name="employee_sync">
                            <group>
                                <group>
                                    <field name="employee_sync_dept_ids" 
                                           placeholder="留空同步所有部门，多个部门ID用逗号分隔"/>
                                    <field name="employee_create_missing"/>
                                    <field name="employee_update_existing"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="考勤同步配置" name="attendance_sync">
                            <group>
                                <group>
                                    <field name="attendance_sync_days"/>
                                    <field name="attendance_create_missing"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="同步状态" name="sync_status">
                            <group>
                                <group string="员工同步">
                                    <field name="last_employee_sync" readonly="1"/>
                                    <field name="total_employees_synced" readonly="1"/>
                                </group>
                                <group string="考勤同步">
                                    <field name="last_attendance_sync" readonly="1"/>
                                    <field name="total_attendance_synced" readonly="1"/>
                                </group>
                            </group>
                            <group string="最后同步状态">
                                <field name="last_sync_status" readonly="1"/>
                                <field name="last_sync_message" readonly="1" widget="text"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- 钉钉配置搜索视图 -->
    <record id="dingtalk_config_search_view" model="ir.ui.view">
        <field name="name">dingtalk.config.search</field>
        <field name="model">dingtalk.config</field>
        <field name="arch" type="xml">
            <search string="钉钉配置">
                <field name="name"/>
                <field name="app_key"/>
                <separator/>
                <filter string="激活" name="active" domain="[('active', '=', True)]"/>
                <filter string="未激活" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="自动同步员工" name="auto_sync_employees" 
                        domain="[('auto_sync_employees', '=', True)]"/>
                <filter string="自动同步考勤" name="auto_sync_attendance" 
                        domain="[('auto_sync_attendance', '=', True)]"/>
                <separator/>
                <filter string="同步成功" name="sync_success" 
                        domain="[('last_sync_status', '=', 'success')]"/>
                <filter string="同步错误" name="sync_error" 
                        domain="[('last_sync_status', '=', 'error')]"/>
                <group expand="0" string="分组">
                    <filter string="状态" name="group_by_active" context="{'group_by': 'active'}"/>
                    <filter string="同步状态" name="group_by_sync_status" 
                            context="{'group_by': 'last_sync_status'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 钉钉配置动作 -->
    <record id="dingtalk_config_action" model="ir.actions.act_window">
        <field name="name">钉钉配置</field>
        <field name="res_model">dingtalk.config</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="dingtalk_config_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建第一个钉钉配置
            </p>
            <p>
                配置钉钉API连接参数，设置员工和考勤数据的自动同步规则。
            </p>
        </field>
    </record>

</odoo>
