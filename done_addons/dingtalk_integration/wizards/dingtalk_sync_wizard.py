# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class DingTalkSyncWizard(models.TransientModel):
    """钉钉同步向导"""
    _name = 'dingtalk.sync.wizard'
    _description = 'DingTalk Sync Wizard'

    # 基本配置
    config_id = fields.Many2one('dingtalk.config', string='钉钉配置', required=True,
                               default=lambda self: self._get_default_config())
    sync_type = fields.Selection([
        ('employee', '员工同步'),
        ('attendance', '考勤同步'),
        ('both', '员工和考勤同步'),
    ], string='同步类型', required=True, default='both')
    
    # 员工同步选项
    employee_sync_enabled = fields.Boolean('同步员工', default=True)
    employee_dept_ids = fields.Text('部门ID列表', 
                                   help='要同步的部门ID，用逗号分隔，留空则同步所有部门')
    employee_create_missing = fields.Bo<PERSON>an('创建缺失员工', default=True)
    employee_update_existing = fields.Bo<PERSON>an('更新现有员工', default=True)
    
    # 考勤同步选项
    attendance_sync_enabled = fields.Boolean('同步考勤', default=True)
    attendance_date_from = fields.Date('开始日期', 
                                      default=lambda self: fields.Date.today() - timedelta(days=7))
    attendance_date_to = fields.Date('结束日期', default=fields.Date.today())
    attendance_create_missing = fields.Boolean('创建缺失考勤', default=True)
    
    # 高级选项
    force_sync = fields.Boolean('强制同步', default=False,
                               help='忽略上次同步时间，强制重新同步所有数据')
    test_mode = fields.Boolean('测试模式', default=False,
                              help='测试模式下不会实际创建或更新数据')

    @api.model
    def _get_default_config(self):
        """获取默认配置"""
        config = self.env['dingtalk.config'].search([('active', '=', True)], limit=1)
        return config.id if config else False

    @api.onchange('sync_type')
    def _onchange_sync_type(self):
        """根据同步类型设置选项"""
        if self.sync_type == 'employee':
            self.employee_sync_enabled = True
            self.attendance_sync_enabled = False
        elif self.sync_type == 'attendance':
            self.employee_sync_enabled = False
            self.attendance_sync_enabled = True
        elif self.sync_type == 'both':
            self.employee_sync_enabled = True
            self.attendance_sync_enabled = True

    @api.constrains('attendance_date_from', 'attendance_date_to')
    def _check_attendance_dates(self):
        """验证考勤日期范围"""
        for wizard in self:
            if wizard.attendance_date_from and wizard.attendance_date_to:
                if wizard.attendance_date_from > wizard.attendance_date_to:
                    raise UserError(_('开始日期不能晚于结束日期'))
                
                # 限制日期范围不超过90天
                delta = wizard.attendance_date_to - wizard.attendance_date_from
                if delta.days > 90:
                    raise UserError(_('日期范围不能超过90天'))

    def action_sync(self):
        """执行同步"""
        self.ensure_one()
        
        if not self.config_id:
            raise UserError(_('请选择钉钉配置'))
        
        if not self.employee_sync_enabled and not self.attendance_sync_enabled:
            raise UserError(_('请至少选择一种同步类型'))
        
        results = []
        
        try:
            # 员工同步
            if self.employee_sync_enabled:
                employee_result = self._sync_employees()
                results.append(employee_result)
            
            # 考勤同步
            if self.attendance_sync_enabled:
                attendance_result = self._sync_attendance()
                results.append(attendance_result)
            
            # 汇总结果
            total_success = sum(r.get('synced_count', 0) for r in results)
            total_errors = sum(r.get('error_count', 0) for r in results)
            
            # 显示结果
            message_parts = []
            for result in results:
                if result.get('message'):
                    message_parts.append(result['message'])
            
            final_message = '\n'.join(message_parts)
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('同步完成'),
                    'message': final_message,
                    'type': 'success' if total_errors == 0 else 'warning',
                    'sticky': True,
                }
            }
            
        except Exception as e:
            _logger.error(f'Sync wizard error: {str(e)}')
            raise UserError(_('同步失败：%s') % str(e))

    def _sync_employees(self):
        """同步员工"""
        if self.test_mode:
            return {
                'success': True,
                'synced_count': 0,
                'error_count': 0,
                'message': '员工同步（测试模式）'
            }
        
        # 临时更新配置
        original_values = {}
        if self.employee_dept_ids:
            original_values['employee_sync_dept_ids'] = self.config_id.employee_sync_dept_ids
            self.config_id.employee_sync_dept_ids = self.employee_dept_ids
        
        if self.employee_create_missing != self.config_id.employee_create_missing:
            original_values['employee_create_missing'] = self.config_id.employee_create_missing
            self.config_id.employee_create_missing = self.employee_create_missing
        
        if self.employee_update_existing != self.config_id.employee_update_existing:
            original_values['employee_update_existing'] = self.config_id.employee_update_existing
            self.config_id.employee_update_existing = self.employee_update_existing
        
        try:
            # 执行同步
            sync_service = self.env['dingtalk.sync.service']
            result = sync_service.sync_employees(self.config_id)
            
            return result
            
        finally:
            # 恢复原始配置
            if original_values:
                self.config_id.write(original_values)

    def _sync_attendance(self):
        """同步考勤"""
        if self.test_mode:
            return {
                'success': True,
                'synced_count': 0,
                'error_count': 0,
                'message': '考勤同步（测试模式）'
            }
        
        # 使用HR考勤模型的同步方法
        result = self.env['hr.attendance'].sync_attendance_from_dingtalk(
            self.config_id,
            self.attendance_date_from,
            self.attendance_date_to
        )
        
        return result

    def action_test_connection(self):
        """测试连接"""
        self.ensure_one()
        if not self.config_id:
            raise UserError(_('请选择钉钉配置'))
        
        return self.config_id.test_connection()

    def action_preview_employees(self):
        """预览员工数据"""
        self.ensure_one()
        if not self.config_id:
            raise UserError(_('请选择钉钉配置'))
        
        try:
            api_client = self.env['dingtalk.api.client'].with_config(self.config_id)
            
            # 获取部门列表
            dept_result = api_client.get_department_list()
            if not dept_result.get('success'):
                raise UserError(_('获取部门列表失败：%s') % dept_result.get('message'))
            
            departments = dept_result.get('data', {}).get('department', [])
            
            # 过滤部门
            if self.employee_dept_ids:
                dept_ids = [int(x.strip()) for x in self.employee_dept_ids.split(',') if x.strip().isdigit()]
                departments = [dept for dept in departments if dept.get('id') in dept_ids]
            
            # 统计员工数量
            total_employees = 0
            dept_info = []
            
            for dept in departments[:5]:  # 只预览前5个部门
                users_result = api_client.get_user_list(dept.get('id'), 0, 100)
                if users_result.get('success'):
                    user_count = len(users_result.get('data', {}).get('userlist', []))
                    total_employees += user_count
                    dept_info.append(f"• {dept.get('name')}: {user_count} 个员工")
            
            if len(departments) > 5:
                dept_info.append(f"... 还有 {len(departments) - 5} 个部门")
            
            message = f"预计同步 {len(departments)} 个部门，约 {total_employees} 个员工：\n" + "\n".join(dept_info)
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('员工数据预览'),
                    'message': message,
                    'type': 'info',
                    'sticky': True,
                }
            }
            
        except Exception as e:
            raise UserError(_('预览失败：%s') % str(e))
