id,name,model_id/id,group_id/id,perm_read,perm_write,perm_create,perm_unlink
access_dingtalk_config_user,dingtalk.config.user,model_dingtalk_config,group_dingtalk_user,1,0,0,0
access_dingtalk_config_manager,dingtalk.config.manager,model_dingtalk_config,group_dingtalk_manager,1,1,1,0
access_dingtalk_config_admin,dingtalk.config.admin,model_dingtalk_config,group_dingtalk_admin,1,1,1,1
access_dingtalk_employee_binding_user,dingtalk.employee.binding.user,model_dingtalk_employee_binding,group_dingtalk_user,1,0,0,0
access_dingtalk_employee_binding_manager,dingtalk.employee.binding.manager,model_dingtalk_employee_binding,group_dingtalk_manager,1,1,1,1
access_dingtalk_sync_log_user,dingtalk.sync.log.user,model_dingtalk_sync_log,group_dingtalk_user,1,0,0,0
access_dingtalk_sync_log_manager,dingtalk.sync.log.manager,model_dingtalk_sync_log,group_dingtalk_manager,1,1,1,1
access_dingtalk_api_client_user,dingtalk.api.client.user,model_dingtalk_api_client,group_dingtalk_user,1,0,0,0
access_dingtalk_api_client_manager,dingtalk.api.client.manager,model_dingtalk_api_client,group_dingtalk_manager,1,1,1,0
access_dingtalk_sync_service_user,dingtalk.sync.service.user,model_dingtalk_sync_service,group_dingtalk_user,1,0,0,0
access_dingtalk_sync_service_manager,dingtalk.sync.service.manager,model_dingtalk_sync_service,group_dingtalk_manager,1,1,1,0
