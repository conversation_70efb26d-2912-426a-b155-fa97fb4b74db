<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- 钉钉集成权限组 -->
        <record id="group_dingtalk_user" model="res.groups">
            <field name="name">DingTalk User</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="comment">钉钉集成用户权限</field>
        </record>

        <record id="group_dingtalk_manager" model="res.groups">
            <field name="name">DingTalk Manager</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="implied_ids" eval="[(4, ref('group_dingtalk_user'))]"/>
            <field name="comment">钉钉集成管理员权限</field>
        </record>

        <record id="group_dingtalk_admin" model="res.groups">
            <field name="name">DingTalk Administrator</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="implied_ids" eval="[(4, ref('group_dingtalk_manager'))]"/>
            <field name="comment">钉钉集成系统管理员权限</field>
        </record>

        <!-- 数据访问规则 -->
        
        <!-- 钉钉配置访问规则 -->
        <record id="dingtalk_config_rule_user" model="ir.rule">
            <field name="name">DingTalk Config: User Access</field>
            <field name="model_id" ref="model_dingtalk_config"/>
            <field name="domain_force">[('active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="dingtalk_config_rule_manager" model="ir.rule">
            <field name="name">DingTalk Config: Manager Access</field>
            <field name="model_id" ref="model_dingtalk_config"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="dingtalk_config_rule_admin" model="ir.rule">
            <field name="name">DingTalk Config: Admin Access</field>
            <field name="model_id" ref="model_dingtalk_config"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- 员工绑定访问规则 -->
        <record id="dingtalk_binding_rule_user" model="ir.rule">
            <field name="name">DingTalk Binding: User Access</field>
            <field name="model_id" ref="model_dingtalk_employee_binding"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="dingtalk_binding_rule_manager" model="ir.rule">
            <field name="name">DingTalk Binding: Manager Access</field>
            <field name="model_id" ref="model_dingtalk_employee_binding"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- 同步日志访问规则 -->
        <record id="dingtalk_log_rule_user" model="ir.rule">
            <field name="name">DingTalk Log: User Access</field>
            <field name="model_id" ref="model_dingtalk_sync_log"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="dingtalk_log_rule_manager" model="ir.rule">
            <field name="name">DingTalk Log: Manager Access</field>
            <field name="model_id" ref="model_dingtalk_sync_log"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_dingtalk_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="True"/>
        </record>

    </data>
</odoo>
