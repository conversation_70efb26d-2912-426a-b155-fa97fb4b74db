# DingTalk Integration for Odoo 18

钉钉集成模块为 Odoo 18 提供完整的钉钉企业应用集成功能。

## 功能特性

### 🔗 员工信息同步
- 从钉钉API获取员工信息并同步到Odoo的hr.employee模型
- 建立钉钉用户ID与Odoo员工记录的绑定关系
- 支持增量同步和全量同步
- 处理员工信息的创建、更新和停用状态同步
- 智能匹配：通过钉钉用户ID、手机号、邮箱进行员工匹配

### ⏰ 考勤数据同步
- 从钉钉API获取员工考勤打卡记录
- 将考勤数据同步到Odoo的hr.attendance模型
- 支持按日期范围同步考勤数据
- 处理上班打卡、下班打卡的状态映射
- 考勤异常状态识别（迟到、早退、外勤等）

### ⚙️ 配置管理
- 灵活的钉钉API配置界面
- 支持多个钉钉应用配置
- 自定义同步规则和频率
- 部门级别的同步控制

### 📊 同步监控
- 详细的同步日志记录
- 实时同步状态监控
- 错误处理和重试机制
- 数据统计和成功率分析

### 🔄 自动化同步
- 定时任务自动同步数据
- 可配置的同步间隔
- 增量同步优化性能
- 异常处理和通知

## 安装要求

### 系统要求
- Odoo 18.0+
- Python 3.8+
- 网络访问钉钉API端点

### Python依赖
```bash
pip install requests
```

### Odoo模块依赖
- `base`
- `hr`
- `hr_attendance`
- `mail`

## 安装步骤

1. **下载模块**
   ```bash
   cd /path/to/odoo/addons
   git clone <repository_url> dingtalk_integration
   ```

2. **安装Python依赖**
   ```bash
   pip install -r dingtalk_integration/requirements.txt
   ```

3. **更新应用列表**
   - 进入Odoo后台
   - 应用 > 更新应用列表

4. **安装模块**
   - 搜索"DingTalk Integration"
   - 点击安装

## 配置指南

### 1. 钉钉应用配置

1. **创建钉钉企业内部应用**
   - 登录钉钉开发者后台
   - 创建企业内部应用
   - 获取 App Key 和 App Secret

2. **配置应用权限**
   - 通讯录管理权限
   - 考勤管理权限
   - 其他必要权限

### 2. Odoo配置

1. **创建钉钉配置**
   - 钉钉集成 > 配置 > 钉钉配置
   - 填入App Key和App Secret
   - 测试连接

2. **配置同步规则**
   - 设置员工同步选项
   - 配置考勤同步参数
   - 启用自动同步

3. **权限配置**
   - 分配用户到相应权限组
   - 配置数据访问规则

## 使用说明

### 员工同步

1. **手动同步**
   - 钉钉集成 > 配置 > 数据同步
   - 选择员工同步
   - 配置同步选项
   - 执行同步

2. **自动同步**
   - 在钉钉配置中启用自动同步
   - 设置同步间隔
   - 系统将定时执行同步

### 考勤同步

1. **手动同步**
   - 钉钉集成 > 配置 > 数据同步
   - 选择考勤同步
   - 设置日期范围
   - 执行同步

2. **查看考勤记录**
   - 钉钉集成 > 考勤管理 > 钉钉考勤记录
   - 或在HR > 考勤中查看

### 员工绑定管理

1. **查看绑定状态**
   - 钉钉集成 > 员工管理 > 员工绑定

2. **手动创建绑定**
   - 在员工表单中点击"创建钉钉绑定"
   - 填入钉钉用户ID

3. **测试绑定**
   - 在绑定记录中点击"测试同步"

## API参考

### 钉钉API端点
- 获取访问令牌：`/gettoken`
- 获取部门列表：`/department/list`
- 获取用户列表：`/user/list`
- 获取用户详情：`/user/get`
- 获取考勤记录：`/attendance/list`

### 主要模型

#### dingtalk.config
钉钉配置模型，存储API连接参数和同步设置。

#### dingtalk.employee.binding
员工绑定模型，关联钉钉用户和Odoo员工。

#### dingtalk.sync.log
同步日志模型，记录同步过程和结果。

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查App Key和App Secret是否正确
   - 确认网络连接正常
   - 验证钉钉应用权限

2. **员工同步失败**
   - 检查部门ID配置
   - 确认员工信息完整性
   - 查看同步日志详情

3. **考勤同步异常**
   - 验证员工绑定状态
   - 检查日期范围设置
   - 确认考勤权限配置

### 日志调试

启用调试日志：
```python
# 在odoo.conf中添加
log_level = debug
log_handler = :DEBUG
```

查看日志：
```bash
tail -f /var/log/odoo/odoo.log | grep dingtalk
```

## 开发指南

### 扩展同步逻辑

```python
class DingTalkSyncService(models.AbstractModel):
    _inherit = 'dingtalk.sync.service'
    
    def _sync_single_employee(self, user_data, config):
        # 自定义员工同步逻辑
        result = super()._sync_single_employee(user_data, config)
        # 添加额外处理
        return result
```

### 自定义API客户端

```python
class DingTalkApiClient(models.AbstractModel):
    _inherit = 'dingtalk.api.client'
    
    def custom_api_call(self, endpoint, data=None):
        # 自定义API调用
        return self._make_request('POST', endpoint, data=data)
```

## 许可证

本模块基于 LGPL-3 许可证发布。

## 支持

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 文档：https://docs.yourcompany.com/dingtalk-integration

## 更新日志

### v18.0.1.0.0
- 初始版本发布
- 员工信息同步功能
- 考勤数据同步功能
- 基础配置和监控功能
