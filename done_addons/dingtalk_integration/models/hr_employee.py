# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class HrEmployee(models.Model):
    """扩展HR员工模型"""
    _inherit = 'hr.employee'

    # 钉钉相关字段
    dingtalk_binding_id = fields.One2many('dingtalk.employee.binding', 'employee_id', 
                                         string='钉钉绑定', readonly=True)
    dingtalk_user_id = fields.Char('钉钉用户ID', related='dingtalk_binding_id.dingtalk_user_id', 
                                  readonly=True, store=False)
    dingtalk_name = fields.Char('钉钉姓名', related='dingtalk_binding_id.dingtalk_name', 
                               readonly=True, store=False)
    dingtalk_mobile = fields.Char('钉钉手机号', related='dingtalk_binding_id.dingtalk_mobile', 
                                 readonly=True, store=False)
    dingtalk_sync_status = fields.Selection([
        ('not_bound', '未绑定'),
        ('bound', '已绑定'),
        ('error', '错误'),
    ], string='钉钉同步状态', compute='_compute_dingtalk_sync_status', store=True)
    
    # 同步相关字段
    last_dingtalk_sync = fields.Datetime('最后钉钉同步时间', readonly=True)
    dingtalk_sync_enabled = fields.Boolean('启用钉钉同步', default=True,
                                          help='是否参与钉钉数据同步')

    @api.depends('dingtalk_binding_id', 'dingtalk_binding_id.binding_status')
    def _compute_dingtalk_sync_status(self):
        """计算钉钉同步状态"""
        for employee in self:
            if not employee.dingtalk_binding_id:
                employee.dingtalk_sync_status = 'not_bound'
            elif employee.dingtalk_binding_id.binding_status == 'active':
                employee.dingtalk_sync_status = 'bound'
            else:
                employee.dingtalk_sync_status = 'error'

    def action_create_dingtalk_binding(self):
        """创建钉钉绑定"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('创建钉钉绑定'),
            'res_model': 'dingtalk.employee.binding',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_employee_id': self.id,
            }
        }

    def action_view_dingtalk_binding(self):
        """查看钉钉绑定"""
        self.ensure_one()
        if not self.dingtalk_binding_id:
            return self.action_create_dingtalk_binding()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('钉钉绑定'),
            'res_model': 'dingtalk.employee.binding',
            'res_id': self.dingtalk_binding_id[0].id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_sync_from_dingtalk(self):
        """从钉钉同步数据"""
        self.ensure_one()
        if not self.dingtalk_binding_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('同步失败'),
                    'message': _('该员工未绑定钉钉账户'),
                    'type': 'warning',
                    'sticky': False,
                }
            }
        
        return self.dingtalk_binding_id[0].action_test_sync()

    def action_view_attendance_from_dingtalk(self):
        """查看来自钉钉的考勤记录"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('钉钉考勤记录'),
            'res_model': 'hr.attendance',
            'view_mode': 'list,form',
            'domain': [
                ('employee_id', '=', self.id),
                ('dingtalk_record_id', '!=', False)
            ],
            'context': {
                'search_default_employee_id': self.id,
            }
        }
