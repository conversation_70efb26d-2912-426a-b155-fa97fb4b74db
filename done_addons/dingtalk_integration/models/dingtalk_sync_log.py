# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class DingTalkSyncLog(models.Model):
    """钉钉同步日志模型"""
    _name = 'dingtalk.sync.log'
    _description = 'DingTalk Sync Log'
    _order = 'create_date desc'
    _rec_name = 'display_name'

    # 基本信息
    sync_type = fields.Selection([
        ('employee', '员工同步'),
        ('attendance', '考勤同步'),
        ('department', '部门同步'),
        ('test', '连接测试'),
    ], string='同步类型', required=True)
    
    sync_mode = fields.Selection([
        ('manual', '手动同步'),
        ('auto', '自动同步'),
        ('cron', '定时同步'),
    ], string='同步模式', required=True)
    
    # 状态信息
    status = fields.Selection([
        ('running', '运行中'),
        ('success', '成功'),
        ('error', '错误'),
        ('warning', '警告'),
        ('cancelled', '已取消'),
    ], string='状态', required=True, default='running')
    
    # 时间信息
    start_time = fields.Datetime('开始时间', required=True, default=fields.Datetime.now)
    end_time = fields.Datetime('结束时间')
    duration = fields.Float('持续时间(秒)', compute='_compute_duration', store=True)
    
    # 结果信息
    message = fields.Text('消息')
    error_details = fields.Text('错误详情')
    
    # 统计信息
    total_records = fields.Integer('总记录数', default=0)
    success_records = fields.Integer('成功记录数', default=0)
    error_records = fields.Integer('错误记录数', default=0)
    skipped_records = fields.Integer('跳过记录数', default=0)
    
    # 配置信息
    config_id = fields.Many2one('dingtalk.config', string='配置', ondelete='set null')
    config_name = fields.Char('配置名称', related='config_id.name', readonly=True)
    
    # 用户信息
    user_id = fields.Many2one('res.users', string='执行用户', 
                             default=lambda self: self.env.user)
    
    # 计算字段
    display_name = fields.Char('显示名称', compute='_compute_display_name', store=True)
    success_rate = fields.Float('成功率(%)', compute='_compute_success_rate', store=True)

    @api.depends('sync_type', 'status', 'start_time')
    def _compute_display_name(self):
        """计算显示名称"""
        for record in self:
            sync_type_dict = dict(record._fields['sync_type'].selection)
            sync_type_name = sync_type_dict.get(record.sync_type, record.sync_type)
            
            if record.start_time:
                time_str = record.start_time.strftime('%Y-%m-%d %H:%M')
                record.display_name = f"{sync_type_name} - {time_str}"
            else:
                record.display_name = sync_type_name

    @api.depends('start_time', 'end_time')
    def _compute_duration(self):
        """计算持续时间"""
        for record in self:
            if record.start_time and record.end_time:
                delta = record.end_time - record.start_time
                record.duration = delta.total_seconds()
            else:
                record.duration = 0.0

    @api.depends('total_records', 'success_records')
    def _compute_success_rate(self):
        """计算成功率"""
        for record in self:
            if record.total_records > 0:
                record.success_rate = (record.success_records / record.total_records) * 100
            else:
                record.success_rate = 0.0

    @api.model
    def create_log(self, sync_type, sync_mode, config_id=None, message=None):
        """创建同步日志"""
        return self.create({
            'sync_type': sync_type,
            'sync_mode': sync_mode,
            'config_id': config_id,
            'message': message or f'开始{dict(self._fields["sync_type"].selection).get(sync_type, sync_type)}',
            'status': 'running',
        })

    def update_progress(self, total=None, success=None, error=None, skipped=None, message=None):
        """更新进度"""
        self.ensure_one()
        update_vals = {}
        
        if total is not None:
            update_vals['total_records'] = total
        if success is not None:
            update_vals['success_records'] = success
        if error is not None:
            update_vals['error_records'] = error
        if skipped is not None:
            update_vals['skipped_records'] = skipped
        if message is not None:
            update_vals['message'] = message
            
        if update_vals:
            self.write(update_vals)

    def finish_success(self, message=None, **kwargs):
        """完成同步 - 成功"""
        self.ensure_one()
        update_vals = {
            'status': 'success',
            'end_time': fields.Datetime.now(),
            'message': message or '同步完成',
        }
        update_vals.update(kwargs)
        self.write(update_vals)

    def finish_error(self, message=None, error_details=None, **kwargs):
        """完成同步 - 错误"""
        self.ensure_one()
        update_vals = {
            'status': 'error',
            'end_time': fields.Datetime.now(),
            'message': message or '同步失败',
        }
        if error_details:
            update_vals['error_details'] = error_details
        update_vals.update(kwargs)
        self.write(update_vals)

    def finish_warning(self, message=None, **kwargs):
        """完成同步 - 警告"""
        self.ensure_one()
        update_vals = {
            'status': 'warning',
            'end_time': fields.Datetime.now(),
            'message': message or '同步完成但有警告',
        }
        update_vals.update(kwargs)
        self.write(update_vals)

    def cancel_sync(self, message=None):
        """取消同步"""
        self.ensure_one()
        self.write({
            'status': 'cancelled',
            'end_time': fields.Datetime.now(),
            'message': message or '同步已取消',
        })

    @api.model
    def cleanup_old_logs(self, days=30):
        """清理旧日志"""
        cutoff_date = fields.Datetime.now() - fields.timedelta(days=days)
        old_logs = self.search([('create_date', '<', cutoff_date)])
        count = len(old_logs)
        old_logs.unlink()
        _logger.info(f'Cleaned up {count} old sync logs older than {days} days')
        return count

    def action_view_details(self):
        """查看详情"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('同步日志详情'),
            'res_model': 'dingtalk.sync.log',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_retry_sync(self):
        """重试同步"""
        self.ensure_one()
        if self.sync_type == 'employee':
            if self.config_id:
                return self.config_id.action_sync_employees()
        elif self.sync_type == 'attendance':
            if self.config_id:
                return self.config_id.action_sync_attendance()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('重试同步'),
                'message': _('无法重试此类型的同步'),
                'type': 'warning',
                'sticky': False,
            }
        }
