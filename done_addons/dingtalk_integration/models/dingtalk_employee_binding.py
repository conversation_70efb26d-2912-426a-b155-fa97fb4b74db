# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class DingTalkEmployeeBinding(models.Model):
    """钉钉员工绑定模型"""
    _name = 'dingtalk.employee.binding'
    _description = 'DingTalk Employee Binding'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'display_name'

    # 基本信息
    employee_id = fields.Many2one('hr.employee', string='Odoo员工', required=True, 
                                 ondelete='cascade')
    dingtalk_user_id = fields.Char('钉钉用户ID', required=True, index=True,
                                  help='钉钉系统中的用户唯一标识')
    dingtalk_union_id = fields.Char('钉钉UnionID', index=True,
                                   help='钉钉开放平台统一用户标识')
    
    # 钉钉用户信息
    dingtalk_name = fields.Char('钉钉姓名')
    dingtalk_mobile = fields.Char('钉钉手机号')
    dingtalk_email = fields.Char('钉钉邮箱')
    dingtalk_job_number = fields.Char('钉钉工号')
    dingtalk_dept_ids = fields.Text('钉钉部门ID列表', help='用逗号分隔的部门ID')
    dingtalk_position = fields.Char('钉钉职位')
    dingtalk_avatar = fields.Char('钉钉头像URL')
    
    # 状态信息
    active = fields.Boolean('激活', default=True)
    binding_status = fields.Selection([
        ('active', '正常'),
        ('inactive', '未激活'),
        ('error', '错误'),
        ('pending', '待处理'),
    ], string='绑定状态', default='active', tracking=True)
    
    # 同步信息
    last_sync_time = fields.Datetime('最后同步时间')
    sync_status = fields.Selection([
        ('success', '成功'),
        ('error', '错误'),
        ('warning', '警告'),
    ], string='同步状态', tracking=True)
    sync_message = fields.Text('同步消息')
    
    # 创建和更新信息
    created_by_sync = fields.Boolean('由同步创建', default=False,
                                    help='标识此绑定是否由自动同步创建')
    
    # 计算字段
    display_name = fields.Char('显示名称', compute='_compute_display_name', store=True)
    employee_name = fields.Char('员工姓名', related='employee_id.name', readonly=True)
    employee_work_email = fields.Char('员工邮箱', related='employee_id.work_email', readonly=True)
    employee_mobile_phone = fields.Char('员工手机', related='employee_id.mobile_phone', readonly=True)

    _sql_constraints = [
        ('unique_dingtalk_user_id', 'UNIQUE(dingtalk_user_id)', 
         '钉钉用户ID必须唯一！'),
        ('unique_employee_id', 'UNIQUE(employee_id)', 
         '每个员工只能绑定一个钉钉账户！'),
    ]

    @api.depends('employee_id', 'dingtalk_user_id', 'dingtalk_name')
    def _compute_display_name(self):
        """计算显示名称"""
        for record in self:
            if record.employee_id and record.dingtalk_user_id:
                record.display_name = f"{record.employee_id.name} ({record.dingtalk_user_id})"
            elif record.dingtalk_name and record.dingtalk_user_id:
                record.display_name = f"{record.dingtalk_name} ({record.dingtalk_user_id})"
            else:
                record.display_name = record.dingtalk_user_id or '未命名绑定'

    @api.constrains('dingtalk_user_id')
    def _check_dingtalk_user_id(self):
        """验证钉钉用户ID"""
        for record in self:
            if not record.dingtalk_user_id:
                raise ValidationError(_('钉钉用户ID不能为空'))
            if len(record.dingtalk_user_id) < 3:
                raise ValidationError(_('钉钉用户ID长度不能少于3个字符'))

    @api.model
    def find_or_create_binding(self, employee_id, dingtalk_user_data):
        """查找或创建员工绑定"""
        dingtalk_user_id = dingtalk_user_data.get('userid')
        if not dingtalk_user_id:
            return None
            
        # 先尝试通过钉钉用户ID查找
        binding = self.search([('dingtalk_user_id', '=', dingtalk_user_id)], limit=1)
        
        if binding:
            # 更新现有绑定
            binding.write({
                'employee_id': employee_id,
                'dingtalk_name': dingtalk_user_data.get('name'),
                'dingtalk_mobile': dingtalk_user_data.get('mobile'),
                'dingtalk_email': dingtalk_user_data.get('email'),
                'dingtalk_job_number': dingtalk_user_data.get('jobnumber'),
                'dingtalk_dept_ids': ','.join(map(str, dingtalk_user_data.get('department', []))),
                'dingtalk_position': dingtalk_user_data.get('position'),
                'dingtalk_avatar': dingtalk_user_data.get('avatar'),
                'dingtalk_union_id': dingtalk_user_data.get('unionid'),
                'last_sync_time': fields.Datetime.now(),
                'sync_status': 'success',
                'binding_status': 'active',
            })
        else:
            # 创建新绑定
            binding = self.create({
                'employee_id': employee_id,
                'dingtalk_user_id': dingtalk_user_id,
                'dingtalk_name': dingtalk_user_data.get('name'),
                'dingtalk_mobile': dingtalk_user_data.get('mobile'),
                'dingtalk_email': dingtalk_user_data.get('email'),
                'dingtalk_job_number': dingtalk_user_data.get('jobnumber'),
                'dingtalk_dept_ids': ','.join(map(str, dingtalk_user_data.get('department', []))),
                'dingtalk_position': dingtalk_user_data.get('position'),
                'dingtalk_avatar': dingtalk_user_data.get('avatar'),
                'dingtalk_union_id': dingtalk_user_data.get('unionid'),
                'created_by_sync': True,
                'last_sync_time': fields.Datetime.now(),
                'sync_status': 'success',
                'binding_status': 'active',
            })
            
        return binding

    @api.model
    def get_employee_by_dingtalk_id(self, dingtalk_user_id):
        """通过钉钉用户ID获取员工"""
        binding = self.search([
            ('dingtalk_user_id', '=', dingtalk_user_id),
            ('active', '=', True),
            ('binding_status', '=', 'active')
        ], limit=1)
        return binding.employee_id if binding else None

    def action_test_sync(self):
        """测试同步"""
        self.ensure_one()
        try:
            # 获取钉钉API客户端
            config = self.env['dingtalk.config'].get_default_config()
            api_client = self.env['dingtalk.api.client'].with_config(config)
            
            # 获取用户信息
            user_info = api_client.get_user_detail(self.dingtalk_user_id)
            
            if user_info.get('success'):
                # 更新绑定信息
                user_data = user_info.get('data', {})
                self.write({
                    'dingtalk_name': user_data.get('name'),
                    'dingtalk_mobile': user_data.get('mobile'),
                    'dingtalk_email': user_data.get('email'),
                    'dingtalk_job_number': user_data.get('jobnumber'),
                    'dingtalk_position': user_data.get('position'),
                    'last_sync_time': fields.Datetime.now(),
                    'sync_status': 'success',
                    'sync_message': '测试同步成功',
                })
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('测试同步'),
                        'message': _('绑定测试同步成功！'),
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise Exception(user_info.get('message', '未知错误'))
                
        except Exception as e:
            _logger.error('Binding test sync failed: %s', str(e))
            self.write({
                'sync_status': 'error',
                'sync_message': str(e),
                'last_sync_time': fields.Datetime.now(),
            })
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('测试同步'),
                    'message': _('绑定测试同步失败：%s') % str(e),
                    'type': 'danger',
                    'sticky': True,
                }
            }
