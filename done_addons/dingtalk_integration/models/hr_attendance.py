# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class HrAttendance(models.Model):
    """扩展HR考勤模型"""
    _inherit = 'hr.attendance'

    # 钉钉相关字段
    dingtalk_record_id = fields.Char('钉钉记录ID', index=True,
                                    help='钉钉考勤记录的唯一标识')
    dingtalk_user_id = fields.Char('钉钉用户ID', index=True,
                                  help='钉钉用户ID')
    dingtalk_check_type = fields.Selection([
        ('OnDuty', '上班'),
        ('OffDuty', '下班'),
    ], string='钉钉打卡类型')
    
    # 钉钉考勤详细信息
    dingtalk_location_result = fields.Selection([
        ('Normal', '正常'),
        ('Outside', '外勤'),
        ('NotSigned', '未打卡'),
    ], string='钉钉位置结果')
    dingtalk_time_result = fields.Selection([
        ('Normal', '正常'),
        ('Early', '早退'),
        ('Late', '迟到'),
        ('SeriousLate', '严重迟到'),
        ('Absenteeism', '旷工迟到'),
        ('NotSigned', '未打卡'),
    ], string='钉钉时间结果')
    
    dingtalk_location_title = fields.Char('钉钉打卡地点')
    dingtalk_location_detail = fields.Text('钉钉位置详情')
    dingtalk_base_check_time = fields.Datetime('钉钉基准时间')
    dingtalk_user_check_time = fields.Datetime('钉钉实际打卡时间')
    
    # 同步信息
    synced_from_dingtalk = fields.Boolean('来自钉钉同步', default=False, readonly=True)
    dingtalk_sync_time = fields.Datetime('钉钉同步时间', readonly=True)

    _sql_constraints = [
        ('unique_dingtalk_record', 'UNIQUE(dingtalk_record_id)', 
         '钉钉考勤记录ID必须唯一！'),
    ]

    @api.model
    def create_from_dingtalk(self, record_data, employee_id):
        """从钉钉数据创建考勤记录"""
        try:
            # 解析钉钉数据
            user_check_time = record_data.get('userCheckTime')
            if not user_check_time:
                raise ValueError('缺少打卡时间')
            
            # 转换时间格式
            from datetime import datetime
            check_time = datetime.strptime(user_check_time, '%Y-%m-%d %H:%M:%S')
            
            # 检查是否已存在相同记录
            existing = self.search([
                ('dingtalk_record_id', '=', record_data.get('id')),
            ], limit=1)
            
            if existing:
                return existing
            
            # 准备创建数据
            vals = {
                'employee_id': employee_id,
                'check_in': check_time,
                'dingtalk_record_id': record_data.get('id'),
                'dingtalk_user_id': record_data.get('userId'),
                'dingtalk_check_type': record_data.get('checkType', 'OnDuty'),
                'dingtalk_location_result': record_data.get('locationResult', 'Normal'),
                'dingtalk_time_result': record_data.get('timeResult', 'Normal'),
                'dingtalk_location_title': record_data.get('locationTitle'),
                'dingtalk_location_detail': record_data.get('locationDetail'),
                'dingtalk_user_check_time': check_time,
                'synced_from_dingtalk': True,
                'dingtalk_sync_time': fields.Datetime.now(),
            }
            
            # 处理基准时间
            base_check_time = record_data.get('baseCheckTime')
            if base_check_time:
                vals['dingtalk_base_check_time'] = datetime.strptime(
                    base_check_time, '%Y-%m-%d %H:%M:%S'
                )
            
            # 根据打卡类型处理签入签出
            if record_data.get('checkType') == 'OffDuty':
                # 查找当天的签入记录
                same_day_checkin = self.search([
                    ('employee_id', '=', employee_id),
                    ('check_in', '>=', check_time.replace(hour=0, minute=0, second=0)),
                    ('check_in', '<', check_time.replace(hour=23, minute=59, second=59)),
                    ('check_out', '=', False),
                ], limit=1)
                
                if same_day_checkin:
                    # 更新签出时间
                    same_day_checkin.write({
                        'check_out': check_time,
                    })
                    return same_day_checkin
                else:
                    # 创建只有签出时间的记录（异常情况）
                    vals['check_out'] = check_time
                    vals.pop('check_in')
            
            return self.create(vals)
            
        except Exception as e:
            _logger.error(f'创建钉钉考勤记录失败: {str(e)}')
            raise

    def action_view_dingtalk_details(self):
        """查看钉钉详情"""
        self.ensure_one()
        if not self.synced_from_dingtalk:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('提示'),
                    'message': _('此记录不是来自钉钉同步'),
                    'type': 'info',
                    'sticky': False,
                }
            }
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('钉钉考勤详情'),
            'res_model': 'hr.attendance',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    @api.model
    def sync_attendance_from_dingtalk(self, config, date_from=None, date_to=None):
        """从钉钉同步考勤数据"""
        try:
            # 获取员工绑定
            bindings = self.env['dingtalk.employee.binding'].search([
                ('active', '=', True),
                ('binding_status', '=', 'active')
            ])
            
            if not bindings:
                return {'success': False, 'message': '未找到有效的员工绑定'}
            
            # 设置默认日期范围
            if not date_from or not date_to:
                from datetime import datetime, timedelta
                date_to = datetime.now().date()
                date_from = date_to - timedelta(days=config.attendance_sync_days)
            
            api_client = self.env['dingtalk.api.client'].with_config(config)
            
            total_synced = 0
            total_errors = 0
            
            # 按批次处理员工
            batch_size = 20
            for i in range(0, len(bindings), batch_size):
                batch_bindings = bindings[i:i + batch_size]
                user_ids = [b.dingtalk_user_id for b in batch_bindings]
                
                try:
                    # 获取考勤数据
                    from datetime import datetime
                    attendance_result = api_client.get_attendance_list(
                        user_ids,
                        datetime.combine(date_from, datetime.min.time()),
                        datetime.combine(date_to, datetime.max.time())
                    )
                    
                    if attendance_result.get('success'):
                        records = attendance_result.get('data', {}).get('recordresult', [])
                        
                        for record in records:
                            try:
                                # 查找对应员工
                                user_id = record.get('userId')
                                binding = next((b for b in batch_bindings if b.dingtalk_user_id == user_id), None)
                                
                                if binding and binding.employee_id:
                                    self.create_from_dingtalk(record, binding.employee_id.id)
                                    total_synced += 1
                                    
                            except Exception as e:
                                _logger.error(f'同步单条考勤记录失败: {str(e)}')
                                total_errors += 1
                    else:
                        _logger.warning(f'获取考勤数据失败: {attendance_result.get("message")}')
                        total_errors += len(user_ids)
                        
                except Exception as e:
                    _logger.error(f'批次考勤同步失败: {str(e)}')
                    total_errors += len(user_ids)
            
            return {
                'success': True,
                'synced_count': total_synced,
                'error_count': total_errors,
                'message': f'同步完成，成功 {total_synced} 条，失败 {total_errors} 条'
            }
            
        except Exception as e:
            _logger.error(f'考勤同步失败: {str(e)}')
            return {
                'success': False,
                'message': str(e)
            }
