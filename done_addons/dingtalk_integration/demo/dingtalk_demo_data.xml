<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- 演示钉钉配置 -->
        <record id="demo_dingtalk_config" model="dingtalk.config">
            <field name="name">演示钉钉配置</field>
            <field name="active" eval="False"/>
            <field name="app_key">demo_app_key</field>
            <field name="app_secret">demo_app_secret</field>
            <field name="api_base_url">https://oapi.dingtalk.com</field>
            <field name="auto_sync_employees" eval="False"/>
            <field name="auto_sync_attendance" eval="False"/>
            <field name="sync_interval_hours">24</field>
            <field name="employee_create_missing" eval="True"/>
            <field name="employee_update_existing" eval="True"/>
            <field name="attendance_sync_days">7</field>
            <field name="attendance_create_missing" eval="True"/>
        </record>

        <!-- 演示员工绑定 -->
        <record id="demo_employee_binding_1" model="dingtalk.employee.binding">
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="dingtalk_user_id">demo_user_001</field>
            <field name="dingtalk_name">演示管理员</field>
            <field name="dingtalk_mobile">13800138000</field>
            <field name="dingtalk_email"><EMAIL></field>
            <field name="dingtalk_job_number">001</field>
            <field name="dingtalk_position">系统管理员</field>
            <field name="binding_status">active</field>
            <field name="sync_status">success</field>
            <field name="created_by_sync" eval="False"/>
        </record>

        <!-- 演示同步日志 -->
        <record id="demo_sync_log_1" model="dingtalk.sync.log">
            <field name="sync_type">employee</field>
            <field name="sync_mode">manual</field>
            <field name="status">success</field>
            <field name="start_time" eval="(DateTime.now() - timedelta(hours=2))"/>
            <field name="end_time" eval="(DateTime.now() - timedelta(hours=2, minutes=-5))"/>
            <field name="message">演示员工同步完成</field>
            <field name="total_records">10</field>
            <field name="success_records">10</field>
            <field name="error_records">0</field>
            <field name="config_id" ref="demo_dingtalk_config"/>
        </record>

        <record id="demo_sync_log_2" model="dingtalk.sync.log">
            <field name="sync_type">attendance</field>
            <field name="sync_mode">cron</field>
            <field name="status">success</field>
            <field name="start_time" eval="(DateTime.now() - timedelta(hours=1))"/>
            <field name="end_time" eval="(DateTime.now() - timedelta(hours=1, minutes=-3))"/>
            <field name="message">演示考勤同步完成</field>
            <field name="total_records">25</field>
            <field name="success_records">23</field>
            <field name="error_records">2</field>
            <field name="config_id" ref="demo_dingtalk_config"/>
        </record>

    </data>
</odoo>
