<?xml version="1.0" encoding="utf-8" ?>
<!-- -->
<!-- (c) Copyright Ascensio System SIA 2024 -->
<!-- -->
<odoo>
  <record id="res_config_settings_view_form" model="ir.ui.view">
    <field name="name">res.config.settings.view.form.inherit.onlyoffice_odoo</field>
    <field name="model">res.config.settings</field>
    <field name="priority" eval="10" />
    <field name="inherit_id" ref="base.res_config_settings_view_form" />
    <field name="arch" type="xml">
      <xpath expr="//form" position="inside">
        <link type="text/css" rel="stylesheet" href="onlyoffice_odoo/static/src/css/res_config_settings_views.css" />
        <app data-string="ONLYOFFICE" string="ONLYOFFICE" name="onlyoffice_odoo" groups="base.group_system">
          <block title="General Settings" name="onlyoffice_odoo">
            <setting string="Document Server Url">
              <field name="doc_server_public_url" />
            </setting>
            <setting string="Server address for internal requests from the Document Editing Service">
              <field name="doc_server_odoo_url" />
            </setting>
            <setting string="Document Editing Service address for internal requests from the server">
              <field name="doc_server_inner_url" />
            </setting>
          </block>
          <block title="Security" name="onlyoffice_odoo" id="o_onlyoffice_settings_security">
            <setting string="Document Server JWT Secret">
              <field name="doc_server_jwt_secret" password="True" />
            </setting>
            <setting string="Document Server JWT Header">
              <field name="doc_server_jwt_header" />
            </setting>
          </block>
          <block title="Other" name="onlyoffice_odoo">
            <div class="o_setting_box col-12 col-lg-6 o_searchable_setting">
              <div class="o_setting_left_pane">
                <field name="doc_server_demo" />
              </div>
              <div class="o_setting_right_pane">
                <label class="o_form_label me-2" for="doc_server_demo">
                  <span searchabletext="Connect to demo ONLYOFFICE Docs server">
                    Connect to demo ONLYOFFICE Docs server
                  </span>
                </label>
                <div class="o_widget o_widget_documentation_link d-inline">
                  <a
                    title="ONLYOFFICE Templates requires a properly licensed Document Server connected with Odoo. Contact <NAME_EMAIL> to acquire the proper license."
                    class="o_doc_link"
                  >
                    <i class="fa fa-question-circle" />
                    <span />
                  </a>
                </div>
                <div class="mt16">
                  <div class="text-muted">
                    <span
                      searchabletext="This is a public test server, please do not use it for private sensitive data. The server will be available during a 30-day period."
                    >
                      This is a public test server, please do not use it for private sensitive data. The server will be available during a 30-day period.
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <setting string="Open file in the same tab">
              <field name="same_tab" />
            </setting>
            <setting>
              <div class="onlyoffice_link_container">
                <a
                  class="onlyoffice_link_button"
                  href="https://www.onlyoffice.com/docs-registration.aspx"
                  target="_blank"
                >
                  GET NOW
                </a>
              </div>
            </setting>
          </block>
        </app>
      </xpath>
    </field>
  </record>

  <record id="action_onlyoffice_config_settings" model="ir.actions.act_window">
    <field name="name">Settings</field>
    <field name="type">ir.actions.act_window</field>
    <field name="res_model">res.config.settings</field>
    <field name="view_id" ref="res_config_settings_view_form" />
    <field name="view_mode">form</field>
    <field name="target">inline</field>
    <field name="context">{'module' : 'onlyoffice_odoo'}</field>
  </record>
</odoo>
