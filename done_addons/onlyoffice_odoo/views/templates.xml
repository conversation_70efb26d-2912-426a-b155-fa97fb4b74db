<?xml version="1.0" encoding="utf-8" ?>
<!-- -->
<!-- (c) Copyright Ascensio System SIA 2024 -->
<!-- -->
<odoo>
  <template id="onlyoffice_layout" name="ONLYOFFICE Editor">
    <html>
      <head>
        <meta charset="UTF-8" />
        <title> ONLYOFFICE - <t t-out="docTitle" /> </title>
        <link rel="icon" t-att-href="docIcon" type="image/x-icon" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <t t-call-assets="web.assets_frontend" t-js="false" />
        <style>
          html, body {
            height: 100%;
          }
        </style>
        <script t-nocache="Session information should always be up to date." type="text/javascript">
          var odoo = {
            csrf_token: "<t t-nocache="The csrf token must always be up to date." t-esc="request.csrf_token(None)" />",
            debug: "<t t-esc="debug" />",
          };
          odoo.__session_info__ = <t t-out="json.dumps(request.env['ir.http'].get_frontend_session_info())" />;
          if (!/(^|;\s)tz=/.test(document.cookie)) {
            const userTZ = Intl.DateTimeFormat().resolvedOptions().timeZone;
            document.cookie = `tz=${userTZ}; path=/`;
          }
        </script>
        <t t-call-assets="web.assets_frontend_minimal" t-css="false" defer_load="True" />
        <t t-call="web.conditional_assets_tests" />
        <t t-call-assets="web.assets_frontend_lazy" t-css="false" lazy_load="True" />
      </head>
      <body>
        <t t-out="0" />
      </body>
    </html>
  </template>

  <template id="onlyoffice_editor" name="ONLYOFFICE Editor">
    <t t-call="onlyoffice_odoo.onlyoffice_layout">
      <div id="doceditor" />
      <div id="error" class="w-25 m-auto my-5 py-5 d-flex flex-column d-none">
        <img src="/onlyoffice_odoo/static/svg/logo.svg" alt="ONLYOFFICE logo" />
        <div class="my-3 alert alert-danger">
          <p> ONLYOFFICE cannot be reached. Please contact admin. </p>
        </div>
      </div>
      <script type="text/javascript" t-att-src="docApiJS" />
      <script type="text/javascript">
        var config = JSON.parse('<t t-out="editorConfig" />');
        if (window?.opener?.location?.href) {
          if (window.opener.location.origin === window.origin) {
            config.editorConfig.customization.goback = {
              blank: false,
              url: window.opener.location.href
            }
          }
        }
        if (window.DocsAPI) {
          var docEditor = new DocsAPI.DocEditor("doceditor", config);
        } else {
          document.getElementById("error").classList.remove("d-none");
        }
      </script>
    </t>
  </template>
</odoo>
