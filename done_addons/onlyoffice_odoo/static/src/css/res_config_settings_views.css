.onlyoffice_link_container {
  position: relative;
  width: 549px;
  height: 116px;
  border-left: 0;
  background-image: url("/onlyoffice_odoo/static/src/css/onlyoffice_link_container.png");
  background-size: contain;
}

.onlyoffice_link_button {
  display: flex;
  position: absolute;
  top: 40.5px;
  right: 14px;
  justify-content: center;
  align-items: center;
  min-width: 93px;
  max-width: 120px;
  max-height: 40px;
  padding: 5px 0;
  border-radius: 3px;
  color: white;
  font-weight: 500;
  font-family: "SF UI Display", sans-serif;
  text-align: center;
  background-color: #71639e;
}

@media (max-width: 767px) {
  .onlyoffice_link_container {
    width: 345px;
    height: 169px;
    background-image: url("/onlyoffice_odoo/static/src/css/onlyoffice_link_container_mobile.png");
  }

  .onlyoffice_link_button {
    top: unset;
    right: unset;
    bottom: 16px;
    left: 20px;
    width: 77px;
    height: 34px;
  }
}
