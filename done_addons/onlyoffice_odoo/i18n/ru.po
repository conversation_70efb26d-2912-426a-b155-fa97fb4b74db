# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onlyoffice_odoo
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20221116\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-03 08:14+0000\n"
"PO-Revision-Date: 2025-05-13 14:40+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Editing Service address for internal "
"requests from the server</span>"
msgstr ""
"<span class=\"o_form_label\">Адрес сервиса редактирования документов для "
"внутренних запросов с сервера</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Header</span>"
msgstr "<span class=\"o_form_label\">Заголовок JWT сервера документов</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Secret</span>"
msgstr ""
"<span class=\"o_form_label\">Секретный JWT ключ сервера документов</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server Url</span>"
msgstr "<span class=\"o_form_label\">URL сервера документов</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Server address for internal requests from the "
"Document Editing Service</span>"
msgstr ""
"<span class=\"o_form_label\">Адрес сервера для внутренних запросов от "
"сервиса редактирования документов</span>"

#. module: onlyoffice_odoo
#: model:ir.model,name:onlyoffice_odoo.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки конфигурации"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_demo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Connect to demo ONLYOFFICE Docs server"
msgstr "Подключиться к демо-серверу ONLYOFFICE Docs"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_uid
msgid "Created by"
msgstr "Создано"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_inner_url
msgid "Document Server Inner URL"
msgstr "Внутренний URL сервера документов"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_header
msgid "Document Server JWT Header"
msgstr "Заголовок JWT сервера документов"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_secret
msgid "Document Server JWT Secret"
msgstr "Секретный JWT ключ сервера документов"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_public_url
msgid "Document Server Public URL"
msgstr "Публичный URL сервера документов"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "GET NOW"
msgstr "ПОЛУЧИТЬ СЕЙЧАС"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "General Settings"
msgstr "Основные настройки"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__id
msgid "ID"
msgstr "ID"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__internal_jwt_secret
msgid "Internal JWT Secret"
msgstr "Внутренний секретный JWT ключ"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo____last_update
msgid "Last Modified on"
msgstr "Дата последнего изменения"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_date
msgid "Last Updated on"
msgstr "Дата последнего обновления"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "ONLYOFFICE"
msgstr "ONLYOFFICE"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid "ONLYOFFICE Docs server"
msgstr "Сервер ONLYOFFICE Docs"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"ONLYOFFICE Templates requires a properly licensed Document Server connected "
"with Odoo. Contact <NAME_EMAIL> to acquire the proper "
"license."
msgstr ""
"Для шаблонов ONLYOFFICE требуется надлежащим образом лицензированный сервер "
"документов, подключенный к Odoo. Чтобы получить соответствующую лицензию, "
"свяжитесь с отделом продаж по адресу <EMAIL>."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE cannot be reached. Please contact admin."
msgstr "ONLYOFFICE недоступен. Пожалуйста, свяжитесь с администратором."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE logo"
msgstr "Логотип ONLYOFFICE"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_odoo_url
msgid "Odoo URL"
msgstr "URL Odoo"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__same_tab
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Open file in the same tab"
msgstr "Открыть файл в той же вкладке"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/components/attachment_card_onlyoffice/attachment_card_onlyoffice.xml:0
#, python-format
msgid "Open in ONLYOFFICE"
msgstr "Открыть в ONLYOFFICE"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Other"
msgstr "Другое"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Security"
msgstr "Безопасность"

#. module: onlyoffice_odoo
#: model:ir.actions.act_window,name:onlyoffice_odoo.action_onlyoffice_config_settings
msgid "Settings"
msgstr "Настройки"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid ""
"The 30-day test period is over, you can no longer connect to demo ONLYOFFICE "
"Docs server"
msgstr ""
"30-дневный тестовый период закончился, вы больше не можете подключиться к "
"демо-серверу ONLYOFFICE Docs."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"This is a public test server, please do not use it for private sensitive "
"data. The server will be available during a 30-day period."
msgstr ""
"Это общедоступный тестовый сервер, не используйте его для конфиденциальных "
"данных. Сервер будет доступен в течение 30-дневного периода."
