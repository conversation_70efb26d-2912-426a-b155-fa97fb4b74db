# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onlyoffice_odoo
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20221116\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-03 08:14+0000\n"
"PO-Revision-Date: 2025-05-13 14:26+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Editing Service address for internal "
"requests from the server</span>"
msgstr ""
"<span class=\"o_form_label\">Indirizzo del servizio di modifica dei "
"documenti per richieste interne del server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Header</span>"
msgstr ""
"<span class=\"o_form_label\">Intestazione JWT del Document Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Secret</span>"
msgstr "<span class=\"o_form_label\">Segreto JWT del Document Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server Url</span>"
msgstr "<span class=\"o_form_label\">URL del Document Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Server address for internal requests from the "
"Document Editing Service</span>"
msgstr ""
"<span class=\"o_form_label\">Indirizzo del server per richieste interne dal "
"Servizio di modifica dei documenti</span>"

#. module: onlyoffice_odoo
#: model:ir.model,name:onlyoffice_odoo.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_demo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Connect to demo ONLYOFFICE Docs server"
msgstr "Connessione al server demo ONLYOFFICE Docs"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_date
msgid "Created on"
msgstr "Creato il"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__display_name
msgid "Display Name"
msgstr "Visualizza nome"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_inner_url
msgid "Document Server Inner URL"
msgstr "URL interno del server dei documenti"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_header
msgid "Document Server JWT Header"
msgstr "Intestazione JWT del Document Server"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_secret
msgid "Document Server JWT Secret"
msgstr "Segreto JWT del Document Server"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_public_url
msgid "Document Server Public URL"
msgstr "URL pubblico del server dei documenti"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "GET NOW"
msgstr "OTTIENI ORA"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "General Settings"
msgstr "Impostazioni generali"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__id
msgid "ID"
msgstr "ID"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__internal_jwt_secret
msgid "Internal JWT Secret"
msgstr "Segreto JWT interno"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo____last_update
msgid "Last Modified on"
msgstr "Ultima modifica effettuata il"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento effettuato da"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento effettuato il"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "ONLYOFFICE"
msgstr "ONLYOFFICE"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid "ONLYOFFICE Docs server"
msgstr "Server ONLYOFFICE Docs"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"ONLYOFFICE Templates requires a properly licensed Document Server connected "
"with Odoo. Contact <NAME_EMAIL> to acquire the proper "
"license."
msgstr ""
"ONLYOFFICE Templates richiede un Document Server con licenza valida "
"collegato a Odoo. Contatta il reparto vendite all’indirizzo sales@onlyoffice."
"com per ottenere una licenza adeguata."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE cannot be reached. Please contact admin."
msgstr ""
"ONLYOFFICE non può essere raggiunto. Si prega di contattare l'amministratore."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE logo"
msgstr "Logo di ONLYOFFICE"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_odoo_url
msgid "Odoo URL"
msgstr "URL di Odoo"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__same_tab
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Open file in the same tab"
msgstr "Apri il file nella stessa scheda"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/components/attachment_card_onlyoffice/attachment_card_onlyoffice.xml:0
#, python-format
msgid "Open in ONLYOFFICE"
msgstr "Aprire in ONLYOFFICE"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Other"
msgstr "Altro"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Security"
msgstr "Sicurezza"

#. module: onlyoffice_odoo
#: model:ir.actions.act_window,name:onlyoffice_odoo.action_onlyoffice_config_settings
msgid "Settings"
msgstr "Impostazioni"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid ""
"The 30-day test period is over, you can no longer connect to demo ONLYOFFICE "
"Docs server"
msgstr ""
"Il periodo di prova di 30 giorni è terminato, non puoi più connetterti alla "
"demo ONLYOFFICE Docs Server"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"This is a public test server, please do not use it for private sensitive "
"data. The server will be available during a 30-day period."
msgstr ""
"Questo è un server di test pubblico, si prega di non usarlo per dati privati "
"e sensibili. Il server sarà disponibile per un periodo di 30 giorni."
