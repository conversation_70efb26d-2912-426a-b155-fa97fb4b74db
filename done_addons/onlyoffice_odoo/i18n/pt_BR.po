# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onlyoffice_odoo
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20221116\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-03 08:14+0000\n"
"PO-Revision-Date: 2025-05-13 14:33+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Editing Service address for internal "
"requests from the server</span>"
msgstr ""
"<span class=\"o_form_label\">Endereço do Serviço de Edição de Documentos "
"para interno solicitações do servidor</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Header</span>"
msgstr ""
"<span class=\"o_form_label\">Cabeçalho JWT do servidor de documentos</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Secret</span>"
msgstr ""
"<span class=\"o_form_label\">Segredo JWT do servidor de documentos</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server Url</span>"
msgstr "<span class=\"o_form_label\">URL do servidor de documentos</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Server address for internal requests from the "
"Document Editing Service</span>"
msgstr ""
"<span class=\"o_form_label\">Endereço do servidor para solicitações internas "
"do Serviço de edição de documentos</span>"

#. module: onlyoffice_odoo
#: model:ir.model,name:onlyoffice_odoo.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações de configuração"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_demo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Connect to demo ONLYOFFICE Docs server"
msgstr "Conecte-se ao servidor de demonstração do ONLYOFFICE Docs"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_date
msgid "Created on"
msgstr "Criado em"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__display_name
msgid "Display Name"
msgstr "Nome de exibição"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_inner_url
msgid "Document Server Inner URL"
msgstr "URL interno do servidor de documentos"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_header
msgid "Document Server JWT Header"
msgstr "Cabeçalho JWT do servidor de documentos"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_secret
msgid "Document Server JWT Secret"
msgstr "Segredo JWT do servidor de documentos"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_public_url
msgid "Document Server Public URL"
msgstr "URL pública do servidor de documentos"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "GET NOW"
msgstr "OBTENHA AGORA"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "General Settings"
msgstr "Configurações gerais"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__id
msgid "ID"
msgstr "ID"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__internal_jwt_secret
msgid "Internal JWT Secret"
msgstr "Segredo JWT Interno"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "ONLYOFFICE"
msgstr "ONLYOFFICE"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid "ONLYOFFICE Docs server"
msgstr "Servidor ONLYOFFICE Docs"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"ONLYOFFICE Templates requires a properly licensed Document Server connected "
"with Odoo. Contact <NAME_EMAIL> to acquire the proper "
"license."
msgstr ""
"Os Modelos do ONLYOFFICE requerem um Servidor de Documentos devidamente "
"licenciado e conectado ao Odoo. Entre em contato com o departamento de "
"vendas pelo e-mail <EMAIL> para adquirir a licença adequada."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE cannot be reached. Please contact admin."
msgstr ""
"ONLYOFFICE não pode ser alcançado. Entre em contato com o administrador."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE logo"
msgstr "Logotipo ONLYOFFICE"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_odoo_url
msgid "Odoo URL"
msgstr "URL Odoo"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__same_tab
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Open file in the same tab"
msgstr "Abra o arquivo na mesma aba."

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/components/attachment_card_onlyoffice/attachment_card_onlyoffice.xml:0
#, python-format
msgid "Open in ONLYOFFICE"
msgstr "Abrir no ONLYOFFICE"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Other"
msgstr "Outros"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Security"
msgstr "Segurança"

#. module: onlyoffice_odoo
#: model:ir.actions.act_window,name:onlyoffice_odoo.action_onlyoffice_config_settings
msgid "Settings"
msgstr "Configurações"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid ""
"The 30-day test period is over, you can no longer connect to demo ONLYOFFICE "
"Docs server"
msgstr ""
"O período de teste de 30 dias acabou, você não pode mais se conectar ao "
"ONLYOFFICE de demonstração Servidor Docs"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"This is a public test server, please do not use it for private sensitive "
"data. The server will be available during a 30-day period."
msgstr ""
"Este é um servidor de teste público, por favor, não o use para dados "
"confidenciais. O servidor estará disponível durante um período de 30 dias."
