# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onlyoffice_odoo
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e-20240216\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-15 17:24+0000\n"
"PO-Revision-Date: 2025-04-15 17:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Editing Service address for internal "
"requests from the server</span>"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Header</span>"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Secret</span>"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server Url</span>"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Server address for internal requests from the "
"Document Editing Service</span>"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model,name:onlyoffice_odoo.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_demo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Connect to demo ONLYOFFICE Docs server"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_uid
msgid "Created by"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_date
msgid "Created on"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__display_name
msgid "Display Name"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_inner_url
msgid "Document Server Inner URL"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_header
msgid "Document Server JWT Header"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_secret
msgid "Document Server JWT Secret"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_public_url
msgid "Document Server Public URL"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "GET NOW"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "General Settings"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__id
msgid "ID"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__internal_jwt_secret
msgid "Internal JWT Secret"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo____last_update
msgid "Last Modified on"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_uid
msgid "Last Updated by"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_date
msgid "Last Updated on"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model,name:onlyoffice_odoo.model_onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "ONLYOFFICE"
msgstr ""

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid "ONLYOFFICE Docs server"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"ONLYOFFICE Templates requires a properly licensed Document Server connected "
"with Odoo. Contact <NAME_EMAIL> to acquire the proper "
"license."
msgstr ""

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/actions/documents_action.xml:0
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
#, python-format
msgid "ONLYOFFICE cannot be reached. Please contact admin."
msgstr ""

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/actions/documents_action.xml:0
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
#, python-format
msgid "ONLYOFFICE logo"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_odoo_url
msgid "Odoo URL"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__same_tab
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Open file in the same tab"
msgstr ""

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/components/attachment_card_onlyoffice/attachment_card_onlyoffice.xml:0
#: code:addons/onlyoffice_odoo/static/src/components/attachment_card_onlyoffice/attachment_card_onlyoffice.xml:0
#, python-format
msgid "Open in ONLYOFFICE"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Other"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Security"
msgstr ""

#. module: onlyoffice_odoo
#: model:ir.actions.act_window,name:onlyoffice_odoo.action_onlyoffice_config_settings
msgid "Settings"
msgstr ""

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid ""
"The 30-day test period is over, you can no longer connect to demo ONLYOFFICE"
" Docs server"
msgstr ""

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"This is a public test server, please do not use it for private sensitive "
"data. The server will be available during a 30-day period."
msgstr ""
