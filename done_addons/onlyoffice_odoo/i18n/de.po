# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onlyoffice_odoo
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20221116\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-03 08:14+0000\n"
"PO-Revision-Date: 2025-05-13 14:19+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Editing Service address for internal "
"requests from the server</span>"
msgstr ""
"<span class=\"o_form_label\">Adresse des Document Editing Service für "
"interne Anfragen vom Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Header</span>"
msgstr "<span class=\"o_form_label\">JWT-Header von Document Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server JWT Secret</span>"
msgstr ""
"<span class=\"o_form_label\">JWT-Geheimschlüssel von Document Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Document Server Url</span>"
msgstr "<span class=\"o_form_label\">URL von Document Server</span>"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Server address for internal requests from the "
"Document Editing Service</span>"
msgstr ""
"<span class=\"o_form_label\">Serveradresse für interne Anfragen des Document "
"Editing Service</span>"

#. module: onlyoffice_odoo
#: model:ir.model,name:onlyoffice_odoo.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_demo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Connect to demo ONLYOFFICE Docs server"
msgstr "Zum Demoserver von ONLYOFFICE Docs verbinden"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_inner_url
msgid "Document Server Inner URL"
msgstr "Innere URL von Document Server"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_header
msgid "Document Server JWT Header"
msgstr "JWT-Header von Document Server"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_jwt_secret
msgid "Document Server JWT Secret"
msgstr "JWT-Geheimschlüssel von Document Server"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_public_url
msgid "Document Server Public URL"
msgstr "Öffentliche URL von Document Server"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "GET NOW"
msgstr "JETZT ERHALTEN"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "General Settings"
msgstr "Allgemeine Einstellungen"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__id
msgid "ID"
msgstr "ID"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__internal_jwt_secret
msgid "Internal JWT Secret"
msgstr "Interner JWT-Geheimschlüssel"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_onlyoffice_odoo__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "ONLYOFFICE"
msgstr "ONLYOFFICE"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid "ONLYOFFICE Docs server"
msgstr "ONLYOFFICE Docs-Server"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"ONLYOFFICE Templates requires a properly licensed Document Server connected "
"with Odoo. Contact <NAME_EMAIL> to acquire the proper "
"license."
msgstr ""
"Für ONLYOFFICE Templates benötigen Sie einen ordnungsgemäß lizenzierten "
"Document Server, der mit Odoo verbunden ist. Kontaktieren Sie das Sales-Team "
"unter <EMAIL>, um die entsprechende Lizenz zu erwerben."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE cannot be reached. Please contact admin."
msgstr ""
"ONLYOFFICE kann nicht erreicht werden. Bitte kontaktieren Sie den "
"Administrator."

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.onlyoffice_editor
msgid "ONLYOFFICE logo"
msgstr "ONLYOFFICE-Logo"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__doc_server_odoo_url
msgid "Odoo URL"
msgstr "Odoo-URL"

#. module: onlyoffice_odoo
#: model:ir.model.fields,field_description:onlyoffice_odoo.field_res_config_settings__same_tab
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Open file in the same tab"
msgstr "Datei im selben Tab öffnen"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/components/attachment_card_onlyoffice/attachment_card_onlyoffice.xml:0
#, python-format
msgid "Open in ONLYOFFICE"
msgstr "In ONLYOFFICE öffnen"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Other"
msgstr "Andere"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid "Security"
msgstr "Sicherheit"

#. module: onlyoffice_odoo
#: model:ir.actions.act_window,name:onlyoffice_odoo.action_onlyoffice_config_settings
msgid "Settings"
msgstr "Einstellungen"

#. module: onlyoffice_odoo
#. odoo-javascript
#: code:addons/onlyoffice_odoo/static/src/models/attachment_onlyoffice.js:0
#, python-format
msgid ""
"The 30-day test period is over, you can no longer connect to demo ONLYOFFICE "
"Docs server"
msgstr ""
"Der 30-tägige Testzeitraum ist vorbei. Sie können sich nicht mehr mit dem "
"Demo-Server von ONLYOFFICE Docs verbinden"

#. module: onlyoffice_odoo
#: model_terms:ir.ui.view,arch_db:onlyoffice_odoo.res_config_settings_view_form
msgid ""
"This is a public test server, please do not use it for private sensitive "
"data. The server will be available during a 30-day period."
msgstr ""
"Dies ist ein öffentlicher Testserver, bitte verwenden Sie ihn nicht für "
"private sensible Daten. Der Server wird für einen Zeitraum von 30 Tagen "
"verfügbar sein."
