# -*- coding: utf-8 -*-

import logging
from odoo import http, _
from odoo.http import request
from odoo.exceptions import AccessError, UserError, ValidationError
import werkzeug

_logger = logging.getLogger(__name__)


class CrmLeadClaimController(http.Controller):
    
    @http.route('/crm/lead/auto_claim/<int:lead_id>', type='http', auth='user', methods=['GET'])
    def auto_claim_lead(self, lead_id, **kwargs):
        """
        自动认领线索并跳转到线索详情页面
        
        Args:
            lead_id: 线索ID
            **kwargs: 其他参数
            
        Returns:
            重定向到线索详情页面或错误页面
        """
        try:
            # 获取线索记录
            lead = request.env['crm.lead'].browse(lead_id)
            
            if not lead.exists():
                return self._render_error_page('线索不存在', '您要认领的线索不存在或已被删除。')
            
            # 验证权限：检查线索是否分配给当前用户
            if lead.claim_teacher_user_id.id != request.env.user.id:
                return self._render_error_page(
                    '无权限认领', 
                    f'线索 {lead.name} 未分配给您，无法认领。'
                )
            
            # 验证线索状态
            if lead.lead_state != 'to_claim':
                if lead.lead_state == 'to_score':
                    # 已经认领过了，直接跳转到详情页面
                    return self._redirect_to_lead_detail(lead_id)
                else:
                    return self._render_error_page(
                        '线索状态错误', 
                        f'线索 {lead.name} 当前状态为 {dict(lead._fields["lead_state"].selection).get(lead.lead_state, lead.lead_state)}，无法认领。'
                    )
            
            # 执行认领逻辑
            try:
                # 调用现有的认领方法
                result = lead.action_claim()
                
                # 记录认领成功日志
                _logger.info(f"用户 {request.env.user.name} 通过微信链接成功认领线索 {lead.name}")
                
                # 跳转到线索详情页面
                return self._redirect_to_lead_detail(lead_id)
                
            except Exception as claim_error:
                _logger.error(f"认领线索 {lead.name} 失败: {claim_error}")
                return self._render_error_page(
                    '认领失败', 
                    f'认领线索时发生错误：{str(claim_error)}'
                )
                
        except Exception as e:
            _logger.error(f"自动认领线索异常: {e}")
            return self._render_error_page(
                '系统错误', 
                '处理认领请求时发生系统错误，请稍后重试或联系管理员。'
            )
    
    def _redirect_to_lead_detail(self, lead_id):
        """重定向到线索详情页面"""
        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        detail_url = f"{base_url}/web#id={lead_id}&action=done_crm_upgrade.crm_lead_claim_action_teacher&model=crm.lead&view_type=form"
        return werkzeug.utils.redirect(detail_url)
    
    def _render_error_page(self, title, message):
        """渲染错误页面"""
        return request.render('done_crm_upgrade.lead_claim_error_template', {
            'title': title,
            'message': message,
            'back_url': '/web#action=done_crm_upgrade.crm_lead_claim_action_teacher'
        })


class CrmLeadQuickClaimController(http.Controller):
    
    @http.route('/crm/lead/quick_claim/<int:lead_id>/<string:token>', type='http', auth='public', methods=['GET'])
    def quick_claim_lead(self, lead_id, token, **kwargs):
        """
        通过令牌快速认领线索（无需登录）
        
        Args:
            lead_id: 线索ID
            token: 认证令牌
            **kwargs: 其他参数
            
        Returns:
            重定向到登录页面或线索详情页面
        """
        try:
            # 验证令牌
            lead = request.env['crm.lead'].sudo().browse(lead_id)
            
            if not lead.exists():
                return self._render_public_error_page('线索不存在', '您要认领的线索不存在或已被删除。')
            
            # 生成预期的令牌进行验证
            expected_token = self._generate_claim_token(lead_id, lead.claim_teacher_user_id.id)
            
            if token != expected_token:
                return self._render_public_error_page('链接无效', '认领链接无效或已过期，请重新获取。')
            
            # 如果用户未登录，重定向到登录页面，登录后自动跳转回来
            if not request.env.user or request.env.user._is_public():
                login_url = f"/web/login?redirect=/crm/lead/auto_claim/{lead_id}"
                return werkzeug.utils.redirect(login_url)
            
            # 用户已登录，验证是否为正确的用户
            if lead.claim_teacher_user_id.id != request.env.user.id:
                return self._render_public_error_page(
                    '用户不匹配', 
                    '当前登录用户与线索分配的老师不匹配，请使用正确的账号登录。'
                )
            
            # 重定向到自动认领接口
            return werkzeug.utils.redirect(f"/crm/lead/auto_claim/{lead_id}")
            
        except Exception as e:
            _logger.error(f"快速认领线索异常: {e}")
            return self._render_public_error_page(
                '系统错误', 
                '处理认领请求时发生系统错误，请稍后重试或联系管理员。'
            )
    
    def _generate_claim_token(self, lead_id, user_id):
        """生成认领令牌"""
        import hashlib
        import time
        
        # 使用线索ID、用户ID和当前日期生成令牌（24小时有效）
        date_str = time.strftime('%Y%m%d')
        token_string = f"{lead_id}_{user_id}_{date_str}_claim_token"
        return hashlib.md5(token_string.encode()).hexdigest()[:16]
    
    def _render_public_error_page(self, title, message):
        """渲染公共错误页面"""
        return request.render('done_crm_upgrade.lead_claim_public_error_template', {
            'title': title,
            'message': message,
            'login_url': '/web/login'
        })
