# -*- coding: utf-8 -*-

from odoo import fields, models


class TeacherGroup(models.Model):
    _name = 'teacher.group'
    _description = '招生老师组别'
    _order = 'sequence, name'

    name = fields.Char('组别名称', required=True)
    code = fields.Char('组别代码', required=True)
    sequence = fields.Integer('排序', default=10)
    active = fields.Bo<PERSON>an('启用', default=True)
    description = fields.Text('描述')

    _sql_constraints = [
        ('code_unique', 'unique(code)', '组别代码必须唯一！'),
    ]

    def name_get(self):
        result = []
        for record in self:
            result.append((record.id, f"[{record.code}] {record.name}"))
        return result

    # crm.teacher.weight
    teacher_weight_ids = fields.One2many('crm.teacher.weight', 'teacher_group_id', string='教师权重配置')


# 配置教师
class TeacherGroupLine(models.Model):
    _name = 'teacher.group.line'
    _description = '业务类型明细'

    teacher_group_id = fields.Many2one('teacher.group', string='业务类型', required=True)
    employee_id = fields.Many2one('hr.employee', string='招生老师', required=True)
    weight = fields.Integer('权重', default=1)
    additional_score = fields.Integer('附加分', default=0)
    success_rate_score = fields.Integer('成交率分', default=0)
    speed_score = fields.Integer('成交速度分', default=0)
    kpi_score = fields.Integer('KPI分', default=0)
    gender_match_score = fields.Integer('性别匹配分', default=0)
    amount_match_score = fields.Integer('金额匹配分', default=0)