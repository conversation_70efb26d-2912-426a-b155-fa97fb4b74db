# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class DoneCrmConfig(models.Model):
    _name = 'done.crm.config'
    _description = 'Done CRM 配置参数'
    _order = 'sequence, name'
    _rec_name = 'name'

    # 基本信息
    name = fields.Char(string='参数名称', required=True, help='配置参数的名称')
    value = fields.Char(string='参数值', required=True, help='配置参数的值')
    description = fields.Text(string='描述', help='配置参数的详细描述')
    
    # 分类和排序
    category = fields.Selection([
        ('system', '系统配置'),
        ('distribution', '分配配置'),
        ('scoring', '评分配置'),
        ('notification', '通知配置'),
        ('integration', '集成配置'),
        ('other', '其他配置'),
    ], string='配置分类', default='system', required=True)
    
    sequence = fields.Integer(string='排序', default=10, help='用于排序显示')
    
    # 数据类型
    data_type = fields.Selection([
        ('string', '字符串'),
        ('integer', '整数'),
        ('float', '浮点数'),
        ('boolean', '布尔值'),
        ('selection', '选择项'),
        ('json', 'JSON'),
    ], string='数据类型', default='string', required=True)
    
    # 选择项配置（当data_type为selection时使用）
    selection_options = fields.Text(
        string='选择项配置', 
        help='当数据类型为选择项时，请输入选项配置，格式：key1:label1,key2:label2'
    )
    
    # 验证规则
    is_required = fields.Boolean(string='必填', default=False)
    min_value = fields.Float(string='最小值', help='数值类型的最小值限制')
    max_value = fields.Float(string='最大值', help='数值类型的最大值限制')
    regex_pattern = fields.Char(string='正则表达式', help='字符串类型的验证正则表达式')
    
    # 状态和权限
    active = fields.Boolean(string='启用', default=True)
    is_system = fields.Boolean(string='系统参数', default=False, help='系统参数不允许删除')
    readonly = fields.Boolean(string='只读', default=False, help='只读参数不允许修改值')
    
    # 审计字段
    create_uid = fields.Many2one('res.users', string='创建人', readonly=True)
    create_date = fields.Datetime(string='创建时间', readonly=True)
    write_uid = fields.Many2one('res.users', string='修改人', readonly=True)
    write_date = fields.Datetime(string='修改时间', readonly=True)
    
    # 计算字段
    display_value = fields.Char(string='显示值', compute='_compute_display_value', store=False)
    
    @api.depends('value', 'data_type', 'selection_options')
    def _compute_display_value(self):
        """计算显示值"""
        for config in self:
            if config.data_type == 'boolean':
                config.display_value = '是' if config.value.lower() in ['true', '1', 'yes'] else '否'
            elif config.data_type == 'selection' and config.selection_options:
                # 解析选择项配置
                options = {}
                for option in config.selection_options.split(','):
                    if ':' in option:
                        key, label = option.strip().split(':', 1)
                        options[key.strip()] = label.strip()
                config.display_value = options.get(config.value, config.value)
            else:
                config.display_value = config.value
    
    @api.constrains('name')
    def _check_name_unique(self):
        """检查参数名称唯一性"""
        for config in self:
            if self.search_count([('name', '=', config.name), ('id', '!=', config.id)]) > 0:
                raise ValidationError(_('参数名称 "%s" 已存在，请使用不同的名称。') % config.name)
    
    @api.constrains('value', 'data_type', 'min_value', 'max_value')
    def _check_value_format(self):
        """检查参数值格式"""
        for config in self:
            if not config.value:
                continue
                
            try:
                if config.data_type == 'integer':
                    int_value = int(config.value)
                    if config.min_value and int_value < config.min_value:
                        raise ValidationError(_('参数值不能小于 %s') % config.min_value)
                    if config.max_value and int_value > config.max_value:
                        raise ValidationError(_('参数值不能大于 %s') % config.max_value)
                        
                elif config.data_type == 'float':
                    float_value = float(config.value)
                    if config.min_value and float_value < config.min_value:
                        raise ValidationError(_('参数值不能小于 %s') % config.min_value)
                    if config.max_value and float_value > config.max_value:
                        raise ValidationError(_('参数值不能大于 %s') % config.max_value)
                        
                elif config.data_type == 'boolean':
                    if config.value.lower() not in ['true', 'false', '1', '0', 'yes', 'no']:
                        raise ValidationError(_('布尔值参数只能是 true/false, 1/0, yes/no'))
                        
                elif config.data_type == 'json':
                    import json
                    json.loads(config.value)
                    
            except ValueError as e:
                raise ValidationError(_('参数值格式错误: %s') % str(e))
    
    @api.constrains('regex_pattern', 'value')
    def _check_regex_pattern(self):
        """检查正则表达式验证"""
        for config in self:
            if config.regex_pattern and config.value and config.data_type == 'string':
                import re
                try:
                    if not re.match(config.regex_pattern, config.value):
                        raise ValidationError(_('参数值不符合正则表达式规则: %s') % config.regex_pattern)
                except re.error as e:
                    raise ValidationError(_('正则表达式格式错误: %s') % str(e))
    
    def unlink(self):
        """重写删除方法，防止删除系统参数"""
        for config in self:
            if config.is_system:
                raise UserError(_('系统参数 "%s" 不允许删除') % config.name)
        return super().unlink()
    
    def write(self, vals):
        """重写写入方法，检查只读参数"""
        for config in self:
            if config.readonly and 'value' in vals and vals['value'] != config.value:
                raise UserError(_('只读参数 "%s" 不允许修改值') % config.name)
        return super().write(vals)
    
    @api.model
    def get_param(self, name, default=None):
        """获取配置参数值"""
        config = self.search([('name', '=', name), ('active', '=', True)], limit=1)
        if config:
            return config.value
        return default
    
    @api.model
    def set_param(self, name, value, description=None):
        """设置配置参数值"""
        config = self.search([('name', '=', name)], limit=1)
        if config:
            if config.readonly:
                raise UserError(_('只读参数 "%s" 不允许修改') % name)
            config.write({'value': str(value)})
        else:
            # 创建新参数
            self.create({
                'name': name,
                'value': str(value),
                'description': description or f'自动创建的参数: {name}',
                'category': 'other',
            })
        return True
    
    @api.model
    def get_param_int(self, name, default=0):
        """获取整数类型参数"""
        value = self.get_param(name, default)
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    @api.model
    def get_param_float(self, name, default=0.0):
        """获取浮点数类型参数"""
        value = self.get_param(name, default)
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @api.model
    def get_param_bool(self, name, default=False):
        """获取布尔类型参数"""
        value = self.get_param(name, default)
        if isinstance(value, str):
            return value.lower() in ['true', '1', 'yes']
        return bool(value)
    
    @api.model
    def get_param_json(self, name, default=None):
        """获取JSON类型参数"""
        value = self.get_param(name, default)
        if value:
            try:
                import json
                return json.loads(value)
            except (ValueError, TypeError):
                pass
        return default or {}
    
    def action_reset_to_default(self):
        """重置为默认值"""
        for config in self:
            if config.name == 'distribution_mode':
                config.write({'value': '1'})
            # 可以在这里添加其他参数的默认值重置逻辑
        return True
    
    def action_test_regex(self):
        """测试正则表达式"""
        for config in self:
            if config.regex_pattern and config.value:
                import re
                try:
                    if re.match(config.regex_pattern, config.value):
                        return {
                            'type': 'ir.actions.client',
                            'tag': 'display_notification',
                            'params': {
                                'title': _('验证成功'),
                                'message': _('参数值符合正则表达式规则'),
                                'type': 'success',
                            }
                        }
                    else:
                        return {
                            'type': 'ir.actions.client',
                            'tag': 'display_notification',
                            'params': {
                                'title': _('验证失败'),
                                'message': _('参数值不符合正则表达式规则'),
                                'type': 'warning',
                            }
                        }
                except re.error as e:
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('正则表达式错误'),
                            'message': str(e),
                            'type': 'danger',
                        }
                    }
        return True
