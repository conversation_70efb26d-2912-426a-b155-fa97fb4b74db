# 员工表增加 教师组别

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    teacher_group_id = fields.Many2one(
        'teacher.group',
        string='教师组别',
        tracking=True,
        help='选择教师所属的业务组别'
    )

    @api.constrains('teacher_group_id', 'user_id')
    def _check_user_id_required_for_teacher_group(self):
        """当设置教师组别时，必须配置相关用户"""
        for record in self:
            if record.teacher_group_id and not record.user_id:
                raise ValidationError(_('设置教师组别时，必须先配置相关用户账号'))

    @api.model_create_multi
    def create(self, vals_list):
        """创建员工时处理教师权重配置"""
        employees = super().create(vals_list)
        for employee in employees:
            if employee.teacher_group_id and employee.user_id:
                employee._create_teacher_weight_record()
        return employees

    def write(self, vals):
        """更新员工时处理教师权重配置"""
        result = super().write(vals)

        # 如果更新了teacher_group_id或user_id，需要处理权重配置
        if 'teacher_group_id' in vals or 'user_id' in vals:
            for record in self:
                if record.teacher_group_id and record.user_id:
                    record._create_teacher_weight_record()
                elif not record.teacher_group_id:
                    # 如果清空了教师组别，删除对应的权重配置
                    record._remove_teacher_weight_record()

        return result

    def _create_teacher_weight_record(self):
        """创建教师权重配置记录"""
        self.ensure_one()

        # 检查是否已存在权重配置
        existing_weight = self.env['crm.teacher.weight'].search([
            ('teacher_group_id', '=', self.teacher_group_id.id),
            ('user_id', '=', self.user_id.id)
        ], limit=1)

        if not existing_weight:
            # 创建新的权重配置
            self.env['crm.teacher.weight'].create({
                'teacher_group_id': self.teacher_group_id.id,
                'user_id': self.user_id.id,
                'weight': 1.0,  # 默认权重
                'additional_score': 0.0,
                'deal_rate_score': 0.0,
                'deal_speed_score': 0.0,
                'kpi_score': 0.0,
                'gender_match_score': 0.0,
                'amount_match_score': 0.0,
                'active': True,
                'notes': f'自动创建于员工 {self.name} 配置教师组别时'
            })

    def _remove_teacher_weight_record(self):
        """移除教师权重配置记录"""
        self.ensure_one()

        # 查找并删除对应的权重配置
        weight_records = self.env['crm.teacher.weight'].search([
            ('user_id', '=', self.user_id.id)
        ])

        if weight_records:
            weight_records.unlink()

    def _get_all_subordinates(self):
        """
        获取当前员工的所有下属（包括跨级下属）

        Returns:
            hr.employee: 所有下属员工的记录集
        """
        self.ensure_one()

        def _get_subordinates_recursive(employee):
            """递归获取所有下属"""
            subordinates = self.env['hr.employee']

            # 获取直接下属
            direct_subordinates = employee.child_ids
            subordinates |= direct_subordinates

            # 递归获取每个直接下属的下属
            for subordinate in direct_subordinates:
                subordinates |= _get_subordinates_recursive(subordinate)

            return subordinates

        return _get_subordinates_recursive(self)

    def _get_subordinate_user_ids(self):
        """
        获取当前员工所有下属的用户ID列表

        Returns:
            list: 下属用户ID列表
        """
        subordinates = self._get_all_subordinates()
        return subordinates.mapped('user_id').ids
