# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import logging


class CrmLeadAllocateWizard(models.TransientModel):
    _name = 'crm.lead.allocate.wizard'
    _description = '线索分配向导'

    # 基本信息
    lead_ids = fields.Many2many('crm.lead', string='待分配线索', readonly=True)
    lead_count = fields.Integer(string='线索数量', compute='_compute_lead_count')
    
    # 分配信息
    claim_teacher_user_id = fields.Many2one(
        'res.users',
        string='分配给老师',
        required=True,
        domain="[('active', '=', True), ('share', '=', False)]",  # 显示所有活跃的内部用户，排除管理员
        help='选择要分配线索的老师'
    )
    
    # 分配选项
    allocation_type = fields.Selection([
        ('single', '单个分配'),
        ('batch', '批量分配')
    ], string='分配类型', default='single', required=True)
    
    # 备注
    allocation_note = fields.Text(string='分配备注', help='可选的分配说明')
    
    @api.depends('lead_ids')
    def _compute_lead_count(self):
        """计算线索数量"""
        for wizard in self:
            wizard.lead_count = len(wizard.lead_ids)
    
    @api.model
    def default_get(self, fields_list):
        """设置默认值"""
        res = super().default_get(fields_list)
        
        # 从上下文获取线索ID
        active_ids = self.env.context.get('active_ids', [])
        active_model = self.env.context.get('active_model', '')
        
        if active_model == 'crm.lead' and active_ids:
            # 过滤只有待分配状态的线索
            leads = self.env['crm.lead'].browse(active_ids).filtered(
                lambda l: l.lead_state == 'to_allocate'
            )
            res['lead_ids'] = [(6, 0, leads.ids)]
            
            # 根据线索数量设置分配类型
            if len(leads) == 1:
                res['allocation_type'] = 'single'
            else:
                res['allocation_type'] = 'batch'
        
        return res
    
    def action_allocate(self):
        """执行分配操作"""
        # 分配完成后给对应老师通过微信公众号发送消息
        self.ensure_one()
        
        if not self.lead_ids:
            raise UserError(_('没有可分配的线索'))
        
        if not self.claim_teacher_user_id:
            raise UserError(_('请选择要分配的老师'))
        
        # 检查线索状态
        invalid_leads = self.lead_ids.filtered(lambda l: l.lead_state != 'to_allocate')
        if invalid_leads:
            raise UserError(_('只能分配状态为"待分配"的线索'))
        
        # 执行分配
        allocation_data = {
            'claim_teacher_user_id': self.claim_teacher_user_id.id,
            'user_id': self.claim_teacher_user_id.id,  # 同时设置负责人
            'lead_state': 'to_claim',
            'allocate_date': fields.Datetime.now(),
        }
        
        # 如果有备注，添加到线索备注中
        if self.allocation_note:
            for lead in self.lead_ids:
                current_note = lead.description or ''
                new_note = f"{current_note}\n\n【分配备注】{self.allocation_note}" if current_note else f"【分配备注】{self.allocation_note}"
                allocation_data['description'] = new_note
                lead.write(allocation_data.copy())
        else:
            self.lead_ids.write(allocation_data)

        # 发送系统通知
        self._send_system_notification()

        # 发送微信模板消息通知
        self._send_wechat_template_notification()

        # 返回成功消息
        message = _('成功分配 %d 个线索给 %s') % (len(self.lead_ids), self.claim_teacher_user_id.name)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('分配成功'),
                'message': message,
                'type': 'success',
            }
        }
    
    def action_cancel(self):
        """取消分配"""
        return {'type': 'ir.actions.act_window_close'}

    def _send_wechat_notification(self):
        """发送微信模板消息通知给分配的老师"""
        try:
            # 检查是否安装了微信公众号模块
            if 'wechat.template' not in self.env:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning("微信公众号模块未安装，无法发送模板消息")
                return

            # 查找报名审批通知模板
            template = self.env['wechat.template'].search([
                ('name', '=', '报名审批通知'),
                ('active', '=', True)
            ], limit=1)

            if not template:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning("没有找到'报名审批通知'模板，无法发送模板消息")
                return

            # 获取已激活的微信配置
            wechat_config = self.env['wechat.config'].search([
                ('active', '=', True)
            ], limit=1)

            if not wechat_config:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning("没有找到激活的微信公众号配置，无法发送通知消息")
                return

            # 查找老师对应的微信用户
            teacher_wechat_user = self._find_teacher_wechat_user()

            if not teacher_wechat_user:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info(f"老师 {self.claim_teacher_user_id.name} 没有绑定微信账号，无法发送通知")
                return

            # 创建并发送微信模板消息
            self._create_and_send_wechat_template_message(wechat_config, teacher_wechat_user, template)

        except Exception as e:
            # 记录错误但不影响分配流程
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"发送微信模板消息失败: {e}")

    def _find_teacher_wechat_user(self):
        """查找老师对应的微信用户"""
        # 方法1: 通过自动登录记录查找
        auto_login = self.env['wechat.auto.login'].search([
            ('user_id', '=', self.claim_teacher_user_id.id),
            ('active', '=', True),
            ('enabled', '=', True)
        ], limit=1)

        if auto_login:
            # 通过自动登录记录找到对应的微信用户
            wechat_user = self.env['wechat.user'].search([
                ('openid', '=', auto_login.openid),
                ('config_id', '=', auto_login.config_id.id),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法2: 通过手机号匹配查找
        if self.claim_teacher_user_id.phone:
            wechat_user = self.env['wechat.user'].search([
                ('phone', '=', self.claim_teacher_user_id.phone),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法3: 通过邮箱匹配查找（如果微信用户有邮箱信息）
        if self.claim_teacher_user_id.email:
            wechat_user = self.env['wechat.user'].search([
                ('email', '=', self.claim_teacher_user_id.email),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user
        # 方法4 通过微信用户匹配查找
        if self.claim_teacher_user_id.name:
            wechat_user = self.env['wechat.user'].search([
                ('user_id', '=', self.claim_teacher_user_id.id),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        return None

    def _create_and_send_wechat_template_message(self, wechat_config, teacher_wechat_user, template):
        """创建并发送微信模板消息"""
        try:
            # 构建直接认领线索的URL，点击后自动认领并跳转到线索详情页面
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')

            # 如果只有一个线索，发送单个线索的详细信息并跳转到具体线索
            if len(self.lead_ids) == 1:
                lead = self.lead_ids[0]

                # 方案1：简单URL（需要用户登录）
                lead_url = f"{base_url}/crm/lead/auto_claim/{lead.id}"

                # 方案2：安全URL（带令牌，可选使用）
                # lead_url = lead._get_secure_claim_url(self.claim_teacher_user_id)

                # 准备模板数据 - 使用报名审批通知模板的参数
                import json
                template_data = {
                    'thing18': {  # 用户名称
                        'value': lead.customer_name[:20] if lead.customer_name else '未填写',
                        'color': '#173177'
                    },
                    'time3': {  # 报名时间（这里用线索创建时间）
                        'value': lead.create_date.strftime('%Y年%m月%d日 %H:%M') if lead.create_date else '未知时间',
                        'color': '#173177'
                    },
                    'thing21': {  # 项目名称（这里用业务类型或线索编号）
                        'value': (lead.teacher_group_id.name if lead.teacher_group_id else lead.name)[:20],
                        'color': '#173177'
                    }
                }

                subject = f'线索分配通知 - {lead.customer_name or lead.name}'

            else:
                # 多个线索时，显示汇总信息并跳转到认领列表
                first_lead = self.lead_ids[0]
                lead_count = len(self.lead_ids)

                # 跳转到我的线索认领列表页面
                lead_url = f"{base_url}/web#action=done_crm_upgrade.crm_lead_claim_action_teacher&model=crm.lead"

                # 准备模板数据（适配多线索场景）
                import json
                template_data = {
                    'thing18': {  # 用户名称（使用第一个客户名称）
                        'value': first_lead.customer_name[:20] if first_lead.customer_name else f'批量分配{lead_count}个',
                        'color': '#173177'
                    },
                    'time3': {  # 报名时间（使用分配时间）
                        'value': fields.Datetime.now().strftime('%Y年%m月%d日 %H:%M'),
                        'color': '#173177'
                    },
                    'thing21': {  # 项目名称（显示线索数量）
                        'value': f'批量分配{lead_count}个线索',
                        'color': '#d9534f'
                    }
                }

                subject = f'线索分配通知 - {lead_count}个新线索'

            # 创建并发送微信模板消息
            wechat_message = self.env['wechat.message'].create({
                'subject': subject,
                'message_type': 'template',
                'config_id': wechat_config.id,
                'recipient_type': 'single',
                'user_id': teacher_wechat_user.id,
                'template_id': template.id,
                'template_data': json.dumps(template_data, ensure_ascii=False),
                'url': lead_url,  # 跳转到具体线索页面或认领列表
            })

            # 发送消息
            wechat_message.send_message()

            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"成功发送报名审批通知模板消息给老师 {self.claim_teacher_user_id.name}")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"创建或发送微信模板消息失败: {e}")
            raise

    def _build_notification_message(self):
        """构建通知消息内容"""
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')

        # 构建认领页面链接
        claim_url = f"{base_url}/web#action=done_crm_upgrade.action_crm_lead_claim&model=crm.lead"

        # 线索信息摘要
        lead_names = ', '.join([lead.name for lead in self.lead_ids[:3]])  # 最多显示3个线索名称
        if len(self.lead_ids) > 3:
            lead_names += f" 等{len(self.lead_ids)}个线索"

        # 构建消息内容
        message_content = f"""📋 线索分配通知

您好 {self.claim_teacher_user_id.name}！

您有新的线索已分配给您：
🔸 线索数量：{len(self.lead_ids)}个
🔸 线索编号：{lead_names}

请及时登录系统进行认领和跟进操作。

👆 点击下方链接直接进入认领页面：
{claim_url}

⏰ 请在24小时内完成认领，逾期将自动退回分配池。

祝您工作顺利！"""

        return message_content

    def _create_and_send_wechat_message(self, wechat_config, teacher_wechat_user, message_content):
        """创建并发送微信消息"""
        try:
            # 创建微信消息记录
            wechat_message = self.env['wechat.message'].create({
                'subject': f'线索分配通知 - {self.claim_teacher_user_id.name}',
                'message_type': 'text',
                'content': message_content,
                'recipient_type': 'single',
                'user_id': teacher_wechat_user.id,
                'recipient_openid': teacher_wechat_user.openid,
                'config_id': wechat_config.id,
            })

            # 发送消息
            wechat_message.send_message()

            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"成功发送微信通知给老师 {self.claim_teacher_user_id.name} (OpenID: {teacher_wechat_user.openid})")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"创建或发送微信消息失败: {e}")
            raise

    def _send_batch_wechat_notification(self, teacher_user, allocated_leads):
        """发送批量分配的微信消息通知"""
        try:
            # 获取随机选中的已激活微信配置
            wechat_config = self.env['wechat.config'].search([
                ('active', '=', True)
            ], limit=1)

            if not wechat_config:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning("没有找到激活的微信公众号配置，无法发送通知消息")
                return

            # 查找老师对应的微信用户
            teacher_wechat_user = self._find_teacher_wechat_user_by_user_id(teacher_user)

            if not teacher_wechat_user:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info(f"老师 {teacher_user.name} 没有绑定微信账号，无法发送通知")
                return

            # 构建批量分配消息内容
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            claim_url = f"{base_url}/web#action=done_crm_upgrade.crm_lead_claim_action_teacher&model=crm.lead"

            # 线索信息摘要
            lead_names = ', '.join([lead.name for lead in allocated_leads[:3]])
            if len(allocated_leads) > 3:
                lead_names += f" 等{len(allocated_leads)}个线索"

            message_content = f"""📋 批量线索分配通知

您好 {teacher_user.name}！

您有新的线索已批量分配给您：
🔸 线索数量：{len(allocated_leads)}个
🔸 线索编号：{lead_names}

请及时登录系统进行认领和跟进操作。

👆 点击下方链接直接进入认领页面：
{claim_url}

⏰ 请在24小时内完成认领，逾期将自动退回分配池。

祝您工作顺利！"""

            # 创建并发送微信消息
            wechat_message = self.env['wechat.message'].create({
                'subject': f'批量线索分配通知 - {teacher_user.name}',
                'message_type': 'text',
                'content': message_content,
                'recipient_type': 'single',
                'user_id': teacher_wechat_user.id,
                'recipient_openid': teacher_wechat_user.openid,
                'config_id': wechat_config.id,
            })

            # 发送消息
            wechat_message.send_message()

            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"成功发送批量分配微信通知给老师 {teacher_user.name} (OpenID: {teacher_wechat_user.openid})")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"发送批量分配微信通知失败: {e}")

    def _find_teacher_wechat_user_by_user_id(self, teacher_user):
        """根据用户ID查找对应的微信用户"""
        # 方法1: 通过自动登录记录查找
        auto_login = self.env['wechat.auto.login'].search([
            ('user_id', '=', teacher_user.id),
            ('active', '=', True),
            ('enabled', '=', True)
        ], limit=1)

        if auto_login:
            wechat_user = self.env['wechat.user'].search([
                ('openid', '=', auto_login.openid),
                ('config_id', '=', auto_login.config_id.id),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法2: 通过手机号匹配查找
        if teacher_user.phone:
            wechat_user = self.env['wechat.user'].search([
                ('phone', '=', teacher_user.phone),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法3: 通过邮箱匹配查找
        if teacher_user.email:
            wechat_user = self.env['wechat.user'].search([
                ('email', '=', teacher_user.email),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法4： 通过微信用户匹配查找
        if teacher_user.name:
            wechat_user = self.env['wechat.user'].search([
                ('user_id', '=', teacher_user.id),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        return None
    
    @api.onchange('claim_teacher_user_id')
    def _onchange_claim_teacher_user_id(self):
        """当选择老师时的处理"""
        if self.claim_teacher_user_id and self.lead_ids:
            # 可以在这里添加一些逻辑，比如检查老师的工作负载等
            pass


class CrmLeadAllocateBatchWizard(models.TransientModel):
    _name = 'crm.lead.allocate.batch.wizard'
    _description = '线索批量分配向导'

    # 分配规则
    allocation_rule = fields.Selection([
        ('manual', '手动分配'),
        ('auto_weight', '按权重自动分配'),
        ('auto_round', '轮询分配'),
    ], string='分配规则', default='manual', required=True)
    
    # 老师列表
    teacher_allocation_ids = fields.One2many(
        'crm.lead.allocate.teacher.line', 
        'wizard_id', 
        string='老师分配'
    )
    
    # 线索信息
    lead_ids = fields.Many2many('crm.lead', string='待分配线索', readonly=True)
    total_leads = fields.Integer(string='总线索数', compute='_compute_total_leads')
    
    @api.depends('lead_ids')
    def _compute_total_leads(self):
        """计算总线索数"""
        for wizard in self:
            wizard.total_leads = len(wizard.lead_ids)
    
    @api.model
    def default_get(self, fields_list):
        """设置默认值"""
        res = super().default_get(fields_list)
        
        # 从上下文获取线索ID
        active_ids = self.env.context.get('active_ids', [])
        active_model = self.env.context.get('active_model', '')
        
        if active_model == 'crm.lead' and active_ids:
            leads = self.env['crm.lead'].browse(active_ids).filtered(
                lambda l: l.lead_state == 'to_allocate'
            )
            res['lead_ids'] = [(6, 0, leads.ids)]
        
        return res
    
    def action_allocate_batch(self):
        """执行批量分配"""
        self.ensure_one()
        
        if not self.lead_ids:
            raise UserError(_('没有可分配的线索'))
        
        if self.allocation_rule == 'manual':
            return self._allocate_manual()
        elif self.allocation_rule == 'auto_weight':
            return self._allocate_by_weight()
        elif self.allocation_rule == 'auto_round':
            return self._allocate_round_robin()
    
    def _allocate_manual(self):
        """手动分配"""
        if not self.teacher_allocation_ids:
            raise UserError(_('请添加老师分配信息'))
        
        total_allocated = sum(line.lead_count for line in self.teacher_allocation_ids)
        if total_allocated != len(self.lead_ids):
            raise UserError(_('分配的线索总数(%d)与待分配线索数(%d)不匹配') % (total_allocated, len(self.lead_ids)))
        
        # 执行分配
        lead_index = 0
        for line in self.teacher_allocation_ids:
            if line.lead_count > 0:
                leads_to_allocate = self.lead_ids[lead_index:lead_index + line.lead_count]
                leads_to_allocate.write({
                    'claim_teacher_user_id': line.teacher_id.id,
                    'user_id': line.teacher_id.id,
                    'lead_state': 'to_claim',
                    'allocate_date': fields.Datetime.now(),
                })

                # 为每个老师发送微信通知
                self._send_batch_wechat_notification(line.teacher_id, leads_to_allocate)

                lead_index += line.lead_count
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('批量分配成功'),
                'message': _('成功分配 %d 个线索') % len(self.lead_ids),
                'type': 'success',
            }
        }
    
    def _allocate_by_weight(self):
        """按权重自动分配"""
        # 这里可以实现按权重分配的逻辑
        raise UserError(_('按权重自动分配功能正在开发中'))
    
    def _allocate_round_robin(self):
        """轮询分配"""
        # 这里可以实现轮询分配的逻辑
        raise UserError(_('轮询分配功能正在开发中'))


class CrmLeadAllocateTeacherLine(models.TransientModel):
    _name = 'crm.lead.allocate.teacher.line'
    _description = '老师分配行'

    wizard_id = fields.Many2one('crm.lead.allocate.batch.wizard', string='向导', ondelete='cascade')
    teacher_id = fields.Many2one(
        'res.users',
        string='老师',
        required=True,
        domain="[('active', '=', True), ('share', '=', False), ('id', '!=', 1)]"
    )
    lead_count = fields.Integer(string='分配数量', default=0)
    teacher_name = fields.Char(string='老师姓名', related='teacher_id.name', readonly=True)


class CrmLeadAllocateWizard(models.TransientModel):
    _inherit = 'crm.lead.allocate.wizard'

    def _send_system_notification(self):
        """发送系统通知给被分配的老师"""
        try:

            # 尝试发送活动通知
            try:
                self._create_activity_notification()
            except Exception as e:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"创建活动通知失败: {str(e)}")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"发送系统通知失败: {str(e)}")

    def _create_activity_notification(self):
        """创建活动通知"""
        try:
            # 为每个分配的线索创建活动
            for lead in self.lead_ids:
                activity_type = self.env.ref('mail.mail_activity_data_todo', raise_if_not_found=False)
                if not activity_type:
                    # 如果没有找到默认活动类型，使用第一个可用的
                    activity_type = self.env['mail.activity.type'].search([], limit=1)

                if activity_type:
                    self.env['mail.activity'].create({
                        'activity_type_id': activity_type.id,
                        'summary': f'新线索分配：{lead.name}',
                        'note': f'您有新的线索需要认领：<br/>'
                               f'线索编号：{lead.name}<br/>'
                               f'客户姓名：{lead.customer_name or "未填写"}<br/>'
                               f'客户手机：{lead.customer_mobile or "未填写"}<br/>'
                               f'请及时登录系统进行认领和跟进。',
                        'res_id': lead.id,
                        'res_model_id': self.env['ir.model']._get('crm.lead').id,
                        'user_id': self.claim_teacher_user_id.id,
                        'date_deadline': fields.Date.today(),
                    })
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"创建活动通知失败: {str(e)}")

    def _create_mail_notification(self):
        """创建邮件消息通知"""
        try:
            # 构建简单的消息内容
            lead_names = [lead.name for lead in self.lead_ids[:3]]
            if len(self.lead_ids) > 3:
                lead_names.append(f"等{len(self.lead_ids)}个线索")

            message_body = f"""
            <div style="font-family: Arial, sans-serif;">
                <h3 style="color: #007cba;">线索分配通知</h3>
                <p>您好 <strong>{self.claim_teacher_user_id.name}</strong>！</p>
                <p>您有 <strong>{len(self.lead_ids)}</strong> 个新线索已分配给您：</p>
                <p><strong>线索编号：</strong>{', '.join(lead_names)}</p>
                <p style="color: #d9534f;">⏰ 请在24小时内完成认领，逾期将自动退回分配池。</p>
                <hr/>
                <p style="font-size: 12px; color: #666;">
                    分配时间：{fields.Datetime.now()}<br/>
                    分配人：{self.env.user.name}
                </p>
            </div>
            """

            # 创建消息记录
            message = self.env['mail.message'].create({
                'subject': f'线索分配通知 - {len(self.lead_ids)}个新线索',
                'body': message_body,
                'message_type': 'notification',
                'partner_ids': [(4, self.claim_teacher_user_id.partner_id.id)],
                'needaction': True,
            })

            # 记录日志
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"成功创建邮件通知给用户 {self.claim_teacher_user_id.name} (消息ID: {message.id})")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"创建邮件通知失败: {str(e)}")

    def _send_bus_notification(self):
        """发送实时 bus 通知"""
        try:
            # 使用标准的通知服务
            self.env.user.notify_success(
                message=f'已成功分配 {len(self.lead_ids)} 个线索给 {self.claim_teacher_user_id.name}',
                title='分配成功',
                sticky=False
            )

            # 给被分配的老师发送通知
            if hasattr(self.claim_teacher_user_id, 'notify_info'):
                self.claim_teacher_user_id.notify_info(
                    message=f'您有 {len(self.lead_ids)} 个新线索需要认领，请及时处理！',
                    title='新线索分配',
                    sticky=True
                )

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"发送bus通知失败: {str(e)}")

    def _send_wechat_template_notification(self):
        """发送微信模板消息通知 - 使用报名审批通知模板"""
        try:
            # 检查是否安装了微信公众号模块
            if 'wechat.template' not in self.env:
                return

            # 查找报名审批通知模板
            template = self.env['wechat.template'].search([
                ('name', '=', '报名审批通知'),
                ('active', '=', True)
            ], limit=1)

            if not template:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info("未找到报名审批通知模板，跳过微信通知")
                return

            # 查找被分配老师的微信用户记录
            wechat_user = self.env['wechat.user'].search([
                ('user_id', '=', self.claim_teacher_user_id.id),
                ('subscribe', '=', True)
            ], limit=1)

            if not wechat_user:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info(f"用户 {self.claim_teacher_user_id.name} 未绑定微信或未关注公众号，跳过微信通知")
                return

            # 获取基础URL
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')

            # 如果只有一个线索，发送单个线索的详细信息并跳转到具体线索
            if len(self.lead_ids) == 1:
                lead = self.lead_ids[0]

                # 准备报名审批通知模板数据
                template_data = {
                    'thing18': {  # 用户名称
                        'value': lead.customer_name[:20] if lead.customer_name else '未填写',
                        'color': '#173177'
                    },
                    'time9': {  # 报名时间（使用分配时间）
                        'value': fields.Datetime.now().strftime('%Y年%m月%d日 %H:%M'),
                        'color': '#173177'
                    },
                    'thing21': {  # 项目名称（业务类型或线索编号）
                        'value': (lead.teacher_group_id.name if lead.teacher_group_id else lead.name)[:20],
                        'color': '#173177'
                    }
                }

                # 构建直接认领线索的URL，点击后自动认领并跳转到线索详情页面
                jump_url = f"{base_url}/crm/lead/auto_claim/{lead.id}"
                subject = f'线索分配通知 - {lead.customer_name or lead.name}'

            else:
                # 多个线索时，显示汇总信息并跳转到认领列表
                first_lead = self.lead_ids[0]
                lead_count = len(self.lead_ids)

                # 准备报名审批通知模板数据（适配多线索场景）
                template_data = {
                    'thing18': {  # 用户名称（使用第一个客户名称）
                        'value': first_lead.customer_name[:20] if first_lead.customer_name else f'批量分配{lead_count}个',
                        'color': '#173177'
                    },
                    'time9': {  # 报名时间（使用分配时间）
                        'value': fields.Datetime.now().strftime('%Y年%m月%d日 %H:%M'),
                        'color': '#173177'
                    },
                    'phone_number20': {  # 联系人电话（使用第一个客户电话）
                        'value': first_lead.customer_mobile if first_lead.customer_mobile else '多个客户',
                        'color': '#173177'
                    },
                    'thing21': {  # 项目名称（显示线索数量）
                        'value': f'批量分配{lead_count}个线索',
                        'color': '#d9534f'
                    }
                }

                # 跳转到我的线索认领列表页面
                jump_url = f"{base_url}/web#action=done_crm_upgrade.crm_lead_claim_action_teacher&model=crm.lead"
                subject = f'线索分配通知 - {lead_count}个新线索'

            # 创建并发送微信消息
            wechat_message = self.env['wechat.message'].create({
                'subject': subject,
                'message_type': 'template',
                'config_id': wechat_user.config_id.id,
                'recipient_type': 'single',
                'user_id': wechat_user.id,
                'template_id': template.id,
                'template_data': json.dumps(template_data, ensure_ascii=False),
                'url': jump_url,
            })

            # 发送消息
            wechat_message.send_message()

            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"成功发送报名审批通知模板消息给用户 {self.claim_teacher_user_id.name}")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"发送微信模板消息失败: {str(e)}")
