# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class CrmTeacherWeight(models.Model):
    _name = 'crm.teacher.weight'
    _description = '教师加权评分表'
    _order = 'teacher_group_id, total_score desc, id'
    _rec_name = 'display_name'

    # 基础字段
    teacher_group_id = fields.Many2one(
        'teacher.group',
        string='业务类型',
        required=True,
        help='教师所属的招生组别'
    )

    user_id = fields.Many2one(
        'res.users',
        string='老师',
        help='招生老师的用户账号'
    )
    
    # 权重和评分字段
    weight = fields.Float(
        string='权重',
        default=1.0,
        help='基础权重，影响最终分配概率'
    )
    additional_score = fields.Float(
        string='附加分',
        default=0.0,
        help='额外附加分数'
    )

    deal_rate_score = fields.Float(
        string='成交率分',
        default=0.0,
        help='基于历史成交率的评分'
    )
    deal_speed_score = fields.Float(
        string='成交速度分',
        default=0.0,
        help='基于成交速度的评分'
    )
    kpi_score = fields.Float(
        string='KPI分',
        default=0.0,
        help='基于KPI完成情况的评分'
    )
    gender_match_score = fields.Float(
        string='性别匹配分',
        default=0.0,
        help='性别匹配度评分'
    )
    amount_match_score = fields.Float(
        string='金额匹配分',
        default=0.0,
        help='金额匹配度评分'
    )
    
    # 计算字段
    total_score = fields.Float(
        string='合计分值',
        compute='_compute_total_score',
        store=True,
        help='所有评分的总和'
    )
    
    # 状态和配置
    active = fields.Boolean('启用', default=True)
    
    # 统计字段
    assigned_lead_count = fields.Integer(
        string='已分配线索数',
        compute='_compute_assigned_lead_count',
        help='已分配给该教师的线索数量'
    )
    
    # 显示名称
    display_name = fields.Char(
        string='显示名称',
        compute='_compute_display_name',
        store=True
    )
    
    # 备注
    notes = fields.Text('备注')
    
    @api.depends('teacher_group_id', 'user_id', 'total_score')
    def _compute_display_name(self):
        """计算显示名称"""
        for record in self:
            parts = []
            if record.teacher_group_id:
                parts.append(record.teacher_group_id.name)
            if record.user_id:
                parts.append(record.user_id.name)
            if record.total_score:
                parts.append(f'({record.total_score}分)')
            record.display_name = ' - '.join(parts) if parts else _('新记录')
    
    @api.depends('weight', 'additional_score', 'deal_rate_score', 'deal_speed_score', 
                 'kpi_score', 'gender_match_score', 'amount_match_score')
    def _compute_total_score(self):
        """计算总分"""
        for record in self:
            record.total_score = (
                record.weight +
                record.additional_score +
                record.deal_rate_score +
                record.deal_speed_score +
                record.kpi_score +
                record.gender_match_score +
                record.amount_match_score
            )
    
    def _compute_assigned_lead_count(self):
        """计算已分配的线索数量"""
        for record in self:
            # 这里可以根据实际需求统计分配给该教师的线索数量
            # 暂时设为0，后续可以根据分配记录来计算
            record.assigned_lead_count = 0
    
    @api.constrains('weight')
    def _check_weight(self):
        """验证权重值"""
        for record in self:
            if record.weight < 0:
                raise ValidationError(_('权重不能为负数'))
    
    @api.constrains('teacher_group_id', 'user_id')
    def _check_unique_employee_in_group(self):
        """确保同一组别中员工不重复"""
        for record in self:
            if record.teacher_group_id and record.user_id:
                existing = self.search([
                    ('teacher_group_id', '=', record.teacher_group_id.id),
                    ('user_id', '=', record.user_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        _('员工 %s 在组别 %s 中已存在权重配置') % 
                        (record.user_id.name, record.teacher_group_id.name)
                    )

    def _calculate_deal_rate_score(self):

        """计算成交率分"""
        # 这里可以根据实际需求计算成交率分
        # 暂时设为0，后续可以根据历史数据来计算
        for record in self:
            # 认领成功计算权重  完成数量 / 总认领数量
            # 总认领数量
            total_claim_count = self.env['crm.lead'].search_count(
                [('claim_teacher_user_id', '=', record.user_id.id), ('lead_state', 'in', ['to_claim', 'to_score', 'done'])])
            # 完成数量
            done_count = self.env['crm.lead'].search_count(
                [('claim_teacher_user_id', '=', record.user_id.id), ('lead_state', '=', 'done')])
            # 计算权重
            if total_claim_count > 0:
                weight = done_count / total_claim_count
            else:
                weight = 0
            # 更新权重
            record.sudo().write({'deal_rate_score': weight})

    def _calculate_deal_speed_score(self):
        """计算成交速度分"""
        # 这里可以根据实际需求计算成交速度分
        # 暂时设为0，后续可以根据历史数据来计算
        for record in self:
            # 认领成功计算权重  完成数量 / 总认领数量
            # 总认领数量 todo 速度分数规律
            record.write({'deal_speed_score': 20})

    def action_calculate_scores(self):
        """计算各项评分的动作方法"""
        # 这里可以实现自动计算各项评分的逻辑
        # 比如根据历史数据计算成交率分、成交速度分等
        for record in self:
            # 示例：可以根据实际业务逻辑来计算
            record.deal_rate_score = self._calculate_deal_rate_score()
            record.deal_speed_score = self._calculate_deal_speed_score()
            # record.kpi_score = self._calculate_kpi_score()

        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('计算完成'),
                'message': _('评分计算已完成'),
                'type': 'success',
            }
        }
    
    def action_view_assigned_leads(self):
        """查看分配给该教师的线索"""
        self.ensure_one()
        return {
            'name': _('分配的线索'),
            'type': 'ir.actions.act_window',
            'res_model': 'crm.lead',
            'view_mode': 'list,form',
            'domain': [('user_id', '=', self.user_id.id)],
            'context': {'default_user_id': self.user_id.id}
        }
