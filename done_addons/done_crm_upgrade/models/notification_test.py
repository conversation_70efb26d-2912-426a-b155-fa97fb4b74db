# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class NotificationTest(models.TransientModel):
    _name = 'notification.test'
    _description = '通知测试工具'

    target_user_id = fields.Many2one(
        'res.users',
        string='目标用户',
        required=True,
        domain="[('active', '=', True), ('share', '=', False)]",
        help='选择要发送通知的用户'
    )
    
    notification_type = fields.Selection([
        ('activity', '活动通知'),
        ('mail', '邮件消息'),
        ('bus', 'Bus通知'),
        ('all', '全部类型'),
    ], string='通知类型', default='all', required=True)
    
    test_message = fields.Text(
        string='测试消息',
        default='这是一条测试通知消息，用于验证系统通知功能是否正常工作。',
        required=True
    )

    def action_send_test_notification(self):
        """发送测试通知"""
        try:
            if self.notification_type in ['activity', 'all']:
                self._send_activity_notification()
            
            if self.notification_type in ['mail', 'all']:
                self._send_mail_notification()
            
            if self.notification_type in ['bus', 'all']:
                self._send_bus_notification()
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': '测试通知发送成功',
                    'message': f'已向 {self.target_user_id.name} 发送 {self.notification_type} 类型的测试通知',
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            _logger.error(f"发送测试通知失败: {str(e)}")
            raise UserError(f"发送测试通知失败: {str(e)}")

    def _send_activity_notification(self):
        """发送活动通知"""
        try:
            activity_type = self.env.ref('mail.mail_activity_data_todo', raise_if_not_found=False)
            if not activity_type:
                activity_type = self.env['mail.activity.type'].search([], limit=1)
            
            if activity_type:
                self.env['mail.activity'].create({
                    'activity_type_id': activity_type.id,
                    'summary': '系统通知测试',
                    'note': f'<p>{self.test_message}</p><p>发送时间：{fields.Datetime.now()}</p>',
                    'res_id': self.target_user_id.id,
                    'res_model_id': self.env['ir.model']._get('res.users').id,
                    'user_id': self.target_user_id.id,
                    'date_deadline': fields.Date.today(),
                })
                _logger.info(f"活动通知发送成功给用户: {self.target_user_id.name}")
            else:
                _logger.warning("未找到可用的活动类型")
                
        except Exception as e:
            _logger.error(f"发送活动通知失败: {str(e)}")
            raise

    def _send_mail_notification(self):
        """发送邮件消息通知"""
        try:
            message_body = f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px;">
                <h3 style="color: #007cba;">系统通知测试</h3>
                <p>{self.test_message}</p>
                <hr style="border: 1px solid #eee; margin: 20px 0;">
                <p style="color: #666; font-size: 12px;">
                    发送时间：{fields.Datetime.now()}<br/>
                    发送人：{self.env.user.name}
                </p>
            </div>
            """
            
            self.env['mail.message'].create({
                'subject': '系统通知测试',
                'body': message_body,
                'message_type': 'notification',
                'partner_ids': [(4, self.target_user_id.partner_id.id)],
                'needaction': True,
                'model': 'res.users',
                'res_id': self.target_user_id.id,
            })
            _logger.info(f"邮件通知发送成功给用户: {self.target_user_id.name}")
            
        except Exception as e:
            _logger.error(f"发送邮件通知失败: {str(e)}")
            raise

    def _send_bus_notification(self):
        """发送Bus通知"""
        try:
            # 方法1: 使用标准通知
            if hasattr(self.env.user, 'notify_info'):
                self.env.user.notify_info(
                    message=f'测试通知已发送给 {self.target_user_id.name}',
                    title='通知测试',
                    sticky=False
                )
            
            # 方法2: 直接使用bus
            channel = (self.env.cr.dbname, 'res.partner', self.target_user_id.partner_id.id)
            notification_data = {
                'type': 'info',
                'title': '系统通知测试',
                'message': self.test_message,
                'sticky': True,
            }
            
            self.env['bus.bus']._sendone(
                channel,
                'web.notification',
                notification_data
            )
            
            _logger.info(f"Bus通知发送成功给用户: {self.target_user_id.name}")
            
        except Exception as e:
            _logger.error(f"发送Bus通知失败: {str(e)}")
            raise

    def action_test_current_user(self):
        """测试当前用户通知"""
        self.target_user_id = self.env.user
        return self.action_send_test_notification()
