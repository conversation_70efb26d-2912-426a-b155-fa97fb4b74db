# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)


class CrmPaymentMain(models.Model):
    """线索支付管理主表"""
    _name = 'crm.payment.main'
    _description = 'CRM Payment Main'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'display_name'
    _order = 'create_date desc'

    # 基本信息
    lead_id = fields.Many2one('crm.lead', string='线索', required=True, 
                             domain="[('lead_state', '=', 'done')]",
                             help='只能选择已成交的线索')
    claim_teacher_user_id = fields.Many2one('res.users', string='线索认领老师', 
                                           related='lead_id.claim_teacher_user_id', 
                                           readonly=True, store=True)
    customer_name = fields.Char('客户名', related='lead_id.customer_name', 
                               readonly=True, store=True)
    upstream_teacher_user_id = fields.Many2one('res.users', string='上游老师', 
                                              related='lead_id.upstream_teacher_user_id', 
                                              readonly=True, store=True)
    
    # 金额信息
    contract_amount = fields.Float('签约合同金额', related='lead_id.contract_amount',
                                  readonly=True, store=True, help='单位：万元')
    paid_amount = fields.Float('已支付金额', compute='_compute_paid_amount', 
                              store=True, help='单位：万元')
    unpaid_amount = fields.Float('未支付金额', compute='_compute_unpaid_amount', 
                                store=True, help='单位：万元')
    
    # 支付明细
    payment_line_ids = fields.One2many('crm.payment.line', 'payment_main_id', 
                                      string='支付明细')
    
    # 状态信息
    payment_status = fields.Selection([
        ('unpaid', '未支付'),
        ('partial', '部分支付'),
        ('paid', '已支付'),
        ('overpaid', '超额支付'),
    ], string='支付状态', compute='_compute_payment_status', store=True, tracking=True)
    
    # 统计信息
    payment_count = fields.Integer('支付次数', compute='_compute_payment_count', store=True)
    last_payment_date = fields.Date('最后支付日期', compute='_compute_last_payment_date', store=True)
    
    # 计算字段
    display_name = fields.Char('显示名称', compute='_compute_display_name', store=True)

    @api.onchange('lead_id')
    def _onchange_lead_id(self):
        """当选择线索时，自动更新相关字段"""
        if self.lead_id:
            # 检查线索状态
            if self.lead_id.lead_state != 'done':
                return {
                    'warning': {
                        'title': _('警告'),
                        'message': _('只能为已成交的线索创建支付记录')
                    }
                }

            # 检查是否已存在支付记录
            existing = self.search([('lead_id', '=', self.lead_id.id)], limit=1)
            if existing and existing.id != self.id:
                return {
                    'warning': {
                        'title': _('警告'),
                        'message': _('该线索已存在支付记录')
                    }
                }

            # 强制更新相关字段（虽然related字段应该自动更新，但确保一下）
            self.contract_amount = self.lead_id.contract_amount

    @api.depends('customer_name', 'contract_amount')
    def _compute_display_name(self):
        """计算显示名称"""
        for record in self:
            if record.customer_name and record.contract_amount:
                record.display_name = f"{record.customer_name} - {record.contract_amount}万元"
            elif record.customer_name:
                record.display_name = record.customer_name
            else:
                record.display_name = f"支付记录 #{record.id}"

    @api.depends('payment_line_ids.amount')
    def _compute_paid_amount(self):
        """计算已支付金额"""
        for record in self:
            record.paid_amount = sum(record.payment_line_ids.mapped('amount'))

    @api.depends('contract_amount', 'paid_amount')
    def _compute_unpaid_amount(self):
        """计算未支付金额"""
        for record in self:
            record.unpaid_amount = (record.contract_amount or 0) - (record.paid_amount or 0)

    @api.depends('contract_amount', 'paid_amount')
    def _compute_payment_status(self):
        """计算支付状态"""
        for record in self:
            if not record.contract_amount:
                record.payment_status = 'unpaid'
            elif record.paid_amount <= 0:
                record.payment_status = 'unpaid'
            elif record.paid_amount >= record.contract_amount:
                if record.paid_amount > record.contract_amount:
                    record.payment_status = 'overpaid'
                else:
                    record.payment_status = 'paid'
            else:
                record.payment_status = 'partial'

    @api.depends('payment_line_ids')
    def _compute_payment_count(self):
        """计算支付次数"""
        for record in self:
            record.payment_count = len(record.payment_line_ids)

    @api.depends('payment_line_ids.payment_date')
    def _compute_last_payment_date(self):
        """计算最后支付日期"""
        for record in self:
            if record.payment_line_ids:
                record.last_payment_date = max(record.payment_line_ids.mapped('payment_date'))
            else:
                record.last_payment_date = False

    @api.model
    def default_get(self, fields_list):
        """设置默认值"""
        res = super(CrmPaymentMain, self).default_get(fields_list)

        # 从上下文获取线索ID
        lead_id = self.env.context.get('default_lead_id')
        if lead_id:
            lead = self.env['crm.lead'].browse(lead_id)
            if lead.exists() and lead.lead_state == 'done':
                res['lead_id'] = lead_id
                # 由于使用了related字段，这里不需要手动设置contract_amount
                # 它会自动从lead_id.contract_amount获取

        return res

    @api.model
    def create(self, vals):
        """创建时检查线索是否已存在支付记录"""
        if 'lead_id' in vals:
            existing = self.search([('lead_id', '=', vals['lead_id'])], limit=1)
            if existing:
                raise UserError(_('线索 "%s" 已存在支付记录，请直接在现有记录中添加支付明细') % existing.lead_id.name)
        return super(CrmPaymentMain, self).create(vals)

    def action_add_payment(self):
        """添加支付记录"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('添加支付记录'),
            'res_model': 'crm.payment.line',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_payment_main_id': self.id,
                'default_amount': self.unpaid_amount,
            }
        }

    def action_view_payments(self):
        """查看支付明细"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('支付明细'),
            'res_model': 'crm.payment.line',
            'view_mode': 'list,form',
            'domain': [('payment_main_id', '=', self.id)],
            'context': {
                'default_payment_main_id': self.id,
            }
        }


class CrmPaymentLine(models.Model):
    """线索支付明细表"""
    _name = 'crm.payment.line'
    _description = 'CRM Payment Line'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'display_name'
    _order = 'payment_date desc, create_date desc'

    # 关联信息
    payment_main_id = fields.Many2one('crm.payment.main', string='支付主记录', 
                                     required=True, ondelete='cascade')
    lead_id = fields.Many2one('crm.lead', string='线索', 
                             related='payment_main_id.lead_id', 
                             readonly=True, store=True)
    customer_name = fields.Char('客户名', related='payment_main_id.customer_name', 
                               readonly=True, store=True)
    
    # 支付信息
    payment_date = fields.Date('交易日期', required=True, default=fields.Date.today, tracking=True)
    amount = fields.Float('交易金额', required=True, help='单位：万元', tracking=True)
    payment_method = fields.Selection([
        ('cash', '现金'),
        ('bank_transfer', '银行转账'),
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('credit_card', '信用卡'),
        ('other', '其他'),
    ], string='支付方式', default='bank_transfer')
    
    # 备注信息
    notes = fields.Text('备注')
    reference = fields.Char('参考号', help='交易参考号或凭证号')
    
    # 审核信息
    state = fields.Selection([
        ('draft', '草稿'),
        ('confirmed', '已确认'),
        ('cancelled', '已取消'),
    ], string='状态', default='draft', tracking=True)
    
    # 操作人信息
    create_uid = fields.Many2one('res.users', string='创建人', readonly=True)
    confirm_user_id = fields.Many2one('res.users', string='确认人', readonly=True)
    confirm_date = fields.Datetime('确认时间', readonly=True)
    
    # 计算字段
    display_name = fields.Char('显示名称', compute='_compute_display_name', store=True)
    contract_amount = fields.Float('合同金额', related='payment_main_id.contract_amount',
                                  readonly=True)
    remaining_amount = fields.Float('剩余金额', compute='_compute_remaining_amount')

    @api.depends('customer_name', 'amount', 'payment_date')
    def _compute_display_name(self):
        """计算显示名称"""
        for record in self:
            if record.customer_name and record.amount and record.payment_date:
                record.display_name = f"{record.customer_name} - {record.amount}万元 ({record.payment_date})"
            else:
                record.display_name = f"支付明细 #{record.id}"

    @api.depends('payment_main_id.contract_amount', 'payment_main_id.paid_amount')
    def _compute_remaining_amount(self):
        """计算剩余金额"""
        for record in self:
            if record.payment_main_id:
                record.remaining_amount = record.payment_main_id.unpaid_amount
            else:
                record.remaining_amount = 0

    @api.constrains('amount')
    def _check_amount(self):
        """验证交易金额"""
        for record in self:
            if record.amount <= 0:
                raise ValidationError(_('交易金额必须大于0'))
            if record.amount > 1000:
                raise ValidationError(_('单次交易金额不能超过1000万元，请检查输入'))

    def action_confirm(self):
        """确认支付"""
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('只有草稿状态的记录才能确认'))
        
        self.write({
            'state': 'confirmed',
            'confirm_user_id': self.env.user.id,
            'confirm_date': fields.Datetime.now(),
        })
        
        # 记录到线索消息中
        self.lead_id.message_post(
            body=_('收到支付：%.2f万元，支付日期：%s') % (self.amount, self.payment_date),
            message_type='notification',
            subtype_xmlid='mail.mt_comment'
        )

    def action_cancel(self):
        """取消支付"""
        self.ensure_one()
        if self.state == 'cancelled':
            raise UserError(_('记录已经是取消状态'))
        
        self.write({
            'state': 'cancelled',
        })

    def action_reset_to_draft(self):
        """重置为草稿"""
        self.ensure_one()
        self.write({
            'state': 'draft',
            'confirm_user_id': False,
            'confirm_date': False,
        })
