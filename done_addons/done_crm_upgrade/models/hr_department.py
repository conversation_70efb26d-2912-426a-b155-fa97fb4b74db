# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class HrDepartment(models.Model):
    _inherit = 'hr.department'

    # 线索提醒人（可多选）
    lead_reminder_user_ids = fields.Many2many(
        'res.users',
        'hr_department_lead_reminder_rel',
        'department_id',
        'user_id',
        string='线索提醒人',
        tracking=True,
        help='当本部门员工的线索被退回时，会向这些用户发送微信提醒'
    )

    # 计算字段：显示线索提醒人数量
    lead_reminder_count = fields.Integer(
        string='提醒人数量',
        compute='_compute_lead_reminder_count',
        help='线索提醒人的数量'
    )

    @api.depends('lead_reminder_user_ids')
    def _compute_lead_reminder_count(self):
        """计算线索提醒人数量"""
        for record in self:
            record.lead_reminder_count = len(record.lead_reminder_user_ids)

    @api.constrains('lead_reminder_user_ids')
    def _check_lead_reminder_users_active(self):
        """验证线索提醒人必须是激活状态的用户"""
        for record in self:
            inactive_users = record.lead_reminder_user_ids.filtered(lambda u: not u.active)
            if inactive_users:
                raise ValidationError(
                    _('线索提醒人中包含未激活的用户：%s。请选择激活状态的用户。') %
                    ', '.join(inactive_users.mapped('name'))
                )

    def send_lead_return_notification(self, lead, returned_by_user):
        """
        发送线索退回通知给部门的线索提醒人
        
        Args:
            lead: 被退回的线索记录
            returned_by_user: 退回线索的用户
        """
        self.ensure_one()
        
        if not self.lead_reminder_user_ids:
            _logger.info(f"部门 {self.name} 没有配置线索提醒人，跳过退回通知")
            return

        try:
            # 检查是否安装了微信公众号模块
            if 'wechat.template' not in self.env:
                _logger.warning("微信公众号模块未安装，无法发送线索退回通知")
                return

            # 获取已激活的微信配置
            wechat_config = self.env['wechat.config'].search([
                ('active', '=', True)
            ], limit=1)

            if not wechat_config:
                _logger.warning("没有找到激活的微信公众号配置，无法发送线索退回通知")
                return

            # 查找线索退回通知模板（模板名：用户报名申请已撤回通知）
            template = self.env['wechat.template'].search([
                ('name', '=', '用户报名申请已撤回通知'),
                ('active', '=', True)
            ], limit=1)

            if not template:
                _logger.warning("没有找到'用户报名申请已撤回通知'模板，无法发送线索退回通知")
                return

            # 构建跳转到线索详情的URL
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            lead_url = f"{base_url}/web#id={lead.id}&action=done_crm_upgrade.crm_lead_claim_action_teacher&model=crm.lead&view_type=form"

            # 为每个线索提醒人发送通知
            for reminder_user in self.lead_reminder_user_ids:
                self._send_single_lead_return_notification(
                    lead, returned_by_user, reminder_user, 
                    wechat_config, template, lead_url
                )

        except Exception as e:
            _logger.error(f"发送线索退回通知失败: {e}")

    def _send_single_lead_return_notification(self, lead, returned_by_user, reminder_user, 
                                            wechat_config, template, lead_url):
        """
        向单个线索提醒人发送退回通知
        
        Args:
            lead: 被退回的线索记录
            returned_by_user: 退回线索的用户
            reminder_user: 接收通知的提醒人
            wechat_config: 微信配置
            template: 消息模板
            lead_url: 线索详情URL
        """
        try:
            # 查找提醒人对应的微信用户
            reminder_wechat_user = self._find_user_wechat_account(reminder_user)

            if not reminder_wechat_user:
                _logger.info(f"线索提醒人 {reminder_user.name} 没有绑定微信账号，跳过通知")
                return

            # 准备模板数据 - 使用报名审批通知模板的参数
            import json
            template_data = {
                'thing2': {  # 用户名称（这里用退回人的名称）
                    'value': returned_by_user.name[:20] if returned_by_user.name else '未知用户',
                    'color': '#173177'
                },
                'time3': {  # 退回时间
                    'value': fields.Datetime.now().strftime('%Y年%m月%d日'),
                    'color': '#173177'
                },
                'thing1': {  # 项目名称（这里用线索信息）
                    'value': f"线索退回-{lead.customer_name or lead.name}"[:20],
                    'color': '#dc3545'  # 红色表示退回
                },
                'thing18': {  # 项目名称（这里用线索信息）
                    'value': f"线索退回-{lead.customer_name or lead.name}"[:20],
                    'color': '#dc3545'  # 红色表示退回
                }
            }

            # 创建并发送微信模板消息
            wechat_message = self.env['wechat.message'].create({
                'subject': f'线索退回通知 - {lead.customer_name or lead.name}',
                'message_type': 'template',
                'config_id': wechat_config.id,
                'recipient_type': 'single',
                'user_id': reminder_wechat_user.id,
                'template_id': template.id,
                'template_data': json.dumps(template_data, ensure_ascii=False),
                'url': lead_url,  # 跳转到线索详情页面
            })

            # 发送消息
            wechat_message.send_message()

            _logger.info(f"成功发送线索退回通知给 {reminder_user.name} (部门: {self.name})")

        except Exception as e:
            _logger.error(f"向 {reminder_user.name} 发送线索退回通知失败: {e}")

    def _find_user_wechat_account(self, user):
        """
        查找用户对应的微信账号
        
        Args:
            user: res.users记录
            
        Returns:
            wechat.user记录或None
        """
        # 方法1: 通过自动登录记录查找
        auto_login = self.env['wechat.auto.login'].search([
            ('user_id', '=', user.id),
            ('active', '=', True),
            ('enabled', '=', True)
        ], limit=1)

        if auto_login:
            wechat_user = self.env['wechat.user'].search([
                ('openid', '=', auto_login.openid),
                ('config_id', '=', auto_login.config_id.id),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法2: 通过手机号匹配查找
        if user.phone:
            wechat_user = self.env['wechat.user'].search([
                ('phone', '=', user.phone),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法3: 通过邮箱匹配查找
        if user.email:
            wechat_user = self.env['wechat.user'].search([
                ('email', '=', user.email),
                ('subscribe', '=', True)
            ], limit=1)
            if wechat_user:
                return wechat_user

        # 方法4: 通过用户ID直接匹配查找
        wechat_user = self.env['wechat.user'].search([
            ('user_id', '=', user.id),
            ('subscribe', '=', True)
        ], limit=1)
        if wechat_user:
            return wechat_user

        return None

    def action_view_lead_reminders(self):
        """查看线索提醒人的动作"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': f'{self.name} - 线索提醒人',
            'res_model': 'res.users',
            'view_mode': 'list,form',
            'domain': [('id', 'in', self.lead_reminder_user_ids.ids)],
            'context': {
                'default_active': True,
            }
        }
