# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
import logging
import requests
import json

_logger = logging.getLogger(__name__)


class HrAttendance(models.Model):
    _inherit = 'hr.attendance'

    # 添加位置信息字段
    location_info = fields.Text('位置信息', help='打卡时的位置信息')
    wifi_name = fields.Char('WiFi名称', help='打卡时连接的WiFi名称')
    ip_address = fields.Char('IP地址', help='打卡时的IP地址')
    device_info = fields.Text('设备信息', help='打卡设备信息')
    is_valid_location = fields.Bo<PERSON>an('位置有效', default=True, help='是否在有效打卡区域')

    @api.model
    def create(self, vals):
        """重写创建方法，添加位置验证"""
        # 如果有位置信息，进行验证
        if 'wifi_name' in vals or 'ip_address' in vals:
            if not self._validate_location(vals):
                raise ValidationError(_('您不在有效的打卡区域内，请确认您的位置和网络连接'))
        
        return super(HrAttendance, self).create(vals)

    def _validate_location(self, vals):
        """验证打卡位置是否有效"""
        # 获取允许的WiFi列表和IP范围
        allowed_wifi = self.env['ir.config_parameter'].sudo().get_param('hr_attendance.allowed_wifi', '')
        allowed_ips = self.env['ir.config_parameter'].sudo().get_param('hr_attendance.allowed_ips', '')
        
        wifi_name = vals.get('wifi_name', '')
        ip_address = vals.get('ip_address', '')
        
        # 检查WiFi
        if allowed_wifi and wifi_name:
            wifi_list = [w.strip() for w in allowed_wifi.split(',')]
            if wifi_name not in wifi_list:
                return False
        
        # 检查IP范围
        if allowed_ips and ip_address:
            ip_list = [ip.strip() for ip in allowed_ips.split(',')]
            if not any(ip_address.startswith(ip_prefix) for ip_prefix in ip_list):
                return False
        
        return True


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # 添加考勤相关字段
    attendance_location_required = fields.Boolean('需要位置验证', default=True, help='该员工打卡是否需要位置验证')
    allowed_wifi_override = fields.Text('个人允许WiFi', help='该员工特殊允许的WiFi列表，用逗号分隔')

    def attendance_manual(self, next_action, entered_pin=None):
        """重写手动考勤方法，添加位置检查"""
        # 获取前端传递的位置信息
        context = self.env.context
        location_data = {
            'wifi_name': context.get('wifi_name', ''),
            'ip_address': context.get('ip_address', ''),
            'location_info': context.get('location_info', ''),
            'device_info': context.get('device_info', ''),
        }
        
        # 如果需要位置验证
        if self.attendance_location_required:
            attendance_obj = self.env['hr.attendance']
            if not attendance_obj._validate_location(location_data):
                raise ValidationError(_('您不在有效的打卡区域内，请确认您的位置和网络连接'))
        
        # 调用原始方法
        result = super(HrEmployee, self).attendance_manual(next_action, entered_pin)
        
        # 如果成功创建考勤记录，更新位置信息
        if result and isinstance(result, dict) and result.get('attendance'):
            attendance_id = result['attendance'].get('id')
            if attendance_id:
                attendance = self.env['hr.attendance'].browse(attendance_id)
                attendance.write(location_data)
        
        return result


class HrAttendanceConfig(models.TransientModel):
    """考勤配置"""
    _name = 'hr.attendance.config'
    _description = '考勤位置配置'

    allowed_wifi = fields.Text('允许的WiFi列表', help='允许打卡的WiFi名称，用逗号分隔')
    allowed_ips = fields.Text('允许的IP范围', help='允许打卡的IP地址前缀，用逗号分隔')
    location_check_enabled = fields.Boolean('启用位置检查', default=True)

    @api.model
    def get_values(self):
        res = super(HrAttendanceConfig, self).get_values()
        params = self.env['ir.config_parameter'].sudo()
        res.update(
            allowed_wifi=params.get_param('hr_attendance.allowed_wifi', ''),
            allowed_ips=params.get_param('hr_attendance.allowed_ips', ''),
            location_check_enabled=params.get_param('hr_attendance.location_check_enabled', True),
        )
        return res

    def set_values(self):
        super(HrAttendanceConfig, self).set_values()
        params = self.env['ir.config_parameter'].sudo()
        params.set_param('hr_attendance.allowed_wifi', self.allowed_wifi or '')
        params.set_param('hr_attendance.allowed_ips', self.allowed_ips or '')
        params.set_param('hr_attendance.location_check_enabled', self.location_check_enabled)
