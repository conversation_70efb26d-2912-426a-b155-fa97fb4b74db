# -*- coding: utf-8 -*-

from odoo import api, fields, models


class CrmImprovementDemand(models.Model):
    _name = 'crm.improvement.demand'
    _description = '客户提升需求'
    _order = 'sequence, name'

    name = fields.Char('需求名称', required=True, translate=True)
    code = fields.Char('代码', help='需求代码，用于系统识别')
    sequence = fields.Integer('排序', default=10)
    active = fields.<PERSON><PERSON>an('启用', default=True)
    description = fields.Text('描述')
    color = fields.Integer('颜色', help='在界面中显示的颜色')

    _sql_constraints = [
        ('code_unique', 'unique(code)', '需求代码必须唯一！'),
    ]
