
# 线索页面 新增菜单
重构生成线索的form页面，创建时打开的内容如下。

```
线索添加字段 
客户名
客户手机
客户微信/QQ
上游老师   ---- 员工 默认就是填写者
客户来源   ---- 字符串
招生老师组别 ---- （单证类、双证类、其他） 创建组别表，关联
用户备注

手机号/微信号 重复 校验
```

![alt text](9b2f09adb0c249737f17069896c9dd5-1.jpg)



# 详细资料 
```
评分  1-5分 整数
是否要求QS   是、否、未知
客户性别     男、女、未知
是否同行     是、否、未知
资金预算     xxx元
客户年龄     整数
最高学历     小学、初中、高中、大专、本科、硕士、博士、未知
语言能力     字符串
提升需求



```

![alt text](998cdfd61347cd9d3cf1528946dbd9f.jpg)


# 


# crm 添加 状态
待分配、已分配、已认领、已锁定、已关闭
1 待分配的会被定时分配脚本 按照权重分配逻辑自动分配给老师 并将状态改为 已分配
2 已分配/待认领 状态下 对应老师可以认领线索，认领后状态改为 已认领
3 已认领状态下，老师可以锁定线索，锁定后状态改为 已锁定
4 已锁定状态下，老师可以关闭线索，关闭后状态改为 已关闭
5 老师也可以将 已分配、已认领、已锁定 的线索返回到 待分配 状态，由定时脚本重新分配

# 线索分配
## 1 教师加权评分表
crm_teacher_weight 权重表
字段
招生老师组别  员工  权重 附加分 成交率分   成交速度分   KPI分  性别匹配分   金额匹配分   合计分值 

成交率=本老师成交客户数/本老师领取客户线索总数

成交速度是指一个招生老师的平均成交速度=（第1个成交客户时间-第n个客户领取时间）+。。。（第n个客户成交时间-第n个客户领取时间）/ 本老师成交客户总数

## 2 自动分配
线索增加自动分配按钮 auto_distribute 
逻辑暂时 pass
