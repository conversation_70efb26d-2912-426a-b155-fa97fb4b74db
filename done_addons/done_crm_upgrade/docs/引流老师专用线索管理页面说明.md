# 引流老师专用线索管理页面开发说明

## 概述

为引流老师权限组 (`group_done_crm_traffic_teacher`) 单独开发了专用的线索管理页面，实现了在非待分配阶段所有字段都是只读的功能需求。

## 新增组件

### 1. 引流老师专用表单视图 (`crm_lead_traffic_teacher_form_view`)

**特性：**
- 基于现有的 `crm_lead_view_form_done` 视图设计
- 使用条件只读逻辑：`readonly="lead_state not in ['draft', 'to_allocate']"`
- 只显示引流老师相关的操作按钮（提交按钮）
- 保留完整的字段结构和布局

**条件只读字段：**
- **客户信息组：**
  - `customer_name` - 客户名
  - `customer_mobile` - 客户手机
  - `customer_wechat_qq` - 客户微信/QQ
  
- **业务信息组：**
  - `customer_source` - 客户来源
  - `teacher_group_id` - 招生老师组别
  
- **备注信息：**
  - `customer_remark` - 客户备注
  
- **详细资料页面：**
  - `customer_score` - 客户评分
  - `customer_gender` - 客户性别
  - `is_travelling` - 是否出行
  - `highest_education` - 最高学历
  - `improvement_demand` - 提升需求
  - `is_abroad` - 是否出国
  - `intention_country` - 意向国家
  - `is_require_qs` - 是否要求QS
  - `budget` - 预算
  - `customer_age` - 客户年龄
  - `language_ability` - 语言能力
  - `work_nature` - 工作性质
  - `business_type` - 业务类型
  - `major_requirement` - 专业要求

**始终只读字段：**
- `name` - 线索编号
- `upstream_teacher_user_id` - 上游老师
- `claim_teacher_user_id` - 认领老师
- `ip_country` - IP国家
- `customer_score_sys` - 系统评分

### 2. 引流老师专用列表视图 (`crm_lead_traffic_teacher_list_view`)

**特性：**
- 专门为引流老师设计的列表视图
- 显示关键字段：线索编号、客户名、手机、来源、组别、状态等
- 使用颜色装饰区分不同状态：
  - 草稿状态：蓝色 (`decoration-info`)
  - 待分配状态：橙色 (`decoration-warning`)
  - 其他状态：灰色 (`decoration-muted`)

### 3. 引流老师专用动作 (`crm_lead_traffic_teacher_action`)

**配置：**
- 名称：引流老师-线索管理
- 视图模式：列表视图 + 表单视图
- 数据过滤：只显示当前用户创建的线索 (`domain="[('upstream_teacher_user_id', '=', uid)]"`)
- 默认上下文：自动设置上游老师为当前用户

### 4. 引流老师专用菜单 (`menu_crm_lead_traffic_teacher`)

**配置：**
- 菜单名称：引流老师-线索管理
- 父菜单：线索管理中心
- 权限组：仅 `group_done_crm_traffic_teacher` 可见
- 序列号：3（优先显示）

## 业务逻辑

### 字段编辑权限控制

```xml
readonly="lead_state not in ['draft', 'to_allocate']"
```

**可编辑状态：**
- `draft` - 草稿状态
- `to_allocate` - 待分配状态

**只读状态：**
- `to_claim` - 已分配/待认领
- `to_score` - 待评分
- `locked` - 已锁定
- `done` - 完成
- `closed` - 关闭

### 操作按钮控制

引流老师只能看到和使用：
- **提交按钮** - 仅在草稿状态可见
- **状态栏** - 显示当前线索状态

不显示其他管理员或招生老师的操作按钮（分配、认领、评分等）。

## 数据安全

### 访问控制
- 通过现有的数据访问规则 `crm_lead_traffic_teacher_rule` 确保引流老师只能访问自己创建的线索
- 通过状态限制确保引流老师只能在特定状态下编辑线索

### 权限隔离
- 专用菜单只对引流老师权限组可见
- 专用动作自动过滤数据范围
- 专用视图限制操作权限

## 用户体验

### 视觉反馈
- 列表视图使用颜色装饰区分线索状态
- 表单视图保持一致的布局和字段组织
- 状态栏清晰显示当前线索流程位置

### 操作流程
1. **创建线索** → 状态：草稿 → 所有字段可编辑
2. **提交线索** → 状态：待分配 → 所有字段可编辑
3. **线索被分配** → 状态：已分配/待认领 → 所有字段只读
4. **后续状态** → 所有字段只读，仅可查看

## 技术实现

### 文件修改
- `views/crm_lead_views.xml` - 添加专用视图和动作
- `views/crm_menu_views.xml` - 添加专用菜单

### 代码特点
- 保留现有代码结构，不影响其他功能
- 使用Odoo 18标准语法和最佳实践
- 遵循Context7推荐的条件只读实现方式

## 安装和使用

### 升级模块
```bash
# 重启Odoo服务后升级模块
-u done_crm_upgrade
```

### 用户配置
1. 为相关用户分配 `线索-引流老师` 权限组
2. 用户登录后可在CRM菜单中看到 `引流老师-线索管理` 菜单项
3. 点击菜单进入专用的线索管理界面

## 注意事项

- 引流老师创建的线索一旦被分配，将无法再编辑，只能查看
- 专用页面与现有的线索管理功能完全独立，不会相互影响
- 所有数据访问都受到严格的权限控制和状态限制
