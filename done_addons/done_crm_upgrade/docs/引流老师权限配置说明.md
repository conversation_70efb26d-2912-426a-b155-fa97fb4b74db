# 引流老师权限配置说明

## 概述

为 `group_done_crm_traffic_teacher`（线索-引流老师）权限组增加了菜单权限和数据访问控制，确保引流老师能够：

1. **创建线索** - 通过"新增线索"菜单
2. **查看线索管理** - 但只能看到自己创建的线索，且仅在未分配状态下可见
3. **访问相关配置** - 只读权限访问必要的配置数据

## 权限配置详情

### 1. 权限组定义 (`security/done_crm_groups.xml`)

```xml
<!-- 线索-引流老师权限组 -->
<record id="group_done_crm_traffic_teacher" model="res.groups">
    <field name="name">线索-引流老师</field>
    <field name="category_id" ref="module_category_done_crm"/>
    <field name="comment">引流老师权限：仅能创建线索，分配后的线索无法查看修改；仅有线索管理和创建新线索按钮</field>
    <!-- 继承基础CRM权限以访问CRM菜单 -->
    <field name="implied_ids" eval="[(4, ref('crm.group_use_lead'))]"/>
</record>
```

### 2. 数据访问规则

```xml
<!-- 引流老师只能查看自己创建的线索，且只能在未分配状态下查看 -->
<record id="crm_lead_traffic_teacher_rule" model="ir.rule">
    <field name="name">引流老师访问规则</field>
    <field name="model_id" ref="crm.model_crm_lead"/>
    <field name="domain_force">[
        ('upstream_teacher_user_id', '=', user.id),
        ('lead_state', 'in', ['draft', 'to_allocate'])
    ]</field>
    <field name="groups" eval="[(4, ref('group_done_crm_traffic_teacher'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="True"/>
    <field name="perm_unlink" eval="False"/>
</record>
```

**访问限制说明：**
- 只能查看 `upstream_teacher_user_id` 等于当前用户的线索
- 只能查看状态为 `draft`（草稿）或 `to_allocate`（待分配）的线索
- 一旦线索被分配（状态变为 `to_claim` 或其他），引流老师将无法再查看

### 3. 菜单权限 (`views/crm_menu_views.xml`)

为以下菜单添加了引流老师权限组：

1. **线索管理中心** (根菜单)
2. **新增线索** 
3. **线索管理**

```xml
groups="done_crm_upgrade.group_done_crm_traffic_teacher,done_crm_upgrade.group_done_crm_user,done_crm_upgrade.group_done_crm_manager"
```

### 4. 模型访问权限 (`security/ir.model.access.csv`)

为引流老师权限组添加了以下模型的只读访问权限：

- `teacher.group` - 招生老师组别（只读）
- `crm.teacher.weight` - 教师权重配置（只读）
- `crm.improvement.demand` - 客户提升需求（只读）
- `done.crm.config` - 系统配置（只读）

## 功能特性

### ✅ 引流老师可以：
- 访问CRM模块和线索管理中心
- 创建新线索（通过"新增线索"菜单）
- 查看和编辑自己创建的线索（仅在草稿和待分配状态）
- 查看相关的配置信息（只读）

### ❌ 引流老师不能：
- 查看其他人创建的线索
- 查看已分配给其他老师的线索（包括自己创建的）
- 删除线索
- 修改系统配置
- 访问权重管理等高级功能

## 业务流程

1. **引流老师创建线索** → 状态：`draft`
2. **提交线索** → 状态：`to_allocate`
3. **系统自动分配或管理员手动分配** → 状态：`to_claim`
4. **线索被分配后，引流老师无法再查看该线索**

这样确保了引流老师专注于线索创建工作，分配后的线索由对应的招生老师处理，避免了权限混乱和数据安全问题。

## 安装和升级

配置更改后需要：
1. 重启Odoo服务
2. 升级 `done_crm_upgrade` 模块
3. 为相关用户分配 `线索-引流老师` 权限组

## 注意事项

- 引流老师权限组继承了 `crm.group_use_lead` 权限，确保能够访问基础CRM功能
- 数据访问规则确保了严格的数据隔离
- 所有配置都遵循Odoo 18的最佳实践
