# 部门线索提醒功能说明

## 功能概述

本功能为HR部门模块增加了"线索提醒人"配置，当部门员工的线索被退回时，会自动向该部门配置的线索提醒人发送微信通知。

## 主要特性

### 1. 部门线索提醒人配置
- **字段名称**: `lead_reminder_user_ids`
- **字段类型**: Many2many关联res.users
- **功能**: 可以为每个部门配置多个线索提醒人
- **权限**: 部门管理员和HR管理员可以配置

### 2. 自动微信通知
- **触发条件**: 当线索被退回（action_claim_back）时
- **通知对象**: 退回老师所在部门的所有线索提醒人
- **通知方式**: 微信模板消息
- **消息模板**: 复用"报名审批通知"模板

### 3. 智能匹配微信账号
系统会通过以下方式查找用户的微信账号：
1. 通过自动登录记录匹配
2. 通过手机号匹配
3. 通过邮箱匹配
4. 通过用户ID直接匹配

## 使用方法

### 配置线索提醒人

1. **进入部门管理**
   - 导航到：人力资源 → 部门
   - 选择要配置的部门

2. **配置提醒人**
   - 在部门表单中找到"线索管理配置"区域
   - 在"线索提醒人"字段中选择用户
   - 可以选择多个用户
   - 保存配置

3. **查看配置结果**
   - 部门列表中会显示"提醒人数"列
   - 部门看板中会显示提醒人数量徽章
   - 部门表单中有智能按钮显示提醒人详情

### 线索退回流程

1. **老师退回线索**
   - 老师在线索认领页面点击"退回"按钮
   - 系统执行退回操作

2. **自动发送通知**
   - 系统查找退回老师所在部门
   - 获取该部门配置的线索提醒人
   - 向每个提醒人发送微信通知

3. **通知内容**
   - 退回人姓名
   - 退回时间
   - 线索信息
   - 跳转链接（指向线索详情）

## 技术实现

### 数据库结构

```sql
-- 部门线索提醒人关联表
CREATE TABLE hr_department_lead_reminder_rel (
    department_id INTEGER REFERENCES hr_department(id),
    user_id INTEGER REFERENCES res_users(id),
    PRIMARY KEY (department_id, user_id)
);
```

### 主要方法

1. **hr.department.send_lead_return_notification()**
   - 发送线索退回通知给部门提醒人

2. **crm.lead._send_lead_return_notification_to_department()**
   - 查找退回老师部门并发送通知

3. **hr.department._find_user_wechat_account()**
   - 查找用户对应的微信账号

### 配置要求

1. **微信公众号模块**
   - 需要安装wechat_official_account模块
   - 配置微信公众号参数
   - 创建"报名审批通知"模板

2. **用户微信绑定**
   - 线索提醒人需要绑定微信账号
   - 支持多种绑定方式（手机号、邮箱等）

## 权限说明

- **查看权限**: 所有用户可以查看部门的线索提醒人配置
- **编辑权限**: 部门管理员和HR管理员可以配置线索提醒人
- **通知权限**: 系统自动发送，无需特殊权限

## 日志记录

系统会记录以下操作日志：
- 线索退回操作
- 微信通知发送成功/失败
- 用户微信账号查找结果
- 部门配置变更

## 故障排除

### 常见问题

1. **收不到微信通知**
   - 检查微信公众号配置是否正确
   - 确认用户已绑定微信账号
   - 查看系统日志中的错误信息

2. **部门配置不生效**
   - 确认退回老师有对应的员工记录
   - 确认员工记录关联了正确的部门
   - 检查部门是否配置了线索提醒人

3. **权限问题**
   - 确认当前用户有部门管理权限
   - 检查用户组配置是否正确

### 调试方法

1. **查看日志**
   ```bash
   # 查看Odoo日志
   tail -f /var/log/odoo/odoo.log | grep "线索退回"
   ```

2. **测试微信通知**
   - 在notification_test模块中测试微信发送功能
   - 确认微信模板配置正确

## 扩展建议

1. **通知模板定制**
   - 可以创建专门的线索退回通知模板
   - 支持更丰富的消息内容

2. **通知方式扩展**
   - 支持邮件通知
   - 支持短信通知
   - 支持系统内消息

3. **统计分析**
   - 添加线索退回统计
   - 部门线索处理效率分析
   - 提醒人响应时间统计

## 线索分配方法说明

### 分配方法对比

1. **action_allocate()** - 标准分配方法
   - 打开分配向导
   - 用户手动选择老师
   - 支持批量分配
   - 通过向导发送微信通知

2. **action_quick_allocate(teacher_id)** - 快速分配方法
   - 直接分配给指定老师
   - 跳过向导界面
   - 自动发送微信通知
   - 适合程序化调用

3. **to_action_allocate(teacher_id)** - 底层分配方法
   - 最基础的分配逻辑
   - 直接修改数据库
   - 发送微信通知
   - 被其他方法调用

### 使用示例

```python
# 方法1：标准分配（打开向导）
leads = self.env['crm.lead'].search([('lead_state', '=', 'to_allocate')])
leads.action_allocate()

# 方法2：快速分配给指定老师
teacher_id = 123  # 老师用户ID
leads.action_quick_allocate(teacher_id)

# 方法3：带参数的标准分配（预填老师）
leads.action_allocate(teacher_id=teacher_id, skip_wizard=True)

# 方法4：底层分配（不推荐直接使用）
leads.to_action_allocate(teacher_id)
```

### 微信通知确认

所有分配方法都会自动发送微信模板消息通知：
- ✅ **action_allocate()** → 通过向导 → 发送微信模板消息
- ✅ **action_quick_allocate()** → 直接调用 → 发送微信模板消息
- ✅ **to_action_allocate()** → 底层方法 → 发送微信模板消息

### 微信通知升级说明

#### 升级内容
1. **消息类型升级**: 从文本消息升级为模板消息
2. **模板使用**: 统一使用"报名审批通知"模板
3. **跳转优化**: 单个线索支持直接认领跳转

#### 模板消息参数映射
- **thing18**: 客户姓名（单个线索）或批量分配信息
- **time3**: 线索创建时间或分配时间
- **thing21**: 业务类型/线索编号或批量数量信息

#### 跳转URL策略
- **单个线索**: `/crm/lead/auto_claim/{lead_id}` - 直接认领并跳转详情
- **多个线索**: `/web#action=crm_lead_claim_action_teacher` - 跳转认领列表

#### 优势对比
| 特性 | 文本消息 | 模板消息 |
|------|----------|----------|
| 样式 | 普通文本 | 结构化展示 |
| 跳转 | 支持 | 支持 |
| 审核 | 容易被限制 | 官方认证 |
| 送达率 | 较低 | 较高 |
| 用户体验 | 一般 | 专业 |
