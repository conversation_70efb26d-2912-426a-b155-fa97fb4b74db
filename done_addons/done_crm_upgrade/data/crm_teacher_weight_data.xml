<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 示例教师权重配置数据 -->
        
        <!-- 注意：这些数据需要在有实际员工和招生老师组别数据后才能正常加载 -->
        <!-- 可以在系统运行后手动创建，或者根据实际的员工数据来配置 -->
        
        <!-- 示例配置模板，实际使用时需要替换为真实的员工ID -->
        <!--
        <record id="teacher_weight_example_1" model="crm.teacher.weight">
            <field name="teacher_group_id" ref="teacher_group_sales_1"/>
            <field name="employee_id" ref="hr.employee_example_1"/>
            <field name="weight">1.0</field>
            <field name="additional_score">2.0</field>
            <field name="deal_rate_score">3.0</field>
            <field name="deal_speed_score">2.5</field>
            <field name="kpi_score">4.0</field>
            <field name="gender_match_score">1.0</field>
            <field name="amount_match_score">1.5</field>
            <field name="notes">优秀销售员工，成交率高</field>
        </record>
        
        <record id="teacher_weight_example_2" model="crm.teacher.weight">
            <field name="teacher_group_id" ref="teacher_group_sales_1"/>
            <field name="employee_id" ref="hr.employee_example_2"/>
            <field name="weight">1.0</field>
            <field name="additional_score">1.0</field>
            <field name="deal_rate_score">2.0</field>
            <field name="deal_speed_score">3.0</field>
            <field name="kpi_score">3.0</field>
            <field name="gender_match_score">2.0</field>
            <field name="amount_match_score">1.0</field>
            <field name="notes">新员工，成交速度快</field>
        </record>
        -->
        
    </data>
</odoo>
