<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 初始化招生老师组别数据 -->
        <record id="teacher_group_single" model="teacher.group">
            <field name="name">单证类</field>
            <field name="code">single</field>
            <field name="sequence">10</field>
            <field name="description">单证类招生老师组别</field>
        </record>

        <record id="teacher_group_double" model="teacher.group">
            <field name="name">双证类</field>
            <field name="code">double</field>
            <field name="sequence">20</field>
            <field name="description">双证类招生老师组别</field>
        </record>

        <record id="teacher_group_other" model="teacher.group">
            <field name="name">其他</field>
            <field name="code">other</field>
            <field name="sequence">30</field>
            <field name="description">其他类型招生老师组别</field>
        </record>
    </data>
</odoo>
