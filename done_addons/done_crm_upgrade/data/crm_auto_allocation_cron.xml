<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- CRM线索自动分配定时任务 -->
        <record id="cron_crm_auto_allocation" model="ir.cron">
            <field name="name">CRM线索自动分配</field>
            <field name="model_id" ref="model_crm_auto_allocation_service"/>
            <field name="state">code</field>
            <field name="code">model.auto_allocate_leads()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_admin"/>
        </record>
        
    </data>
</odoo>
