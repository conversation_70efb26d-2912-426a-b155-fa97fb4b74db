<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- 默认配置参数：分配模式 -->
        <record id="config_distribution_mode" model="done.crm.config">
            <field name="name">distribution_mode</field>
            <field name="value">1</field>
            <field name="description">1为经典随机分配模式 2 为自定义高级模式</field>
            <field name="category">distribution</field>
            <field name="data_type">selection</field>
            <field name="selection_options">1:经典随机分配模式,2:自定义高级模式</field>
            <field name="sequence">10</field>
            <field name="is_system">True</field>
            <field name="is_required">True</field>
            <field name="active">True</field>
        </record>

<!--        默认参数，线索认领超时时间数 unclaim_timeout_minutes-->
        <record id="config_unclaim_timeout_minutes" model="done.crm.config">
            <field name="name">unclaim_timeout_minutes</field>
            <field name="value">30</field>
            <field name="description">长时间未认领的线索把状态改回待分配的超时分钟数</field>
            <field name="category">distribution</field>
            <field name="data_type">integer</field>
            <field name="sequence">20</field>
            <field name="is_system">True</field>
            <field name="min_value">1</field>
            <field name="max_value">7200</field>
            <field name="active">True</field>
        </record>

        <!-- 其他系统配置参数 -->
        <record id="config_max_leads_per_teacher" model="done.crm.config">
            <field name="name">max_leads_per_teacher</field>
            <field name="value">50</field>
            <field name="description">每个老师最大线索数量限制</field>
            <field name="category">distribution</field>
            <field name="data_type">integer</field>
            <field name="sequence">20</field>
            <field name="is_system">True</field>
            <field name="min_value">1</field>
            <field name="max_value">1000</field>
            <field name="active">True</field>
        </record>

        <record id="config_auto_claim_timeout" model="done.crm.config">
            <field name="name">auto_claim_timeout</field>
            <field name="value">24</field>
            <field name="description">自动认领超时时间（小时），超过此时间未认领的线索将自动退回</field>
            <field name="category">distribution</field>
            <field name="data_type">integer</field>
            <field name="sequence">30</field>
            <field name="is_system">True</field>
            <field name="min_value">1</field>
            <field name="max_value">168</field>
            <field name="active">True</field>
        </record>

        <record id="config_scoring_enabled" model="done.crm.config">
            <field name="name">scoring_enabled</field>
            <field name="value">true</field>
            <field name="description">是否启用线索评分功能</field>
            <field name="category">scoring</field>
            <field name="data_type">boolean</field>
            <field name="sequence">10</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_min_score_threshold" model="done.crm.config">
            <field name="name">min_score_threshold</field>
            <field name="value">3</field>
            <field name="description">最低评分阈值，低于此分数的线索需要特殊处理</field>
            <field name="category">scoring</field>
            <field name="data_type">integer</field>
            <field name="sequence">20</field>
            <field name="is_system">True</field>
            <field name="min_value">1</field>
            <field name="max_value">5</field>
            <field name="active">True</field>
        </record>

        <record id="config_notification_enabled" model="done.crm.config">
            <field name="name">notification_enabled</field>
            <field name="value">true</field>
            <field name="description">是否启用系统通知功能</field>
            <field name="category">notification</field>
            <field name="data_type">boolean</field>
            <field name="sequence">10</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_email_notification" model="done.crm.config">
            <field name="name">email_notification</field>
            <field name="value">false</field>
            <field name="description">是否启用邮件通知</field>
            <field name="category">notification</field>
            <field name="data_type">boolean</field>
            <field name="sequence">20</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_sms_notification" model="done.crm.config">
            <field name="name">sms_notification</field>
            <field name="value">false</field>
            <field name="description">是否启用短信通知</field>
            <field name="category">notification</field>
            <field name="data_type">boolean</field>
            <field name="sequence">30</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_weight_calculation_mode" model="done.crm.config">
            <field name="name">weight_calculation_mode</field>
            <field name="value">standard</field>
            <field name="description">权重计算模式</field>
            <field name="category">distribution</field>
            <field name="data_type">selection</field>
            <field name="selection_options">standard:标准模式,advanced:高级模式,custom:自定义模式</field>
            <field name="sequence">40</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_lead_expiry_days" model="done.crm.config">
            <field name="name">lead_expiry_days</field>
            <field name="value">30</field>
            <field name="description">线索过期天数，超过此天数的线索将被标记为过期</field>
            <field name="category">system</field>
            <field name="data_type">integer</field>
            <field name="sequence">10</field>
            <field name="is_system">True</field>
            <field name="min_value">1</field>
            <field name="max_value">365</field>
            <field name="active">True</field>
        </record>

        <record id="config_backup_enabled" model="done.crm.config">
            <field name="name">backup_enabled</field>
            <field name="value">true</field>
            <field name="description">是否启用数据备份功能</field>
            <field name="category">system</field>
            <field name="data_type">boolean</field>
            <field name="sequence">20</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_debug_mode" model="done.crm.config">
            <field name="name">debug_mode</field>
            <field name="value">false</field>
            <field name="description">是否启用调试模式</field>
            <field name="category">system</field>
            <field name="data_type">boolean</field>
            <field name="sequence">30</field>
            <field name="is_system">True</field>
            <field name="active">True</field>
        </record>

        <record id="config_api_rate_limit" model="done.crm.config">
            <field name="name">api_rate_limit</field>
            <field name="value">1000</field>
            <field name="description">API请求频率限制（每小时）</field>
            <field name="category">integration</field>
            <field name="data_type">integer</field>
            <field name="sequence">10</field>
            <field name="is_system">True</field>
            <field name="min_value">100</field>
            <field name="max_value">10000</field>
            <field name="active">True</field>
        </record>

        <record id="config_webhook_timeout" model="done.crm.config">
            <field name="name">webhook_timeout</field>
            <field name="value">30</field>
            <field name="description">Webhook请求超时时间（秒）</field>
            <field name="category">integration</field>
            <field name="data_type">integer</field>
            <field name="sequence">20</field>
            <field name="is_system">True</field>
            <field name="min_value">5</field>
            <field name="max_value">300</field>
            <field name="active">True</field>
        </record>

    </data>
</odoo>
