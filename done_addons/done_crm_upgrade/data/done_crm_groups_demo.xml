<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 演示用户和权限组分配 -->
        
        <!-- 创建演示用户 - 线索用户 -->
        <record id="demo_user_crm_user" model="res.users">
            <field name="name">张老师（线索用户）</field>
            <field name="login">crm_user_demo</field>
            <field name="email"><EMAIL></field>
            <field name="groups_id" eval="[(4, ref('group_done_crm_user'))]"/>
            <field name="active" eval="True"/>
        </record>

        <!-- 创建演示用户 - 线索管理员 -->
        <record id="demo_user_crm_manager" model="res.users">
            <field name="name">李主管（线索管理员）</field>
            <field name="login">crm_manager_demo</field>
            <field name="email"><EMAIL></field>
            <field name="groups_id" eval="[(4, ref('group_done_crm_manager'))]"/>
            <field name="active" eval="True"/>
        </record>

        <!-- 创建演示用户 - 线索总监 -->
        <record id="demo_user_crm_director" model="res.users">
            <field name="name">王总监（线索总监）</field>
            <field name="login">crm_director_demo</field>
            <field name="email"><EMAIL></field>
            <field name="groups_id" eval="[(4, ref('group_done_crm_director'))]"/>
            <field name="active" eval="True"/>
        </record>

        <!-- 创建对应的员工记录 -->
        <record id="demo_employee_crm_user" model="hr.employee">
            <field name="name">张老师</field>
            <field name="user_id" ref="demo_user_crm_user"/>
            <field name="teacher_group_id" ref="teacher_group_single"/>
            <field name="work_email"><EMAIL></field>
        </record>

        <record id="demo_employee_crm_manager" model="hr.employee">
            <field name="name">李主管</field>
            <field name="user_id" ref="demo_user_crm_manager"/>
            <field name="work_email"><EMAIL></field>
        </record>

        <record id="demo_employee_crm_director" model="hr.employee">
            <field name="name">王总监</field>
            <field name="user_id" ref="demo_user_crm_director"/>
            <field name="work_email"><EMAIL></field>
        </record>

        <!-- 创建演示线索数据 -->
        <record id="demo_lead_1" model="crm.lead">
            <field name="name">演示线索001</field>
            <field name="customer_name">客户A</field>
            <field name="customer_mobile">13800138001</field>
            <field name="teacher_group_id" ref="teacher_group_single"/>
            <field name="upstream_teacher_user_id" ref="demo_user_crm_user"/>
            <field name="lead_state">to_allocate</field>
            <field name="type">lead</field>
        </record>

        <record id="demo_lead_2" model="crm.lead">
            <field name="name">演示线索002</field>
            <field name="customer_name">客户B</field>
            <field name="customer_mobile">13800138002</field>
            <field name="teacher_group_id" ref="teacher_group_double"/>
            <field name="claim_teacher_user_id" ref="demo_user_crm_user"/>
            <field name="lead_state">to_claim</field>
            <field name="type">lead</field>
        </record>

    </data>
</odoo>
