/* 移动端按钮样式 */
@media (max-width: 767.98px) {
    /* 移动端按钮容器 */
    .mobile-buttons-container {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    /* 移动端按钮样式 */
    .mobile-btn {
        width: 100%;
        padding: 12px 20px;
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        border: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 48px; /* 确保触摸友好的最小高度 */
    }
    
    /* 按钮图标间距 */
    .mobile-btn i {
        margin-right: 8px;
    }
    
    /* 主要操作按钮 */
    .mobile-btn-primary {
        background-color: #007bff;
        color: white;
    }
    
    .mobile-btn-primary:hover {
        background-color: #0056b3;
        color: white;
    }
    
    /* 成功按钮 */
    .mobile-btn-success {
        background-color: #28a745;
        color: white;
    }
    
    .mobile-btn-success:hover {
        background-color: #1e7e34;
        color: white;
    }
    
    /* 次要按钮 */
    .mobile-btn-secondary {
        background-color: #6c757d;
        color: white;
    }
    
    .mobile-btn-secondary:hover {
        background-color: #545b62;
        color: white;
    }
    
    /* 隐藏桌面端按钮在移动端 */
    .o_form_view .o_form_statusbar button {
        display: none !important;
    }
}

/* 桌面端隐藏移动端按钮 */
@media (min-width: 768px) {
    .mobile-buttons-container {
        display: none !important;
    }
}

/* 确保状态徽章在移动端正确显示 */
@media (max-width: 767.98px) {
    .o_form_statusbar .badge {
        font-size: 14px;
        padding: 8px 12px;
    }
}
