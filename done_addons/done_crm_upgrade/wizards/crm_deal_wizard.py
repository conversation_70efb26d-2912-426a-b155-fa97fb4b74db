# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class CrmDealWizard(models.TransientModel):
    """CRM成交向导"""
    _name = 'crm.deal.wizard'
    _description = 'CRM Deal Wizard'

    # 基本信息
    lead_id = fields.Many2one('crm.lead', string='线索', required=True, readonly=True)
    lead_name = fields.Char('线索名称', related='lead_id.name', readonly=True)
    customer_name = fields.Char('客户姓名', related='lead_id.customer_name', readonly=True)
    claim_teacher_name = fields.Char('认领老师', related='lead_id.claim_teacher_user_id.name', readonly=True)
    
    # 成交信息
    contract_amount = fields.Float('签约合同金额（万元）', required=True,
                                  help='请填写客户签约的合同金额，单位：万元')
    deal_notes = fields.Text('成交备注', help='可以填写成交相关的备注信息')
    
    # 确认信息
    confirm_deal = fields.Boolean('确认成交', default=False, 
                                 help='请确认该线索已成交')

    @api.constrains('contract_amount')
    def _check_contract_amount(self):
        """验证签约合同金额"""
        for wizard in self:
            if wizard.contract_amount <= 0:
                raise ValidationError(_('签约合同金额必须大于0'))
            if wizard.contract_amount > 1000:
                raise ValidationError(_('签约合同金额不能超过1000万元，请检查输入'))

    @api.model
    def default_get(self, fields_list):
        """设置默认值"""
        res = super(CrmDealWizard, self).default_get(fields_list)

        # 从上下文获取线索ID
        lead_id = self.env.context.get('active_id')
        if lead_id:
            res['lead_id'] = lead_id

            # 获取线索记录并自动带出合同金额
            lead = self.env['crm.lead'].browse(lead_id)
            if lead.exists():
                # 如果线索已有合同金额，自动带出
                if lead.contract_amount:
                    res['contract_amount'] = lead.contract_amount

        return res

    def action_confirm_deal(self):
        """确认成交"""
        self.ensure_one()
        
        if not self.confirm_deal:
            raise UserError(_('请勾选"确认成交"复选框'))
        
        if not self.contract_amount:
            raise UserError(_('请填写签约合同金额'))

        # 更新线索信息
        self.lead_id.write({
            'contract_amount': self.contract_amount,
            'lead_state': 'done',
        })
        
        # 如果有备注，添加到线索的消息中
        if self.deal_notes:
            self.lead_id.message_post(
                body=_('成交备注：%s') % self.deal_notes,
                message_type='comment',
                subtype_xmlid='mail.mt_note'
            )
        
        # 更新成交率权重
        teacher_weight = self.env['crm.teacher.weight'].search([
            ('user_id', '=', self.lead_id.claim_teacher_user_id.id)
        ], limit=1)
        if teacher_weight:
            teacher_weight._calculate_deal_rate_score()
        
        # 记录成交日志
        self.lead_id.message_post(
            body=_('线索成交，签约合同金额：%.2f万元') % self.contract_amount,
            message_type='notification',
            subtype_xmlid='mail.mt_comment'
        )

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('成交成功'),
                'message': _('线索 "%s" 已成功标记为成交，签约合同金额：%.2f万元') % (
                    self.lead_id.name, self.contract_amount
                ),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_cancel(self):
        """取消"""
        return {'type': 'ir.actions.act_window_close'}
