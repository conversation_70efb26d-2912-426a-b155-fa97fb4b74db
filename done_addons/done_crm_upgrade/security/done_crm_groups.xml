<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Done CRM 权限组定义 -->
        
        <!-- 线索管理权限组类别 -->
        <record id="module_category_done_crm" model="ir.module.category">
            <field name="name">Done线索管理</field>
            <field name="description">Done线索管理系统权限</field>
            <field name="sequence">20</field>
        </record>

                <!-- 线索-引流老师权限组 -->
        <record id="group_done_crm_traffic_teacher" model="res.groups">
            <field name="name">线索-引流老师</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">引流老师权限：仅能创建线索，分配后的线索无法查看修改；仅有线索管理和创建新线索按钮</field>
            <!-- 继承基础CRM权限以访问CRM菜单 -->
            <field name="implied_ids" eval="[(4, ref('crm.group_use_lead'))]"/>
        </record>


        <!-- 线索-招生老师权限组 -->
        <record id="group_done_crm_enrollment_teacher" model="res.groups">
            <field name="name">线索-招生老师</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">招生老师权限：仅能看到自己创建的线索 和分配给自己的线索，认领后可以对信息进行编写；菜单：</field>
            <field name="implied_ids" eval="[(4, ref('crm.group_use_lead'))]"/>
        </record>

        <!-- 线索-推流老师权限组 -->
        <record id="group_done_crm_push_teacher" model="res.groups">
            <field name="name">线索-推流老师</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">招生老师权限：仅能看到自己创建的线索 和分配给自己的线索，认领后可以对信息进行编写</field>
            <field name="implied_ids" eval="[(4, ref('crm.group_use_lead'))]"/>
        </record>


        <!-- 线索用户权限组 -->
        <record id="group_done_crm_user" model="res.groups">
            <field name="name">线索用户</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">线索用户权限：可以查看和认领分配给自己的线索，填写线索信息和评分,有线索菜单权限</field>
        </record>

        <!-- 线索管理员权限组 -->
        <record id="group_done_crm_manager" model="res.groups">
            <field name="name">线索管理员</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">线索管理员权限：拥有所有线索管理权限，包括线索分配、权重配置、系统配置等</field>
        </record>

        <!-- 线索总监-all权限组 -->
        <record id="group_done_crm_director_all" model="res.groups">
            <field name="name">线索-总监(查看所有线索)</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">线索总监权限：最高级别权限，包括所有管理员权限以及高级系统配置</field>
        </record>


        <!-- 线索总监权限组 -->
        <record id="group_done_crm_director" model="res.groups">
            <field name="name">线索-总监(查看自己部门线索)</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">线索总监权限：最高级别权限，包括所有管理员权限以及高级系统配置</field>
        </record>


        <!-- 线索组长权限组 -->
        <record id="group_done_crm_group_leader" model="res.groups">
            <field name="name">线索-组长(查看自己部门线索)</field>
            <field name="category_id" ref="module_category_done_crm"/>
            <field name="comment">线索组长权限：</field>
        </record>


        <!-- 为管理员用户默认添加线索总监权限 -->
        <record id="base.user_admin" model="res.users">
            <field name="groups_id" eval="[(4, ref('group_done_crm_director'))]"/>
        </record>

        <!-- 权限组规则定义 -->

        <!-- 引流老师只能查看自己创建的线索，且只能在未分配状态下查看 -->
        <record id="crm_lead_traffic_teacher_rule" model="ir.rule">
            <field name="name">引流老师访问规则</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="domain_force">[
                ('upstream_teacher_user_id', '=', user.id),
                ('lead_state', 'in', ['draft', 'to_allocate'])
            ]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- 线索用户只能查看分配给自己的线索 -->
        <record id="crm_lead_user_rule" model="ir.rule">
            <field name="name">线索用户访问规则</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="domain_force">[
                '|',
                ('claim_teacher_user_id', '=', user.id),
                ('upstream_teacher_user_id', '=', user.id)
            ]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- 教师权重配置访问规则 - 用户只能查看自己的权重配置 -->
        <record id="crm_teacher_weight_user_rule" model="ir.rule">
            <field name="name">教师权重用户访问规则</field>
            <field name="model_id" ref="model_crm_teacher_weight"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_done_crm_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- 教师权重配置访问规则 - 管理员可以查看和修改所有权重配置 -->
        <record id="crm_teacher_weight_manager_rule" model="ir.rule">
            <field name="name">教师权重管理员访问规则</field>
            <field name="model_id" ref="model_crm_teacher_weight"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_done_crm_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

    </data>
</odoo>
