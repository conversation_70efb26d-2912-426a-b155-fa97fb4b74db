<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- 扩展部门表单视图，添加线索提醒人字段 -->
        <record id="hr_department_form_view_inherit" model="ir.ui.view">
            <field name="name">hr.department.form.inherit</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_form"/>
            <field name="arch" type="xml">
                <!-- 在部门基本信息组后添加线索提醒人配置 -->
                <field name="color" position="after">
                    <separator string="线索管理配置"/>
                    <field name="lead_reminder_user_ids"
                           widget="many2many_tags"
                           options="{'no_create': True}"
                           domain="[('active', '=', True)]"
                           placeholder="选择线索提醒人..."
                           help="当本部门员工的线索被退回时，会向这些用户发送微信提醒"/>
                    <field name="lead_reminder_count" readonly="1"/>
                </field>
            </field>
        </record>

        <!-- 扩展部门列表视图，显示线索提醒人数量 -->
        <record id="hr_department_tree_view_inherit" model="ir.ui.view">
            <field name="name">hr.department.tree.inherit</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_tree"/>
            <field name="arch" type="xml">
                <field name="total_employee" position="after">
                    <field name="lead_reminder_count" string="提醒人数" optional="hide"/>
                </field>
            </field>
        </record>

    </data>
</odoo>
