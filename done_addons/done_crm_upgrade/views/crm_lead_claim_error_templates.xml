<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- 线索认领错误页面模板 -->
        <template id="lead_claim_error_template" name="线索认领错误页面">
            <t t-call="web.layout">
                <t t-set="head_website">
                    <title>线索认领 - <t t-esc="title"/></title>
                    <meta name="viewport" content="width=device-width, initial-scale=1"/>
                    <style>
                        .error-container {
                            max-width: 600px;
                            margin: 50px auto;
                            padding: 30px;
                            text-align: center;
                            background: #fff;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        .error-icon {
                            font-size: 64px;
                            color: #dc3545;
                            margin-bottom: 20px;
                        }
                        .error-title {
                            font-size: 24px;
                            font-weight: bold;
                            color: #333;
                            margin-bottom: 15px;
                        }
                        .error-message {
                            font-size: 16px;
                            color: #666;
                            margin-bottom: 30px;
                            line-height: 1.5;
                        }
                        .btn-back {
                            background-color: #007bff;
                            color: white;
                            padding: 10px 20px;
                            text-decoration: none;
                            border-radius: 4px;
                            display: inline-block;
                            margin: 5px;
                        }
                        .btn-back:hover {
                            background-color: #0056b3;
                            color: white;
                            text-decoration: none;
                        }
                        .btn-secondary {
                            background-color: #6c757d;
                            color: white;
                            padding: 10px 20px;
                            text-decoration: none;
                            border-radius: 4px;
                            display: inline-block;
                            margin: 5px;
                        }
                        .btn-secondary:hover {
                            background-color: #545b62;
                            color: white;
                            text-decoration: none;
                        }
                    </style>
                </t>
                
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">
                        <t t-esc="title"/>
                    </div>
                    <div class="error-message">
                        <t t-esc="message"/>
                    </div>
                    <div>
                        <a t-att-href="back_url" class="btn-back">返回线索列表</a>
                        <a href="/web" class="btn-secondary">返回首页</a>
                    </div>
                </div>
            </t>
        </template>
        
        <!-- 公共错误页面模板（未登录用户） -->
        <template id="lead_claim_public_error_template" name="线索认领公共错误页面">
            <t t-call="web.layout">
                <t t-set="head_website">
                    <title>线索认领 - <t t-esc="title"/></title>
                    <meta name="viewport" content="width=device-width, initial-scale=1"/>
                    <style>
                        .error-container {
                            max-width: 600px;
                            margin: 50px auto;
                            padding: 30px;
                            text-align: center;
                            background: #fff;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        .error-icon {
                            font-size: 64px;
                            color: #dc3545;
                            margin-bottom: 20px;
                        }
                        .error-title {
                            font-size: 24px;
                            font-weight: bold;
                            color: #333;
                            margin-bottom: 15px;
                        }
                        .error-message {
                            font-size: 16px;
                            color: #666;
                            margin-bottom: 30px;
                            line-height: 1.5;
                        }
                        .btn-login {
                            background-color: #28a745;
                            color: white;
                            padding: 10px 20px;
                            text-decoration: none;
                            border-radius: 4px;
                            display: inline-block;
                            margin: 5px;
                        }
                        .btn-login:hover {
                            background-color: #218838;
                            color: white;
                            text-decoration: none;
                        }
                        .btn-secondary {
                            background-color: #6c757d;
                            color: white;
                            padding: 10px 20px;
                            text-decoration: none;
                            border-radius: 4px;
                            display: inline-block;
                            margin: 5px;
                        }
                        .btn-secondary:hover {
                            background-color: #545b62;
                            color: white;
                            text-decoration: none;
                        }
                    </style>
                </t>
                
                <div class="error-container">
                    <div class="error-icon">🔒</div>
                    <div class="error-title">
                        <t t-esc="title"/>
                    </div>
                    <div class="error-message">
                        <t t-esc="message"/>
                    </div>
                    <div>
                        <a t-att-href="login_url" class="btn-login">登录系统</a>
                        <a href="/" class="btn-secondary">返回首页</a>
                    </div>
                </div>
            </t>
        </template>
        
    </data>
</odoo>
