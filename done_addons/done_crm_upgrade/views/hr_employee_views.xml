<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 扩展员工表单视图，在教练字段下方添加教师组别字段 -->
    <record id="view_employee_form_inherit_teacher_group" model="ir.ui.view">
        <field name="name">hr.employee.form.inherit.teacher.group</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <!-- 在教练字段后添加教师组别字段 -->
            <field name="coach_id" position="after">
                <field name="teacher_group_id" 
                       string="教师组别"
                       placeholder="选择教师所属的业务组别..."
                       help="设置教师组别后将自动生成权重配置"/>
            </field>
        </field>
    </record>

    <!-- 扩展员工列表视图，添加教师组别列 -->
    <record id="view_employee_tree_inherit_teacher_group" model="ir.ui.view">
        <field name="name">hr.employee.tree.inherit.teacher.group</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <!-- 在部门字段后添加教师组别列 -->
            <field name="department_id" position="after">
                <field name="teacher_group_id" string="教师组别" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- 扩展员工搜索视图，添加教师组别筛选 -->
    <record id="view_employee_filter_inherit_teacher_group" model="ir.ui.view">
        <field name="name">hr.employee.search.inherit.teacher.group</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <!-- 在部门分组后添加教师组别分组 -->
            <filter name="group_department" position="after">
                <filter name="group_teacher_group" 
                        string="教师组别" 
                        context="{'group_by': 'teacher_group_id'}"
                        help="按教师组别分组"/>
            </filter>
            
            <!-- 在搜索字段中添加教师组别 -->
            <field name="department_id" position="after">
                <field name="teacher_group_id" string="教师组别"/>
            </field>
        </field>
    </record>
</odoo>
