<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- 通知测试表单视图 -->
        <record id="view_notification_test_form" model="ir.ui.view">
            <field name="name">notification.test.form</field>
            <field name="model">notification.test</field>
            <field name="arch" type="xml">
                <form string="通知测试工具">
                    <header>
                        <button name="action_send_test_notification" type="object" string="发送测试通知" 
                                class="btn-primary"/>
                        <button name="action_test_current_user" type="object" string="测试当前用户" 
                                class="btn-secondary"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <span>系统通知测试工具</span>
                            </h1>
                        </div>
                        
                        <div class="alert alert-info" role="alert">
                            <strong>使用说明：</strong><br/>
                            此工具用于测试系统通知功能是否正常工作。您可以选择目标用户和通知类型，然后发送测试通知。
                        </div>
                        
                        <group>
                            <group string="测试配置">
                                <field name="target_user_id" options="{'no_create': True, 'no_open': True}"/>
                                <field name="notification_type"/>
                            </group>
                        </group>

                        <group string="测试消息">
                            <field name="test_message" nolabel="1" placeholder="输入测试消息内容..."/>
                        </group>

                        <div class="alert alert-warning" role="alert">
                            <strong>通知类型说明：</strong><br/>
                            <ul>
                                <li><strong>活动通知</strong>：在用户的活动列表中显示，需要用户手动标记为完成</li>
                                <li><strong>邮件消息</strong>：在收件箱中显示，可以在讨论模块中查看</li>
                                <li><strong>Bus通知</strong>：实时弹窗通知，会在页面右上角显示</li>
                                <li><strong>全部类型</strong>：同时发送所有类型的通知</li>
                            </ul>
                        </div>

                    </sheet>
                </form>
            </field>
        </record>

        <!-- 通知测试动作 -->
        <record id="action_notification_test" model="ir.actions.act_window">
            <field name="name">通知测试工具</field>
            <field name="res_model">notification.test</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_notification_test_form"/>
            <field name="target">new</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    测试系统通知功能
                </p>
                <p>
                    使用此工具测试系统通知功能是否正常工作。<br/>
                    您可以向指定用户发送不同类型的测试通知。
                </p>
            </field>
        </record>

    </data>
</odoo>
