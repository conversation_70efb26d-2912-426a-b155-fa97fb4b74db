<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 教师权重配置列表视图 -->
    <record id="crm_teacher_weight_list_view" model="ir.ui.view">
        <field name="name">crm.teacher.weight.list</field>
        <field name="model">crm.teacher.weight</field>
        <field name="arch" type="xml">
            <list string="教师加权评分表" default_order="teacher_group_id,total_score desc">
                <field name="teacher_group_id"/>
                <field name="user_id"/>
                <field name="weight"/>
                <field name="additional_score"/>
                <field name="deal_rate_score"/>
                <field name="deal_speed_score"/>
                <field name="kpi_score"/>
                <field name="gender_match_score"/>
                <field name="amount_match_score"/>
                <field name="total_score" widget="badge" decoration-success="total_score &gt;= 5" decoration-warning="total_score &gt;= 3 and total_score &lt; 5" decoration-danger="total_score &lt; 3"/>
                <field name="assigned_lead_count"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- 教师权重配置表单视图 -->
    <record id="crm_teacher_weight_form_view" model="ir.ui.view">
        <field name="name">crm.teacher.weight.form</field>
        <field name="model">crm.teacher.weight</field>
        <field name="arch" type="xml">
            <form string="教师权重配置">
                <header>
                    <button name="action_calculate_scores" type="object" string="重新计算评分" 
                            class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_assigned_leads" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="assigned_lead_count" widget="statinfo" string="分配线索"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="已禁用" bg_color="bg-danger" invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="基本信息">
                            <field name="teacher_group_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="user_id" options="{'no_create': True, 'no_open': True}"
                                   domain="[('active', '=', True)]"/>
                            <field name="active"/>
                        </group>
                        <group string="权重配置">
                            <field name="weight"/>
                            <field name="total_score" readonly="1" widget="badge"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="评分详情" name="scores">
                            <group>
                                <group string="基础评分">
                                    <field name="additional_score"/>
                                    <field name="deal_rate_score"/>
                                    <field name="deal_speed_score"/>
                                    <field name="kpi_score"/>
                                </group>
                                <group string="匹配评分">
                                    <field name="gender_match_score"/>
                                    <field name="amount_match_score"/>
                                </group>
                            </group>
                        </page>
                        <page string="备注" name="notes">
                            <field name="notes" placeholder="请输入备注信息..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 教师权重配置搜索视图 -->
    <record id="crm_teacher_weight_search_view" model="ir.ui.view">
        <field name="name">crm.teacher.weight.search</field>
        <field name="model">crm.teacher.weight</field>
        <field name="arch" type="xml">
            <search string="搜索教师权重配置">
                <field name="user_id" string="老师"/>
                <field name="teacher_group_id" string="业务类型"/>
                
                <filter name="active" string="启用" domain="[('active', '=', True)]"/>
                <filter name="inactive" string="禁用" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter name="high_score" string="高分(&gt;=5分)" domain="[('total_score', '&gt;=', 5)]"/>
                <filter name="medium_score" string="中分(3-5分)" domain="[('total_score', '&gt;=', 3), ('total_score', '&lt;', 5)]"/>
                <filter name="low_score" string="低分(&lt;3分)" domain="[('total_score', '&lt;', 3)]"/>
                
                <group expand="0" string="分组">
                    <filter name="group_by_teacher_group" string="业务类型" context="{'group_by': 'teacher_group_id'}"/>
                    <filter name="group_by_total_score" string="总分" context="{'group_by': 'total_score'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 教师权重配置动作 -->
    <record id="crm_teacher_weight_action" model="ir.actions.act_window">
        <field name="name">教师加权评分表</field>
        <field name="res_model">crm.teacher.weight</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="crm_teacher_weight_search_view"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建第一个教师权重配置
            </p>
            <p>
                教师加权评分表用于线索自动分配时的权重计算。<br/>
                您可以为每个招生老师配置不同的权重和评分，系统会根据这些配置自动分配线索。
            </p>
        </field>
    </record>

    <!-- 教师权重配置动作的视图关联 -->
    <record id="crm_teacher_weight_action_view_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="crm_teacher_weight_list_view"/>
        <field name="act_window_id" ref="crm_teacher_weight_action"/>
    </record>
    
    <record id="crm_teacher_weight_action_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="crm_teacher_weight_form_view"/>
        <field name="act_window_id" ref="crm_teacher_weight_action"/>
    </record>
</odoo>
