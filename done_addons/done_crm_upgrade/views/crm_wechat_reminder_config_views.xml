<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 微信提醒配置 Tree View -->
    <record id="view_crm_wechat_reminder_config_list" model="ir.ui.view">
        <field name="name">crm.wechat.reminder.config.list</field>
        <field name="model">crm.wechat.reminder.config</field>
        <field name="arch" type="xml">
            <list string="微信提醒配置" default_order="code">
                <field name="code" string="编码"/>
                <field name="name" string="提醒类型名称"/>
                <field name="description" string="描述"/>
                <field name="reminder_user_count" string="提醒人数"/>
                <field name="active" string="启用"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </list>
        </field>
    </record>

    <!-- 微信提醒配置 Form View -->
    <record id="view_crm_wechat_reminder_config_form" model="ir.ui.view">
        <field name="name">crm.wechat.reminder.config.form</field>
        <field name="model">crm.wechat.reminder.config</field>
        <field name="arch" type="xml">
            <form string="微信提醒配置">
                <sheet>

                    
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="提醒类型名称..."/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="code" placeholder="例如: lead_return_reminder"/>
                            <field name="reminder_user_count" readonly="1"/>
                        </group>
                        <group>
                            <field name="create_uid" readonly="1"/>
                            <field name="create_date" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="基本信息" name="basic_info">
                            <group>
                                <field name="description" placeholder="详细描述该提醒类型的用途和触发条件..."/>
                            </group>
                        </page>
                        
                        <page string="提醒人员" name="reminder_users">
                            <field name="reminder_user_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                        </page>
                    </notebook>
                </sheet>
                
                <chatter/>
            </form>
        </field>
    </record>

    <!-- 微信提醒配置 Search View -->
    <record id="view_crm_wechat_reminder_config_search" model="ir.ui.view">
        <field name="name">crm.wechat.reminder.config.search</field>
        <field name="model">crm.wechat.reminder.config</field>
        <field name="arch" type="xml">
            <search string="搜索微信提醒配置">
                <field name="name" string="名称" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="code" string="编码"/>
                <field name="description" string="描述"/>
                <field name="reminder_user_ids" string="提醒人"/>
                <field name="create_uid" string="创建人"/>
                
                <filter string="启用" name="active" domain="[('active', '=', True)]"/>
                <filter string="禁用" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="我创建的" name="my_configs" domain="[('create_uid', '=', uid)]"/>
                <filter string="包含我的提醒" name="my_reminders" domain="[('reminder_user_ids', 'in', [uid])]"/>
                
                <separator/>
                <filter string="创建日期" name="create_date" date="create_date"/>
                
                <group expand="0" string="分组">
                    <filter string="创建人" name="group_by_create_uid" context="{'group_by': 'create_uid'}"/>
                    <filter string="启用状态" name="group_by_active" context="{'group_by': 'active'}"/>
                    <filter string="创建日期" name="group_by_create_date" context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 微信提醒配置 Action -->
    <record id="action_crm_wechat_reminder_config" model="ir.actions.act_window">
        <field name="name">微信提醒配置</field>
        <field name="res_model">crm.wechat.reminder.config</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建微信提醒配置
            </p>
            <p>
                在这里可以配置各种微信提醒类型，包括提醒人员和触发条件。
            </p>
        </field>
    </record>

</odoo>
