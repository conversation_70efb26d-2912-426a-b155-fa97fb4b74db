<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- 自动分配服务表单视图 -->
        <record id="view_crm_auto_allocation_service_form" model="ir.ui.view">
            <field name="name">crm.auto.allocation.service.form</field>
            <field name="model">crm.auto.allocation.service</field>
            <field name="arch" type="xml">
                <form string="CRM自动分配服务">
                    <header>
                        <button name="action_manual_run" type="object" string="手动执行" 
                                class="btn-primary" icon="fa-play"/>
                        <button name="action_reset_statistics" type="object" string="重置统计" 
                                class="btn-secondary" icon="fa-refresh"
                                confirm="确定要重置所有统计数据吗？"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_config" string="基本配置">
                                <field name="active"/>
                                <field name="max_leads_per_run"/>
                                <field name="enable_wechat_notification"/>
                            </group>
                            <group name="run_status" string="运行状态">
                                <field name="last_run_time" readonly="1"/>
                                <field name="last_run_result" readonly="1"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="statistics" string="统计信息">
                                <field name="total_allocated" readonly="1"/>
                                <field name="success_count" readonly="1"/>
                                <field name="failed_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="运行日志" name="run_log">
                                <group>
                                    <field name="last_run_result" nolabel="1" readonly="1" 
                                           widget="text" placeholder="暂无运行日志"/>
                                </group>
                            </page>
                            <page string="配置说明" name="help">
                                <div class="alert alert-info" role="alert">
                                    <h4><i class="fa fa-info-circle"/> 自动分配服务说明</h4>
                                    <ul>
                                        <li><strong>启用：</strong>控制是否启用自动分配功能</li>
                                        <li><strong>每次最大分配数：</strong>每次定时任务最多处理的线索数量，建议设置为50-100</li>
                                        <li><strong>启用微信通知：</strong>分配成功后是否发送微信消息通知给老师</li>
                                        <li><strong>定时任务：</strong>系统每分钟自动执行一次分配任务</li>
                                        <li><strong>手动执行：</strong>可以手动触发一次分配任务进行测试</li>
                                    </ul>
                                </div>
                                
                                <div class="alert alert-warning" role="alert">
                                    <h4><i class="fa fa-warning"/> 注意事项</h4>
                                    <ul>
                                        <li>请确保已正确配置教师权重和分配模式</li>
                                        <li>建议在业务高峰期适当调整每次最大分配数</li>
                                        <li>如需停止自动分配，请将"启用"设置为否</li>
                                        <li>定时任务的启停可在"设置 → 技术 → 自动化 → 计划动作"中管理</li>
                                    </ul>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- 自动分配服务列表视图 -->
        <record id="view_crm_auto_allocation_service_list" model="ir.ui.view">
            <field name="name">crm.auto.allocation.service.list</field>
            <field name="model">crm.auto.allocation.service</field>
            <field name="arch" type="xml">
                <list string="CRM自动分配服务" create="false" delete="false">
                    <field name="name"/>
                    <field name="active"/>
                    <field name="last_run_time"/>
                    <field name="total_allocated"/>
                    <field name="success_count"/>
                    <field name="failed_count"/>
                    <field name="max_leads_per_run"/>
                </list>
            </field>
        </record>
        
        <!-- 自动分配服务动作 -->
        <record id="action_crm_auto_allocation_service" model="ir.actions.act_window">
            <field name="name">自动分配服务</field>
            <field name="res_model">crm.auto.allocation.service</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    配置CRM线索自动分配服务
                </p>
                <p>
                    自动分配服务会定期将待分配的线索自动分配给合适的老师。
                    您可以在这里配置分配参数和查看运行状态。
                </p>
            </field>
        </record>
        
        <!-- 添加到CRM菜单 -->
        <menuitem id="menu_crm_auto_allocation_service"
                  name="自动分配服务"
                  parent="done_crm_menu_root"
                  action="action_crm_auto_allocation_service"
                  sequence="50"
                  groups="sales_team.group_sale_manager"/>
        
    </data>
</odoo>
