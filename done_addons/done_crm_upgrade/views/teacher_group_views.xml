<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 招生老师组别表单视图 -->
    <record id="teacher_group_view_form" model="ir.ui.view">
        <field name="name">teacher.group.form</field>
        <field name="model">teacher.group</field>
        <field name="arch" type="xml">
            <form string="招生老师组别">
                <sheet>
                    <group>
                        <group>
                            <field name="name" placeholder="例如：单证类"/>
                            <field name="code" placeholder="例如：single"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="active"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="组别描述信息..."/>
                    </group>


                    <notebook>
                    <page string="教师权重" name="weights">
                        <field name="teacher_weight_ids">
                            <list editable="bottom">
                                <field name="user_id"/>
                                <field name="weight"/>
                                <field name="total_score"/>
                            </list>
                        </field>
                    </page>


                </notebook>
                </sheet>

            </form>
        </field>
    </record>

    <!-- 招生老师组别列表视图 -->
    <record id="teacher_group_view_tree" model="ir.ui.view">
        <field name="name">teacher.group.tree</field>
        <field name="model">teacher.group</field>
        <field name="arch" type="xml">
            <list string="招生老师组别">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="active"/>
                <field name="description"/>
            </list>
        </field>
    </record>

    <!-- 招生老师组别搜索视图 -->
    <record id="teacher_group_view_search" model="ir.ui.view">
        <field name="name">teacher.group.search</field>
        <field name="model">teacher.group</field>
        <field name="arch" type="xml">
            <search string="搜索招生老师组别">
                <field name="name"/>
                <field name="code"/>
                <filter string="启用" name="active" domain="[('active', '=', True)]"/>
                <filter string="禁用" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <!-- 招生老师组别动作 -->
    <record id="teacher_group_action" model="ir.actions.act_window">
        <field name="name">招生老师组别</field>
        <field name="res_model">teacher.group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建第一个招生老师组别
            </p>
            <p>
                管理不同类型的招生老师组别，如单证类、双证类等。
            </p>
        </field>
    </record>
</odoo>
