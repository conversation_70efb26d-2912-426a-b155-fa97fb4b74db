<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Done CRM 配置表单视图 -->
        <record id="view_done_crm_config_form" model="ir.ui.view">
            <field name="name">done.crm.config.form</field>
            <field name="model">done.crm.config</field>
            <field name="arch" type="xml">
                <form string="CRM配置参数">
                    <header>
                        <button name="action_reset_to_default" type="object" string="重置默认值" 
                                class="btn-secondary" invisible="not is_system"/>
                        <button name="action_test_regex" type="object" string="测试正则" 
                                class="btn-info" invisible="data_type != 'string' or not regex_pattern"/>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="参数名称，例如：distribution_mode"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="基本信息">
                                <field name="category"/>
                                <field name="data_type"/>
                                <field name="sequence"/>
                                <field name="is_system" readonly="1"/>
                                <field name="readonly"/>
                            </group>
                            <group string="参数值">
                                <field name="value" required="1"/>
                                <field name="display_value" readonly="1"/>
                                <field name="is_required"/>
                            </group>
                        </group>

                        <group string="描述">
                            <field name="description" nolabel="1" placeholder="请输入参数的详细描述..."/>
                        </group>

                        <!-- 选择项配置 -->
                        <group string="选择项配置" invisible="data_type != 'selection'">
                            <field name="selection_options" nolabel="1" 
                                   placeholder="格式：key1:label1,key2:label2&#10;例如：1:经典随机分配模式,2:自定义高级模式"/>
                        </group>

                        <!-- 验证规则 -->
                        <group string="验证规则" invisible="data_type not in ['integer', 'float', 'string']">
                            <group invisible="data_type not in ['integer', 'float']">
                                <field name="min_value"/>
                                <field name="max_value"/>
                            </group>
                            <group invisible="data_type != 'string'">
                                <field name="regex_pattern" placeholder="例如：^[0-9]+$"/>
                            </group>
                        </group>

                        <!-- 审计信息 -->
                        <group string="审计信息" groups="base.group_no_one">
                            <group>
                                <field name="create_uid" readonly="1"/>
                                <field name="create_date" readonly="1"/>
                            </group>
                            <group>
                                <field name="write_uid" readonly="1"/>
                                <field name="write_date" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Done CRM 配置列表视图 -->
        <record id="view_done_crm_config_list" model="ir.ui.view">
            <field name="name">done.crm.config.list</field>
            <field name="model">done.crm.config</field>
            <field name="arch" type="xml">
                <list string="CRM配置参数" default_order="category, sequence, name"
                      decoration-muted="not active"
                      decoration-info="is_system"
                      decoration-warning="readonly">
                    
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="display_value"/>
                    <field name="category"/>
                    <field name="data_type"/>
                    <field name="description"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="is_system" column_invisible="1"/>
                    <field name="readonly" column_invisible="1"/>
                    
                    <!-- 操作按钮 -->
                    <button name="action_reset_to_default" type="object" string="重置" 
                            class="btn-sm btn-secondary" icon="fa-refresh"
                            invisible="not is_system"/>
                </list>
            </field>
        </record>

        <!-- Done CRM 配置搜索视图 -->
        <record id="view_done_crm_config_search" model="ir.ui.view">
            <field name="name">done.crm.config.search</field>
            <field name="model">done.crm.config</field>
            <field name="arch" type="xml">
                <search string="搜索配置参数">
                    <field name="name" string="参数名称"/>
                    <field name="value" string="参数值"/>
                    <field name="description" string="描述"/>
                    
                    <!-- 过滤器 -->
                    <filter name="active" string="启用" domain="[('active', '=', True)]"/>
                    <filter name="inactive" string="禁用" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter name="system" string="系统参数" domain="[('is_system', '=', True)]"/>
                    <filter name="custom" string="自定义参数" domain="[('is_system', '=', False)]"/>
                    <filter name="readonly" string="只读参数" domain="[('readonly', '=', True)]"/>
                    
                    <!-- 分类过滤 -->
                    <separator/>
                    <filter name="system_config" string="系统配置" domain="[('category', '=', 'system')]"/>
                    <filter name="distribution_config" string="分配配置" domain="[('category', '=', 'distribution')]"/>
                    <filter name="scoring_config" string="评分配置" domain="[('category', '=', 'scoring')]"/>
                    <filter name="notification_config" string="通知配置" domain="[('category', '=', 'notification')]"/>
                    
                    <!-- 数据类型过滤 -->
                    <separator/>
                    <filter name="string_type" string="字符串" domain="[('data_type', '=', 'string')]"/>
                    <filter name="integer_type" string="整数" domain="[('data_type', '=', 'integer')]"/>
                    <filter name="boolean_type" string="布尔值" domain="[('data_type', '=', 'boolean')]"/>
                    <filter name="selection_type" string="选择项" domain="[('data_type', '=', 'selection')]"/>
                    
                    <!-- 分组 -->
                    <group expand="0" string="分组">
                        <filter name="group_by_category" string="配置分类" context="{'group_by': 'category'}"/>
                        <filter name="group_by_data_type" string="数据类型" context="{'group_by': 'data_type'}"/>
                        <filter name="group_by_active" string="启用状态" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Done CRM 配置看板视图 -->
        <record id="view_done_crm_config_kanban" model="ir.ui.view">
            <field name="name">done.crm.config.kanban</field>
            <field name="model">done.crm.config</field>
            <field name="arch" type="xml">
                <kanban default_group_by="category" class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="value"/>
                    <field name="display_value"/>
                    <field name="category"/>
                    <field name="data_type"/>
                    <field name="description"/>
                    <field name="active"/>
                    <field name="is_system"/>
                    <field name="readonly"/>
                    
                    <templates>
                        <t t-name="card">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="oe_kanban_details">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <span class="badge" t-att-class="record.data_type.raw_value == 'string' ? 'badge-info' : 
                                                                 record.data_type.raw_value == 'integer' ? 'badge-primary' :
                                                                 record.data_type.raw_value == 'boolean' ? 'badge-success' :
                                                                 record.data_type.raw_value == 'selection' ? 'badge-warning' : 'badge-secondary'">
                                                <field name="data_type"/>
                                            </span>
                                        </div>
                                        <div class="o_kanban_record_body">
                                            <field name="display_value"/>
                                        </div>
                                        <div class="o_kanban_record_bottom">
                                            <div class="oe_kanban_bottom_left">
                                                <span t-if="record.is_system.raw_value" class="badge badge-info">系统</span>
                                                <span t-if="record.readonly.raw_value" class="badge badge-warning">只读</span>
                                                <span t-if="!record.active.raw_value" class="badge badge-secondary">禁用</span>
                                            </div>
                                            <div class="oe_kanban_bottom_right">
                                                <button name="action_reset_to_default" type="object" 
                                                        class="btn btn-sm btn-secondary" 
                                                        t-if="record.is_system.raw_value"
                                                        title="重置默认值">
                                                    <i class="fa fa-refresh"/>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Done CRM 配置动作 -->
        <record id="action_done_crm_config" model="ir.actions.act_window">
            <field name="name">CRM配置参数</field>
            <field name="res_model">done.crm.config</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="search_view_id" ref="view_done_crm_config_search"/>
            <field name="context">{
                'search_default_active': 1,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建第一个配置参数
                </p>
                <p>
                    CRM配置参数用于管理系统的各种设置。<br/>
                    您可以按分类、数据类型等条件筛选和管理配置参数。
                </p>
            </field>
        </record>

    </data>
</odoo>
