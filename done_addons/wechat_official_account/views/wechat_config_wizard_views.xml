<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- WeChat Configuration Wizard Form View -->
        <record id="view_wechat_config_wizard_form" model="ir.ui.view">
            <field name="name">wechat.config.wizard.form</field>
            <field name="model">wechat.config.wizard</field>
            <field name="arch" type="xml">
                <form string="WeChat Setup Wizard">
                    <header>
                        <field name="step" invisible="1"/>
                        
                        <!-- Welcome Step -->
                        <div invisible="step != 'welcome'" class="alert alert-info" role="alert">
                            <h3>🎉 Welcome to WeChat Official Account Setup!</h3>
                            <p>This wizard will help you configure your WeChat Official Account integration with Odoo.</p>
                            <p><strong>What you'll need:</strong></p>
                            <ul>
                                <li>WeChat Official Account AppID</li>
                                <li>WeChat Official Account AppSecret</li>
                                <li>A Token for webhook verification</li>
                            </ul>
                        </div>

                        <!-- Configuration Step -->
                        <div invisible="step != 'config'" class="alert alert-warning" role="alert">
                            <h4>📝 Enter Your WeChat Configuration</h4>
                            <p>Please enter your WeChat Official Account credentials below.</p>
                        </div>

                        <!-- Webhook Step -->
                        <div invisible="step != 'webhook'" class="alert alert-info" role="alert">
                            <h4>🔗 Configure Webhook in WeChat</h4>
                            <p>Copy the webhook URL below and configure it in your WeChat Official Account settings.</p>
                        </div>

                        <!-- Test Step -->
                        <div invisible="step != 'test'" class="alert alert-warning" role="alert">
                            <h4>🧪 Test Your Configuration</h4>
                            <p>Let's test if your WeChat API credentials are working correctly.</p>
                        </div>

                        <!-- Complete Step -->
                        <div invisible="step != 'complete'" class="alert alert-success" role="alert">
                            <h4>✅ Setup Complete!</h4>
                            <p>Your WeChat Official Account is now configured and ready to use.</p>
                        </div>
                    </header>
                    
                    <sheet>
                        <!-- Welcome Step Content -->
                        <div invisible="step != 'welcome'">
                            <div class="text-center">
                                <h2>🚀 Let's Get Started!</h2>
                                <p class="lead">Follow these simple steps to connect your WeChat Official Account with Odoo.</p>
                                
                                <div class="row mt-4">
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <h5>📱 Step 1</h5>
                                                <p>Get your WeChat credentials</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <h5>⚙️ Step 2</h5>
                                                <p>Configure in Odoo</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <h5>🎯 Step 3</h5>
                                                <p>Start messaging!</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Configuration Step Content -->
                        <div invisible="step != 'config'">
                            <group>
                                <group string="Basic Information">
                                    <field name="name" required="1"/>
                                    <field name="is_default"/>
                                </group>
                                <group string="WeChat Credentials">
                                    <field name="app_id" required="1" placeholder="wx1234567890abcdef"/>
                                    <field name="app_secret" required="1" password="True" placeholder="Your AppSecret"/>
                                    <field name="token" required="1" placeholder="Your Token"/>
                                    <field name="encoding_aes_key" placeholder="Optional AES Key"/>
                                </group>
                            </group>
                            
                            <div class="alert alert-info mt-3" role="alert">
                                <h5>💡 Where to find these credentials:</h5>
                                <ol>
                                    <li>Login to <a href="https://mp.weixin.qq.com" target="_blank">WeChat Official Account Platform</a></li>
                                    <li>Go to "Development" → "Basic Configuration"</li>
                                    <li>Copy your AppID and AppSecret</li>
                                    <li>Set a Token (you can create any string)</li>
                                </ol>
                            </div>
                        </div>
                        
                        <!-- Webhook Step Content -->
                        <div invisible="step != 'webhook'">
                            <group>
                                <field name="webhook_url" readonly="1" widget="url"/>
                            </group>
                            
                            <div class="alert alert-warning" role="alert">
                                <h5>🔧 Configure in WeChat Official Account:</h5>
                                <ol>
                                    <li>Go to "Development" → "Basic Configuration"</li>
                                    <li>Click "Modify Configuration"</li>
                                    <li>Set URL to: <code><field name="webhook_url" readonly="1"/></code></li>
                                    <li>Set Token to the same value you entered in the previous step</li>
                                    <li>Choose message encryption method (optional)</li>
                                    <li>Click "Submit" to verify</li>
                                </ol>
                            </div>
                        </div>
                        
                        <!-- Test Step Content -->
                        <div invisible="step != 'test'">
                            <group>
                                <field name="test_result" readonly="1" widget="text"
                                       invisible="not test_result"/>
                            </group>
                            
                            <div class="text-center mt-3">
                                <button name="action_test_connection" string="🧪 Test Connection" 
                                        type="object" class="btn btn-primary btn-lg"/>
                            </div>
                        </div>
                        
                        <!-- Complete Step Content -->
                        <div invisible="step != 'complete'">
                            <div class="text-center">
                                <h2>🎉 Congratulations!</h2>
                                <p class="lead">Your WeChat Official Account is now connected to Odoo.</p>
                                
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <h5>📤 What you can do now:</h5>
                                        <ul class="text-left">
                                            <li>Send messages to your followers</li>
                                            <li>Manage WeChat users</li>
                                            <li>View message history</li>
                                            <li>Handle incoming messages</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>🔗 Quick Links:</h5>
                                        <ul class="text-left">
                                            <li><a href="/web#action=wechat_official_account.action_wechat_message">Send a Message</a></li>
                                            <li><a href="/web#action=wechat_official_account.action_wechat_user">View Users</a></li>
                                            <li><a href="/web#action=wechat_official_account.action_wechat_config">Manage Configs</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </sheet>
                    
                    <footer>
                        <button name="action_previous_step" string="← Previous" type="object"
                                class="btn btn-secondary"
                                invisible="step in ['welcome', 'complete']"/>

                        <button name="action_next_step" string="Next →" type="object"
                                class="btn btn-primary"
                                invisible="step in ['test', 'complete']"/>

                        <button name="action_create_config" string="🎯 Create Configuration" type="object"
                                class="btn btn-success"
                                invisible="step != 'complete'"/>
                        
                        <button name="action_skip_wizard" string="Skip Wizard" type="object" 
                                class="btn btn-link"/>
                        
                        <button string="Cancel" class="btn btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        
    </data>
</odoo>
