<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- WeChat Configuration Form View -->
        <record id="view_wechat_config_form" model="ir.ui.view">
            <field name="name">wechat.config.form</field>
            <field name="model">wechat.config</field>
            <field name="arch" type="xml">
                <form string="WeChat Configuration">
                    <header>
                        <button name="test_connection" string="Test Connection" type="object"
                                class="btn-primary" icon="fa-plug"/>
                        <button name="action_sync_users" string="Sync Users" type="object"
                                class="btn-secondary" icon="fa-users"
                                help="Sync all users from WeChat API"/>
                        <button name="action_refresh_access_token" string="Refresh Token" type="object"
                                class="btn-secondary" icon="fa-refresh"
                                help="Manually refresh access token"/>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Configuration Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Basic Information">
                                <field name="app_id" required="1"/>
                                <field name="app_secret" password="True" required="1"/>
                                <field name="token" required="1"/>
                                <field name="encoding_aes_key"/>
                                <field name="is_default"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group string="Webhook Information">
                                <field name="webhook_url" readonly="1" widget="url"/>
                                <label for="webhook_url" string=""/>
                                <div class="text-muted">
                                    Copy this URL to your WeChat Official Account webhook configuration.
                                </div>
                            </group>
                        </group>
                        
                        <group string="Access Token Information" groups="base.group_system">
                            <field name="access_token" readonly="1"/>
                            <field name="access_token_expires" readonly="1"/>
                        </group>
                        
                        <notebook>
                            <page string="Statistics" name="statistics">
                                <group>
                                    <group string="User Statistics">
                                        <field name="total_users" readonly="1"/>
                                    </group>
                                    <group string="Message Statistics">
                                        <field name="total_messages_sent" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Help" name="help">
                                <div class="alert alert-info" role="alert">
                                    <h4>Setup Instructions:</h4>
                                    <ol>
                                        <li>Create a WeChat Official Account at <a href="https://mp.weixin.qq.com" target="_blank">https://mp.weixin.qq.com</a></li>
                                        <li>Get your AppID and AppSecret from the account settings</li>
                                        <li>Set a Token for webhook verification</li>
                                        <li>Configure the webhook URL in your WeChat account settings</li>
                                        <li>Test the connection using the "Test Connection" button</li>
                                    </ol>
                                    
                                    <h4>Webhook Configuration:</h4>
                                    <p>In your WeChat Official Account settings, configure:</p>
                                    <ul>
                                        <li><strong>URL:</strong> Copy the webhook URL from above</li>
                                        <li><strong>Token:</strong> Use the same token as configured here</li>
                                        <li><strong>EncodingAESKey:</strong> Optional, for message encryption</li>
                                        <li><strong>Message Encryption:</strong> Choose based on your security needs</li>
                                    </ul>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- WeChat Configuration list View -->
        <record id="view_wechat_config_list" model="ir.ui.view">
            <field name="name">wechat.config.list</field>
            <field name="model">wechat.config</field>
            <field name="arch" type="xml">
                <list string="WeChat Configurations">
                    <field name="name"/>
                    <field name="app_id"/>
                    <field name="is_default"/>
                    <field name="total_users"/>
                    <field name="total_messages_sent"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>
        
        <!-- WeChat Configuration Search View -->
        <record id="view_wechat_config_search" model="ir.ui.view">
            <field name="name">wechat.config.search</field>
            <field name="model">wechat.config</field>
            <field name="arch" type="xml">
                <search string="WeChat Configurations">
                    <field name="name"/>
                    <field name="app_id"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Default" name="default" domain="[('is_default', '=', True)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        
    </data>
</odoo>
