<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Phone Collection Form Template -->
        <template id="phone_collect_form" name="WeChat Phone Collection">
            <t t-call="website.layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-header bg-success text-white text-center">
                                        <h4><i class="fa fa-wechat"></i> 微信授权</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="text-center mb-4">
                                            <img t-if="oauth_record.headimgurl" 
                                                 t-att-src="oauth_record.headimgurl" 
                                                 class="rounded-circle" 
                                                 style="width: 80px; height: 80px;"
                                                 alt="Avatar"/>
                                            <h5 class="mt-2" t-esc="oauth_record.nickname or 'WeChat User'"/>
                                            <small class="text-muted">请完善您的联系信息</small>
                                        </div>
                                        
                                        <t t-if="error">
                                            <div class="alert alert-danger" role="alert">
                                                <i class="fa fa-exclamation-triangle"></i>
                                                <t t-esc="error"/>
                                            </div>
                                        </t>
                                        
                                        <form method="post" action="/wechat/phone/submit" id="phoneForm">
                                            <input type="hidden" name="oauth_id" t-att-value="oauth_record.id"/>
                                            
                                            <div class="form-group mb-3">
                                                <label for="phone_number" class="form-label">
                                                    <i class="fa fa-phone"></i> 手机号码 *
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text">+86</span>
                                                    <input type="tel" 
                                                           class="form-control" 
                                                           id="phone_number" 
                                                           name="phone_number" 
                                                           placeholder="请输入手机号码"
                                                           pattern="^1[3-9]\d{9}$"
                                                           required="required"
                                                           maxlength="11"/>
                                                </div>
                                                <small class="form-text text-muted">
                                                    我们将使用此号码为您提供更好的服务
                                                </small>
                                            </div>
                                            
                                            <!-- Optional: SMS Verification -->
                                            <div class="form-group mb-3" style="display: none;" id="smsGroup">
                                                <label for="verification_code" class="form-label">
                                                    <i class="fa fa-shield"></i> 验证码
                                                </label>
                                                <div class="input-group">
                                                    <input type="text" 
                                                           class="form-control" 
                                                           id="verification_code" 
                                                           name="verification_code" 
                                                           placeholder="请输入验证码"
                                                           maxlength="6"/>
                                                    <button type="button" 
                                                            class="btn btn-outline-secondary" 
                                                            id="sendSmsBtn">
                                                        发送验证码
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group mb-3">
                                                <div class="form-check">
                                                    <input type="checkbox" 
                                                           class="form-check-input" 
                                                           id="agree_terms" 
                                                           required="required"/>
                                                    <label class="form-check-label" for="agree_terms">
                                                        我同意 <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">隐私政策</a> 和 <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">服务条款</a>
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <button type="submit" class="btn btn-success btn-block w-100">
                                                <i class="fa fa-check"></i> 确认提交
                                            </button>
                                        </form>
                                        
                                        <div class="text-center mt-3">
                                            <small class="text-muted">
                                                <i class="fa fa-lock"></i> 您的信息将被安全保护
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Privacy Policy Modal -->
                <div class="modal fade" id="privacyModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">隐私政策</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>我们承诺保护您的隐私和个人信息安全。</p>
                                <ul>
                                    <li>我们只收集必要的信息用于提供服务</li>
                                    <li>您的信息不会被出售或分享给第三方</li>
                                    <li>我们采用行业标准的安全措施保护您的数据</li>
                                    <li>您有权随时查看、修改或删除您的个人信息</li>
                                </ul>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Terms of Service Modal -->
                <div class="modal fade" id="termsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">服务条款</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>使用我们的服务即表示您同意以下条款：</p>
                                <ul>
                                    <li>您提供的信息必须真实有效</li>
                                    <li>我们将根据您的授权使用您的信息</li>
                                    <li>您可以随时取消授权</li>
                                    <li>我们保留修改服务条款的权利</li>
                                </ul>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Phone number validation
                        const phoneInput = document.getElementById('phone_number');
                        const sendSmsBtn = document.getElementById('sendSmsBtn');
                        
                        phoneInput.addEventListener('input', function() {
                            const phone = this.value;
                            const isValid = /^1[3-9]\d{9}$/.test(phone);
                            
                            if (isValid) {
                                this.classList.remove('is-invalid');
                                this.classList.add('is-valid');
                                sendSmsBtn.disabled = false;
                            } else {
                                this.classList.remove('is-valid');
                                if (phone.length > 0) {
                                    this.classList.add('is-invalid');
                                }
                                sendSmsBtn.disabled = true;
                            }
                        });
                        
                        // Send SMS verification (optional)
                        sendSmsBtn.addEventListener('click', function() {
                            const phone = phoneInput.value;
                            if (!/^1[3-9]\d{9}$/.test(phone)) {
                                alert('请输入正确的手机号码');
                                return;
                            }
                            
                            // Disable button and show loading
                            this.disabled = true;
                            this.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 发送中...';
                            
                            // Send AJAX request
                            fetch('/wechat/phone/verify', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        phone_number: phone
                                    }
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.result &amp;&amp; data.result.success) {
                                    alert('验证码已发送');
                                    document.getElementById('smsGroup').style.display = 'block';
                                    
                                    // Start countdown
                                    let countdown = 60;
                                    const timer = setInterval(() => {
                                        this.innerHTML = `${countdown}秒后重发`;
                                        countdown--;
                                        if (countdown &lt; 0) {
                                            clearInterval(timer);
                                            this.disabled = false;
                                            this.innerHTML = '发送验证码';
                                        }
                                    }, 1000);
                                } else {
                                    alert(data.result ? data.result.error : '发送失败');
                                    this.disabled = false;
                                    this.innerHTML = '发送验证码';
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('发送失败，请重试');
                                this.disabled = false;
                                this.innerHTML = '发送验证码';
                            });
                        });
                        
                        // Form validation
                        document.getElementById('phoneForm').addEventListener('submit', function(e) {
                            const phone = phoneInput.value;
                            if (!/^1[3-9]\d{9}$/.test(phone)) {
                                e.preventDefault();
                                alert('请输入正确的手机号码');
                                return false;
                            }
                        });
                    });
                </script>
            </t>
        </template>
        
        <!-- Success Page Template -->
        <template id="phone_success" name="WeChat Phone Collection Success">
            <t t-call="website.layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-body text-center">
                                        <div class="mb-4">
                                            <i class="fa fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        </div>
                                        <h4 class="text-success">授权成功！</h4>
                                        <p class="text-muted">感谢您完善联系信息，我们将为您提供更好的服务。</p>
                                        
                                        <div class="mt-4">
                                            <img t-if="oauth_record.headimgurl" 
                                                 t-att-src="oauth_record.headimgurl" 
                                                 class="rounded-circle" 
                                                 style="width: 60px; height: 60px;"
                                                 alt="Avatar"/>
                                            <h6 class="mt-2" t-esc="oauth_record.nickname or 'WeChat User'"/>
                                            <small class="text-muted">
                                                手机号: <t t-esc="oauth_record.phone_number"/>
                                            </small>
                                        </div>
                                        
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-success" onclick="window.close();">
                                                <i class="fa fa-times"></i> 关闭窗口
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        
        <!-- Error Page Template -->
        <template id="oauth_error" name="WeChat OAuth Error">
            <t t-call="website.layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-body text-center">
                                        <div class="mb-4">
                                            <i class="fa fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                                        </div>
                                        <h4 class="text-warning">授权失败</h4>
                                        <p class="text-muted" t-esc="error or '未知错误'"/>
                                        
                                        <div class="mt-4">
                                            <button type="button" class="btn btn-secondary" onclick="history.back();">
                                                <i class="fa fa-arrow-left"></i> 返回
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        
    </data>
</odoo>
