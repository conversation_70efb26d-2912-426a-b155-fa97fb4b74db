<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- 微信模板消息表单视图 -->
        <record id="view_wechat_template_form" model="ir.ui.view">
            <field name="name">wechat.template.form</field>
            <field name="model">wechat.template</field>
            <field name="arch" type="xml">
                <form string="微信模板消息">
                    <header>
                        <button name="action_send_template_message" type="object" string="发送消息"
                                class="btn-primary"/>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="模板名称，如：报名审批通知"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="基本信息">
                                <field name="template_id"/>
                                <field name="title"/>
                                <field name="config_id" options="{'no_create': True, 'no_open': True}"/>
                                <field name="sequence"/>
                            </group>
                            <group string="行业信息">
                                <field name="primary_industry"/>
                                <field name="deputy_industry"/>
                                <field name="usage_count" readonly="1"/>
                                <field name="last_used" readonly="1"/>
                            </group>
                        </group>

                        <group string="模板内容">
                            <field name="content" nolabel="1" placeholder="请输入模板内容，使用 {{参数名.DATA}} 格式定义参数"/>
                        </group>

                        <group string="使用示例">
                            <field name="example" nolabel="1" placeholder="模板使用示例"/>
                        </group>

                        <!-- 跳转设置 -->
                        <group string="跳转设置">
                            <group>
                                <field name="url" placeholder="https://example.com/page"/>
                            </group>
                            <group>
                                <field name="miniprogram_appid" placeholder="小程序AppID"/>
                                <field name="miniprogram_pagepath" placeholder="pages/index/index"/>
                            </group>
                        </group>

                        <!-- 模板参数 -->
                        <notebook>
                            <page string="模板参数" name="parameters">
                                <field name="parameter_ids">
                                    <list editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="data_type"/>
                                        <field name="is_required"/>
                                        <field name="default_value"/>
                                        <field name="example_value"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>

                    </sheet>
                </form>
            </field>
        </record>

        <!-- 微信模板消息列表视图 -->
        <record id="view_wechat_template_list" model="ir.ui.view">
            <field name="name">wechat.template.list</field>
            <field name="model">wechat.template</field>
            <field name="arch" type="xml">
                <list string="微信模板消息" default_order="sequence, name">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="template_id"/>
                    <field name="title"/>
                    <field name="config_id"/>
                    <field name="usage_count"/>
                    <field name="last_used"/>
                    <field name="active" widget="boolean_toggle"/>
                    
                    <!-- 操作按钮 -->
                    <button name="action_send_template_message" type="object" string="发送"
                            class="btn-sm btn-primary" icon="fa-send"/>
                </list>
            </field>
        </record>

        <!-- 微信模板消息搜索视图 -->
        <record id="view_wechat_template_search" model="ir.ui.view">
            <field name="name">wechat.template.search</field>
            <field name="model">wechat.template</field>
            <field name="arch" type="xml">
                <search string="搜索模板消息">
                    <field name="name" string="模板名称"/>
                    <field name="template_id" string="模板ID"/>
                    <field name="title" string="标题"/>
                    <field name="config_id" string="微信配置"/>
                    
                    <!-- 过滤器 -->
                    <filter name="active" string="启用" domain="[('active', '=', True)]"/>
                    <filter name="inactive" string="禁用" domain="[('active', '=', False)]"/>
                    
                    <!-- 分组 -->
                    <group expand="0" string="分组">
                        <filter name="group_config" string="微信配置" context="{'group_by': 'config_id'}"/>
                        <filter name="group_industry" string="主行业" context="{'group_by': 'primary_industry'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- 微信模板消息看板视图 -->
        <record id="view_wechat_template_kanban" model="ir.ui.view">
            <field name="name">wechat.template.kanban</field>
            <field name="model">wechat.template</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="template_id"/>
                    <field name="title"/>
                    <field name="usage_count"/>
                    <field name="active"/>
                    <templates>
                        <t t-name="card">
                            <div class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <div class="o_kanban_record_subtitle">
                                        <field name="template_id"/>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <field name="title"/>
                                    </div>
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <span class="badge badge-pill badge-info">
                                                使用 <field name="usage_count"/> 次
                                            </span>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <field name="active" widget="boolean_toggle"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- 发送模板消息向导表单视图 -->
        <record id="view_wechat_template_send_wizard_form" model="ir.ui.view">
            <field name="name">wechat.template.send.wizard.form</field>
            <field name="model">wechat.template.send.wizard</field>
            <field name="arch" type="xml">
                <form string="发送模板消息">
                    <header>
                        <button name="action_send_template" type="object" string="发送消息" 
                                class="btn-primary"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <span>发送模板消息</span>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="模板信息">
                                <field name="template_id" readonly="1"/>
                                <field name="recipient_type"/>
                            </group>
                        </group>

                        <!-- 接收者设置 -->
                        <group string="接收者设置">
                            <field name="user_id" invisible="recipient_type != 'single'" 
                                   options="{'no_create': True, 'no_open': True}"/>
                            <field name="openid" invisible="recipient_type != 'single'" 
                                   placeholder="或直接输入OpenID"/>
                            <field name="user_ids" invisible="recipient_type != 'multiple'" 
                                   widget="many2many_tags"/>
                        </group>

                        <!-- 跳转设置 -->
                        <group string="跳转设置">
                            <group>
                                <field name="url" placeholder="https://example.com/page"/>
                            </group>
                            <group>
                                <field name="miniprogram_appid" placeholder="小程序AppID"/>
                                <field name="miniprogram_pagepath" placeholder="pages/index/index"/>
                            </group>
                        </group>

                        <!-- 模板参数 -->
                        <group string="模板参数">
                            <field name="parameter_value_ids" nolabel="1">
                                <list editable="bottom">
                                    <field name="parameter_name" readonly="1"/>
                                    <field name="parameter_description" readonly="1"/>
                                    <field name="value" required="1"/>
                                    <field name="color"/>
                                    <field name="parameter_id" column_invisible="1"/>
                                </list>
                            </field>
                        </group>

                    </sheet>
                </form>
            </field>
        </record>

        <!-- 微信模板消息动作 -->
        <record id="action_wechat_template" model="ir.actions.act_window">
            <field name="name">微信模板消息</field>
            <field name="res_model">wechat.template</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="search_view_id" ref="view_wechat_template_search"/>
            <field name="context">{
                'search_default_active': 1,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建第一个微信模板消息
                </p>
                <p>
                    微信模板消息用于向用户发送重要通知。<br/>
                    您需要先在微信公众号平台申请模板，然后在这里配置。
                </p>
            </field>
        </record>

        <!-- 模板消息菜单项 -->
        <menuitem id="menu_wechat_template_main"
                  name="模板消息"
                  parent="wechat_official_account.menu_wechat_messages"
                  action="action_wechat_template"
                  sequence="20"/>

    </data>
</odoo>
