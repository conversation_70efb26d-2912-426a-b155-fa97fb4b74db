<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- WeChat Message Form View -->
        <record id="view_wechat_message_form" model="ir.ui.view">
            <field name="name">wechat.message.form</field>
            <field name="model">wechat.message</field>
            <field name="arch" type="xml">
                <form string="WeChat Message">
                    <header>
                        <button name="send_message" string="Send Message" type="object"
                                class="btn-primary" icon="fa-send"
                                invisible="state != 'draft'"/>
                        <button name="action_send_test" string="Send Test" type="object"
                                class="btn-secondary" icon="fa-flask"
                                invisible="state != 'draft'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,sending,sent"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" icon="fa-users">
                                <field name="total_recipients" widget="statinfo" string="Recipients"/>
                            </button>
                            <button class="oe_stat_button" icon="fa-check"
                                    invisible="sent_count == 0">
                                <field name="sent_count" widget="statinfo" string="Sent"/>
                            </button>
                            <button class="oe_stat_button" icon="fa-times"
                                    invisible="failed_count == 0">
                                <field name="failed_count" widget="statinfo" string="Failed"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="subject" placeholder="Message Subject"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Message Settings">
                                <field name="config_id" required="1"/>
                                <field name="message_type" required="1"/>
                                <field name="scheduled_time"/>
                            </group>
                            <group string="Recipients">
                                <field name="recipient_type" required="1"/>
                                <field name="user_id"
                                       invisible="recipient_type != 'single'"
                                       required="recipient_type == 'single'"
                                       domain="[('config_id', '=', config_id), ('subscribe', '=', True)]"/>
                                <field name="recipient_openid"
                                       invisible="recipient_type != 'single'"/>
                                <field name="user_ids" widget="many2many_tags"
                                       invisible="recipient_type != 'multiple'"
                                       required="recipient_type == 'multiple'"
                                       domain="[('config_id', '=', config_id), ('subscribe', '=', True)]"/>
                                <field name="recipient_tag_id"
                                       invisible="recipient_type != 'tag'"
                                       required="recipient_type == 'tag'"/>
                                <field name="recipient_group_id"
                                       invisible="recipient_type != 'group'"
                                       required="recipient_type == 'group'"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Content" name="content">
                                <group>
                                    <!-- Text Message Content -->
                                    <field name="content" widget="text"
                                           invisible="message_type != 'text'"
                                           required="message_type == 'text'"
                                           placeholder="Enter your message content here..."/>

                                    <!-- Media Message Content -->
                                    <group string="Media Information"
                                           invisible="message_type not in ['image', 'voice', 'video']">
                                        <field name="media_id"
                                               required="message_type in ['image', 'voice', 'video']"/>
                                        <field name="media_url"/>
                                    </group>

                                    <!-- News Article Content -->
                                    <group string="Article Information"
                                           invisible="message_type != 'news'">
                                        <field name="title"
                                               required="message_type == 'news'"/>
                                        <field name="description" widget="text"/>
                                        <field name="pic_url"/>
                                        <field name="url"/>
                                    </group>

                                    <!-- Template Message Content -->
                                    <group string="Template Information"
                                           invisible="message_type != 'template'">
                                        <field name="template_id"
                                               required="message_type == 'template'"
                                               options="{'no_create': True, 'no_open': True}"/>
                                        <field name="template_data" widget="text"
                                               placeholder='{"thing18": {"value": "张三"}, "time9": {"value": "2022年12月22日12:22"}}'
                                               help="JSON格式的模板参数数据"/>
                                        <field name="url" placeholder="跳转链接（可选）"/>
                                        <field name="miniprogram_appid" placeholder="小程序AppID（可选）"/>
                                        <field name="miniprogram_pagepath" placeholder="小程序页面路径（可选）"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Recipients Details" name="recipients"
                                  invisible="recipient_type != 'multiple'">
                                <field name="recipient_openids" widget="text"
                                       placeholder="Enter OpenIDs, one per line"/>
                            </page>

                            <page string="Sending Results" name="results"
                                  invisible="state == 'draft'">
                                <group>
                                    <group string="Sending Information">
                                        <field name="send_time" readonly="1"/>
                                        <field name="sent_count" readonly="1"/>
                                        <field name="failed_count" readonly="1"/>
                                    </group>
                                    <group string="WeChat Response">
                                        <field name="msg_id" readonly="1"/>
                                        <field name="msg_data_id" readonly="1"/>
                                    </group>
                                </group>
                                <group string="Error Information"
                                       invisible="not error_message">
                                    <field name="error_message" readonly="1" widget="text"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- WeChat Message Tree View -->
        <record id="view_wechat_message_tree" model="ir.ui.view">
            <field name="name">wechat.message.tree</field>
            <field name="model">wechat.message</field>
            <field name="arch" type="xml">
                <list string="WeChat Messages" decoration-success="state=='sent'"
                      decoration-danger="state=='failed'" decoration-warning="state=='partial'">
                    <field name="subject"/>
                    <field name="message_type"/>
                    <field name="recipient_type"/>
                    <field name="total_recipients"/>
                    <field name="sent_count"/>
                    <field name="failed_count"/>
                    <field name="send_time"/>
                    <field name="state" widget="badge" 
                           decoration-success="state=='sent'" 
                           decoration-danger="state=='failed'"
                           decoration-warning="state=='partial'"
                           decoration-info="state=='sending'"
                           decoration-muted="state=='draft'"/>
                    <field name="config_id"/>
                </list>
            </field>
        </record>
        
        <!-- WeChat Message Search View -->
        <record id="view_wechat_message_search" model="ir.ui.view">
            <field name="name">wechat.message.search</field>
            <field name="model">wechat.message</field>
            <field name="arch" type="xml">
                <search string="WeChat Messages">
                    <field name="subject"/>
                    <field name="content"/>
                    <field name="config_id"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Sent" name="sent" domain="[('state', '=', 'sent')]"/>
                    <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                    <separator/>
                    <filter string="Text Messages" name="text" domain="[('message_type', '=', 'text')]"/>
                    <filter string="Image Messages" name="image" domain="[('message_type', '=', 'image')]"/>
                    <filter string="News Messages" name="news" domain="[('message_type', '=', 'news')]"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Message Type" name="group_type" domain="[]" context="{'group_by': 'message_type'}"/>
                        <filter string="Recipient Type" name="group_recipient" domain="[]" context="{'group_by': 'recipient_type'}"/>
                        <filter string="Status" name="group_state" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Configuration" name="group_config" domain="[]" context="{'group_by': 'config_id'}"/>
                        <filter string="Send Date" name="group_date" domain="[]" context="{'group_by': 'send_time:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        
    </data>
</odoo>
