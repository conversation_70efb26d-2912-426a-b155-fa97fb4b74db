<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- WeChat Configuration Action -->
        <record id="action_wechat_config" model="ir.actions.act_window">
            <field name="name">微信配置</field>
            <field name="res_model">wechat.config</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WeChat configuration!
                </p>
                <p>
                    Configure your WeChat Official Account settings to start sending messages
                    and managing your WeChat users.
                </p>
            </field>
        </record>

        <!-- WeChat Message Action -->
        <record id="action_wechat_message" model="ir.actions.act_window">
            <field name="name">WeChat Messages</field>
            <field name="res_model">wechat.message</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WeChat message!
                </p>
                <p>
                    Send text messages, images, or news articles to your WeChat users.
                    You can send to individual users, multiple users, or broadcast to all subscribers.
                </p>
            </field>
        </record>

        <!-- WeChat User Action -->
        <record id="action_wechat_user" model="ir.actions.act_window">
            <field name="name">WeChat Users</field>
            <field name="res_model">wechat.user</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WeChat users found!
                </p>
                <p>
                    WeChat users will appear here when they subscribe to your official account
                    or when you sync users from WeChat.
                </p>
            </field>
        </record>
        
        <!-- WeChat Configuration Wizard Action -->
        <record id="action_wechat_config_wizard" model="ir.actions.act_window">
            <field name="name">WeChat Setup Wizard</field>
            <field name="res_model">wechat.config.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <!-- WeChat Menu Action -->
        <record id="action_wechat_menu" model="ir.actions.act_window">
            <field name="name">微信自定义菜单</field>
            <field name="res_model">wechat.menu</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建您的第一个微信自定义菜单！
                </p>
                <p>
                    配置微信公众号的自定义菜单，为用户提供便捷的功能入口。
                </p>
            </field>
        </record>

        <!-- WeChat Menu Event Action -->
        <record id="action_wechat_menu_event" model="ir.actions.act_window">
            <field name="name">微信菜单事件记录</field>
            <field name="res_model">wechat.menu.event</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无菜单事件记录！
                </p>
                <p>
                    用户点击微信菜单时的事件记录将显示在这里。
                </p>
            </field>
        </record>

        <!-- WeChat Auto Login Action -->
        <record id="action_wechat_auto_login" model="ir.actions.act_window">
            <field name="name">微信自动登录</field>
            <field name="res_model">wechat.auto.login</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无自动登录记录！
                </p>
                <p>
                    用户通过微信自动登录的记录将显示在这里。
                </p>
            </field>
        </record>

        <!-- Main WeChat Menu -->
        <menuitem id="menu_wechat_root" name="微信公众号" sequence="100" web_icon="wechat_official_account,static/description/icon.png"/>

        <!-- Configuration Menu -->
        <menuitem id="menu_wechat_config" name="配置" parent="menu_wechat_root" sequence="10"/>
        <menuitem id="menu_wechat_config_main" name="微信配置" parent="menu_wechat_config" action="action_wechat_config" sequence="10"/>
        <menuitem id="menu_wechat_config_wizard" name="配置向导" parent="menu_wechat_config" action="action_wechat_config_wizard" sequence="20"/>

        <!-- Messages Menu -->
        <menuitem id="menu_wechat_messages" name="消息管理" parent="menu_wechat_root" sequence="20"/>
        <menuitem id="menu_wechat_message_main" name="消息发送" parent="menu_wechat_messages" action="action_wechat_message" sequence="10"/>

        <!-- Users Menu -->
        <menuitem id="menu_wechat_users" name="用户管理" parent="menu_wechat_root" sequence="30"/>
        <menuitem id="menu_wechat_user_main" name="微信用户" parent="menu_wechat_users" action="action_wechat_user" sequence="10"/>
        <menuitem id="menu_wechat_auto_login_main" name="自动登录" parent="menu_wechat_users" action="action_wechat_auto_login" sequence="20"/>

        <!-- Menu Management -->
        <menuitem id="menu_wechat_menu_mgmt" name="菜单管理" parent="menu_wechat_root" sequence="40"/>
        <menuitem id="menu_wechat_menu_main" name="自定义菜单" parent="menu_wechat_menu_mgmt" action="action_wechat_menu" sequence="10"/>
        <menuitem id="menu_wechat_menu_event_main" name="菜单事件" parent="menu_wechat_menu_mgmt" action="action_wechat_menu_event" sequence="20"/>

    </data>
</odoo>
