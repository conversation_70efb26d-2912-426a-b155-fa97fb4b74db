<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- WeChat User Form View -->
        <record id="view_wechat_user_form" model="ir.ui.view">
            <field name="name">wechat.user.form</field>
            <field name="model">wechat.user</field>
            <field name="arch" type="xml">
                <form string="WeChat User">
                    <header>
                        <button name="update_user_info" string="Update Info" type="object"
                                class="btn-secondary" icon="fa-refresh"
                                help="Update user information from WeChat API"/>
                        <button name="create_partner" string="Create Partner" type="object"
                                class="btn-primary" icon="fa-user-plus"
                                invisible="partner_id != False"
                                help="Create a partner record for this WeChat user"/>
                        <button name="unsubscribe_user" string="Mark as Unsubscribed" type="object"
                                class="btn-warning" icon="fa-user-times"
                                invisible="subscribe == False"
                                help="Mark this user as unsubscribed"/>
                        <button name="subscribe_user" string="Mark as Subscribed" type="object"
                                class="btn-success" icon="fa-user-check"
                                invisible="subscribe == True"
                                help="Mark this user as subscribed"/>
                        <field name="subscribe" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="%(wechat_official_account.action_wechat_message)d" type="action"
                                    class="oe_stat_button" icon="fa-envelope">
                                <field name="message_count" widget="statinfo" string="Messages"/>
                            </button>
                            <button name="action_view_partner" type="object"
                                    class="oe_stat_button" icon="fa-user"
                                    invisible="partner_id == False">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Partner</span>
                                </div>
                            </button>
                        </div>
                        
                        <field name="headimgurl" widget="image" class="oe_avatar" 
                               options="{'preview_image': 'headimgurl', 'size': [90, 90]}"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="nickname" placeholder="User Nickname"/>
                            </h1>
                            <h3>
                                <field name="openid" readonly="1"/>
                            </h3>
                        </div>
                        
                        <group>
                            <group string="Basic Information">
                                <field name="unionid"/>
                                <field name="sex"/>
                                <field name="language"/>
                                <field name="config_id"/>
                                <field name="partner_id"/>
                            </group>
                            <group string="Location">
                                <field name="country"/>
                                <field name="province"/>
                                <field name="city"/>
                                <field name="user_id" string="内部用户/老师绑定"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Subscription Information">
                                <field name="subscribe_time" readonly="1"/>
                                <field name="unsubscribe_time" readonly="1"
                                       invisible="not unsubscribe_time"/>
                                <field name="subscribe_scene"/>
                                <field name="qr_scene"/>
                                <field name="qr_scene_str"/>
                            </group>
                            <group string="Message Statistics">
                                <field name="message_count" readonly="1"/>
                                <field name="last_message_time" readonly="1"/>
                            </group>
                        </group>
                        
                        <group string="Tags and Groups">
                            <field name="tagid_list"/>
                            <field name="group_id"/>
                        </group>
                        
                        <group string="Notes">
                            <field name="remark" widget="text" placeholder="Internal notes about this user..."/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- WeChat User Tree View -->
        <record id="view_wechat_user_tree" model="ir.ui.view">
            <field name="name">wechat.user.tree</field>
            <field name="model">wechat.user</field>
            <field name="arch" type="xml">
                <list string="WeChat Users" decoration-muted="subscribe==False">
                    <header>
                        <button name="update_user_info" string="Batch Update Selected" type="object"
                                class="btn-secondary" icon="fa-refresh"
                                help="Update information for selected users from WeChat API"/>
                    </header>
                    <field name="headimgurl" widget="image" options="{'size': [40, 40]}"/>
                    <field name="nickname"/>
                    <field name="openid"/>
                    <field name="user_id" string="绑定用户"/>
                    <field name="sex"/>
                    <field name="city"/>
                    <field name="subscribe" widget="boolean_toggle"/>
                    <field name="subscribe_time"/>
                    <field name="unsubscribe_time" optional="hide"/>
                    <field name="message_count"/>
                    <field name="last_message_time"/>
                    <field name="config_id"/>
                    <field name="partner_id"/>
                </list>
            </field>
        </record>
        
        <!-- WeChat User Kanban View -->
        <record id="view_wechat_user_kanban" model="ir.ui.view">
            <field name="name">wechat.user.kanban</field>
            <field name="model">wechat.user</field>
            <field name="arch" type="xml">
                <kanban string="WeChat Users">
                    <field name="id"/>
                    <field name="nickname"/>
                    <field name="openid"/>
                    <field name="headimgurl"/>
                    <field name="subscribe"/>
                    <field name="city"/>
                    <field name="message_count"/>
                    <field name="subscribe_time"/>
                    <templates>
                        <t t-name="card">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_image">
                                    <img t-att-src="record.headimgurl.raw_value or '/web/static/img/placeholder.png'"
                                         alt="Avatar" class="o_image_64_cover"/>
                                </div>
                                <div class="oe_kanban_details">
                                    <strong class="o_kanban_record_title">
                                        <field name="nickname"/>
                                    </strong>
                                    <div class="o_kanban_record_subtitle">
                                        <field name="city"/> • <field name="openid"/>
                                    </div>
                                    <div class="oe_kanban_bottom_left">
                                        <span class="badge badge-pill"
                                              t-att-class="record.subscribe.raw_value ? 'badge-success' : 'badge-secondary'">
                                            <t t-if="record.subscribe.raw_value">Subscribed</t>
                                            <t t-else="">Unsubscribed</t>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span class="text-muted">
                                            <i class="fa fa-envelope" title="消息数量"/> <field name="message_count"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- WeChat User Search View -->
        <record id="view_wechat_user_search" model="ir.ui.view">
            <field name="name">wechat.user.search</field>
            <field name="model">wechat.user</field>
            <field name="arch" type="xml">
                <search string="WeChat Users">
                    <field name="nickname"/>
                    <field name="openid"/>
                    <field name="city"/>
                    <field name="config_id"/>
                    <filter string="Subscribed" name="subscribed" domain="[('subscribe', '=', True)]"/>
                    <filter string="Unsubscribed" name="unsubscribed" domain="[('subscribe', '=', False)]"/>
                    <separator/>
                    <filter string="Has Partner" name="has_partner" domain="[('partner_id', '!=', False)]"/>
                    <filter string="No Partner" name="no_partner" domain="[('partner_id', '=', False)]"/>
                    <separator/>
                    <filter string="Active Users" name="active_users" domain="[('message_count', '>', 0)]"/>
                    <filter string="Recent Subscribers" name="recent" 
                            domain="[('subscribe_time', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Subscription Status" name="group_subscribe" domain="[]" context="{'group_by': 'subscribe'}"/>
                        <filter string="Gender" name="group_sex" domain="[]" context="{'group_by': 'sex'}"/>
                        <filter string="City" name="group_city" domain="[]" context="{'group_by': 'city'}"/>
                        <filter string="Configuration" name="group_config" domain="[]" context="{'group_by': 'config_id'}"/>
                        <filter string="Subscribe Date" name="group_date" domain="[]" context="{'group_by': 'subscribe_time:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        
        <!-- Action to view partner from WeChat user -->
        <record id="action_view_partner_from_wechat_user" model="ir.actions.act_window">
            <field name="name">Partner</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">form</field>
            <field name="target">current</field>
        </record>
        
    </data>
</odoo>
