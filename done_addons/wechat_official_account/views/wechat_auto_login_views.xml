<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Auto Login List View -->
        <record id="view_wechat_auto_login_list" model="ir.ui.view">
            <field name="name">wechat.auto.login.list</field>
            <field name="model">wechat.auto.login</field>
            <field name="arch" type="xml">
                <list string="WeChat Auto Login Records" default_order="create_date desc">
                    <field name="display_name"/>
                    <field name="user_id"/>
                    <field name="phone_number"/>
                    <field name="config_id"/>
                    <field name="enabled" widget="boolean_toggle"/>
                    <field name="login_count"/>
                    <field name="last_login"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>
        
        <!-- Auto Login Form View -->
        <record id="view_wechat_auto_login_form" model="ir.ui.view">
            <field name="name">wechat.auto.login.form</field>
            <field name="model">wechat.auto.login</field>
            <field name="arch" type="xml">
                <form string="WeChat Auto Login">
                    <header>
                        <button name="enable_auto_login" string="Enable Auto Login" 
                                type="object" class="btn-primary"
                                invisible="enabled"/>
                        <button name="disable_auto_login" string="Disable Auto Login" 
                                type="object" class="btn-warning"
                                invisible="not enabled"/>
                        <button name="revoke_access" string="Revoke Access" 
                                type="object" class="btn-danger"
                                confirm="Are you sure you want to revoke auto login access? This action cannot be undone."/>
                        <field name="active" widget="boolean_button" 
                               options="{'terminology': {'string_true': 'Active', 'string_false': 'Archived'}}"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_user" type="object" class="oe_stat_button" icon="fa-user">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">View User</span>
                                </div>
                            </button>
                            <button name="action_view_wechat_user" type="object" class="oe_stat_button" icon="fa-wechat">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">WeChat User</span>
                                </div>
                            </button>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="openid"/>
                                <field name="unionid"/>
                                <field name="config_id"/>
                                <field name="user_id"/>
                                <field name="wechat_user_id"/>
                            </group>
                            <group name="contact_info" string="Contact Information">
                                <field name="phone_number"/>
                                <field name="phone_verified" widget="boolean_toggle"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="login_settings" string="Auto Login Settings">
                                <field name="enabled" widget="boolean_toggle"/>
                                <field name="trusted_device" widget="boolean_toggle"/>
                                <field name="login_token" password="True" readonly="1"/>
                                <field name="token_expires"/>
                            </group>
                            <group name="statistics" string="Login Statistics">
                                <field name="login_count"/>
                                <field name="last_login"/>
                                <field name="last_ip"/>
                                <field name="create_date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Auto Login Kanban View -->
        <record id="view_wechat_auto_login_kanban" model="ir.ui.view">
            <field name="name">wechat.auto.login.kanban</field>
            <field name="model">wechat.auto.login</field>
            <field name="arch" type="xml">
                <kanban default_group_by="config_id" class="o_kanban_small_column">
                    <field name="id"/>
                    <field name="display_name"/>
                    <field name="user_id"/>
                    <field name="phone_number"/>
                    <field name="enabled"/>
                    <field name="login_count"/>
                    <field name="last_login"/>
                    <field name="wechat_user_id"/>
                    <templates>
                        <t t-name="card">
                            <div class="oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="display_name"/>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <field name="user_id"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                                
                                <div class="o_kanban_record_body">
                                    <div class="o_kanban_tags_section">
                                        <span t-if="record.phone_number.raw_value" class="badge badge-pill badge-info">
                                            <i class="fa fa-phone"/> <t t-esc="record.phone_number.value"/>
                                        </span>
                                        <span t-if="record.enabled.raw_value" class="badge badge-pill badge-success">
                                            <i class="fa fa-check"/> Enabled
                                        </span>
                                        <span t-if="!record.enabled.raw_value" class="badge badge-pill badge-secondary">
                                            <i class="fa fa-times"/> Disabled
                                        </span>
                                    </div>
                                    
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            Logins: <t t-esc="record.login_count.value"/>
                                            <t t-if="record.last_login.raw_value">
                                                | Last: <t t-esc="record.last_login.value"/>
                                            </t>
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="o_kanban_manage_button_section o_kanban_manage_view">
                                    <div class="o_kanban_left_panel">
                                        <a type="edit">Edit</a>
                                    </div>
                                    <div class="o_kanban_right_panel">
                                        <a name="enable_auto_login" type="object" 
                                           t-if="!record.enabled.raw_value">Enable</a>
                                        <a name="disable_auto_login" type="object" 
                                           t-if="record.enabled.raw_value">Disable</a>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Auto Login Search View -->
        <record id="view_wechat_auto_login_search" model="ir.ui.view">
            <field name="name">wechat.auto.login.search</field>
            <field name="model">wechat.auto.login</field>
            <field name="arch" type="xml">
                <search string="WeChat Auto Login">
                    <field name="display_name"/>
                    <field name="user_id"/>
                    <field name="phone_number"/>
                    <field name="openid"/>
                    <field name="config_id"/>
                    
                    <filter name="enabled" string="Enabled" domain="[('enabled', '=', True)]"/>
                    <filter name="disabled" string="Disabled" domain="[('enabled', '=', False)]"/>
                    <filter name="phone_verified" string="Phone Verified" domain="[('phone_verified', '=', True)]"/>
                    <filter name="trusted" string="Trusted Device" domain="[('trusted_device', '=', True)]"/>
                    
                    <separator/>
                    <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                    <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                    
                    <separator/>
                    <filter name="recent_login" string="Recent Login (7 days)" 
                            domain="[('last_login', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter name="group_config" string="WeChat Config" context="{'group_by': 'config_id'}"/>
                        <filter name="group_user" string="User" context="{'group_by': 'user_id'}"/>
                        <filter name="group_enabled" string="Status" context="{'group_by': 'enabled'}"/>
                        <filter name="group_company" string="Company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Auto Login Action -->
        <record id="action_wechat_auto_login" model="ir.actions.act_window">
            <field name="name">WeChat Auto Login</field>
            <field name="res_model">wechat.auto.login</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WeChat auto login records found!
                </p>
                <p>
                    WeChat auto login records are created automatically when users set up passwordless login via WeChat.
                </p>
            </field>
        </record>
        
        <!-- User Form View Extension -->
        <record id="view_users_form_wechat_auto_login" model="ir.ui.view">
            <field name="name">res.users.form.wechat.auto.login</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <div name="button_box" position="inside">
                    <button name="action_view_wechat_auto_login" type="object" 
                            class="oe_stat_button" icon="fa-wechat">
                        <field name="wechat_auto_login_count" widget="statinfo" 
                               string="WeChat Auto Login"/>
                    </button>
                </div>
            </field>
        </record>
        
    </data>
</odoo>
