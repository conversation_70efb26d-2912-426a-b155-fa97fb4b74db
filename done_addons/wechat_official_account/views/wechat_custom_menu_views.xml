<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- 微信菜单列表视图 -->
        <record id="view_wechat_menu_list" model="ir.ui.view">
            <field name="name">wechat.menu.list</field>
            <field name="model">wechat.menu</field>
            <field name="arch" type="xml">
                <list string="微信自定义菜单" create="true" edit="true" delete="true">
                    <field name="name"/>
                    <field name="config_id"/>
                    <field name="state" decoration-success="state == 'published'" decoration-danger="state == 'error'"/>
                    <field name="total_items"/>
                    <field name="published_date"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

        <!-- 微信菜单表单视图 -->
        <record id="view_wechat_menu_form" model="ir.ui.view">
            <field name="name">wechat.menu.form</field>
            <field name="model">wechat.menu</field>
            <field name="arch" type="xml">
                <form string="微信自定义菜单">
                    <header>
                        <button name="action_publish_menu" type="object" string="发布菜单"
                                class="btn-primary" invisible="state == 'published'"/>
                        <button name="action_update_menu" type="object" string="更新菜单"
                                class="btn-primary" invisible="state != 'published'"
                                title="删除现有菜单并重新发布"/>
                        <button name="action_delete_menu" type="object" string="删除菜单"
                                class="btn-secondary" invisible="state != 'published'"/>
                        <button name="action_reset_menu" type="object" string="重置状态"
                                class="btn-secondary" invisible="state == 'draft'"
                                title="重置菜单状态为草稿"/>
                        <button name="action_query_menu" type="object" string="查询菜单"
                                class="btn-info"/>
                        <button name="add_auto_login_menu" type="object" string="添加登录菜单"
                                class="btn-success" title="添加自动登录菜单项"/>
                        <button name="create_quick_login_menu" type="object" string="创建登录模板"
                                class="btn-warning" title="创建快速登录菜单模板"
                                confirm="这将清除现有菜单项并创建登录模板，确定继续吗？"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,published,error"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="菜单名称..."/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="config_id" required="1"/>
                                <field name="total_items"/>
                            </group>
                            <group>
                                <field name="published_date" readonly="1"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        
                        <field name="error_message" invisible="not error_message"
                               readonly="1" widget="text" class="alert alert-danger" role="alert"/>
                        
                        <notebook>
                            <page string="菜单项" name="menu_items">
                                <field name="menu_items" context="{'default_menu_id': id}">
                                    <list editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name" required="1"/>
                                        <field name="level" readonly="1"/>
                                        <field name="parent_id" domain="[('menu_id', '=', parent.id), ('level', '=', 1)]"/>
                                        <field name="menu_type" required="1"/>
                                        <field name="key" invisible="menu_type not in ['click', 'scancode_push', 'scancode_waitmsg', 'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin', 'location_select']"/>
                                        <field name="url" invisible="menu_type not in ['view', 'miniprogram']"/>
                                        <field name="media_id" invisible="menu_type not in ['media_id', 'view_limited']"/>
                                        <field name="article_id" invisible="menu_type not in ['article_id', 'article_view_limited']"/>
                                        <field name="appid" invisible="menu_type != 'miniprogram'"/>
                                        <field name="pagepath" invisible="menu_type != 'miniprogram'"/>
                                        <field name="active"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- 微信菜单搜索视图 -->
        <record id="view_wechat_menu_search" model="ir.ui.view">
            <field name="name">wechat.menu.search</field>
            <field name="model">wechat.menu</field>
            <field name="arch" type="xml">
                <search string="微信菜单搜索">
                    <field name="name"/>
                    <field name="config_id"/>
                    <filter string="草稿" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="已发布" name="published" domain="[('state', '=', 'published')]"/>
                    <filter string="发布失败" name="error" domain="[('state', '=', 'error')]"/>
                    <group expand="0" string="分组">
                        <filter string="微信配置" name="config" domain="[]" context="{'group_by': 'config_id'}"/>
                        <filter string="状态" name="state" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="公司" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- 微信菜单项表单视图 -->
        <record id="view_wechat_menu_item_form" model="ir.ui.view">
            <field name="name">wechat.menu.item.form</field>
            <field name="model">wechat.menu.item</field>
            <field name="arch" type="xml">
                <form string="微信菜单项">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" required="1"/>
                                <field name="menu_id"/>
                                <field name="parent_id" domain="[('menu_id', '=', menu_id), ('level', '=', 1)]"/>
                                <field name="level" readonly="1"/>
                                <field name="sequence"/>
                            </group>
                            <group>
                                <field name="menu_type" required="1"/>
                                <field name="active"/>
                            </group>
                        </group>
                        
                        <group string="动作参数">
                            <group>
                                <field name="key" invisible="menu_type not in ['click', 'scancode_push', 'scancode_waitmsg', 'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin', 'location_select']" required="menu_type in ['click', 'scancode_push', 'scancode_waitmsg', 'pic_sysphoto', 'pic_photo_or_album', 'pic_weixin', 'location_select']"/>
                                <field name="url" invisible="menu_type not in ['view', 'miniprogram']" required="menu_type in ['view', 'miniprogram']"/>
                                <field name="media_id" invisible="menu_type not in ['media_id', 'view_limited']" required="menu_type in ['media_id', 'view_limited']"/>
                                <field name="article_id" invisible="menu_type not in ['article_id', 'article_view_limited']" required="menu_type in ['article_id', 'article_view_limited']"/>
                            </group>
                            <group>
                                <field name="appid" invisible="menu_type != 'miniprogram'" required="menu_type == 'miniprogram'"/>
                                <field name="pagepath" invisible="menu_type != 'miniprogram'" required="menu_type == 'miniprogram'"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- 微信菜单查询向导视图 -->
        <record id="view_wechat_menu_query_wizard_form" model="ir.ui.view">
            <field name="name">wechat.menu.query.wizard.form</field>
            <field name="model">wechat.menu.query.wizard</field>
            <field name="arch" type="xml">
                <form string="微信菜单查询结果">
                    <sheet>
                        <group>
                            <field name="menu_data" widget="text" readonly="1" nolabel="1"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_close" type="object" string="关闭" class="btn-primary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- 微信菜单动作 -->
        <record id="action_wechat_menu" model="ir.actions.act_window">
            <field name="name">微信自定义菜单</field>
            <field name="res_model">wechat.menu</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建微信自定义菜单
                </p>
                <p>
                    微信自定义菜单可以帮助公众号丰富界面，让用户更好更快地理解公众号的功能。
                </p>
            </field>
        </record>

        <!-- 微信菜单项动作 -->
        <record id="action_wechat_menu_item" model="ir.actions.act_window">
            <field name="name">微信菜单项</field>
            <field name="res_model">wechat.menu.item</field>
            <field name="view_mode">list,form</field>
        </record>

        <!-- 微信菜单事件记录列表视图 -->
        <record id="view_wechat_menu_event_list" model="ir.ui.view">
            <field name="name">wechat.menu.event.list</field>
            <field name="model">wechat.menu.event</field>
            <field name="arch" type="xml">
                <list string="微信菜单事件记录" create="false" edit="false" delete="false">
                    <field name="event_time"/>
                    <field name="config_id"/>
                    <field name="user_id"/>
                    <field name="event_type"/>
                    <field name="event_key"/>
                    <field name="description"/>
                </list>
            </field>
        </record>

        <!-- 微信菜单事件记录表单视图 -->
        <record id="view_wechat_menu_event_form" model="ir.ui.view">
            <field name="name">wechat.menu.event.form</field>
            <field name="model">wechat.menu.event</field>
            <field name="arch" type="xml">
                <form string="微信菜单事件记录" create="false" edit="false" delete="false">
                    <sheet>
                        <group>
                            <group>
                                <field name="event_time" readonly="1"/>
                                <field name="config_id" readonly="1"/>
                                <field name="user_id" readonly="1"/>
                                <field name="openid" readonly="1"/>
                            </group>
                            <group>
                                <field name="event_type" readonly="1"/>
                                <field name="event_key" readonly="1"/>
                                <field name="description" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- 微信菜单事件记录搜索视图 -->
        <record id="view_wechat_menu_event_search" model="ir.ui.view">
            <field name="name">wechat.menu.event.search</field>
            <field name="model">wechat.menu.event</field>
            <field name="arch" type="xml">
                <search string="菜单事件搜索">
                    <field name="event_key"/>
                    <field name="user_id"/>
                    <field name="config_id"/>
                    <filter string="今天" name="today" domain="[('event_time', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                    <filter string="本周" name="this_week" domain="[('event_time', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('event_time', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <filter string="本月" name="this_month" domain="[('event_time', '&gt;=', context_today().strftime('%Y-%m-01')), ('event_time', '&lt;=', (context_today().replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1))]"/>
                    <group expand="0" string="分组">
                        <filter string="事件类型" name="event_type" domain="[]" context="{'group_by': 'event_type'}"/>
                        <filter string="微信配置" name="config" domain="[]" context="{'group_by': 'config_id'}"/>
                        <filter string="日期" name="date" domain="[]" context="{'group_by': 'event_time:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- 微信菜单事件记录动作 -->
        <record id="action_wechat_menu_event" model="ir.actions.act_window">
            <field name="name">微信菜单事件记录</field>
            <field name="res_model">wechat.menu.event</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无菜单事件记录
                </p>
                <p>
                    这里显示用户与微信自定义菜单的交互记录。
                </p>
            </field>
        </record>

    </data>
</odoo>
