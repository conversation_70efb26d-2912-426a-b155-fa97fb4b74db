<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WeChat Direct Login Form View -->
        <record id="view_wechat_direct_login_form" model="ir.ui.view">
            <field name="name">wechat.direct.login.form</field>
            <field name="model">wechat.direct.login</field>
            <field name="arch" type="xml">
                <form string="微信直接登录绑定">
                    <header>
                        <button name="action_test_login" string="测试登录" type="object" 
                                class="btn-primary" icon="fa-sign-in"/>
                        <field name="enabled" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="user_id"/>
                                <field name="config_id"/>
                                <field name="openid"/>
                            </group>
                            <group>
                                <field name="wechat_user_id"/>
                                <field name="last_login_time" readonly="1"/>
                                <field name="login_count" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WeChat Direct Login List View -->
        <record id="view_wechat_direct_login_list" model="ir.ui.view">
            <field name="name">wechat.direct.login.list</field>
            <field name="model">wechat.direct.login</field>
            <field name="arch" type="xml">
                <list string="微信直接登录绑定">
                    <field name="user_id"/>
                    <field name="openid"/>
                    <field name="config_id"/>
                    <field name="wechat_user_id"/>
                    <field name="enabled" widget="boolean_toggle"/>
                    <field name="login_count"/>
                    <field name="last_login_time"/>
                </list>
            </field>
        </record>

        <!-- WeChat Direct Login Action -->
        <record id="action_wechat_direct_login" model="ir.actions.act_window">
            <field name="name">微信直接登录</field>
            <field name="res_model">wechat.direct.login</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建微信直接登录绑定
                </p>
                <p>
                    通过绑定用户和微信OpenID，实现无需OAuth的直接登录。
                </p>
            </field>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_wechat_direct_login"
                  name="直接登录"
                  parent="menu_wechat_root"
                  action="action_wechat_direct_login"
                  sequence="60"/>



        <!-- Add to Users Form View -->
        <record id="view_users_form_wechat_binding" model="ir.ui.view">
            <field name="name">res.users.form.wechat.binding</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <notebook position="inside">
                    <page string="微信绑定" name="wechat_binding">
                        <group>
                            <field name="wechat_openid" readonly="1"/>
                        </group>
                        <group>
                            <button name="action_view_wechat_bindings" string="查看绑定" type="object"
                                    class="btn-secondary" icon="fa-list"/>
                        </group>
                        <field name="wechat_binding_ids" readonly="1">
                            <list>
                                <field name="config_id"/>
                                <field name="openid"/>
                                <field name="enabled" widget="boolean_toggle"/>
                                <field name="login_count"/>
                                <field name="last_login_time"/>
                            </list>
                        </field>
                    </page>
                </notebook>
            </field>
        </record>

    </data>
</odoo>
