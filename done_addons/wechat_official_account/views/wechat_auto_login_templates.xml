<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Basic Layout Template -->
        <template id="basic_layout" name="Basic Layout">
            <html>
                <head>
                    <meta charset="utf-8"/>
                    <meta name="viewport" content="width=device-width, initial-scale=1"/>
                    <title>微信登录</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/>
                </head>
                <body>
                    <t t-raw="0"/>
                    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
                </body>
            </html>
        </template>

        <!-- Auto Login Phone Collection Form -->
        <template id="auto_phone_collect_form" name="WeChat Auto Login Phone Collection">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-header bg-primary text-white text-center">
                                        <h4><i class="fa fa-wechat"></i> 微信免密登录</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="text-center mb-4">
                                            <img t-if="user_info.get('headimgurl')" 
                                                 t-att-src="user_info['headimgurl']" 
                                                 class="rounded-circle" 
                                                 style="width: 80px; height: 80px;"
                                                 alt="Avatar"/>
                                            <div class="mt-2">
                                                <h5 t-esc="user_info.get('nickname', 'WeChat User')"/>
                                                <small class="text-muted">设置免密登录，下次自动登录</small>
                                            </div>
                                        </div>
                                        
                                        <t t-if="error">
                                            <div class="alert alert-danger" role="alert">
                                                <i class="fa fa-exclamation-triangle"></i>
                                                <t t-esc="error"/>
                                            </div>
                                        </t>
                                        
                                        <div class="alert alert-info" role="alert">
                                            <i class="fa fa-info-circle"></i>
                                            <strong>一次设置，永久免密</strong><br/>
                                            完成手机号验证后，下次微信扫码即可自动登录，无需重复输入密码。
                                        </div>
                                        
                                        <form method="post" action="/wechat/auto/phone/submit" id="autoPhoneForm">
                                            <div class="form-group mb-3">
                                                <label for="phone_number" class="form-label">
                                                    <i class="fa fa-phone"></i> 手机号码 *
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text">+86</span>
                                                    <input type="tel" 
                                                           class="form-control" 
                                                           id="phone_number" 
                                                           name="phone_number" 
                                                           placeholder="请输入手机号码"
                                                           pattern="^1[3-9]\d{9}$"
                                                           required="required"
                                                           maxlength="11"/>
                                                </div>
                                                <small class="form-text text-muted">
                                                    手机号用于账户安全验证和找回
                                                </small>
                                            </div>
                                            
                                            <div class="form-group mb-3">
                                                <div class="form-check">
                                                    <input type="checkbox" 
                                                           class="form-check-input" 
                                                           id="agree_auto_login" 
                                                           required="required"/>
                                                    <label class="form-check-label" for="agree_auto_login">
                                                        我同意启用微信免密登录功能
                                                    </label>
                                                </div>
                                                <small class="form-text text-muted">
                                                    启用后，您可以通过微信直接登录系统，无需输入用户名密码
                                                </small>
                                            </div>
                                            
                                            <button type="submit" class="btn btn-primary btn-block w-100">
                                                <i class="fa fa-check"></i> 完成设置
                                            </button>
                                        </form>
                                        
                                        <div class="text-center mt-3">
                                            <small class="text-muted">
                                                <i class="fa fa-shield"></i> 您的信息将被安全保护<br/>
                                                <i class="fa fa-mobile"></i> 支持随时在设置中关闭免密登录
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const phoneInput = document.getElementById('phone_number');
                        const form = document.getElementById('autoPhoneForm');
                        
                        // Phone number validation
                        phoneInput.addEventListener('input', function() {
                            const phone = this.value;
                            const isValid = /^1[3-9]\d{9}$/.test(phone);
                            
                            if (isValid) {
                                this.classList.remove('is-invalid');
                                this.classList.add('is-valid');
                            } else {
                                this.classList.remove('is-valid');
                                if (phone.length > 0) {
                                    this.classList.add('is-invalid');
                                }
                            }
                        });
                        
                        // Form validation
                        form.addEventListener('submit', function(e) {
                            const phone = phoneInput.value;
                            if (!/^1[3-9]\d{9}$/.test(phone)) {
                                e.preventDefault();
                                alert('请输入正确的手机号码');
                                return false;
                            }
                        });
                    });
                </script>
            </t>
        </template>
        
        <!-- Auto Login Success Page -->
        <template id="auto_login_success" name="WeChat Auto Login Success">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-body text-center">
                                        <div class="mb-4">
                                            <i class="fa fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        </div>
                                        <h4 class="text-success">免密登录设置成功！</h4>
                                        <p class="text-muted">
                                            恭喜！您已成功设置微信免密登录。<br/>
                                            下次使用微信扫码即可自动登录。
                                        </p>
                                        
                                        <div class="mt-4 p-3 bg-light rounded">
                                            <h6><i class="fa fa-user"></i> 账户信息</h6>
                                            <p class="mb-1"><strong>用户名:</strong> <t t-esc="user.name"/></p>
                                            <p class="mb-1"><strong>登录账号:</strong> <t t-esc="user.login"/></p>
                                            <p class="mb-0"><strong>手机号:</strong> <t t-esc="phone_number"/></p>
                                        </div>
                                        
                                        <div class="alert alert-info mt-3" role="alert">
                                            <i class="fa fa-lightbulb-o"></i>
                                            <strong>温馨提示:</strong><br/>
                                            • 请保管好您的微信账号安全<br/>
                                            • 可在个人设置中随时关闭免密登录<br/>
                                            • 如有安全问题，请及时联系管理员
                                        </div>
                                        
                                        <div class="mt-4">
                                            <a t-att-href="redirect_url" class="btn btn-success btn-lg">
                                                <i class="fa fa-sign-in"></i> 进入系统
                                            </a>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                3秒后自动跳转...
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script>
                    // Auto redirect after 3 seconds
                    var redirectUrl = '<t t-raw="redirect_url"/>';
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 3000);
                </script>
            </t>
        </template>
        
        <!-- Auto Login QR Code Page -->
        <template id="auto_login_qr" name="WeChat Auto Login QR Code">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-8 col-lg-6">
                                <div class="card shadow">
                                    <div class="card-header bg-success text-white text-center">
                                        <h4><i class="fa fa-qrcode"></i> 微信扫码登录</h4>
                                    </div>
                                    <div class="card-body text-center">
                                        <t t-if="qr_code">
                                            <div class="mb-4">
                                                <img t-att-src="'data:image/png;base64,' + qr_code" 
                                                     class="img-fluid" 
                                                     style="max-width: 300px;"
                                                     alt="WeChat Login QR Code"/>
                                            </div>
                                        </t>
                                        <t t-else="">
                                            <div class="mb-4">
                                                <div class="border p-4" style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
                                                    <div class="text-center">
                                                        <i class="fa fa-qrcode" style="font-size: 4rem; color: #ccc;"></i>
                                                        <p class="mt-2 text-muted">
                                                            <t t-if="error">
                                                                <t t-esc="error"/>
                                                            </t>
                                                            <t t-else="">
                                                                QR Code not available
                                                            </t>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                        
                                        <h5>使用微信扫码登录</h5>
                                        <p class="text-muted">
                                            使用微信扫描上方二维码，即可快速登录系统
                                        </p>
                                        
                                        <div class="row mt-4">
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <i class="fa fa-mobile fa-2x text-primary"></i>
                                                    <p class="mt-2"><small>打开微信</small></p>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <i class="fa fa-qrcode fa-2x text-primary"></i>
                                                    <p class="mt-2"><small>扫描二维码</small></p>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <i class="fa fa-sign-in fa-2x text-primary"></i>
                                                    <p class="mt-2"><small>自动登录</small></p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info mt-4" role="alert">
                                            <i class="fa fa-info-circle"></i>
                                            <strong>首次使用？</strong><br/>
                                            首次扫码需要绑定手机号，之后即可免密登录
                                        </div>
                                        
                                        <div class="mt-4">
                                            <a href="/web/login" class="btn btn-outline-secondary">
                                                <i class="fa fa-keyboard-o"></i> 使用账号密码登录
                                            </a>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                登录链接: <a t-att-href="login_url" target="_blank" t-esc="login_url"/>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script>
                    // Auto refresh QR code every 5 minutes
                    setTimeout(function() {
                        location.reload();
                    }, 300000);
                </script>
            </t>
        </template>
        
        <!-- Auth Error Template -->
        <template id="auth_error" name="WeChat Auth Error">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-body text-center">
                                        <div class="mb-4">
                                            <i class="fa fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                                        </div>
                                        <h4 class="text-warning">登录失败</h4>
                                        <p class="text-muted" t-esc="error or '未知错误'"/>
                                        
                                        <div class="mt-4">
                                            <a href="/web/login" class="btn btn-primary">
                                                <i class="fa fa-sign-in"></i> 返回登录页
                                            </a>
                                            <button type="button" class="btn btn-secondary ml-2" onclick="history.back();">
                                                <i class="fa fa-arrow-left"></i> 返回
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- WeChat In-App Phone Collection Form -->
        <template id="inapp_phone_collect_form" name="WeChat In-App Phone Collection">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container-fluid" style="padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
                        <div class="row justify-content-center align-items-center" style="min-height: 100vh; margin: 0;">
                            <div class="col-11 col-md-8 col-lg-6">
                                <div class="card shadow-lg" style="border: none; border-radius: 20px; overflow: hidden;">
                                    <!-- Header -->
                                    <div class="card-header text-center" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 30px 20px; border: none;">
                                        <div class="mb-3">
                                            <i class="fa fa-wechat" style="font-size: 3rem;"></i>
                                        </div>
                                        <h3 class="mb-2" style="font-weight: 600;">微信登录</h3>
                                        <p class="mb-0" style="opacity: 0.9;">完善信息，开启免密登录</p>
                                    </div>

                                    <div class="card-body" style="padding: 40px 30px;">
                                        <!-- User Info -->
                                        <div class="text-center mb-4">
                                            <img t-if="user_info.get('headimgurl')"
                                                 t-att-src="user_info['headimgurl']"
                                                 class="rounded-circle shadow"
                                                 style="width: 80px; height: 80px; border: 3px solid #e9ecef;"
                                                 alt="Avatar"/>
                                            <div class="mt-3">
                                                <h5 class="mb-1" style="color: #495057;" t-esc="user_info.get('nickname', 'WeChat User')"/>
                                                <small class="text-muted">设置后可直接免密登录系统</small>
                                            </div>
                                        </div>

                                        <t t-if="error">
                                            <div class="alert alert-danger" role="alert" style="border-radius: 10px;">
                                                <i class="fa fa-exclamation-triangle"></i>
                                                <t t-esc="error"/>
                                            </div>
                                        </t>

                                        <!-- Benefits -->
                                        <div class="alert alert-info" role="alert" style="border-radius: 15px; background: #e3f2fd; border: none; color: #1976d2;">
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <i class="fa fa-shield fa-2x mb-2" style="color: #4caf50;"></i>
                                                    <div><small><strong>安全</strong></small></div>
                                                </div>
                                                <div class="col-4">
                                                    <i class="fa fa-flash fa-2x mb-2" style="color: #ff9800;"></i>
                                                    <div><small><strong>快速</strong></small></div>
                                                </div>
                                                <div class="col-4">
                                                    <i class="fa fa-key fa-2x mb-2" style="color: #2196f3;"></i>
                                                    <div><small><strong>免密</strong></small></div>
                                                </div>
                                            </div>
                                        </div>

                                        <form method="post" action="/wechat/inapp/phone/submit" id="inappPhoneForm">
                                            <div class="form-group mb-4">
                                                <label for="phone_number" class="form-label" style="font-weight: 600; color: #495057;">
                                                    <i class="fa fa-phone text-success"></i> 手机号码
                                                </label>
                                                <div class="input-group" style="border-radius: 10px; overflow: hidden;">
                                                    <span class="input-group-text" style="background: #f8f9fa; border: 1px solid #dee2e6; border-right: none;">+86</span>
                                                    <input type="tel"
                                                           class="form-control"
                                                           id="phone_number"
                                                           name="phone_number"
                                                           placeholder="请输入您的手机号码"
                                                           pattern="^1[3-9]\d{9}$"
                                                           required="required"
                                                           maxlength="11"
                                                           style="border-left: none; font-size: 16px; padding: 15px;"/>
                                                </div>
                                                <small class="form-text text-muted mt-2">
                                                    <i class="fa fa-info-circle"></i> 用于账户安全验证，信息将被安全保护
                                                </small>
                                            </div>

                                            <button type="submit" class="btn btn-lg w-100 mb-3"
                                                    style="background: linear-gradient(45deg, #28a745, #20c997); border: none; border-radius: 10px; color: white; font-weight: 600; padding: 15px;">
                                                <i class="fa fa-check"></i> 完成设置，立即登录
                                            </button>
                                        </form>

                                        <div class="text-center">
                                            <small class="text-muted">
                                                <i class="fa fa-lock"></i> 您的信息将被安全保护<br/>
                                                <i class="fa fa-mobile"></i> 下次登录将自动识别您的身份
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const phoneInput = document.getElementById('phone_number');
                        const form = document.getElementById('inappPhoneForm');

                        // Auto focus
                        phoneInput.focus();

                        // Phone number validation
                        phoneInput.addEventListener('input', function() {
                            const phone = this.value;
                            const isValid = /^1[3-9]\d{9}$/.test(phone);

                            if (isValid) {
                                this.classList.remove('is-invalid');
                                this.classList.add('is-valid');
                            } else {
                                this.classList.remove('is-valid');
                                if (phone.length > 0) {
                                    this.classList.add('is-invalid');
                                }
                            }
                        });

                        // Form validation
                        form.addEventListener('submit', function(e) {
                            const phone = phoneInput.value;
                            if (!/^1[3-9]\d{9}$/.test(phone)) {
                                e.preventDefault();
                                alert('请输入正确的手机号码');
                                phoneInput.focus();
                                return false;
                            }
                        });
                    });
                </script>
            </t>
        </template>

        <!-- WeChat In-App Login Success -->
        <template id="inapp_login_success" name="WeChat In-App Login Success">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container-fluid" style="padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
                        <div class="row justify-content-center align-items-center" style="min-height: 100vh; margin: 0;">
                            <div class="col-11 col-md-8 col-lg-6">
                                <div class="card shadow-lg text-center" style="border: none; border-radius: 20px; overflow: hidden;">
                                    <div class="card-body" style="padding: 50px 30px;">
                                        <div class="mb-4">
                                            <i class="fa fa-check-circle text-success" style="font-size: 5rem;"></i>
                                        </div>
                                        <h3 class="text-success mb-3" style="font-weight: 600;">登录成功！</h3>
                                        <p class="text-muted mb-4">
                                            恭喜！您已成功设置微信免密登录。<br/>
                                            下次访问将自动识别您的身份。
                                        </p>

                                        <div class="p-4 bg-light rounded mb-4">
                                            <h6 class="mb-3"><i class="fa fa-user text-primary"></i> 账户信息</h6>
                                            <div class="row">
                                                <div class="col-6 text-right"><strong>用户名:</strong></div>
                                                <div class="col-6 text-left" t-esc="user.name"/>
                                            </div>
                                            <div class="row">
                                                <div class="col-6 text-right"><strong>手机号:</strong></div>
                                                <div class="col-6 text-left" t-esc="phone_number"/>
                                            </div>
                                        </div>

                                        <div class="alert alert-info" role="alert" style="border-radius: 15px;">
                                            <i class="fa fa-lightbulb-o"></i>
                                            <strong>下次登录更简单：</strong><br/>
                                            直接点击公众号菜单即可自动登录
                                        </div>

                                        <a t-att-href="redirect_url" class="btn btn-success btn-lg"
                                           style="border-radius: 10px; padding: 15px 30px; font-weight: 600;">
                                            <i class="fa fa-sign-in"></i> 进入系统
                                        </a>

                                        <div class="mt-4">
                                            <small class="text-muted">
                                                <span id="countdown">3</span> 秒后自动跳转...
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    // Countdown and auto redirect
                    let countdown = 3;
                    const countdownElement = document.getElementById('countdown');
                    const redirectUrl = '<t t-esc="redirect_url"/>';

                    const timer = setInterval(function() {
                        countdown--;
                        if (countdownElement) {
                            countdownElement.textContent = countdown;
                        }

                        if (countdown &lt;= 0) {
                            clearInterval(timer);
                            window.location.href = redirectUrl;
                        }
                    }, 1000);
                </script>
            </t>
        </template>

        <!-- Not in WeChat Browser -->
        <template id="not_in_wechat" name="Not in WeChat Browser">
            <t t-call="wechat_official_account.basic_layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6 col-lg-4">
                                <div class="card shadow">
                                    <div class="card-body text-center">
                                        <div class="mb-4">
                                            <i class="fa fa-wechat text-success" style="font-size: 4rem;"></i>
                                        </div>
                                        <h4 class="text-warning">请在微信中打开</h4>
                                        <p class="text-muted">
                                            此功能需要在微信公众号中使用。<br/>
                                            请通过微信公众号菜单访问。
                                        </p>

                                        <div class="mt-4">
                                            <a href="/web/login" class="btn btn-primary">
                                                <i class="fa fa-sign-in"></i> 使用账号密码登录
                                            </a>
                                        </div>

                                        <div class="mt-3">
                                            <small class="text-muted">
                                                或扫描二维码在微信中打开
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- OAuth Redirect Template -->
        <template id="oauth_redirect" name="WeChat OAuth Redirect">
            <html>
            <head>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <title>微信授权登录</title>
                <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"/>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"/>
            </head>
            <body>
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container-fluid" style="padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
                        <div class="row justify-content-center align-items-center" style="min-height: 100vh; margin: 0;">
                            <div class="col-11 col-md-8 col-lg-6">
                                <div class="card shadow-lg text-center" style="border: none; border-radius: 20px; overflow: hidden;">
                                    <div class="card-body" style="padding: 50px 30px;">
                                        <div class="mb-4">
                                            <i class="fa fa-wechat text-success" style="font-size: 5rem;"></i>
                                        </div>
                                        <h3 class="text-primary mb-3" style="font-weight: 600;">正在跳转到微信授权</h3>
                                        <p class="text-muted mb-4">
                                            请稍候，正在为您跳转到微信授权页面...<br/>
                                            如果没有自动跳转，请点击下方按钮。
                                        </p>

                                        <div class="spinner-border text-primary mb-4" role="status">
                                            <span class="sr-only">Loading...</span>
                                        </div>

                                        <div class="mb-4">
                                            <a t-att-href="oauth_url" class="btn btn-success btn-lg"
                                               style="border-radius: 10px; padding: 15px 30px; font-weight: 600;">
                                                <i class="fa fa-wechat"></i> 点击授权登录
                                            </a>
                                        </div>

                                        <div class="alert alert-info" role="alert" style="border-radius: 15px;">
                                            <i class="fa fa-info-circle"></i>
                                            <strong>提示：</strong>授权后将自动创建您的账户并登录系统
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    // 使用JavaScript变量避免HTML转义问题
                    var oauthUrl = "<t t-raw='oauth_url'/>";
                    window.location.href = oauthUrl;
                </script>

            </body>
            </html>
        </template>

    </data>
</odoo>
