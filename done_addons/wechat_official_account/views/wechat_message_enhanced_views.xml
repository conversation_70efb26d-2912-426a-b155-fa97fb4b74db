<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 扩展微信消息表单视图，添加用户状态检查功能 -->
    <record id="view_wechat_message_form_enhanced" model="ir.ui.view">
        <field name="name">wechat.message.form.enhanced</field>
        <field name="model">wechat.message</field>
        <field name="inherit_id" ref="wechat_official_account.view_wechat_message_form"/>
        <field name="arch" type="xml">
            <!-- 在发送按钮旁边添加状态检查按钮 -->
            <button name="send_message" position="after">
                <button name="action_check_user_status" 
                        type="object" 
                        string="检查用户状态" 
                        class="btn-secondary"
                        invisible="recipient_type != 'single'"
                        help="检查目标用户是否仍然关注公众号"/>
                <button name="send_message"
                        type="object"
                        string="重新发送"
                        class="btn-warning"
                        invisible="state not in ['failed', 'partial']"
                        help="重新发送失败的消息"/>
            </button>
            
            <!-- 在状态字段后添加错误信息字段 -->
            <field name="state" position="after">
                <field name="error_message" 
                       invisible="not error_message"
                       readonly="1"
                       string="错误信息"/>
            </field>
        </field>
    </record>

    <!-- 扩展微信消息列表视图，显示发送统计 -->
    <record id="view_wechat_message_tree_enhanced" model="ir.ui.view">
        <field name="name">wechat.message.tree.enhanced</field>
        <field name="model">wechat.message</field>
        <field name="inherit_id" ref="wechat_official_account.view_wechat_message_tree"/>
        <field name="arch" type="xml">
            <!-- 在状态字段后添加发送统计 -->
            <field name="state" position="after">
                <field name="sent_count" string="成功"/>
                <field name="failed_count" string="失败"/>
            </field>
        </field>
    </record>

    <!-- 添加微信用户状态管理视图 -->
    <record id="view_wechat_user_form_status" model="ir.ui.view">
        <field name="name">wechat.user.form.status</field>
        <field name="model">wechat.user</field>
        <field name="inherit_id" ref="wechat_official_account.view_wechat_user_form"/>
        <field name="arch" type="xml">
            <!-- 在关注状态字段后添加取消关注时间 -->
            <field name="subscribe" position="after">
                <field name="unsubscribe_time"
                       invisible="subscribe == True"
                       readonly="1"
                       string="取消关注时间"/>
            </field>
        </field>
    </record>

    <!-- 添加微信用户列表视图的状态筛选 -->
    <record id="view_wechat_user_search_status" model="ir.ui.view">
        <field name="name">wechat.user.search.status</field>
        <field name="model">wechat.user</field>
        <field name="inherit_id" ref="wechat_official_account.view_wechat_user_search"/>
        <field name="arch" type="xml">
            <!-- 添加关注状态筛选 -->
            <filter name="subscribed" position="after">
                <filter name="unsubscribed" 
                        string="已取消关注" 
                        domain="[('subscribe', '=', False)]"/>
                <separator/>
                <filter name="recent_unsubscribed" 
                        string="最近取消关注" 
                        domain="[('subscribe', '=', False), ('unsubscribe_time', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
            </filter>
        </field>
    </record>

    <!-- 添加消息发送失败的筛选器 -->
    <record id="view_wechat_message_search_enhanced" model="ir.ui.view">
        <field name="name">wechat.message.search.enhanced</field>
        <field name="model">wechat.message</field>
        <field name="inherit_id" ref="wechat_official_account.view_wechat_message_search"/>
        <field name="arch" type="xml">
            <!-- 添加发送状态筛选 -->
            <filter name="sent" position="after">
                <filter name="failed" 
                        string="发送失败" 
                        domain="[('state', '=', 'failed')]"/>
                <filter name="partial" 
                        string="部分成功" 
                        domain="[('state', '=', 'partial')]"/>
                <separator/>
                <filter name="user_unsubscribed" 
                        string="用户取消关注" 
                        domain="[('error_message', 'ilike', '取消关注')]"/>
            </filter>
        </field>
    </record>
</odoo>
