# -*- coding: utf-8 -*-
{
    'name': '微信公众号',
    'version': '1.1.0',
    'category': 'Marketing',
    'summary': '微信公众号集成模块',
    'description': """
WeChat Official Account Integration Module
==========================================

微信公众号集成模块，提供与微信公众号平台的完整集成功能。

功能特性：
---------
* 微信公众号配置管理
* 消息发送功能（文本、图片、图文等）
* 用户管理和关注者信息
* 微信服务器回调处理
* 消息记录和统计

主要功能：
---------
* 配置微信公众号AppID和AppSecret
* 发送文本消息给指定用户或群发
* 管理微信用户信息
* 处理微信服务器的消息推送
* 消息发送记录和状态跟踪

技术特点：
---------
* 支持微信公众号API 2.0
* 安全的Token验证机制
* 完整的错误处理和日志记录
* 灵活的消息模板系统

    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': ['base', 'mail', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'security/wechat_security.xml',
        'data/wechat_message_template_data.xml',
        'data/wechat_template_data.xml',
        'views/wechat_auto_login_views.xml',
        'views/wechat_actions.xml',
        'views/wechat_config_views.xml',
        'views/wechat_message_views.xml',
        'views/wechat_template_views.xml',
        'views/wechat_user_views.xml',
        'views/wechat_config_wizard_views.xml',
        'views/wechat_custom_menu_views.xml',
        'views/wechat_menu_views.xml',
        'views/wechat_oauth_templates.xml',
        'views/wechat_auto_login_templates.xml',
        'views/wechat_message_enhanced_views.xml',
        'views/wechat_direct_login_views.xml',
        'views/wechat_direct_login_templates.xml',
    ],
    'demo': [
        'demo/wechat_demo_data.xml',
        'data/wechat_menu_demo_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'wechat_official_account/static/src/css/wechat.css',
            'wechat_official_account/static/src/js/wechat_widget.js',
            'wechat_official_account/static/src/xml/wechat_templates.xml',
        ],
    },
    'external_dependencies': {
        'python': ['requests'],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    'sequence': 100,
}
