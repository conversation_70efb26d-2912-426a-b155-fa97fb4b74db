# -*- coding: utf-8 -*-

import logging
from odoo import api, fields, models, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WeChatBindingWizard(models.TransientModel):
    _name = 'wechat.binding.wizard'
    _description = 'WeChat Binding Wizard'

    user_id = fields.Many2one('res.users', string='用户', required=True)
    config_id = fields.Many2one('wechat.config', string='微信配置', required=True)
    openid = fields.Char(string='微信OpenID', required=True, help='用户的微信OpenID')
    wechat_user_id = fields.Many2one('wechat.user', string='选择微信用户', domain="[('config_id', '=', config_id)]")
    
    # 显示字段
    available_users = fields.Text(string='可用的微信用户', compute='_compute_available_users')

    @api.onchange('config_id')
    def _onchange_config_id(self):
        if self.config_id:
            # 获取默认配置
            if not self.config_id:
                default_config = self.env['wechat.config'].search([('active', '=', True)], limit=1)
                if default_config:
                    self.config_id = default_config.id

    @api.depends('config_id')
    def _compute_available_users(self):
        for wizard in self:
            if wizard.config_id:
                users = self.env['wechat.user'].search([
                    ('config_id', '=', wizard.config_id.id),
                    ('subscribe', '=', True)
                ], limit=10)
                
                if users:
                    user_list = []
                    for user in users:
                        user_list.append(f"• {user.nickname or '未知'} ({user.openid})")
                    wizard.available_users = "\n".join(user_list)
                else:
                    wizard.available_users = "没有找到已关注的微信用户"
            else:
                wizard.available_users = "请先选择微信配置"

    @api.onchange('wechat_user_id')
    def _onchange_wechat_user_id(self):
        if self.wechat_user_id:
            self.openid = self.wechat_user_id.openid

    def action_create_binding(self):
        """创建绑定"""
        self.ensure_one()
        
        try:
            # 验证OpenID
            if not self.openid:
                raise UserError(_('请输入或选择微信OpenID'))
            
            # 检查OpenID是否存在于微信用户中
            wechat_user = self.env['wechat.user'].search([
                ('openid', '=', self.openid),
                ('config_id', '=', self.config_id.id)
            ], limit=1)
            
            if not wechat_user:
                # 如果不存在，询问是否创建
                return {
                    'type': 'ir.actions.act_window',
                    'name': _('确认创建'),
                    'res_model': 'wechat.binding.confirm.wizard',
                    'view_mode': 'form',
                    'target': 'new',
                    'context': {
                        'default_user_id': self.user_id.id,
                        'default_config_id': self.config_id.id,
                        'default_openid': self.openid,
                    }
                }
            
            # 创建绑定
            binding = self.env['wechat.direct.login'].create_binding(
                user_id=self.user_id.id,
                openid=self.openid,
                config_id=self.config_id.id
            )
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('绑定成功'),
                    'message': _('微信账号已成功绑定到用户 %s') % self.user_id.name,
                    'type': 'success',
                }
            }
            
        except Exception as e:
            raise UserError(_('绑定失败：%s') % str(e))

    def action_test_openid(self):
        """测试OpenID"""
        self.ensure_one()
        
        if not self.openid:
            raise UserError(_('请输入OpenID'))
        
        # 查找微信用户
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', self.openid),
            ('config_id', '=', self.config_id.id)
        ], limit=1)
        
        if wechat_user:
            message = f"找到微信用户：{wechat_user.nickname or '未知昵称'}\n"
            message += f"关注状态：{'已关注' if wechat_user.subscribe else '已取消关注'}\n"
            message += f"关注时间：{wechat_user.subscribe_time or '未知'}"
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('OpenID验证成功'),
                    'message': message,
                    'type': 'success',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('OpenID验证失败'),
                    'message': _('未找到对应的微信用户，请检查OpenID是否正确'),
                    'type': 'warning',
                }
            }


class WeChatBindingConfirmWizard(models.TransientModel):
    _name = 'wechat.binding.confirm.wizard'
    _description = 'WeChat Binding Confirm Wizard'

    user_id = fields.Many2one('res.users', string='用户', required=True)
    config_id = fields.Many2one('wechat.config', string='微信配置', required=True)
    openid = fields.Char(string='微信OpenID', required=True)
    create_wechat_user = fields.Boolean(string='创建微信用户记录', default=True)
    nickname = fields.Char(string='昵称', default='微信用户')

    def action_confirm_create(self):
        """确认创建绑定"""
        self.ensure_one()
        
        try:
            # 如果需要，先创建微信用户记录
            if self.create_wechat_user:
                wechat_user = self.env['wechat.user'].create({
                    'openid': self.openid,
                    'config_id': self.config_id.id,
                    'nickname': self.nickname,
                    'subscribe': True,  # 假设已关注
                    'subscribe_time': fields.Datetime.now(),
                })
                _logger.info(f"Created WeChat user record for openid {self.openid}")
            
            # 创建绑定
            binding = self.env['wechat.direct.login'].create_binding(
                user_id=self.user_id.id,
                openid=self.openid,
                config_id=self.config_id.id
            )
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('绑定成功'),
                    'message': _('微信账号已成功绑定到用户 %s') % self.user_id.name,
                    'type': 'success',
                }
            }
            
        except Exception as e:
            raise UserError(_('绑定失败：%s') % str(e))
