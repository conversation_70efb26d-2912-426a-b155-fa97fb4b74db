<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- WeChat User Group -->
        <record id="group_wechat_user" model="res.groups">
            <field name="name">WeChat User</field>
            <field name="category_id" ref="base.module_category_marketing"/>
            <field name="comment">Can view and send WeChat messages, view WeChat users</field>
        </record>
        
        <!-- WeChat Manager Group -->
        <record id="group_wechat_manager" model="res.groups">
            <field name="name">WeChat Manager</field>
            <field name="category_id" ref="base.module_category_marketing"/>
            <field name="implied_ids" eval="[(4, ref('group_wechat_user'))]"/>
            <field name="comment">Can manage WeChat configurations, users, and all message operations</field>
        </record>
        
        <!-- Record Rules -->
        
        <!-- WeChat Configuration Rules -->
        <record id="wechat_config_rule_user" model="ir.rule">
            <field name="name">WeChat Config: User Access</field>
            <field name="model_id" ref="model_wechat_config"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_wechat_user'))]"/>
        </record>
        
        <record id="wechat_config_rule_manager" model="ir.rule">
            <field name="name">WeChat Config: Manager Access</field>
            <field name="model_id" ref="model_wechat_config"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_wechat_manager'))]"/>
        </record>
        
        <!-- WeChat Message Rules -->
        <record id="wechat_message_rule_user" model="ir.rule">
            <field name="name">WeChat Message: User Access</field>
            <field name="model_id" ref="model_wechat_message"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_wechat_user'))]"/>
        </record>
        
        <record id="wechat_message_rule_manager" model="ir.rule">
            <field name="name">WeChat Message: Manager Access</field>
            <field name="model_id" ref="model_wechat_message"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_wechat_manager'))]"/>
        </record>
        
        <!-- WeChat User Rules -->
        <record id="wechat_user_rule_user" model="ir.rule">
            <field name="name">WeChat User: User Access</field>
            <field name="model_id" ref="model_wechat_user"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_wechat_user'))]"/>
        </record>
        
        <record id="wechat_user_rule_manager" model="ir.rule">
            <field name="name">WeChat User: Manager Access</field>
            <field name="model_id" ref="model_wechat_user"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_wechat_manager'))]"/>
        </record>

    </data>

    <!-- Auto-assign WeChat Manager role to admin users -->
    <data noupdate="0">
        <record id="base.user_admin" model="res.users">
            <field name="groups_id" eval="[(4, ref('group_wechat_manager'))]"/>
        </record>
    </data>
</odoo>
