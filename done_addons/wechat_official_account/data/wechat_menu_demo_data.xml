<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- 示例微信菜单配置 -->
        <record id="demo_wechat_menu" model="wechat.menu">
            <field name="name">示例自定义菜单</field>
            <field name="state">draft</field>
        </record>

        <!-- 一级菜单项 -->
        <record id="demo_menu_item_about" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="name">关于我们</field>
            <field name="sequence">10</field>
            <field name="menu_type">submenu</field>
            <field name="active">True</field>
        </record>

        <record id="demo_menu_item_services" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="name">服务</field>
            <field name="sequence">20</field>
            <field name="menu_type">submenu</field>
            <field name="active">True</field>
        </record>

        <record id="demo_menu_item_contact" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="name">联系我们</field>
            <field name="sequence">30</field>
            <field name="menu_type">click</field>
            <field name="key">CONTACT_US</field>
            <field name="active">True</field>
        </record>

        <!-- 关于我们的二级菜单 -->
        <record id="demo_menu_item_company_info" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="parent_id" ref="demo_menu_item_about"/>
            <field name="name">公司介绍</field>
            <field name="sequence">10</field>
            <field name="menu_type">click</field>
            <field name="key">COMPANY_INFO</field>
            <field name="active">True</field>
        </record>

        <record id="demo_menu_item_team" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="parent_id" ref="demo_menu_item_about"/>
            <field name="name">团队介绍</field>
            <field name="sequence">20</field>
            <field name="menu_type">click</field>
            <field name="key">TEAM_INFO</field>
            <field name="active">True</field>
        </record>

        <record id="demo_menu_item_history" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="parent_id" ref="demo_menu_item_about"/>
            <field name="name">发展历程</field>
            <field name="sequence">30</field>
            <field name="menu_type">view</field>
            <field name="url">https://www.example.com/history</field>
            <field name="active">True</field>
        </record>

        <!-- 服务的二级菜单 -->
        <record id="demo_menu_item_products" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="parent_id" ref="demo_menu_item_services"/>
            <field name="name">产品展示</field>
            <field name="sequence">10</field>
            <field name="menu_type">view</field>
            <field name="url">https://www.example.com/products</field>
            <field name="active">True</field>
        </record>

        <record id="demo_menu_item_support" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="parent_id" ref="demo_menu_item_services"/>
            <field name="name">技术支持</field>
            <field name="sequence">20</field>
            <field name="menu_type">click</field>
            <field name="key">TECH_SUPPORT</field>
            <field name="active">True</field>
        </record>

        <record id="demo_menu_item_scan" model="wechat.menu.item">
            <field name="menu_id" ref="demo_wechat_menu"/>
            <field name="parent_id" ref="demo_menu_item_services"/>
            <field name="name">扫码服务</field>
            <field name="sequence">30</field>
            <field name="menu_type">scancode_push</field>
            <field name="key">SCAN_SERVICE</field>
            <field name="active">True</field>
        </record>

    </data>
</odoo>
