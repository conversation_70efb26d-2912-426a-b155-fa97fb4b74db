# -*- coding: utf-8 -*-

def migrate(cr, version):
    """Add missing fields to wechat_user table"""
    
    # Check if phone field exists
    cr.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='wechat_user' AND column_name='phone'
    """)
    if not cr.fetchone():
        cr.execute("""
            ALTER TABLE wechat_user 
            ADD COLUMN phone VARCHAR
        """)
        print("Added phone field to wechat_user table")
    
    # Check if email field exists
    cr.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='wechat_user' AND column_name='email'
    """)
    if not cr.fetchone():
        cr.execute("""
            ALTER TABLE wechat_user 
            ADD COLUMN email VARCHAR
        """)
        print("Added email field to wechat_user table")
    
    # Check if phone_verified field exists
    cr.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='wechat_user' AND column_name='phone_verified'
    """)
    if not cr.fetchone():
        cr.execute("""
            ALTER TABLE wechat_user 
            ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE
        """)
        print("Added phone_verified field to wechat_user table")
    
    # Check if user_id field exists
    cr.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='wechat_user' AND column_name='user_id'
    """)
    if not cr.fetchone():
        cr.execute("""
            ALTER TABLE wechat_user 
            ADD COLUMN user_id INTEGER REFERENCES res_users(id) ON DELETE SET NULL
        """)
        print("Added user_id field to wechat_user table")
    
    # Check if auto_login_enabled field exists
    cr.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='wechat_user' AND column_name='auto_login_enabled'
    """)
    if not cr.fetchone():
        # This is a computed field, no need to add to database
        print("auto_login_enabled is a computed field, skipping")
    
    print("Migration completed successfully")
