# -*- coding: utf-8 -*-

import logging
import hashlib
import secrets
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, AccessDenied

_logger = logging.getLogger(__name__)


class WeChatAutoLogin(models.Model):
    _name = 'wechat.auto.login'
    _description = 'WeChat Auto Login Management'
    _rec_name = 'display_name'
    _order = 'create_date desc'

    # Basic Information
    openid = fields.Char(string='OpenID', required=True, index=True)
    unionid = fields.Char(string='UnionID', index=True)
    config_id = fields.Many2one('wechat.config', string='WeChat Config', required=True, ondelete='cascade')
    
    # User Relations
    user_id = fields.Many2one('res.users', string='Odoo User', required=True, ondelete='cascade')
    wechat_user_id = fields.Many2one('wechat.user', string='WeChat User', ondelete='cascade')
    
    # Auto Login Settings
    enabled = fields.Boolean(string='Auto Login Enabled', default=True)
    login_token = fields.Char(string='Login Token', help='Secure token for auto login')
    token_expires = fields.Datetime(string='Token Expires')
    
    # Phone Information
    phone_number = fields.Char(string='Phone Number', help='User phone number for verification')
    phone_verified = fields.Boolean(string='Phone Verified', default=False)
    
    # Login Statistics
    login_count = fields.Integer(string='Login Count', default=0)
    last_login = fields.Datetime(string='Last Login')
    last_ip = fields.Char(string='Last IP Address')
    
    # Security
    active = fields.Boolean(string='Active', default=True)
    trusted_device = fields.Boolean(string='Trusted Device', default=False,
                                   help='Mark as trusted device for enhanced security')
    
    # Display
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', 
                                related='config_id.company_id', store=True)

    @api.depends('user_id.name', 'openid')
    def _compute_display_name(self):
        for record in self:
            if record.user_id:
                record.display_name = f"{record.user_id.name} ({record.openid[:8]}...)"
            else:
                record.display_name = f"WeChat User ({record.openid[:8]}...)"

    @api.model
    def create_auto_login_record(self, openid, config_id, user_id, phone_number=None, wechat_user_id=None):
        """
        Create auto login record for WeChat user
        
        Args:
            openid: WeChat OpenID
            config_id: WeChat configuration ID
            user_id: Odoo user ID
            phone_number: User phone number
            wechat_user_id: WeChat user record ID
        
        Returns:
            wechat.auto.login record
        """
        try:
            # Check if record already exists
            existing = self.search([
                ('openid', '=', openid),
                ('config_id', '=', config_id),
                ('user_id', '=', user_id)
            ], limit=1)
            
            if existing:
                # Update existing record
                existing.write({
                    'phone_number': phone_number or existing.phone_number,
                    'phone_verified': bool(phone_number),
                    'wechat_user_id': wechat_user_id or existing.wechat_user_id,
                    'enabled': True,
                    'active': True,
                })
                existing._generate_login_token()
                return existing
            
            # Create new record
            values = {
                'openid': openid,
                'config_id': config_id,
                'user_id': user_id,
                'phone_number': phone_number,
                'phone_verified': bool(phone_number),
                'wechat_user_id': wechat_user_id,
                'enabled': True,
                'active': True,
            }
            
            # Get unionid if available
            if wechat_user_id:
                wechat_user = self.env['wechat.user'].browse(wechat_user_id)
                if wechat_user.unionid:
                    values['unionid'] = wechat_user.unionid
            
            record = self.create(values)
            record._generate_login_token()
            
            _logger.info(f"Created auto login record for user {user_id} with OpenID {openid}")
            return record
            
        except Exception as e:
            _logger.error(f"Failed to create auto login record: {e}")
            raise UserError(_('Failed to create auto login record: %s') % str(e))

    def _generate_login_token(self):
        """Generate secure login token"""
        self.ensure_one()
        
        # Generate secure token
        token_data = f"{self.openid}{self.user_id.id}{secrets.token_hex(16)}{datetime.now().isoformat()}"
        token = hashlib.sha256(token_data.encode()).hexdigest()
        
        # Set expiry (30 days from now)
        expires = datetime.now() + timedelta(days=30)
        
        self.write({
            'login_token': token,
            'token_expires': expires,
        })
        
        return token

    @api.model
    def authenticate_by_openid(self, openid, config_id, ip_address=None):
        """
        Authenticate user by WeChat OpenID
        
        Args:
            openid: WeChat OpenID
            config_id: WeChat configuration ID
            ip_address: Client IP address
        
        Returns:
            res.users record or False
        """
        try:
            # Find auto login record
            auto_login = self.search([
                ('openid', '=', openid),
                ('config_id', '=', config_id),
                ('enabled', '=', True),
                ('active', '=', True),
                ('user_id.active', '=', True),
            ], limit=1)
            
            if not auto_login:
                _logger.warning(f"No auto login record found for OpenID {openid}")
                return False
            
            # Check token expiry
            if auto_login.token_expires and auto_login.token_expires < fields.Datetime.now():
                _logger.warning(f"Login token expired for OpenID {openid}")
                auto_login._generate_login_token()  # Regenerate token
            
            # Update login statistics
            auto_login.write({
                'login_count': auto_login.login_count + 1,
                'last_login': fields.Datetime.now(),
                'last_ip': ip_address,
            })
            
            _logger.info(f"Auto login successful for user {auto_login.user_id.login} via OpenID {openid}")
            return auto_login.user_id
            
        except Exception as e:
            _logger.error(f"Auto login authentication error: {e}")
            return False

    @api.model
    def get_or_create_user_for_wechat(self, openid, config_id, user_info, phone_number=None):
        """
        Get existing user or create new user for WeChat login
        
        Args:
            openid: WeChat OpenID
            user_info: WeChat user information dict
            phone_number: User phone number
        
        Returns:
            res.users record
        """
        try:
            # First check if auto login record exists
            auto_login = self.search([
                ('openid', '=', openid),
                ('config_id', '=', config_id),
                ('active', '=', True)
            ], limit=1)
            
            if auto_login and auto_login.user_id.active:
                # Update phone number if provided
                if phone_number and not auto_login.phone_number:
                    auto_login.write({
                        'phone_number': phone_number,
                        'phone_verified': True,
                    })
                return auto_login.user_id
            
            # Try to find existing user by phone number
            if phone_number:
                existing_user = self._find_user_by_phone(phone_number)
                if existing_user:
                    # Create auto login record for existing user
                    wechat_user = self._get_or_create_wechat_user(openid, config_id, user_info, phone_number)
                    self.create_auto_login_record(
                        openid=openid,
                        config_id=config_id,
                        user_id=existing_user.id,
                        phone_number=phone_number,
                        wechat_user_id=wechat_user.id if wechat_user else None
                    )
                    return existing_user
            
            # Create new user
            new_user = self._create_new_user(openid, config_id, user_info, phone_number)
            
            # Create WeChat user record
            wechat_user = self._get_or_create_wechat_user(openid, config_id, user_info, phone_number)
            if wechat_user:
                wechat_user.user_id = new_user.id
            
            # Create auto login record
            self.create_auto_login_record(
                openid=openid,
                config_id=config_id,
                user_id=new_user.id,
                phone_number=phone_number,
                wechat_user_id=wechat_user.id if wechat_user else None
            )
            
            return new_user
            
        except Exception as e:
            _logger.error(f"Failed to get or create user for WeChat: {e}")
            raise UserError(_('Failed to process WeChat login: %s') % str(e))

    def _find_user_by_phone(self, phone_number):
        """Find existing user by phone number"""
        if not phone_number:
            return False
        
        # Search in users
        user = self.env['res.users'].search([
            ('phone', '=', phone_number),
            ('active', '=', True)
        ], limit=1)
        
        if user:
            return user
        
        # Search in partners
        partner = self.env['res.partner'].search([
            ('phone', '=', phone_number),
            ('is_company', '=', False)
        ], limit=1)
        
        if partner and partner.user_ids:
            return partner.user_ids.filtered('active')[0] if partner.user_ids.filtered('active') else False
        
        return False

    def _create_new_user(self, openid, config_id, user_info, phone_number):
        """Create new Odoo user"""
        config = self.env['wechat.config'].browse(config_id)
        
        # Generate unique login
        if phone_number:
            base_login = phone_number
        else:
            base_login = f"wechat_{openid[:8]}"
        
        login = base_login
        counter = 1
        while self.env['res.users'].search([('login', '=', login)], limit=1):
            login = f"{base_login}_{counter}"
            counter += 1
        
        # Prepare user values
        user_vals = {
            'name': user_info.get('nickname') or f'WeChat User {openid[:8]}',
            'login': login,
            'phone': phone_number,
            'active': True,
            'company_id': config.company_id.id,
            'company_ids': [(6, 0, [config.company_id.id])],
            'groups_id': [(6, 0, [self.env.ref('base.group_portal').id])],  # Default to portal user
        }
        
        # Generate email if not provided
        if not user_vals.get('email'):
            user_vals['email'] = f"{login}@wechat.local"
        
        # Create user
        new_user = self.env['res.users'].sudo().create(user_vals)
        
        # Update partner with WeChat info
        self._update_partner_with_wechat_info(new_user.partner_id, user_info, phone_number)
        
        _logger.info(f"Created new user {new_user.login} for WeChat OpenID {openid}")
        return new_user

    def _get_or_create_wechat_user(self, openid, config_id, user_info, phone_number):
        """Get or create WeChat user record"""
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id)
        ], limit=1)
        
        if wechat_user:
            # Update existing record
            update_vals = {
                'phone': phone_number or wechat_user.phone,
                'phone_verified': bool(phone_number),
            }
            if user_info.get('nickname'):
                update_vals['nickname'] = user_info['nickname']
            if user_info.get('headimgurl'):
                update_vals['headimgurl'] = user_info['headimgurl']
            
            wechat_user.write(update_vals)
            return wechat_user
        
        # Create new WeChat user
        wechat_vals = {
            'openid': openid,
            'config_id': config_id,
            'nickname': user_info.get('nickname', ''),
            'sex': str(user_info.get('sex', 0)),
            'city': user_info.get('city', ''),
            'country': user_info.get('country', ''),
            'province': user_info.get('province', ''),
            'language': user_info.get('language', 'zh_CN'),
            'headimgurl': user_info.get('headimgurl', ''),
            'unionid': user_info.get('unionid', ''),
            'phone': phone_number,
            'phone_verified': bool(phone_number),
            'subscribe': True,
            'subscribe_time': fields.Datetime.now(),
        }
        
        return self.env['wechat.user'].create(wechat_vals)

    def _update_partner_with_wechat_info(self, partner, user_info, phone_number):
        """Update partner with WeChat information"""
        try:
            partner_vals = {}
            
            # Update phone
            if phone_number and not partner.phone:
                partner_vals['phone'] = phone_number
            
            # Update location
            location_parts = []
            if user_info.get('province'):
                location_parts.append(user_info['province'])
            if user_info.get('city') and user_info.get('city') != user_info.get('province'):
                location_parts.append(user_info['city'])
            
            if location_parts and not partner.city:
                partner_vals['city'] = ' '.join(location_parts)
            
            # Update country
            if user_info.get('country') and not partner.country_id:
                country = self.env['res.country'].search([
                    ('name', 'ilike', user_info['country'])
                ], limit=1)
                if country:
                    partner_vals['country_id'] = country.id
            
            # Add WeChat info to comment
            wechat_info = f"WeChat User - OpenID: {user_info.get('openid', '')}"
            if user_info.get('unionid'):
                wechat_info += f"\nUnionID: {user_info['unionid']}"
            
            if partner.comment:
                if "WeChat User" not in partner.comment:
                    partner_vals['comment'] = f"{partner.comment}\n\n{wechat_info}"
            else:
                partner_vals['comment'] = wechat_info
            
            # Update avatar if available
            if user_info.get('headimgurl') and not partner.image_1920:
                try:
                    import requests
                    import base64
                    response = requests.get(user_info['headimgurl'], timeout=10)
                    if response.status_code == 200:
                        partner_vals['image_1920'] = base64.b64encode(response.content)
                except:
                    pass
            
            if partner_vals:
                partner.write(partner_vals)
                
        except Exception as e:
            _logger.error(f"Failed to update partner with WeChat info: {e}")

    def disable_auto_login(self):
        """Disable auto login for this record"""
        self.ensure_one()
        self.write({
            'enabled': False,
            'login_token': False,
        })

    def enable_auto_login(self):
        """Enable auto login for this record"""
        self.ensure_one()
        self.write({'enabled': True})
        self._generate_login_token()

    def revoke_access(self):
        """Revoke auto login access"""
        self.ensure_one()
        self.write({
            'active': False,
            'enabled': False,
            'login_token': False,
        })

    def action_view_user(self):
        """View related user"""
        self.ensure_one()

        if not self.user_id:
            raise UserError(_('No related user found'))

        return {
            'type': 'ir.actions.act_window',
            'name': _('Related User'),
            'res_model': 'res.users',
            'res_id': self.user_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_wechat_user(self):
        """View related WeChat user"""
        self.ensure_one()

        if not self.wechat_user_id:
            raise UserError(_('No related WeChat user found'))

        return {
            'type': 'ir.actions.act_window',
            'name': _('Related WeChat User'),
            'res_model': 'wechat.user',
            'res_id': self.wechat_user_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
