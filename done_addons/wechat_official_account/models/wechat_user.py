# -*- coding: utf-8 -*-

import requests
import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WeChatUser(models.Model):
    _name = 'wechat.user'
    _description = 'WeChat User'
    _rec_name = 'nickname'
    _order = 'subscribe_time desc'

    # Basic Information
    openid = fields.Char(string='OpenID', required=True, index=True, help='Unique identifier for the user')
    unionid = fields.Char(string='UnionID', index=True, help='Union identifier across different WeChat apps')
    nickname = fields.Char(string='Nickname', help='User nickname')
    sex = fields.Selection([
        ('0', 'Unknown'),
        ('1', 'Male'),
        ('2', 'Female')
    ], string='Gender', default='0')
    city = fields.Char(string='City')
    country = fields.Char(string='Country')
    province = fields.Char(string='Province')
    language = fields.Char(string='Language', default='zh_CN')
    headimgurl = fields.Char(string='Avatar URL', help='User avatar image URL')

    # Contact Information
    phone = fields.Char(string='Phone Number', help='User phone number (collected via OAuth or forms)')
    email = fields.Char(string='Email', help='User email address')
    phone_verified = fields.Boolean(string='Phone Verified', default=False, help='Whether phone number is verified')
    
    # Subscription Information
    subscribe = fields.Boolean(string='Subscribed', default=True, help='Whether user is subscribed')
    subscribe_time = fields.Datetime(string='Subscribe Time', help='Time when user subscribed')
    unsubscribe_time = fields.Datetime(string='Unsubscribe Time', help='Time when user unsubscribed')
    subscribe_scene = fields.Char(string='Subscribe Scene', help='Scene where user subscribed')
    qr_scene = fields.Char(string='QR Scene', help='QR code scene value')
    qr_scene_str = fields.Char(string='QR Scene String', help='QR code scene string')
    
    # Configuration and Relations
    config_id = fields.Many2one('wechat.config', string='WeChat Config', required=True, ondelete='cascade')
    partner_id = fields.Many2one('res.partner', string='Related Partner', help='Related Odoo partner')
    user_id = fields.Many2one('res.users', string='Related User', help='Related Odoo user')

    # Auto Login
    auto_login_enabled = fields.Boolean(string='Auto Login Enabled', compute='_compute_auto_login_enabled')
    
    # Message Statistics
    message_count = fields.Integer(string='Messages Received', default=0, help='Number of messages received from this user')
    last_message_time = fields.Datetime(string='Last Message Time', help='Time of last message from this user')
    
    # Tags and Groups
    tagid_list = fields.Char(string='Tag IDs', help='Comma-separated list of tag IDs')
    group_id = fields.Char(string='Group ID', help='User group ID')
    
    # Status
    active = fields.Boolean(string='Active', default=True)
    remark = fields.Text(string='Remark', help='Internal remark about this user')
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', related='config_id.company_id', store=True)
    
    _sql_constraints = [
        ('openid_config_unique', 'unique(openid, config_id)', 'OpenID must be unique per WeChat configuration!'),
    ]

    def _compute_auto_login_enabled(self):
        """Compute if auto login is enabled for this user"""
        for record in self:
            auto_login = self.env['wechat.auto.login'].search([
                ('openid', '=', record.openid),
                ('config_id', '=', record.config_id.id),
                ('enabled', '=', True),
                ('active', '=', True)
            ], limit=1)
            record.auto_login_enabled = bool(auto_login)
    
    @api.model
    def create_or_update_user(self, openid, config_id, user_info=None):
        """Create or update WeChat user with information from WeChat API"""
        existing_user = self.search([('openid', '=', openid), ('config_id', '=', config_id)], limit=1)
        
        if user_info is None:
            user_info = self._get_user_info_from_wechat(openid, config_id)
        
        if existing_user:
            # Update existing user
            existing_user.write(self._prepare_user_values(user_info))
            return existing_user
        else:
            # Create new user
            values = self._prepare_user_values(user_info)
            values.update({
                'openid': openid,
                'config_id': config_id,
                'subscribe_time': fields.Datetime.now() if user_info.get('subscribe') else False,
            })
            return self.create(values)
    
    def _prepare_user_values(self, user_info):
        """Prepare values for user creation/update from WeChat user info"""
        values = {}
        
        # Map WeChat fields to Odoo fields
        field_mapping = {
            'nickname': 'nickname',
            'sex': 'sex',
            'city': 'city',
            'country': 'country',
            'province': 'province',
            'language': 'language',
            'headimgurl': 'headimgurl',
            'subscribe': 'subscribe',
            'subscribe_scene': 'subscribe_scene',
            'qr_scene': 'qr_scene',
            'qr_scene_str': 'qr_scene_str',
            'unionid': 'unionid',
            'groupid': 'group_id',
        }
        
        for wechat_field, odoo_field in field_mapping.items():
            if wechat_field in user_info:
                values[odoo_field] = user_info[wechat_field]
        
        # Handle subscribe time
        if 'subscribe_time' in user_info and user_info['subscribe_time']:
            import datetime
            values['subscribe_time'] = datetime.datetime.fromtimestamp(user_info['subscribe_time'])
        
        # Handle tag list
        if 'tagid_list' in user_info:
            values['tagid_list'] = ','.join(map(str, user_info['tagid_list']))
        
        return values
    
    def _get_user_info_from_wechat(self, openid, config_id):
        """Get user information from WeChat API"""
        config = self.env['wechat.config'].browse(config_id)
        access_token = config.get_access_token()
        
        url = 'https://api.weixin.qq.com/cgi-bin/user/info'
        params = {
            'access_token': access_token,
            'openid': openid,
            'lang': 'zh_CN'
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'errcode' in data and data['errcode'] != 0:
                _logger.error(f"WeChat API error getting user info: {data}")
                return {'openid': openid, 'subscribe': 0}  # Return minimal info
            
            return data
            
        except requests.RequestException as e:
            _logger.error(f"Failed to get WeChat user info: {e}")
            return {'openid': openid, 'subscribe': 0}  # Return minimal info
    
    def update_user_info(self):
        """Update user information from WeChat API"""
        updated_count = 0
        failed_count = 0

        for user in self:
            try:
                user_info = user._get_user_info_from_wechat(user.openid, user.config_id.id)
                old_nickname = user.nickname
                old_subscribe = user.subscribe

                user.write(user._prepare_user_values(user_info))

                # 记录更新信息
                changes = []
                if old_nickname != user.nickname:
                    changes.append(f"昵称: {old_nickname} → {user.nickname}")
                if old_subscribe != user.subscribe:
                    status = "已关注" if user.subscribe else "已取消关注"
                    changes.append(f"关注状态: {status}")

                if changes:
                    _logger.info(f"Updated user {user.openid}: {', '.join(changes)}")

                updated_count += 1

            except Exception as e:
                _logger.error(f"Failed to update user {user.openid}: {e}")
                failed_count += 1

        # 返回用户友好的通知
        if len(self) == 1:
            # 单个用户更新
            if updated_count > 0:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('User information updated successfully!'),
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Failed to update user information'),
                        'type': 'danger',
                    }
                }
        else:
            # 批量更新
            message = f"Updated {updated_count} users successfully"
            if failed_count > 0:
                message += f", {failed_count} failed"

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Batch Update Complete'),
                    'message': _(message),
                    'type': 'success' if failed_count == 0 else 'warning',
                }
            }
    
    def unsubscribe_user(self):
        """Mark user as unsubscribed"""
        self.write({
            'subscribe': False,
            'unsubscribe_time': fields.Datetime.now()
        })

        # 同时禁用自动登录
        auto_login = self.env['wechat.auto.login'].search([
            ('openid', '=', self.openid),
            ('config_id', '=', self.config_id.id)
        ], limit=1)

        if auto_login:
            auto_login.write({'enabled': False})

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('User marked as unsubscribed and auto-login disabled'),
                'type': 'success',
            }
        }
    
    def subscribe_user(self):
        """Mark user as subscribed"""
        self.write({
            'subscribe': True,
            'subscribe_time': fields.Datetime.now(),
            'unsubscribe_time': False
        })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('User marked as subscribed'),
                'type': 'success',
            }
        }
    
    def create_partner(self):
        """Create a related partner for this WeChat user"""
        self.ensure_one()
        
        if self.partner_id:
            raise UserError(_('This WeChat user already has a related partner.'))
        
        partner_vals = {
            'name': self.nickname or f'WeChat User {self.openid[:8]}',
            'is_company': False,
            'customer_rank': 1,
            'city': self.city,
            'country_id': self.env['res.country'].search([('name', 'ilike', self.country)], limit=1).id if self.country else False,
            'comment': f'WeChat User - OpenID: {self.openid}',
        }
        
        partner = self.env['res.partner'].create(partner_vals)
        self.partner_id = partner.id
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Partner'),
            'res_model': 'res.partner',
            'res_id': partner.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    @api.model
    def sync_users_from_wechat(self, config_id=None):
        """Sync all users from WeChat API"""
        if config_id:
            configs = self.env['wechat.config'].browse(config_id)
        else:
            configs = self.env['wechat.config'].search([('active', '=', True)])
        
        for config in configs:
            self._sync_users_for_config(config)
    
    def _sync_users_for_config(self, config):
        """Sync users for a specific WeChat configuration"""
        access_token = config.get_access_token()

        # Get user list from WeChat
        url = 'https://api.weixin.qq.com/cgi-bin/user/get'
        params = {
            'access_token': access_token,
        }

        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()

            if 'errcode' in data and data['errcode'] != 0:
                _logger.error(f"WeChat API error getting user list: {data}")
                return

            if 'data' in data and 'openid' in data['data']:
                openid_list = data['data']['openid']

                # Update or create users
                for openid in openid_list:
                    self.create_or_update_user(openid, config.id)

                _logger.info(f"Synced {len(openid_list)} WeChat users for config {config.name}")

        except requests.RequestException as e:
            _logger.error(f"Failed to sync WeChat users: {e}")
            raise UserError(_('Failed to sync WeChat users: %s') % str(e))

    def action_view_partner(self):
        """Action to view the related partner"""
        self.ensure_one()
        if not self.partner_id:
            raise UserError(_('This WeChat user has no related partner.'))

        return {
            'type': 'ir.actions.act_window',
            'name': _('Partner'),
            'res_model': 'res.partner',
            'res_id': self.partner_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
