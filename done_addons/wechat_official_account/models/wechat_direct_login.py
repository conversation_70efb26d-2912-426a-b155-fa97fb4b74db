# -*- coding: utf-8 -*-

import logging
from odoo import api, fields, models, _
from odoo.exceptions import UserError, AccessDenied
from odoo.http import request

_logger = logging.getLogger(__name__)


class WeChatDirectLogin(models.Model):
    _name = 'wechat.direct.login'
    _description = 'WeChat Direct Login Binding'
    _rec_name = 'display_name'

    user_id = fields.Many2one('res.users', string='用户', required=True, ondelete='cascade')
    openid = fields.Char(string='微信OpenID', required=True, index=True)
    config_id = fields.Many2one('wechat.config', string='微信配置', required=True)
    wechat_user_id = fields.Many2one('wechat.user', string='微信用户', ondelete='set null')
    enabled = fields.Boolean(string='启用', default=True)
    last_login_time = fields.Datetime(string='最后登录时间')
    login_count = fields.Integer(string='登录次数', default=0)
    display_name = fields.Char(string='显示名称', compute='_compute_display_name', store=True)
    
    _sql_constraints = [
        ('unique_openid_config', 'unique(openid, config_id)', '同一个微信配置下，OpenID必须唯一！'),
        ('unique_user_config', 'unique(user_id, config_id)', '同一个微信配置下，用户只能绑定一个OpenID！'),
    ]

    @api.depends('user_id', 'openid', 'wechat_user_id')
    def _compute_display_name(self):
        for record in self:
            if record.wechat_user_id and record.wechat_user_id.nickname:
                record.display_name = f"{record.user_id.name} ({record.wechat_user_id.nickname})"
            elif record.openid:
                # 确保openid是字符串且不为空
                openid_display = record.openid[:10] + "..." if len(record.openid) > 10 else record.openid
                record.display_name = f"{record.user_id.name} ({openid_display})"
            else:
                # 如果openid为空，使用用户名
                record.display_name = f"{record.user_id.name} (无OpenID)"

    @api.model
    def create_binding(self, user_id, openid, config_id):
        """创建用户与OpenID的绑定"""
        try:
            # 验证参数
            if not openid or not isinstance(openid, str):
                raise UserError(_('OpenID不能为空且必须是字符串'))

            if not user_id or not config_id:
                raise UserError(_('用户ID和配置ID不能为空'))

            # 检查是否已存在绑定
            existing = self.search([
                ('openid', '=', openid),
                ('config_id', '=', config_id)
            ])
            
            if existing:
                if existing.user_id.id != user_id:
                    raise UserError(_('该微信账号已绑定到其他用户'))
                return existing
            
            # 查找对应的微信用户
            wechat_user = self.env['wechat.user'].search([
                ('openid', '=', openid),
                ('config_id', '=', config_id)
            ], limit=1)
            
            # 创建绑定
            binding = self.create({
                'user_id': user_id,
                'openid': openid,
                'config_id': config_id,
                'wechat_user_id': wechat_user.id if wechat_user else False,
                'enabled': True
            })
            
            _logger.info(f"Created WeChat binding: user {user_id} -> openid {openid}")
            return binding
            
        except Exception as e:
            _logger.error(f"Failed to create WeChat binding: {e}")
            raise UserError(_('创建微信绑定失败：%s') % str(e))

    @api.model
    def authenticate_by_openid(self, openid, config_id):
        """通过OpenID进行身份验证"""
        try:
            # 验证参数
            if not openid or not isinstance(openid, str):
                _logger.warning(f"Invalid openid parameter: {openid}")
                return False

            if not config_id:
                _logger.warning(f"Invalid config_id parameter: {config_id}")
                return False

            # 查找绑定记录
            binding = self.search([
                ('openid', '=', openid),
                ('config_id', '=', config_id),
                ('enabled', '=', True)
            ], limit=1)
            
            if not binding:
                _logger.warning(f"No binding found for openid {openid}")
                return False
            
            # 检查用户是否有效
            user = binding.user_id
            if not user.active:
                _logger.warning(f"User {user.id} is not active")
                return False
            
            # 更新登录统计
            binding.write({
                'last_login_time': fields.Datetime.now(),
                'login_count': binding.login_count + 1
            })
            
            _logger.info(f"WeChat authentication successful: openid {openid} -> user {user.id}")
            return user
            
        except Exception as e:
            _logger.error(f"WeChat authentication failed: {e}")
            return False

    def action_test_login(self):
        """测试登录功能"""
        self.ensure_one()
        
        try:
            user = self.authenticate_by_openid(self.openid, self.config_id.id)
            if user:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('测试成功'),
                        'message': _('用户 %s 可以通过微信登录') % user.name,
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('测试失败'),
                        'message': _('微信登录验证失败'),
                        'type': 'danger',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('测试错误'),
                    'message': _('测试过程中发生错误：%s') % str(e),
                    'type': 'danger',
                }
            }

    @api.model
    def get_login_url(self, config_id, redirect_url=None):
        """生成直接登录URL"""
        base_url = self.env['ir.config_parameter'].get_param('web.base.url')
        login_url = f"{base_url}/wechat/direct/login/{config_id}"
        
        if redirect_url:
            from urllib.parse import urlencode
            login_url += f"?{urlencode({'redirect': redirect_url})}"
        
        return login_url


class ResUsers(models.Model):
    _inherit = 'res.users'

    wechat_binding_ids = fields.One2many(
        'wechat.direct.login', 'user_id', 
        string='微信绑定'
    )
    wechat_openid = fields.Char(
        string='微信OpenID', 
        compute='_compute_wechat_openid',
        help='主要的微信OpenID'
    )

    @api.depends('wechat_binding_ids')
    def _compute_wechat_openid(self):
        for user in self:
            binding = user.wechat_binding_ids.filtered('enabled')[:1]
            user.wechat_openid = binding.openid if binding else False



    def action_view_wechat_bindings(self):
        """查看微信绑定"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('微信绑定'),
            'res_model': 'wechat.direct.login',
            'view_mode': 'list,form',
            'domain': [('user_id', '=', self.id)],
            'context': {
                'default_user_id': self.id,
            }
        }
