# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class WeChatConfigWizard(models.TransientModel):
    _name = 'wechat.config.wizard'
    _description = 'WeChat Configuration Wizard'

    name = fields.Char(string='Configuration Name', required=True, default='My WeChat Account')
    app_id = fields.Char(string='AppID', required=True, help='WeChat Official Account AppID')
    app_secret = fields.Char(string='AppSecret', required=True, help='WeChat Official Account AppSecret')
    token = fields.Char(string='Token', required=True, help='Token for webhook verification')
    encoding_aes_key = fields.Char(string='EncodingAESKey', help='AES key for message encryption (optional)')
    is_default = fields.Boolean(string='Set as Default Configuration', default=True)
    
    # Help and guidance fields
    step = fields.Selection([
        ('welcome', 'Welcome'),
        ('config', 'Configuration'),
        ('webhook', 'Webhook Setup'),
        ('test', 'Test Connection'),
        ('complete', 'Complete')
    ], string='Setup Step', default='welcome')
    
    webhook_url = fields.Char(string='Webhook URL', readonly=True)
    test_result = fields.Text(string='Test Result', readonly=True)
    
    @api.onchange('app_id', 'app_secret', 'token')
    def _onchange_config_fields(self):
        """Generate webhook URL when config fields change"""
        if self.app_id and self.app_secret and self.token:
            # This will be updated after the config is created
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            self.webhook_url = f"{base_url}/wechat/webhook/[CONFIG_ID]"
    
    def action_next_step(self):
        """Move to next step in wizard"""
        if self.step == 'welcome':
            self.step = 'config'
        elif self.step == 'config':
            if not all([self.name, self.app_id, self.app_secret, self.token]):
                raise UserError(_('Please fill in all required fields.'))
            self.step = 'webhook'
        elif self.step == 'webhook':
            self.step = 'test'
        elif self.step == 'test':
            self.step = 'complete'
        
        return self._return_wizard()
    
    def action_previous_step(self):
        """Move to previous step in wizard"""
        if self.step == 'config':
            self.step = 'welcome'
        elif self.step == 'webhook':
            self.step = 'config'
        elif self.step == 'test':
            self.step = 'webhook'
        elif self.step == 'complete':
            self.step = 'test'
        
        return self._return_wizard()
    
    def action_test_connection(self):
        """Test the WeChat API connection"""
        if not all([self.app_id, self.app_secret]):
            raise UserError(_('AppID and AppSecret are required for testing.'))
        
        try:
            # Create a temporary config for testing
            temp_config = self.env['wechat.config'].create({
                'name': 'Temp Test Config',
                'app_id': self.app_id,
                'app_secret': self.app_secret,
                'token': self.token,
                'active': False,  # Don't activate until confirmed
            })
            
            # Test the connection
            access_token = temp_config.get_access_token()
            
            if access_token:
                self.test_result = _('✅ Connection successful! Your WeChat API credentials are valid.')
            else:
                self.test_result = _('❌ Connection failed. Please check your credentials.')
            
            # Clean up temp config
            temp_config.unlink()
            
        except Exception as e:
            self.test_result = _('❌ Connection failed: %s') % str(e)
        
        return self._return_wizard()
    
    def action_create_config(self):
        """Create the WeChat configuration"""
        if not all([self.name, self.app_id, self.app_secret, self.token]):
            raise UserError(_('Please fill in all required fields.'))
        
        # Check if this should be the default config
        if self.is_default:
            # Remove default flag from other configs
            other_configs = self.env['wechat.config'].search([('is_default', '=', True)])
            other_configs.write({'is_default': False})
        
        # Create the configuration
        config = self.env['wechat.config'].create({
            'name': self.name,
            'app_id': self.app_id,
            'app_secret': self.app_secret,
            'token': self.token,
            'encoding_aes_key': self.encoding_aes_key,
            'is_default': self.is_default,
            'active': True,
        })
        
        # Update webhook URL with actual config ID
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        self.webhook_url = f"{base_url}/wechat/webhook/{config.id}"
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('WeChat Configuration'),
            'res_model': 'wechat.config',
            'res_id': config.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def action_skip_wizard(self):
        """Skip wizard and go to manual configuration"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('WeChat Configurations'),
            'res_model': 'wechat.config',
            'view_mode': 'tree,form',
            'target': 'current',
        }
    
    def _return_wizard(self):
        """Return to wizard view"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('WeChat Setup Wizard'),
            'res_model': 'wechat.config.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }
    
    @api.model
    def action_open_wizard(self):
        """Open the configuration wizard"""
        wizard = self.create({})
        return {
            'type': 'ir.actions.act_window',
            'name': _('WeChat Setup Wizard'),
            'res_model': 'wechat.config.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
        }
