# -*- coding: utf-8 -*-

import logging
import requests
from odoo import models, fields, api, _
from odoo.exceptions import UserError, AccessDenied
from odoo.http import request

_logger = logging.getLogger(__name__)


class WeChatAuthProvider(models.Model):
    _name = 'wechat.auth.provider'
    _description = 'WeChat Authentication Provider'
    _rec_name = 'name'

    name = fields.Char(string='Provider Name', required=True)
    config_id = fields.Many2one('wechat.config', string='WeChat Config', required=True)
    enabled = fields.Boolean(string='Enabled', default=True)
    auto_create_user = fields.Boolean(string='Auto Create User', default=True, 
                                     help='Automatically create Odoo user for new WeChat users')
    auto_get_phone = fields.Boolean(string='Auto Get Phone', default=True,
                                   help='Automatically request phone number during login')
    default_groups = fields.Many2many('res.groups', string='Default Groups',
                                     help='Default groups for auto-created users')
    
    # Login Settings
    login_redirect_url = fields.Char(string='Login Redirect URL', 
                                    help='URL to redirect after successful login')
    logout_redirect_url = fields.Char(string='Logout Redirect URL',
                                     help='URL to redirect after logout')
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', 
                                related='config_id.company_id', store=True)

    @api.model
    def authenticate_wechat_user(self, openid, config_id, user_info=None, phone_number=None):
        """
        Authenticate WeChat user and create/update Odoo user
        
        Args:
            openid: WeChat user OpenID
            config_id: WeChat configuration ID
            user_info: WeChat user information dict
            phone_number: User phone number (if available)
        
        Returns:
            res.users record or False
        """
        try:
            # Find auth provider
            provider = self.search([
                ('config_id', '=', config_id),
                ('enabled', '=', True)
            ], limit=1)
            
            if not provider:
                _logger.warning(f"No enabled auth provider found for config {config_id}")
                return False
            
            # Find or create WeChat user
            wechat_user = self._find_or_create_wechat_user(openid, config_id, user_info, phone_number)
            
            # Find or create Odoo user
            odoo_user = self._find_or_create_odoo_user(wechat_user, provider)
            
            # Link WeChat user with Odoo user
            if odoo_user and wechat_user:
                wechat_user.write({
                    'user_id': odoo_user.id,
                    'phone': phone_number or wechat_user.phone,
                })

                # Update user's partner with WeChat info
                self._update_user_partner(odoo_user, wechat_user)

                # Create direct login binding for future logins
                self._create_direct_login_binding(odoo_user, wechat_user, config_id)
            
            return odoo_user
            
        except Exception as e:
            _logger.error(f"WeChat authentication error: {e}")
            return False

    def _find_or_create_wechat_user(self, openid, config_id, user_info, phone_number):
        """Find or create WeChat user"""
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id)
        ], limit=1)
        
        if not wechat_user and user_info:
            # Create new WeChat user
            wechat_user_vals = {
                'openid': openid,
                'config_id': config_id,
                'nickname': user_info.get('nickname', ''),
                'sex': str(user_info.get('sex', 0)),
                'city': user_info.get('city', ''),
                'country': user_info.get('country', ''),
                'province': user_info.get('province', ''),
                'language': user_info.get('language', 'zh_CN'),
                'headimgurl': user_info.get('headimgurl', ''),
                'unionid': user_info.get('unionid', ''),
                'phone': phone_number,
                'phone_verified': bool(phone_number),
                'subscribe': True,
                'subscribe_time': fields.Datetime.now(),
            }
            wechat_user = self.env['wechat.user'].create(wechat_user_vals)
        elif wechat_user and phone_number:
            # Update phone number if provided
            wechat_user.write({
                'phone': phone_number,
                'phone_verified': True,
            })
        
        return wechat_user

    def _find_or_create_odoo_user(self, wechat_user, provider):
        """Find or create Odoo user"""
        if not wechat_user:
            return False
        
        # First, try to find existing user by WeChat user relation
        if wechat_user.user_id:
            return wechat_user.user_id
        
        # Try to find by phone number
        if wechat_user.phone:
            existing_user = self.env['res.users'].search([
                ('phone', '=', wechat_user.phone),
                ('active', '=', True)
            ], limit=1)
            if existing_user:
                return existing_user
        
        # Try to find by partner phone
        if wechat_user.phone:
            partner = self.env['res.partner'].search([
                ('phone', '=', wechat_user.phone),
                ('is_company', '=', False)
            ], limit=1)
            if partner and partner.user_ids:
                return partner.user_ids[0]
        
        # Create new user if auto_create_user is enabled
        if provider.auto_create_user:
            return self._create_odoo_user(wechat_user, provider)
        
        return False

    def _create_odoo_user(self, wechat_user, provider):
        """Create new Odoo user"""
        try:
            # Generate unique login
            base_login = wechat_user.phone or f"wechat_{wechat_user.openid[:8]}"
            login = base_login
            counter = 1
            while self.env['res.users'].search([('login', '=', login)], limit=1):
                login = f"{base_login}_{counter}"
                counter += 1
            
            # Prepare user values
            user_vals = {
                'name': wechat_user.nickname or f'WeChat User {wechat_user.openid[:8]}',
                'login': login,
                'email': f"{login}@wechat.local",  # Temporary email
                'phone': wechat_user.phone,
                'active': True,
                'company_id': provider.company_id.id,
                'company_ids': [(6, 0, [provider.company_id.id])],
            }
            
            # Add default groups
            if provider.default_groups:
                user_vals['groups_id'] = [(6, 0, provider.default_groups.ids)]
            
            # Create user
            new_user = self.env['res.users'].create(user_vals)
            
            _logger.info(f"Created new user {new_user.login} for WeChat user {wechat_user.openid}")
            return new_user
            
        except Exception as e:
            _logger.error(f"Failed to create user for WeChat user {wechat_user.openid}: {e}")
            return False

    def _update_user_partner(self, odoo_user, wechat_user):
        """Update user's partner with WeChat information"""
        try:
            partner_vals = {}
            
            # Update basic info if not set
            if not odoo_user.partner_id.image_1920 and wechat_user.headimgurl:
                try:
                    response = requests.get(wechat_user.headimgurl, timeout=10)
                    if response.status_code == 200:
                        import base64
                        partner_vals['image_1920'] = base64.b64encode(response.content)
                except:
                    pass
            
            # Update phone if not set
            if not odoo_user.partner_id.phone and wechat_user.phone:
                partner_vals['phone'] = wechat_user.phone
            
            # Update location info
            if wechat_user.city or wechat_user.province:
                location_parts = []
                if wechat_user.province:
                    location_parts.append(wechat_user.province)
                if wechat_user.city and wechat_user.city != wechat_user.province:
                    location_parts.append(wechat_user.city)
                
                if location_parts and not odoo_user.partner_id.city:
                    partner_vals['city'] = ' '.join(location_parts)
            
            # Update country
            if wechat_user.country and not odoo_user.partner_id.country_id:
                country = self.env['res.country'].search([
                    ('name', 'ilike', wechat_user.country)
                ], limit=1)
                if country:
                    partner_vals['country_id'] = country.id
            
            # Add WeChat info to comment
            wechat_info = f"WeChat OpenID: {wechat_user.openid}"
            if wechat_user.unionid:
                wechat_info += f"\nWeChat UnionID: {wechat_user.unionid}"
            
            if odoo_user.partner_id.comment:
                if "WeChat OpenID" not in odoo_user.partner_id.comment:
                    partner_vals['comment'] = f"{odoo_user.partner_id.comment}\n\n{wechat_info}"
            else:
                partner_vals['comment'] = wechat_info
            
            # Update partner
            if partner_vals:
                odoo_user.partner_id.write(partner_vals)
                
            # Link WeChat user to partner
            wechat_user.partner_id = odoo_user.partner_id.id
            
        except Exception as e:
            _logger.error(f"Failed to update partner for user {odoo_user.login}: {e}")

    @api.model
    def get_wechat_login_url(self, config_id, state=None):
        """Generate WeChat login URL"""
        try:
            config = self.env['wechat.config'].browse(config_id)
            if not config.exists():
                return False
            
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = f"{base_url}/wechat/auth/login/callback/{config_id}"
            
            # Generate OAuth URL with phone scope
            oauth_url = self.env['wechat.oauth'].generate_oauth_url(
                config_id=config_id,
                redirect_uri=redirect_uri,
                scope='snsapi_userinfo',
                state=state or 'login'
            )
            
            return oauth_url
            
        except Exception as e:
            _logger.error(f"Failed to generate WeChat login URL: {e}")
            return False

    def get_user_by_wechat(self, openid, config_id):
        """Get Odoo user by WeChat OpenID"""
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id)
        ], limit=1)
        
        return wechat_user.user_id if wechat_user else False

    def _create_direct_login_binding(self, odoo_user, wechat_user, config_id):
        """创建直接登录绑定记录"""
        try:
            # 检查是否已存在绑定
            existing_binding = self.env['wechat.direct.login'].search([
                ('openid', '=', wechat_user.openid),
                ('config_id', '=', config_id),
            ], limit=1)

            if existing_binding:
                # 如果绑定存在但用户不同，更新绑定
                if existing_binding.user_id.id != odoo_user.id:
                    existing_binding.write({
                        'user_id': odoo_user.id,
                        'wechat_user_id': wechat_user.id,
                        'enabled': True,
                    })
                    _logger.info(f"Updated existing WeChat direct login binding: {wechat_user.openid} -> user {odoo_user.id}")
                else:
                    # 绑定已存在且正确，确保启用状态
                    if not existing_binding.enabled:
                        existing_binding.write({'enabled': True})
                    _logger.info(f"WeChat direct login binding already exists: {wechat_user.openid} -> user {odoo_user.id}")
                return existing_binding

            # 创建新的绑定记录
            binding = self.env['wechat.direct.login'].create({
                'user_id': odoo_user.id,
                'openid': wechat_user.openid,
                'config_id': config_id,
                'wechat_user_id': wechat_user.id,
                'enabled': True,
            })

            _logger.info(f"Created WeChat direct login binding: {wechat_user.openid} -> user {odoo_user.id}")
            return binding

        except Exception as e:
            _logger.error(f"Failed to create WeChat direct login binding: {e}")
            return False
