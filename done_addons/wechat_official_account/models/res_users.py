# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class ResUsers(models.Model):
    _inherit = 'res.users'

    # WeChat Auto Login
    wechat_auto_login_count = fields.Integer(
        string='WeChat Auto Login Count',
        compute='_compute_wechat_auto_login_count'
    )
    wechat_user_ids = fields.One2many(
        'wechat.user', 'user_id',
        string='WeChat Users',
        help='WeChat users linked to this Odoo user'
    )
    wechat_auto_login_ids = fields.One2many(
        'wechat.auto.login', 'user_id',
        string='WeChat Auto Login Records',
        help='WeChat auto login records for this user'
    )

    def _compute_wechat_auto_login_count(self):
        """Compute the number of WeChat auto login records"""
        for user in self:
            user.wechat_auto_login_count = len(user.wechat_auto_login_ids)

    def action_view_wechat_auto_login(self):
        """Action to view WeChat auto login records"""
        self.ensure_one()
        
        action = self.env.ref('wechat_official_account.action_wechat_auto_login').read()[0]
        action['domain'] = [('user_id', '=', self.id)]
        action['context'] = {
            'default_user_id': self.id,
            'search_default_user_id': self.id,
        }
        
        if len(self.wechat_auto_login_ids) == 1:
            action['views'] = [(False, 'form')]
            action['res_id'] = self.wechat_auto_login_ids.id
        
        return action

    def action_view_wechat_users(self):
        """Action to view related WeChat users"""
        self.ensure_one()
        
        action = self.env.ref('wechat_official_account.action_wechat_user').read()[0]
        action['domain'] = [('user_id', '=', self.id)]
        action['context'] = {
            'default_user_id': self.id,
            'search_default_user_id': self.id,
        }
        
        return action

    def enable_wechat_auto_login(self):
        """Enable WeChat auto login for this user"""
        self.ensure_one()
        
        auto_login_records = self.wechat_auto_login_ids.filtered(lambda r: r.active)
        if not auto_login_records:
            raise UserError(_('No WeChat auto login records found for this user.'))
        
        for record in auto_login_records:
            record.enable_auto_login()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('WeChat auto login has been enabled.'),
                'type': 'success',
            }
        }

    def disable_wechat_auto_login(self):
        """Disable WeChat auto login for this user"""
        self.ensure_one()
        
        auto_login_records = self.wechat_auto_login_ids.filtered(lambda r: r.active)
        if not auto_login_records:
            raise UserError(_('No WeChat auto login records found for this user.'))
        
        for record in auto_login_records:
            record.disable_auto_login()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('WeChat auto login has been disabled.'),
                'type': 'success',
            }
        }

    def get_wechat_login_qr_url(self, config_id=None):
        """Get WeChat login QR code URL for this user"""
        self.ensure_one()
        
        if not config_id:
            # Get the first available WeChat config
            config = self.env['wechat.config'].search([('active', '=', True)], limit=1)
            if not config:
                return False
            config_id = config.id
        
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        return f"{base_url}/wechat/auto/qr/{config_id}"

    @api.model
    def authenticate_wechat_user(self, openid, config_id):
        """
        Authenticate user by WeChat OpenID
        This method is called during WeChat auto login process
        """
        auto_login = self.env['wechat.auto.login'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id),
            ('enabled', '=', True),
            ('active', '=', True),
            ('user_id.active', '=', True),
        ], limit=1)
        
        if auto_login:
            return auto_login.user_id
        
        return False

    def link_wechat_user(self, openid, config_id, phone_number=None):
        """
        Link this Odoo user with a WeChat user
        
        Args:
            openid: WeChat OpenID
            config_id: WeChat configuration ID
            phone_number: User phone number (optional)
        
        Returns:
            wechat.auto.login record
        """
        self.ensure_one()
        
        # Check if WeChat user exists
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id)
        ], limit=1)
        
        if wechat_user:
            # Link existing WeChat user
            wechat_user.user_id = self.id
            if phone_number and not wechat_user.phone:
                wechat_user.phone = phone_number
        
        # Create auto login record
        auto_login_mgr = self.env['wechat.auto.login']
        auto_login = auto_login_mgr.create_auto_login_record(
            openid=openid,
            config_id=config_id,
            user_id=self.id,
            phone_number=phone_number,
            wechat_user_id=wechat_user.id if wechat_user else None
        )
        
        return auto_login

    def unlink_wechat_user(self, openid, config_id):
        """
        Unlink WeChat user from this Odoo user
        
        Args:
            openid: WeChat OpenID
            config_id: WeChat configuration ID
        """
        self.ensure_one()
        
        # Find and revoke auto login records
        auto_login_records = self.env['wechat.auto.login'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id),
            ('user_id', '=', self.id)
        ])
        
        for record in auto_login_records:
            record.revoke_access()
        
        # Unlink WeChat user
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', openid),
            ('config_id', '=', config_id),
            ('user_id', '=', self.id)
        ], limit=1)
        
        if wechat_user:
            wechat_user.user_id = False

    def get_wechat_info(self):
        """Get WeChat information for this user"""
        self.ensure_one()
        
        wechat_users = self.wechat_user_ids
        auto_login_records = self.wechat_auto_login_ids.filtered('active')
        
        return {
            'wechat_users': [{
                'openid': user.openid,
                'nickname': user.nickname,
                'phone': user.phone,
                'config_name': user.config_id.name,
                'auto_login_enabled': user.auto_login_enabled,
            } for user in wechat_users],
            'auto_login_count': len(auto_login_records),
            'enabled_auto_login_count': len(auto_login_records.filtered('enabled')),
        }
