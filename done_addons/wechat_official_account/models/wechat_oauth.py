# -*- coding: utf-8 -*-

import requests
import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError
from urllib.parse import urlencode, quote

_logger = logging.getLogger(__name__)


class WeChatOAuth(models.Model):
    _name = 'wechat.oauth'
    _description = 'WeChat OAuth Authorization'
    _rec_name = 'openid'
    _order = 'create_date desc'

    # Basic Information
    openid = fields.Char(string='OpenID', required=True, index=True)
    unionid = fields.Char(string='UnionID', index=True)
    access_token = fields.Char(string='Access Token')
    refresh_token = fields.Char(string='Refresh Token')
    expires_in = fields.Integer(string='Expires In (seconds)')
    scope = fields.Char(string='Scope')
    
    # User Information from OAuth
    nickname = fields.Char(string='Nickname')
    sex = fields.Selection([
        ('0', 'Unknown'),
        ('1', 'Male'),
        ('2', 'Female')
    ], string='Gender')
    city = fields.Char(string='City')
    country = fields.Char(string='Country')
    province = fields.Char(string='Province')
    language = fields.Char(string='Language')
    headimgurl = fields.Char(string='Avatar URL')
    
    # Phone Number (if obtained)
    phone_number = fields.Char(string='Phone Number', help='User phone number if authorized')
    phone_verified = fields.Boolean(string='Phone Verified', default=False)
    
    # Configuration and Relations
    config_id = fields.Many2one('wechat.config', string='WeChat Config', required=True, ondelete='cascade')
    user_id = fields.Many2one('wechat.user', string='WeChat User', help='Related WeChat user')
    partner_id = fields.Many2one('res.partner', string='Related Partner', help='Related Odoo partner')
    
    # Status
    active = fields.Boolean(string='Active', default=True)
    state = fields.Selection([
        ('pending', 'Pending Authorization'),
        ('authorized', 'Authorized'),
        ('expired', 'Expired'),
        ('revoked', 'Revoked')
    ], string='Status', default='pending')
    
    # Timestamps
    authorized_date = fields.Datetime(string='Authorized Date')
    expires_date = fields.Datetime(string='Expires Date')
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', related='config_id.company_id', store=True)

    @api.model
    def generate_oauth_url(self, config_id, redirect_uri, scope='snsapi_userinfo', state=None):
        """
        Generate WeChat OAuth authorization URL
        
        Args:
            config_id: WeChat configuration ID
            redirect_uri: Redirect URI after authorization
            scope: Authorization scope ('snsapi_base' or 'snsapi_userinfo')
            state: Custom state parameter
        
        Returns:
            Authorization URL
        """
        config = self.env['wechat.config'].browse(config_id)
        if not config.exists():
            raise UserError(_('WeChat configuration not found'))
        
        # WeChat OAuth URL
        base_url = 'https://open.weixin.qq.com/connect/oauth2/authorize'
        
        # Ensure scope is not empty and valid
        if not scope or scope.strip() == '' or scope == 'None' or scope == 'null':
            scope = 'snsapi_userinfo'
            _logger.warning(f"Empty or invalid scope provided, using default: {scope}")

        # Validate scope value
        valid_scopes = ['snsapi_base', 'snsapi_userinfo']
        if scope not in valid_scopes:
            _logger.warning(f"Invalid scope '{scope}' provided, using default: snsapi_userinfo")
            scope = 'snsapi_userinfo'

        # Build URL manually to ensure all parameters are included
        from urllib.parse import quote, urlencode

        # Final validation - ensure scope is absolutely not empty
        if not scope or len(scope.strip()) == 0:
            scope = 'snsapi_userinfo'
            _logger.error(f"CRITICAL: scope was still empty after validation, forcing to: {scope}")

        # Log parameters for debugging
        _logger.info(f"OAuth parameters: appid={config.app_id}, scope='{scope}', state='{state}'")

        # Use urlencode to ensure proper encoding

        params = {
            'appid': config.app_id,
            'redirect_uri': redirect_uri,
            'response_type': 'code',
            'scope': scope,
            'state': state or 'STATE'
        }

        # Build URL with proper encoding
        oauth_url = f"{base_url}?{urlencode(params)}#wechat_redirect"

        _logger.info(f"Generated OAuth URL: {oauth_url}")
        return oauth_url

    @api.model
    def handle_oauth_callback(self, code, state, config_id):
        """
        Handle OAuth callback and get user information
        
        Args:
            code: Authorization code from WeChat
            state: State parameter
            config_id: WeChat configuration ID
        
        Returns:
            OAuth record
        """
        config = self.env['wechat.config'].browse(config_id)
        if not config.exists():
            raise UserError(_('WeChat configuration not found'))
        
        try:
            # Step 1: Get access token
            access_token_data = self._get_access_token(code, config)
            
            # Step 2: Get user information
            user_info = self._get_user_info(access_token_data, config)
            
            # Step 3: Create or update OAuth record
            oauth_record = self._create_or_update_oauth(access_token_data, user_info, config)
            
            # Step 4: Link with WeChat user if exists
            self._link_wechat_user(oauth_record)
            
            return oauth_record
            
        except Exception as e:
            _logger.error(f"OAuth callback error: {e}")
            raise UserError(_('OAuth authorization failed: %s') % str(e))

    def _get_access_token(self, code, config):
        """Get access token from WeChat"""
        url = 'https://api.weixin.qq.com/sns/oauth2/access_token'
        params = {
            'appid': config.app_id,
            'secret': config.app_secret,
            'code': code,
            'grant_type': 'authorization_code'
        }
        
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        if 'errcode' in data:
            raise UserError(_('Failed to get access token: %s') % data.get('errmsg', 'Unknown error'))
        
        return data

    def _get_user_info(self, access_token_data, config):
        """Get user information from WeChat"""
        # If scope is snsapi_base, we only have openid
        if access_token_data.get('scope') == 'snsapi_base':
            return {
                'openid': access_token_data.get('openid'),
                'unionid': access_token_data.get('unionid'),
            }
        
        # For snsapi_userinfo, get detailed user info
        url = 'https://api.weixin.qq.com/sns/userinfo'
        params = {
            'access_token': access_token_data.get('access_token'),
            'openid': access_token_data.get('openid'),
            'lang': 'zh_CN'
        }
        
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        if 'errcode' in data:
            raise UserError(_('Failed to get user info: %s') % data.get('errmsg', 'Unknown error'))
        
        return data

    def _create_or_update_oauth(self, access_token_data, user_info, config):
        """Create or update OAuth record"""
        openid = user_info.get('openid')
        
        # Find existing record
        existing = self.search([
            ('openid', '=', openid),
            ('config_id', '=', config.id)
        ], limit=1)
        
        # Prepare values
        values = {
            'openid': openid,
            'unionid': user_info.get('unionid') or access_token_data.get('unionid'),
            'access_token': access_token_data.get('access_token'),
            'refresh_token': access_token_data.get('refresh_token'),
            'expires_in': access_token_data.get('expires_in', 7200),
            'scope': access_token_data.get('scope'),
            'config_id': config.id,
            'state': 'authorized',
            'authorized_date': fields.Datetime.now(),
        }
        
        # Add user info if available
        if user_info.get('nickname'):
            values.update({
                'nickname': user_info.get('nickname'),
                'sex': str(user_info.get('sex', 0)),
                'city': user_info.get('city'),
                'country': user_info.get('country'),
                'province': user_info.get('province'),
                'language': user_info.get('language'),
                'headimgurl': user_info.get('headimgurl'),
            })
        
        # Calculate expiry date
        if values['expires_in']:
            from datetime import datetime, timedelta
            values['expires_date'] = datetime.now() + timedelta(seconds=values['expires_in'])
        
        if existing:
            existing.write(values)
            return existing
        else:
            return self.create(values)

    def _link_wechat_user(self, oauth_record):
        """Link OAuth record with existing WeChat user"""
        wechat_user = self.env['wechat.user'].search([
            ('openid', '=', oauth_record.openid),
            ('config_id', '=', oauth_record.config_id.id)
        ], limit=1)
        
        if wechat_user:
            oauth_record.user_id = wechat_user.id
            # Update WeChat user with OAuth info if available
            if oauth_record.phone_number:
                wechat_user.write({
                    'phone': oauth_record.phone_number,
                })

    def get_phone_number(self, access_token, code):
        """
        Get user phone number (requires special permission from WeChat)
        This is for Mini Programs, not available for Official Accounts
        """
        # Note: Phone number access is typically only available for Mini Programs
        # Official Accounts need to use other methods like forms or customer service
        raise UserError(_('Phone number access is not available for Official Accounts. Please use web forms or customer service to collect phone numbers.'))

    def refresh_access_token(self):
        """Refresh access token"""
        self.ensure_one()
        
        if not self.refresh_token:
            raise UserError(_('No refresh token available'))
        
        url = 'https://api.weixin.qq.com/sns/oauth2/refresh_token'
        params = {
            'appid': self.config_id.app_id,
            'grant_type': 'refresh_token',
            'refresh_token': self.refresh_token
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'errcode' in data:
                self.state = 'expired'
                raise UserError(_('Failed to refresh token: %s') % data.get('errmsg'))
            
            # Update token info
            self.write({
                'access_token': data.get('access_token'),
                'refresh_token': data.get('refresh_token'),
                'expires_in': data.get('expires_in', 7200),
                'state': 'authorized',
            })
            
            # Update expiry date
            if data.get('expires_in'):
                from datetime import datetime, timedelta
                self.expires_date = datetime.now() + timedelta(seconds=data['expires_in'])
            
        except Exception as e:
            _logger.error(f"Token refresh error: {e}")
            self.state = 'expired'
            raise UserError(_('Failed to refresh access token: %s') % str(e))

    def revoke_authorization(self):
        """Revoke authorization"""
        self.ensure_one()
        self.write({
            'state': 'revoked',
            'access_token': False,
            'refresh_token': False,
        })
