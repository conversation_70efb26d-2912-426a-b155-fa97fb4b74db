# -*- coding: utf-8 -*-

import json
import logging
import requests
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WeChatMenu(models.Model):
    _name = 'wechat.menu'
    _description = '微信自定义菜单'
    _rec_name = 'name'
    _order = 'config_id, create_date desc'

    name = fields.Char(string='菜单名称', required=True, help='菜单配置名称')
    config_id = fields.Many2one('wechat.config', string='微信配置', required=True, ondelete='cascade')
    
    # 菜单状态
    state = fields.Selection([
        ('draft', '草稿'),
        ('published', '已发布'),
        ('error', '发布失败')
    ], string='状态', default='draft', readonly=True)
    
    # 菜单项
    menu_items = fields.One2many('wechat.menu.item', 'menu_id', string='菜单项')
    
    # 发布信息
    published_date = fields.Datetime(string='发布时间', readonly=True)
    error_message = fields.Text(string='错误信息', readonly=True)
    
    # 统计信息
    total_items = fields.Integer(string='菜单项总数', compute='_compute_total_items', store=True)
    
    # 公司
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    
    @api.depends('menu_items')
    def _compute_total_items(self):
        """计算菜单项总数"""
        for record in self:
            record.total_items = len(record.menu_items)
    
    @api.constrains('menu_items')
    def _check_menu_structure(self):
        """验证菜单结构"""
        for record in self:
            # 检查一级菜单数量（最多3个）
            level1_items = record.menu_items.filtered(lambda x: x.level == 1)
            if len(level1_items) > 3:
                raise ValidationError(_('一级菜单最多只能有3个'))
            
            # 检查二级菜单数量（每个一级菜单最多5个二级菜单）
            for level1_item in level1_items:
                level2_items = record.menu_items.filtered(lambda x: x.parent_id == level1_item)
                if len(level2_items) > 5:
                    raise ValidationError(_('每个一级菜单最多只能有5个二级菜单'))
                
                # 如果有二级菜单，一级菜单不能有动作
                if level2_items and level1_item.menu_type != 'submenu':
                    raise ValidationError(_('有二级菜单的一级菜单不能设置动作类型'))
    
    def action_publish_menu(self):
        """发布菜单到微信"""
        self.ensure_one()

        # 检查菜单项
        if not self.menu_items:
            raise UserError(_('请先添加菜单项'))

        # 验证菜单项配置
        for item in self.menu_items.filtered('active'):
            if item.menu_type == 'click' and not item.key:
                raise UserError(_('菜单项 "%s" 缺少 KEY 值') % item.name)
            elif item.menu_type == 'view' and not item.url:
                raise UserError(_('菜单项 "%s" 缺少 URL 地址') % item.name)

        try:
            # 如果已发布，先删除现有菜单
            if self.state == 'published':
                try:
                    delete_result = self._delete_wechat_menu()
                    _logger.info(f"删除现有菜单: {delete_result}")
                except Exception as e:
                    _logger.warning(f"删除现有菜单失败，继续发布: {e}")

            # 构建菜单数据
            menu_data = self._build_menu_data()
            _logger.info(f"菜单数据: {menu_data}")

            # 调用微信API创建菜单
            result = self._create_wechat_menu(menu_data)

            if result.get('errcode') == 0:
                self.write({
                    'state': 'published',
                    'published_date': fields.Datetime.now(),
                    'error_message': False
                })
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('成功'),
                        'message': _('菜单发布成功'),
                        'type': 'success',
                    }
                }
            else:
                error_msg = f"错误码: {result.get('errcode')}, 错误信息: {result.get('errmsg')}"
                self.write({
                    'state': 'error',
                    'error_message': error_msg
                })
                raise UserError(_('菜单发布失败: %s') % error_msg)

        except Exception as e:
            error_msg = str(e)
            _logger.error(f"发布菜单失败: {e}")
            self.write({
                'state': 'error',
                'error_message': error_msg
            })
            raise UserError(_('菜单发布失败: %s') % error_msg)
    
    def action_delete_menu(self):
        """删除微信菜单"""
        self.ensure_one()

        try:
            # 调用微信API删除菜单
            result = self._delete_wechat_menu()

            if result.get('errcode') == 0:
                self.write({
                    'state': 'draft',
                    'published_date': False,
                    'error_message': False
                })
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('成功'),
                        'message': _('菜单删除成功，可以重新发布'),
                        'type': 'success',
                    }
                }
            else:
                error_msg = f"错误码: {result.get('errcode')}, 错误信息: {result.get('errmsg')}"
                self.write({
                    'state': 'error',
                    'error_message': error_msg
                })
                raise UserError(_('菜单删除失败: %s') % error_msg)

        except Exception as e:
            error_msg = str(e)
            self.write({
                'state': 'error',
                'error_message': error_msg
            })
            _logger.error(f"删除菜单失败: {e}")
            raise UserError(_('菜单删除失败: %s') % error_msg)

    def action_update_menu(self):
        """更新菜单（删除后重新发布）"""
        self.ensure_one()

        if self.state != 'published':
            return self.action_publish_menu()

        try:
            # 先删除现有菜单
            delete_result = self._delete_wechat_menu()
            if delete_result.get('errcode') != 0:
                _logger.warning(f"删除菜单警告: {delete_result}")

            # 重新发布菜单
            return self.action_publish_menu()

        except Exception as e:
            _logger.error(f"更新菜单失败: {e}")
            raise UserError(_('更新菜单失败: %s') % str(e))

    def action_reset_menu(self):
        """重置菜单状态为草稿"""
        self.ensure_one()

        self.write({
            'state': 'draft',
            'published_date': False,
            'error_message': False
        })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('成功'),
                'message': _('菜单状态已重置为草稿'),
                'type': 'success',
            }
        }

    def action_query_menu(self):
        """查询微信菜单"""
        self.ensure_one()
        
        try:
            # 调用微信API查询菜单
            result = self._query_wechat_menu()
            
            if result.get('errcode') == 0 or 'menu' in result:
                menu_info = result.get('menu', {})
                return {
                    'type': 'ir.actions.act_window',
                    'name': _('微信菜单信息'),
                    'view_mode': 'form',
                    'res_model': 'wechat.menu.query.wizard',
                    'target': 'new',
                    'context': {
                        'default_menu_data': json.dumps(menu_info, ensure_ascii=False, indent=2)
                    }
                }
            else:
                error_msg = f"错误码: {result.get('errcode')}, 错误信息: {result.get('errmsg')}"
                raise UserError(_('查询菜单失败: %s') % error_msg)
                
        except Exception as e:
            _logger.error(f"查询菜单失败: {e}")
            raise UserError(_('查询菜单失败: %s') % str(e))
    
    def _build_menu_data(self):
        """构建微信菜单数据"""
        self.ensure_one()

        # 获取一级菜单
        level1_items = self.menu_items.filtered(lambda x: x.level == 1 and x.active).sorted('sequence')

        buttons = []
        for item in level1_items:
            button_data = item._get_button_data()

            # 获取二级菜单
            level2_items = self.menu_items.filtered(lambda x: x.parent_id == item and x.active).sorted('sequence')
            if level2_items:
                button_data['sub_button'] = []
                for sub_item in level2_items:
                    sub_button_data = sub_item._get_button_data()
                    button_data['sub_button'].append(sub_button_data)

            buttons.append(button_data)

        return {'button': buttons}

    def _clean_text_for_wechat(self, text):
        """清理文本，移除微信不支持的字符"""
        if not text:
            return text

        # 移除或替换可能导致问题的字符
        import re

        # 移除控制字符和特殊Unicode字符
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # 移除可能的Unicode转义序列
        text = re.sub(r'\\u[0-9a-fA-F]{4}', '', text)

        # 确保文本是UTF-8编码
        try:
            text = text.encode('utf-8').decode('utf-8')
        except:
            text = text.encode('utf-8', errors='ignore').decode('utf-8')

        return text
    
    def _create_wechat_menu(self, menu_data):
        """调用微信API创建菜单"""
        access_token = self.config_id.get_access_token()
        url = f"https://api.weixin.qq.com/cgi-bin/menu/create?access_token={access_token}"

        # 记录发送的数据用于调试
        _logger.info(f"Creating WeChat menu with data: {json.dumps(menu_data, ensure_ascii=False, indent=2)}")

        # 确保数据是UTF-8编码
        try:
            json_data = json.dumps(menu_data, ensure_ascii=False)
            json_data = json_data.encode('utf-8').decode('utf-8')

            response = requests.post(
                url,
                data=json_data.encode('utf-8'),
                headers={'Content-Type': 'application/json; charset=utf-8'},
                timeout=30
            )

            result = response.json()
            _logger.info(f"WeChat menu creation response: {result}")

            return result

        except Exception as e:
            _logger.error(f"Error creating WeChat menu: {e}")
            return {'errcode': -1, 'errmsg': str(e)}
    
    def _delete_wechat_menu(self):
        """调用微信API删除菜单"""
        access_token = self.config_id.get_access_token()
        url = f"https://api.weixin.qq.com/cgi-bin/menu/delete?access_token={access_token}"
        
        response = requests.get(url, timeout=30)
        return response.json()
    
    def _query_wechat_menu(self):
        """调用微信API查询菜单"""
        access_token = self.config_id.get_access_token()
        url = f"https://api.weixin.qq.com/cgi-bin/menu/get?access_token={access_token}"

        response = requests.get(url, timeout=30)
        return response.json()

    def add_auto_login_menu(self):
        """添加自动登录菜单项"""
        self.ensure_one()

        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        login_url = f"{base_url}/wechat/auto/login/{self.config_id.id}"

        # 检查是否已存在登录菜单
        existing_menu = self.env['wechat.menu.item'].search([
            ('menu_id', '=', self.id),
            ('name', 'in', ['系统登录', 'System Login', 'Login'])
        ], limit=1)

        if existing_menu:
            existing_menu.write({
                'name': 'Login',  # 使用英文避免编码问题
                'menu_type': 'view',
                'url': login_url,
                'active': True,
            })
            message = _('自动登录菜单已更新')
        else:
            # 创建新的登录菜单项
            self.env['wechat.menu.item'].create({
                'menu_id': self.id,
                'name': 'Login',  # 使用英文避免编码问题
                'menu_type': 'view',
                'url': login_url,
                'sequence': 999,  # 放在最后
                'active': True,
            })
            message = _('自动登录菜单已添加')

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('成功'),
                'message': message,
                'type': 'success',
            }
        }

    def create_quick_login_menu(self):
        """创建快速登录菜单模板"""
        self.ensure_one()

        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        login_url = f"{base_url}/wechat/auto/login/{self.config_id.id}"
        qr_url = f"{base_url}/wechat/auto/qr/{self.config_id.id}"

        # 清除现有菜单项
        self.menu_items.unlink()

        # 创建登录相关菜单（使用英文避免编码问题）
        menu_items = [
            {
                'name': 'Login',
                'menu_type': 'view',
                'url': login_url,
                'sequence': 10,
            },
            {
                'name': 'QR Login',
                'menu_type': 'view',
                'url': qr_url,
                'sequence': 20,
            },
            {
                'name': 'Help',
                'menu_type': 'click',
                'key': 'HELP_CENTER',
                'sequence': 30,
            }
        ]

        for item_data in menu_items:
            item_data.update({
                'menu_id': self.id,
                'active': True,
            })
            self.env['wechat.menu.item'].create(item_data)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('成功'),
                'message': _('快速登录菜单模板已创建'),
                'type': 'success',
            }
        }


class WeChatMenuItem(models.Model):
    _name = 'wechat.menu.item'
    _description = '微信菜单项'
    _rec_name = 'name'
    _order = 'menu_id, level, sequence, id'

    menu_id = fields.Many2one('wechat.menu', string='菜单', required=True, ondelete='cascade')
    parent_id = fields.Many2one('wechat.menu.item', string='父菜单', ondelete='cascade')
    
    # 基本信息
    name = fields.Char(string='菜单名称', required=True, help='菜单显示名称')
    sequence = fields.Integer(string='排序', default=10)
    level = fields.Integer(string='层级', compute='_compute_level', store=True)
    
    # 菜单类型和动作
    menu_type = fields.Selection([
        ('click', '点击事件'),
        ('view', '跳转链接'),
        ('scancode_push', '扫码推事件'),
        ('scancode_waitmsg', '扫码推事件(等待消息)'),
        ('pic_sysphoto', '系统拍照发图'),
        ('pic_photo_or_album', '拍照或相册发图'),
        ('pic_weixin', '微信相册发图'),
        ('location_select', '地理位置选择'),
        ('media_id', '下发消息'),
        ('view_limited', '跳转图文消息'),
        ('article_id', '发布后的图文消息'),
        ('article_view_limited', '发布后的图文消息(受限)'),
        ('miniprogram', '小程序'),
        ('submenu', '子菜单')
    ], string='菜单类型', required=True, default='click')
    
    # 动作参数
    key = fields.Char(string='菜单KEY', help='点击事件的KEY值')
    url = fields.Char(string='链接地址', help='跳转的URL地址')
    media_id = fields.Char(string='素材ID', help='永久素材的media_id')
    article_id = fields.Char(string='图文消息ID', help='发布后的图文消息ID')
    appid = fields.Char(string='小程序AppID', help='小程序的appid')
    pagepath = fields.Char(string='小程序页面路径', help='小程序的页面路径')
    
    # 状态
    active = fields.Boolean(string='启用', default=True)
    
    @api.depends('parent_id')
    def _compute_level(self):
        """计算菜单层级"""
        for record in self:
            if record.parent_id:
                record.level = 2
            else:
                record.level = 1
    
    @api.constrains('name')
    def _check_name_length(self):
        """检查菜单名称长度"""
        for record in self:
            if not record.name:
                continue

            # 计算显示长度（中文字符算2个长度，英文字符算1个长度）
            display_length = 0
            for char in record.name:
                if ord(char) > 127:  # 非ASCII字符（包括中文）
                    display_length += 2
                else:  # ASCII字符（包括英文）
                    display_length += 1

            if record.level == 1 and display_length > 8:  # 一级菜单最多4个汉字或8个英文字符
                raise ValidationError(_('一级菜单名称过长，建议不超过4个汉字或8个英文字符'))
            elif record.level == 2 and display_length > 16:  # 二级菜单最多8个汉字或16个英文字符
                raise ValidationError(_('二级菜单名称过长，建议不超过8个汉字或16个英文字符'))
    
    @api.constrains('menu_type', 'key', 'url', 'media_id', 'article_id', 'appid', 'pagepath')
    def _check_required_fields(self):
        """检查必填字段"""
        for record in self:
            if record.menu_type == 'click' and not record.key:
                raise ValidationError(_('点击事件类型必须填写KEY值'))
            elif record.menu_type in ['view', 'miniprogram'] and not record.url:
                raise ValidationError(_('跳转链接类型必须填写URL地址'))
            elif record.menu_type in ['media_id', 'view_limited'] and not record.media_id:
                raise ValidationError(_('素材类型必须填写素材ID'))
            elif record.menu_type in ['article_id', 'article_view_limited'] and not record.article_id:
                raise ValidationError(_('图文消息类型必须填写图文消息ID'))
            elif record.menu_type == 'miniprogram' and (not record.appid or not record.pagepath):
                raise ValidationError(_('小程序类型必须填写AppID和页面路径'))
    
    def _get_button_data(self):
        """获取按钮数据"""
        self.ensure_one()

        # 清理菜单名称
        clean_name = self.menu_id._clean_text_for_wechat(self.name)

        button_data = {
            'name': clean_name
        }

        if self.menu_type != 'submenu':
            button_data['type'] = self.menu_type

            if self.menu_type == 'click':
                button_data['key'] = self.key or f'MENU_{self.id}'
            elif self.menu_type in ['view', 'miniprogram']:
                button_data['url'] = self.url
            elif self.menu_type in ['media_id', 'view_limited']:
                button_data['media_id'] = self.media_id
            elif self.menu_type in ['article_id', 'article_view_limited']:
                button_data['article_id'] = self.article_id
            elif self.menu_type == 'miniprogram':
                button_data['appid'] = self.appid
                button_data['pagepath'] = self.pagepath
            elif self.menu_type in ['scancode_push', 'scancode_waitmsg', 'pic_sysphoto',
                                   'pic_photo_or_album', 'pic_weixin', 'location_select']:
                button_data['key'] = self.key or f'MENU_{self.id}'

        return button_data


class WeChatMenuQueryWizard(models.TransientModel):
    _name = 'wechat.menu.query.wizard'
    _description = '微信菜单查询向导'

    menu_data = fields.Text(string='菜单数据', readonly=True)

    def action_close(self):
        """关闭向导"""
        return {'type': 'ir.actions.act_window_close'}


class WeChatMenuEvent(models.Model):
    _name = 'wechat.menu.event'
    _description = '微信菜单事件记录'
    _rec_name = 'event_key'
    _order = 'event_time desc'

    config_id = fields.Many2one('wechat.config', string='微信配置', required=True, ondelete='cascade')
    openid = fields.Char(string='用户OpenID', required=True)
    event_type = fields.Selection([
        ('click', '点击事件'),
        ('view', '跳转链接'),
        ('scancode_push', '扫码推事件'),
        ('scancode_waitmsg', '扫码推事件(等待消息)'),
        ('pic_sysphoto', '系统拍照发图'),
        ('pic_photo_or_album', '拍照或相册发图'),
        ('pic_weixin', '微信相册发图'),
        ('location_select', '地理位置选择'),
    ], string='事件类型', required=True)
    event_key = fields.Char(string='事件KEY', required=True)
    description = fields.Text(string='事件描述')
    event_time = fields.Datetime(string='事件时间', default=fields.Datetime.now)

    # 关联用户
    user_id = fields.Many2one('wechat.user', string='微信用户',
                             compute='_compute_user_id', store=True)

    @api.depends('openid', 'config_id')
    def _compute_user_id(self):
        """计算关联的微信用户"""
        for record in self:
            if record.openid and record.config_id:
                user = self.env['wechat.user'].search([
                    ('openid', '=', record.openid),
                    ('config_id', '=', record.config_id.id)
                ], limit=1)
                record.user_id = user.id if user else False
            else:
                record.user_id = False
