# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging
import json
import requests

_logger = logging.getLogger(__name__)


class WeChatTemplate(models.Model):
    _name = 'wechat.template'
    _description = 'WeChat Template Message'
    _order = 'sequence, name'
    _rec_name = 'name'

    # 基本信息
    name = fields.Char(string='模板名称', required=True, help='模板的显示名称')
    template_id = fields.Char(string='模板ID', required=True, help='微信公众号平台的模板ID')
    title = fields.Char(string='模板标题', help='模板的标题')
    primary_industry = fields.Char(string='主行业', help='模板所属主行业')
    deputy_industry = fields.Char(string='副行业', help='模板所属副行业')
    content = fields.Text(string='模板内容', help='模板的内容格式')
    example = fields.Text(string='示例', help='模板使用示例')
    
    # 配置信息
    config_id = fields.Many2one('wechat.config', string='微信配置',
                               default=lambda self: self._get_default_config())
    sequence = fields.Integer(string='排序', default=10)
    active = fields.Boolean(string='启用', default=True)
    
    # 模板参数
    parameter_ids = fields.One2many('wechat.template.parameter', 'template_id', string='模板参数')
    
    # 统计信息
    usage_count = fields.Integer(string='使用次数', default=0, readonly=True)
    last_used = fields.Datetime(string='最后使用时间', readonly=True)
    
    # 跳转链接
    url = fields.Char(string='跳转链接', help='点击模板消息后跳转的链接')
    miniprogram_appid = fields.Char(string='小程序AppID', help='跳转小程序的AppID')
    miniprogram_pagepath = fields.Char(string='小程序页面路径', help='跳转小程序的页面路径')
    
    def _get_default_config(self):
        """获取默认的微信配置"""
        config = self.env['wechat.config'].search([], limit=1)
        return config.id if config else False

    @api.model_create_multi
    def create(self, vals_list):
        """创建模板时自动解析参数"""
        templates = super().create(vals_list)
        for template in templates:
            if template.content:
                template._parse_template_parameters()
        return templates
    
    def write(self, vals):
        """更新模板时重新解析参数"""
        result = super().write(vals)
        if 'content' in vals:
            for template in self:
                template._parse_template_parameters()
        return result
    
    def _parse_template_parameters(self):
        """解析模板内容中的参数"""
        if not self.content:
            return
        
        import re
        # 查找所有 {{参数名.DATA}} 格式的参数
        parameters = re.findall(r'\{\{(\w+)\.DATA\}\}', self.content)
        
        # 删除现有参数
        self.parameter_ids.unlink()
        
        # 创建新参数
        for i, param in enumerate(set(parameters), 1):
            self.env['wechat.template.parameter'].create({
                'template_id': self.id,
                'name': param,
                'sequence': i,
                'description': f'参数{i}：{param}',
            })
    
    def action_send_template_message(self):
        """发送模板消息的向导"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('发送模板消息'),
            'res_model': 'wechat.template.send.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
            }
        }
    



class WeChatTemplateParameter(models.Model):
    _name = 'wechat.template.parameter'
    _description = 'WeChat Template Parameter'
    _order = 'sequence, name'
    
    template_id = fields.Many2one('wechat.template', string='模板', required=True, ondelete='cascade')
    name = fields.Char(string='参数名', required=True)
    description = fields.Char(string='参数描述')
    sequence = fields.Integer(string='排序', default=10)
    data_type = fields.Selection([
        ('text', '文本'),
        ('number', '数字'),
        ('date', '日期'),
        ('datetime', '日期时间'),
        ('url', '链接'),
    ], string='数据类型', default='text')
    is_required = fields.Boolean(string='必填', default=True)
    default_value = fields.Char(string='默认值')
    example_value = fields.Char(string='示例值')


class WeChatTemplateSendWizard(models.TransientModel):
    _name = 'wechat.template.send.wizard'
    _description = 'WeChat Template Send Wizard'
    
    template_id = fields.Many2one('wechat.template', string='模板', required=True)
    recipient_type = fields.Selection([
        ('single', '单个用户'),
        ('multiple', '多个用户'),
        ('all', '所有用户'),
    ], string='接收者类型', default='single', required=True)
    
    # 接收者
    user_id = fields.Many2one('wechat.user', string='接收用户')
    user_ids = fields.Many2many('wechat.user', string='接收用户列表')
    openid = fields.Char(string='OpenID')
    
    # 模板参数值
    parameter_value_ids = fields.One2many('wechat.template.parameter.value', 'wizard_id', string='参数值')
    
    # 跳转设置
    url = fields.Char(string='跳转链接')
    miniprogram_appid = fields.Char(string='小程序AppID')
    miniprogram_pagepath = fields.Char(string='小程序页面路径')
    
    @api.onchange('template_id')
    def _onchange_template_id(self):
        """模板变更时更新参数"""
        if self.template_id:
            # 清除现有参数值
            self.parameter_value_ids = [(5, 0, 0)]
            
            # 创建新的参数值记录
            parameter_values = []
            for param in self.template_id.parameter_ids:
                parameter_values.append((0, 0, {
                    'parameter_id': param.id,
                    'value': param.default_value or '',
                }))
            self.parameter_value_ids = parameter_values
            
            # 设置默认跳转链接
            self.url = self.template_id.url
            self.miniprogram_appid = self.template_id.miniprogram_appid
            self.miniprogram_pagepath = self.template_id.miniprogram_pagepath
    
    def action_send_template(self):
        """发送模板消息"""
        self.ensure_one()
        
        if not self.template_id:
            raise UserError(_('请选择模板'))
        
        # 准备参数数据
        template_data = {}
        for param_value in self.parameter_value_ids:
            if param_value.parameter_id and param_value.value:
                template_data[param_value.parameter_id.name] = {
                    'value': param_value.value,
                    'color': param_value.color or '#173177'
                }
        
        # 准备接收者列表
        recipients = []
        if self.recipient_type == 'single':
            if self.user_id:
                recipients = [self.user_id.openid]
            elif self.openid:
                recipients = [self.openid]
        elif self.recipient_type == 'multiple':
            recipients = self.user_ids.mapped('openid')
        elif self.recipient_type == 'all':
            all_users = self.env['wechat.user'].search([
                ('config_id', '=', self.template_id.config_id.id),
                ('subscribe', '=', True)
            ])
            recipients = all_users.mapped('openid')
        
        if not recipients:
            raise UserError(_('没有找到接收者'))
        
        # 发送模板消息
        success_count = 0
        failed_count = 0
        
        for openid in recipients:
            try:
                result = self._send_template_to_openid(openid, template_data)
                if result:
                    success_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                _logger.error(f"发送模板消息到 {openid} 失败: {e}")
                failed_count += 1
        
        # 更新模板使用统计
        self.template_id.write({
            'usage_count': self.template_id.usage_count + success_count,
            'last_used': fields.Datetime.now(),
        })
        
        # 返回结果
        message = _('发送完成：成功 %d 条，失败 %d 条') % (success_count, failed_count)
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('发送结果'),
                'message': message,
                'type': 'success' if failed_count == 0 else 'warning',
            }
        }
    
    def _send_template_to_openid(self, openid, template_data):
        """发送模板消息到指定OpenID"""
        access_token = self.template_id.config_id.get_access_token()
        
        url = 'https://api.weixin.qq.com/cgi-bin/message/template/send'
        
        # 准备消息数据
        message_data = {
            'touser': openid,
            'template_id': self.template_id.template_id,
            'data': template_data
        }
        
        # 添加跳转链接
        if self.url:
            message_data['url'] = self.url
        
        # 添加小程序跳转
        if self.miniprogram_appid and self.miniprogram_pagepath:
            message_data['miniprogram'] = {
                'appid': self.miniprogram_appid,
                'pagepath': self.miniprogram_pagepath
            }
        
        params = {'access_token': access_token}
        
        try:
            import json
            import requests
            
            json_data = json.dumps(message_data, ensure_ascii=False)
            _logger.info(f"发送模板消息到 {openid}: {json_data}")
            
            response = requests.post(
                url,
                params=params,
                data=json_data.encode('utf-8'),
                headers={'Content-Type': 'application/json; charset=utf-8'},
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get('errcode', 0) == 0:
                _logger.info(f"模板消息发送成功到 {openid}: {result}")
                return True
            else:
                error_msg = result.get('errmsg', 'Unknown error')
                _logger.error(f"模板消息发送失败到 {openid}: {error_msg}")
                return False
                
        except Exception as e:
            _logger.error(f"发送模板消息到 {openid} 异常: {e}")
            return False


class WeChatTemplateParameterValue(models.TransientModel):
    _name = 'wechat.template.parameter.value'
    _description = 'WeChat Template Parameter Value'
    
    wizard_id = fields.Many2one('wechat.template.send.wizard', string='向导', ondelete='cascade')
    parameter_id = fields.Many2one('wechat.template.parameter', string='参数', required=True)
    value = fields.Char(string='参数值', required=True)
    color = fields.Char(string='颜色', default='#173177', help='文字颜色，格式如 #173177')
    
    parameter_name = fields.Char(string='参数名', related='parameter_id.name', readonly=True)
    parameter_description = fields.Char(string='参数描述', related='parameter_id.description', readonly=True)
