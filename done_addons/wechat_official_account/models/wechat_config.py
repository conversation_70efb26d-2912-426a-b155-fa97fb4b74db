# -*- coding: utf-8 -*-

import requests
import json
import hashlib
import time
import logging
from datetime import timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WeChatConfig(models.Model):
    _name = 'wechat.config'
    _description = 'WeChat Official Account Configuration'
    _rec_name = 'name'

    name = fields.Char(string='Configuration Name', required=True, help='Name for this WeChat configuration')
    app_id = fields.Char(string='AppID', required=True, help='WeChat Official Account AppID')
    app_secret = fields.Char(string='AppSecret', required=True, help='WeChat Official Account AppSecret')
    token = fields.Char(string='Token', required=True, help='Token for webhook verification')
    encoding_aes_key = fields.Char(string='EncodingAESKey', help='AES key for message encryption (optional)')
    
    # Access Token Management
    access_token = fields.Char(string='Access Token', readonly=True, help='Current access token from WeChat')
    access_token_expires = fields.Datetime(string='Access Token Expires', readonly=True)
    
    # Status and Settings
    active = fields.Boolean(string='Active', default=True)
    is_default = fields.Boolean(string='Default Configuration', default=False)
    webhook_url = fields.Char(string='Webhook URL', readonly=True)
    
    # Statistics
    total_messages_sent = fields.Integer(string='Total Messages Sent', default=0, readonly=True)
    total_users = fields.Integer(string='Total Users', compute='_compute_total_users', store=True)
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    
    @api.model_create_multi
    def create(self, vals_list):
        """Override create to set webhook URL"""
        records = super().create(vals_list)
        for record in records:
            record._update_webhook_url()
        return records

    def _update_webhook_url(self):
        """Update the webhook URL for this configuration"""
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        for record in self:
            if record.id:
                webhook_url = f"{base_url}/wechat/webhook/{record.id}"
                # Use sudo to bypass readonly restriction
                record.sudo().write({'webhook_url': webhook_url})
    
    def _compute_total_users(self):
        """Compute total number of WeChat users for this configuration"""
        for record in self:
            record.total_users = self.env['wechat.user'].search_count([('config_id', '=', record.id)])
    
    @api.constrains('is_default')
    def _check_default_config(self):
        """Ensure only one default configuration exists"""
        if self.is_default:
            other_defaults = self.search([('is_default', '=', True), ('id', '!=', self.id)])
            if other_defaults:
                raise ValidationError(_('Only one default WeChat configuration is allowed.'))
    
    def get_access_token(self):
        """Get valid access token, refresh if necessary"""
        self.ensure_one()
        
        # Check if current token is still valid
        if self.access_token and self.access_token_expires and fields.Datetime.now() < self.access_token_expires:
            return self.access_token
        
        # Request new access token
        return self._refresh_access_token()
    
    def _refresh_access_token(self):
        """Refresh access token from WeChat API"""
        self.ensure_one()
        
        url = 'https://api.weixin.qq.com/cgi-bin/token'
        params = {
            'grant_type': 'client_credential',
            'appid': self.app_id,
            'secret': self.app_secret
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'access_token' in data:
                # Update access token and expiration time
                expires_in = data.get('expires_in', 7200)  # Default 2 hours
                expires_at = fields.Datetime.now() + timedelta(seconds=expires_in - 300)  # 5 min buffer
                
                self.write({
                    'access_token': data['access_token'],
                    'access_token_expires': expires_at
                })
                
                _logger.info(f"WeChat access token refreshed for config {self.name}")
                return data['access_token']
            else:
                error_msg = data.get('errmsg', 'Unknown error')
                raise UserError(_('Failed to get WeChat access token: %s') % error_msg)
                
        except requests.RequestException as e:
            _logger.error(f"Failed to refresh WeChat access token: {e}")
            raise UserError(_('Failed to connect to WeChat API: %s') % str(e))

    def refresh_access_token(self):
        """Public method to refresh access token"""
        return self._refresh_access_token()

    def test_connection(self):
        """Test connection to WeChat API"""
        self.ensure_one()
        
        try:
            access_token = self.get_access_token()
            if access_token:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('WeChat API connection successful!'),
                        'type': 'success',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('WeChat API connection failed: %s') % str(e),
                    'type': 'danger',
                }
            }
    
    def verify_webhook_signature(self, signature, timestamp, nonce):
        """Verify webhook signature from WeChat"""
        self.ensure_one()
        
        # Create signature string
        tmp_list = [self.token, timestamp, nonce]
        tmp_list.sort()
        tmp_str = ''.join(tmp_list)
        
        # Calculate SHA1 hash
        sha1 = hashlib.sha1()
        sha1.update(tmp_str.encode('utf-8'))
        calculated_signature = sha1.hexdigest()
        
        return calculated_signature == signature
    
    def action_sync_users(self):
        """Action to sync users from WeChat API"""
        self.ensure_one()

        try:
            # 调用用户同步方法
            self.env['wechat.user'].sync_users_from_wechat(self.id)

            # 重新计算用户统计
            self._compute_total_users()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Users synchronized successfully from WeChat API!'),
                    'type': 'success',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to sync users: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_refresh_access_token(self):
        """Action to manually refresh access token"""
        self.ensure_one()

        try:
            new_token = self.refresh_access_token()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Access token refreshed successfully!'),
                    'type': 'success',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to refresh access token: %s') % str(e),
                    'type': 'danger',
                }
            }

    @api.model
    def get_default_config(self):
        """Get the default WeChat configuration"""
        default_config = self.search([('is_default', '=', True), ('active', '=', True)], limit=1)
        if not default_config:
            default_config = self.search([('active', '=', True)], limit=1)
        return default_config
