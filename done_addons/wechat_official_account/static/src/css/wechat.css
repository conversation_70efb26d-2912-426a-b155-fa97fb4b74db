/* WeChat Official Account <PERSON><PERSON><PERSON> Styles */

/* WeChat Configuration Form */
.o_wechat_config_form .oe_title h1 {
    color: #07c160;
    font-weight: bold;
}

.o_wechat_config_form .alert-info {
    background-color: #e8f5e8;
    border-color: #07c160;
    color: #155724;
}

.o_wechat_config_form .alert-info h4 {
    color: #07c160;
}

/* WeChat Message Form */
.o_wechat_message_form .oe_title h1 {
    color: #07c160;
}

.o_wechat_message_form .o_stat_button {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.o_wechat_message_form .o_stat_button:hover {
    background-color: #e9ecef;
}

/* WeChat User Kanban */
.o_wechat_user_kanban .oe_kanban_card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.o_wechat_user_kanban .oe_kanban_card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.o_wechat_user_kanban .o_kanban_image img {
    border-radius: 50%;
    border: 2px solid #07c160;
}

.o_wechat_user_kanban .badge-success {
    background-color: #07c160;
}

/* WeChat Icons */
.fa-wechat:before {
    content: "\f1d7";
    color: #07c160;
}

/* Status Badges */
.badge-wechat-sent {
    background-color: #28a745;
    color: white;
}

.badge-wechat-failed {
    background-color: #dc3545;
    color: white;
}

.badge-wechat-draft {
    background-color: #6c757d;
    color: white;
}

.badge-wechat-sending {
    background-color: #007bff;
    color: white;
}

.badge-wechat-partial {
    background-color: #ffc107;
    color: #212529;
}

/* WeChat Menu Styling */
.o_main_navbar .o_menu_brand img[src*="wechat"] {
    filter: brightness(0) invert(1);
}

/* Message Content Preview */
.o_wechat_message_preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.o_wechat_message_preview .preview-header {
    font-weight: bold;
    color: #07c160;
    margin-bottom: 10px;
}

.o_wechat_message_preview .preview-content {
    white-space: pre-wrap;
    line-height: 1.5;
}

/* WeChat User Avatar */
.o_wechat_user_avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #07c160;
    object-fit: cover;
}

/* Webhook URL Display */
.o_wechat_webhook_url {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
}

/* Statistics Cards */
.o_wechat_stats_card {
    background: linear-gradient(135deg, #07c160 0%, #38d9a9 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
}

.o_wechat_stats_card .stats-number {
    font-size: 2em;
    font-weight: bold;
}

.o_wechat_stats_card .stats-label {
    font-size: 0.9em;
    opacity: 0.9;
}

/* Message Type Icons */
.o_message_type_text:before {
    content: "\f075";
    font-family: FontAwesome;
    color: #6c757d;
}

.o_message_type_image:before {
    content: "\f03e";
    font-family: FontAwesome;
    color: #17a2b8;
}

.o_message_type_voice:before {
    content: "\f130";
    font-family: FontAwesome;
    color: #28a745;
}

.o_message_type_video:before {
    content: "\f03d";
    font-family: FontAwesome;
    color: #dc3545;
}

.o_message_type_news:before {
    content: "\f1ea";
    font-family: FontAwesome;
    color: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .o_wechat_user_kanban .oe_kanban_card {
        margin: 5px;
    }
    
    .o_wechat_webhook_url {
        font-size: 10px;
    }
    
    .o_wechat_stats_card {
        margin: 5px 0;
        padding: 15px;
    }
}

/* Animation for sending status */
@keyframes wechat-sending {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

.o_wechat_sending {
    animation: wechat-sending 1.5s infinite;
}

/* Success/Error Messages */
.o_wechat_success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

.o_wechat_error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}
