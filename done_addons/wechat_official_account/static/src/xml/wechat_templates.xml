<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- WeChat Message Preview Template -->
    <t t-name="wechat_official_account.MessagePreview" owl="1">
        <div class="wechat-message-preview">
            <div class="wechat-bubble">
                <div class="wechat-content">
                    <t t-esc="previewContent"/>
                </div>
            </div>
        </div>
    </t>

    <!-- WeChat User Status Template -->
    <t t-name="wechat_official_account.UserStatus" owl="1">
        <span t-att-class="'badge ' + statusClass">
            <i t-att-class="'fa ' + statusIcon"/>
            <t t-esc="statusText"/>
        </span>
    </t>

    <!-- WeChat Message Type Template -->
    <t t-name="wechat_official_account.MessageType" owl="1">
        <span t-att-class="typeClass">
            <i t-att-class="'fa ' + typeIcon"/>
            <t t-esc="props.value"/>
        </span>
    </t>

    <!-- WeChat Webhook URL Template -->
    <t t-name="wechat_official_account.WebhookUrl" owl="1">
        <div class="wechat-webhook-url">
            <div class="input-group">
                <input type="text" class="form-control" t-att-value="props.value" readonly="readonly"/>
                <div class="input-group-append">
                    <button class="btn btn-outline-secondary" type="button" t-on-click="copyToClipboard">
                        <i class="fa fa-copy"/> Copy
                    </button>
                </div>
            </div>
        </div>
    </t>

    <!-- WeChat Statistics Dashboard Template -->
    <t t-name="wechat_official_account.StatsDashboard" owl="1">
        <div class="wechat-stats-dashboard">
            <t t-if="state.loading">
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin"/> Loading statistics...
                </div>
            </t>
            <t t-else="">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Total Users</h5>
                                <h2 class="text-primary" t-esc="state.totalUsers"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Subscribed</h5>
                                <h2 class="text-success" t-esc="state.subscribedUsers"/>
                                <small class="text-muted" t-esc="subscriptionRate + '%'"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Total Messages</h5>
                                <h2 class="text-info" t-esc="state.totalMessages"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">Sent Messages</h5>
                                <h2 class="text-warning" t-esc="state.sentMessages"/>
                                <small class="text-muted" t-esc="messageSuccessRate + '%'"/>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </div>
    </t>

</templates>
