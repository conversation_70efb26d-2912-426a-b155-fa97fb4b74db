/** @odoo-module **/

import { Component, useState } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { standardFieldProps } from "@web/views/fields/standard_field_props";

/**
 * WeChat Message Preview Widget
 * Shows a preview of how the message will look in WeChat
 */
export class WeChatMessagePreview extends Component {
    static template = "wechat_official_account.MessagePreview";
    static props = { ...standardFieldProps };
    
    setup() {
        this.state = useState({
            messageType: this.props.record.data.message_type,
            content: this.props.record.data.content,
            title: this.props.record.data.title,
            description: this.props.record.data.description,
        });
    }
    
    get previewContent() {
        const { messageType, content, title, description } = this.state;
        
        switch (messageType) {
            case 'text':
                return content || 'No content';
            case 'news':
                return `${title || 'No title'}\n${description || 'No description'}`;
            case 'image':
                return 'Image message';
            case 'voice':
                return 'Voice message';
            case 'video':
                return 'Video message';
            default:
                return 'Unknown message type';
        }
    }
}

/**
 * WeChat User Status Widget
 * Shows user subscription status with visual indicators
 */
export class WeChatUserStatus extends Component {
    static template = "wechat_official_account.UserStatus";
    static props = { ...standardFieldProps };
    
    get statusClass() {
        return this.props.value ? 'badge-success' : 'badge-secondary';
    }
    
    get statusText() {
        return this.props.value ? 'Subscribed' : 'Unsubscribed';
    }
    
    get statusIcon() {
        return this.props.value ? 'fa-check' : 'fa-times';
    }
}

/**
 * WeChat Message Type Widget
 * Shows message type with appropriate icons
 */
export class WeChatMessageType extends Component {
    static template = "wechat_official_account.MessageType";
    static props = { ...standardFieldProps };
    
    get typeIcon() {
        const icons = {
            'text': 'fa-comment',
            'image': 'fa-image',
            'voice': 'fa-microphone',
            'video': 'fa-video',
            'news': 'fa-newspaper-o',
            'template': 'fa-file-text'
        };
        return icons[this.props.value] || 'fa-question';
    }
    
    get typeClass() {
        const classes = {
            'text': 'text-muted',
            'image': 'text-info',
            'voice': 'text-success',
            'video': 'text-danger',
            'news': 'text-warning',
            'template': 'text-primary'
        };
        return classes[this.props.value] || 'text-muted';
    }
}

/**
 * WeChat Statistics Dashboard Widget
 */
export class WeChatStatsDashboard extends Component {
    static template = "wechat_official_account.StatsDashboard";
    static props = { ...standardFieldProps };
    
    setup() {
        this.orm = useService("orm");
        this.state = useState({
            totalUsers: 0,
            subscribedUsers: 0,
            totalMessages: 0,
            sentMessages: 0,
            loading: true
        });
        
        this.loadStats();
    }
    
    async loadStats() {
        try {
            const [userStats, messageStats] = await Promise.all([
                this.orm.readGroup("wechat.user", [], ["subscribe"], ["subscribe"]),
                this.orm.readGroup("wechat.message", [], ["state"], ["state"])
            ]);
            
            // Process user statistics
            let totalUsers = 0;
            let subscribedUsers = 0;
            userStats.forEach(stat => {
                totalUsers += stat.__count;
                if (stat.subscribe) {
                    subscribedUsers += stat.__count;
                }
            });
            
            // Process message statistics
            let totalMessages = 0;
            let sentMessages = 0;
            messageStats.forEach(stat => {
                totalMessages += stat.__count;
                if (stat.state === 'sent') {
                    sentMessages += stat.__count;
                }
            });
            
            this.state.totalUsers = totalUsers;
            this.state.subscribedUsers = subscribedUsers;
            this.state.totalMessages = totalMessages;
            this.state.sentMessages = sentMessages;
            this.state.loading = false;
            
        } catch (error) {
            console.error('Error loading WeChat statistics:', error);
            this.state.loading = false;
        }
    }
    
    get subscriptionRate() {
        if (this.state.totalUsers === 0) return 0;
        return Math.round((this.state.subscribedUsers / this.state.totalUsers) * 100);
    }
    
    get messageSuccessRate() {
        if (this.state.totalMessages === 0) return 0;
        return Math.round((this.state.sentMessages / this.state.totalMessages) * 100);
    }
}

/**
 * WeChat Webhook URL Copy Widget
 */
export class WeChatWebhookUrl extends Component {
    static template = "wechat_official_account.WebhookUrl";
    static props = { ...standardFieldProps };
    
    setup() {
        this.notification = useService("notification");
    }
    
    copyToClipboard() {
        const url = this.props.value;
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                this.notification.add("Webhook URL copied to clipboard!", {
                    type: "success"
                });
            }).catch(() => {
                this.fallbackCopy(url);
            });
        } else {
            this.fallbackCopy(url);
        }
    }
    
    fallbackCopy(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            this.notification.add("Webhook URL copied to clipboard!", {
                type: "success"
            });
        } catch (err) {
            this.notification.add("Failed to copy URL. Please copy manually.", {
                type: "warning"
            });
        }
        document.body.removeChild(textArea);
    }
}

// Register widgets
registry.category("fields").add("wechat_message_preview", {
    component: WeChatMessagePreview,
});
registry.category("fields").add("wechat_user_status", {
    component: WeChatUserStatus,
});
registry.category("fields").add("wechat_message_type", {
    component: WeChatMessageType,
});
registry.category("fields").add("wechat_webhook_url", {
    component: WeChatWebhookUrl,
});
registry.category("fields").add("wechat_stats_dashboard", {
    component: WeChatStatsDashboard,
});
