<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>WeChat Official Account - <PERSON><PERSON><PERSON> Module</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #07c160 0%, #38d9a9 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #07c160 0%, #38d9a9 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #07c160;
        }
        .feature h3 {
            color: #07c160;
            margin-top: 0;
            font-size: 1.3em;
        }
        .feature ul {
            margin: 15px 0;
            padding-left: 20px;
        }
        .feature li {
            margin: 8px 0;
        }
        .screenshots {
            margin: 40px 0;
        }
        .screenshots h2 {
            text-align: center;
            color: #07c160;
            margin-bottom: 30px;
        }
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .screenshot {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .installation {
            background: #e8f5e8;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        .installation h2 {
            color: #07c160;
            margin-top: 0;
        }
        .installation ol {
            margin: 20px 0;
            padding-left: 25px;
        }
        .installation li {
            margin: 10px 0;
            font-weight: 500;
        }
        .tech-specs {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        .tech-specs h2 {
            color: #07c160;
            margin-top: 0;
        }
        .spec-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .spec-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .spec-item strong {
            color: #07c160;
        }
        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .footer a {
            color: #07c160;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WeChat Official Account</h1>
            <p>Complete WeChat Integration for Odoo 18</p>
        </div>
        
        <div class="content">
            <div class="features">
                <div class="feature">
                    <h3>📱 Message Management</h3>
                    <ul>
                        <li>Send text, image, voice, and video messages</li>
                        <li>Create and manage news articles</li>
                        <li>Broadcast to all subscribers or specific users</li>
                        <li>Message templates and scheduling</li>
                        <li>Delivery status tracking</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>👥 User Management</h3>
                    <ul>
                        <li>Automatic user synchronization</li>
                        <li>Subscription status tracking</li>
                        <li>User profile information</li>
                        <li>Integration with Odoo partners</li>
                        <li>Message interaction history</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>⚙️ Configuration</h3>
                    <ul>
                        <li>Easy WeChat API setup</li>
                        <li>Webhook URL generation</li>
                        <li>Access token management</li>
                        <li>Multi-company support</li>
                        <li>Security and encryption</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>🔄 Automation</h3>
                    <ul>
                        <li>Automatic welcome messages</li>
                        <li>Event-driven responses</li>
                        <li>Menu interaction handling</li>
                        <li>User subscription management</li>
                        <li>Message routing and processing</li>
                    </ul>
                </div>
            </div>
            
            <div class="installation">
                <h2>📦 Installation Guide</h2>
                <ol>
                    <li>Install the module in your Odoo instance</li>
                    <li>Go to Settings → WeChat Official Account</li>
                    <li>Create a new WeChat configuration with your AppID and AppSecret</li>
                    <li>Configure the webhook URL in your WeChat Official Account settings</li>
                    <li>Test the connection and start sending messages!</li>
                </ol>
            </div>
            
            <div class="tech-specs">
                <h2>🛠️ Technical Specifications</h2>
                <div class="spec-grid">
                    <div class="spec-item">
                        <strong>Odoo Version:</strong><br>
                        18.0 Community & Enterprise
                    </div>
                    <div class="spec-item">
                        <strong>Dependencies:</strong><br>
                        base, mail, web
                    </div>
                    <div class="spec-item">
                        <strong>Python Libraries:</strong><br>
                        requests, hashlib, xml
                    </div>
                    <div class="spec-item">
                        <strong>WeChat API:</strong><br>
                        Official Account API 2.0
                    </div>
                    <div class="spec-item">
                        <strong>Security:</strong><br>
                        Token verification, HTTPS
                    </div>
                    <div class="spec-item">
                        <strong>Multi-language:</strong><br>
                        English, Chinese
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>
                <strong>WeChat Official Account Module for Odoo 18</strong><br>
                Developed with ❤️ for seamless WeChat integration<br>
                <a href="mailto:<EMAIL>">Contact Support</a> | 
                <a href="https://www.yourcompany.com">Visit Website</a>
            </p>
        </div>
    </div>
</body>
</html>
