<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="wechatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#07c160;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38d9a9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#wechatGradient)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- WeChat Icon -->
  <g transform="translate(64,64)">
    <!-- Main Chat Bubble -->
    <ellipse cx="-8" cy="-8" rx="28" ry="20" fill="#ffffff" opacity="0.95"/>
    
    <!-- Secondary Chat Bubble -->
    <ellipse cx="12" cy="8" rx="20" ry="15" fill="#ffffff" opacity="0.9"/>
    
    <!-- Eyes on main bubble -->
    <circle cx="-18" cy="-12" r="3" fill="#07c160"/>
    <circle cx="2" cy="-12" r="3" fill="#07c160"/>
    
    <!-- Eyes on secondary bubble -->
    <circle cx="6" cy="4" r="2" fill="#07c160"/>
    <circle cx="18" cy="4" r="2" fill="#07c160"/>
    
    <!-- Message dots -->
    <circle cx="-8" cy="-4" r="1.5" fill="#07c160"/>
    <circle cx="-2" cy="-4" r="1.5" fill="#07c160"/>
    <circle cx="4" cy="-4" r="1.5" fill="#07c160"/>
  </g>
  
  <!-- Odoo Integration Symbol -->
  <g transform="translate(100,28)">
    <circle cx="0" cy="0" r="12" fill="#714b67" stroke="#ffffff" stroke-width="2"/>
    <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="10" font-weight="bold">O</text>
  </g>
</svg>
