# -*- coding: utf-8 -*-

import xml.etree.ElementTree as ET
import logging
from odoo import http, _
from odoo.http import request

_logger = logging.getLogger(__name__)


class WeChatWebhookController(http.Controller):
    
    @http.route('/wechat/webhook/<int:config_id>', type='http', auth='public', methods=['GET', 'POST'], csrf=False)
    def wechat_webhook(self, config_id, **kwargs):
        """Handle WeChat webhook requests"""
        
        # Get WeChat configuration
        config = request.env['wechat.config'].sudo().browse(config_id)
        if not config.exists():
            _logger.error(f"WeChat config {config_id} not found")
            return "error", 404
        
        if request.httprequest.method == 'GET':
            # Handle verification request
            return self._handle_verification(config, kwargs)
        elif request.httprequest.method == 'POST':
            # Handle message/event request
            return self._handle_message(config)
        
        return "error", 405
    
    def _handle_verification(self, config, params):
        """Handle WeChat server verification"""
        signature = params.get('signature', '')
        timestamp = params.get('timestamp', '')
        nonce = params.get('nonce', '')
        echostr = params.get('echostr', '')
        
        _logger.info(f"WeChat verification request: signature={signature}, timestamp={timestamp}, nonce={nonce}")
        
        # Verify signature
        if config.verify_webhook_signature(signature, timestamp, nonce):
            _logger.info("WeChat webhook verification successful")
            return echostr
        else:
            _logger.error("WeChat webhook verification failed")
            return "error"
    
    def _handle_message(self, config):
        """Handle incoming messages and events from WeChat"""
        try:
            # Get XML data from request
            xml_data = request.httprequest.get_data(as_text=True)
            _logger.info(f"Received WeChat message: {xml_data}")
            
            if not xml_data:
                return "error"
            
            # Parse XML
            root = ET.fromstring(xml_data)
            message_data = self._parse_xml_message(root)
            
            # Process the message
            response = self._process_message(config, message_data)
            
            return response or "success"
            
        except Exception as e:
            _logger.error(f"Error handling WeChat message: {e}")
            return "error"
    
    def _parse_xml_message(self, root):
        """Parse XML message from WeChat"""
        message_data = {}
        
        for child in root:
            if child.text:
                message_data[child.tag] = child.text
        
        return message_data
    
    def _process_message(self, config, message_data):
        """Process incoming message or event"""
        msg_type = message_data.get('MsgType', '')
        from_user = message_data.get('FromUserName', '')
        to_user = message_data.get('ToUserName', '')
        
        _logger.info(f"Processing message type: {msg_type} from {from_user}")
        
        # Update or create user
        if from_user:
            self._update_user_info(config, from_user, message_data)
        
        # Handle different message types
        if msg_type == 'text':
            return self._handle_text_message(config, message_data)
        elif msg_type == 'image':
            return self._handle_image_message(config, message_data)
        elif msg_type == 'voice':
            return self._handle_voice_message(config, message_data)
        elif msg_type == 'video':
            return self._handle_video_message(config, message_data)
        elif msg_type == 'event':
            return self._handle_event(config, message_data)
        else:
            _logger.info(f"Unsupported message type: {msg_type}")
            return None
    
    def _update_user_info(self, config, openid, message_data):
        """Update user information and message statistics"""
        try:
            # Find or create user
            user = request.env['wechat.user'].sudo().search([
                ('openid', '=', openid),
                ('config_id', '=', config.id)
            ], limit=1)
            
            if not user:
                # Create new user
                user = request.env['wechat.user'].sudo().create_or_update_user(
                    openid, config.id
                )
            
            # Update message statistics
            from odoo import fields
            user.sudo().write({
                'message_count': user.message_count + 1,
                'last_message_time': fields.Datetime.now(),
            })
            
        except Exception as e:
            _logger.error(f"Error updating user info: {e}")
    
    def _handle_text_message(self, config, message_data):
        """Handle text message"""
        content = message_data.get('Content', '')
        from_user = message_data.get('FromUserName', '')
        
        _logger.info(f"Text message from {from_user}: {content}")
        
        # Simple auto-reply logic
        if content.lower() in ['hello', 'hi', '你好', 'help']:
            return self._create_text_response(
                config, message_data,
                "Hello! Welcome to our WeChat Official Account. How can I help you today?"
            )
        elif content.lower() in ['menu', '菜单']:
            return self._create_text_response(
                config, message_data,
                "Main Menu:\n1. About Us\n2. Products\n3. Contact\n4. Help"
            )
        
        # Default response
        return self._create_text_response(
            config, message_data,
            "Thank you for your message. We will get back to you soon!"
        )
    
    def _handle_image_message(self, config, message_data):
        """Handle image message"""
        media_id = message_data.get('MediaId', '')
        from_user = message_data.get('FromUserName', '')
        
        _logger.info(f"Image message from {from_user}, media_id: {media_id}")
        
        return self._create_text_response(
            config, message_data,
            "Thank you for sharing the image with us!"
        )
    
    def _handle_voice_message(self, config, message_data):
        """Handle voice message"""
        media_id = message_data.get('MediaId', '')
        recognition = message_data.get('Recognition', '')
        from_user = message_data.get('FromUserName', '')
        
        _logger.info(f"Voice message from {from_user}, recognition: {recognition}")
        
        if recognition:
            response_text = f"I heard you say: {recognition}"
        else:
            response_text = "Thank you for your voice message!"
        
        return self._create_text_response(config, message_data, response_text)
    
    def _handle_video_message(self, config, message_data):
        """Handle video message"""
        media_id = message_data.get('MediaId', '')
        from_user = message_data.get('FromUserName', '')
        
        _logger.info(f"Video message from {from_user}, media_id: {media_id}")
        
        return self._create_text_response(
            config, message_data,
            "Thank you for sharing the video with us!"
        )
    
    def _handle_event(self, config, message_data):
        """Handle event messages"""
        event = message_data.get('Event', '')
        from_user = message_data.get('FromUserName', '')
        
        _logger.info(f"Event from {from_user}: {event}")
        
        if event == 'subscribe':
            return self._handle_subscribe_event(config, message_data)
        elif event == 'unsubscribe':
            return self._handle_unsubscribe_event(config, message_data)
        elif event == 'CLICK':
            return self._handle_menu_click_event(config, message_data)
        elif event == 'VIEW':
            return self._handle_menu_view_event(config, message_data)
        elif event == 'scancode_push':
            return self._handle_scancode_push_event(config, message_data)
        elif event == 'scancode_waitmsg':
            return self._handle_scancode_waitmsg_event(config, message_data)
        elif event == 'pic_sysphoto':
            return self._handle_pic_sysphoto_event(config, message_data)
        elif event == 'pic_photo_or_album':
            return self._handle_pic_photo_or_album_event(config, message_data)
        elif event == 'pic_weixin':
            return self._handle_pic_weixin_event(config, message_data)
        elif event == 'location_select':
            return self._handle_location_select_event(config, message_data)

        return None
    
    def _handle_subscribe_event(self, config, message_data):
        """Handle user subscribe event"""
        from_user = message_data.get('FromUserName', '')
        
        # Update user subscription status
        user = request.env['wechat.user'].sudo().search([
            ('openid', '=', from_user),
            ('config_id', '=', config.id)
        ], limit=1)
        
        if user:
            user.subscribe_user()
        else:
            # Create new user
            request.env['wechat.user'].sudo().create_or_update_user(
                from_user, config.id
            )
        
        # Send welcome message
        welcome_message = """Welcome to our WeChat Official Account! 🎉

Thank you for following us. Here's what you can do:

📱 Send "menu" to see our main menu
💬 Send "help" for assistance
📞 Send "contact" for our contact information

We're excited to have you with us!"""
        
        return self._create_text_response(config, message_data, welcome_message)
    
    def _handle_unsubscribe_event(self, config, message_data):
        """Handle user unsubscribe event"""
        from_user = message_data.get('FromUserName', '')
        
        # Update user subscription status
        user = request.env['wechat.user'].sudo().search([
            ('openid', '=', from_user),
            ('config_id', '=', config.id)
        ], limit=1)
        
        if user:
            user.unsubscribe_user()
        
        # No response needed for unsubscribe
        return None
    
    def _handle_menu_click_event(self, config, message_data):
        """Handle menu click event"""
        event_key = message_data.get('EventKey', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Menu click event from {from_user}: {event_key}")

        # 查找对应的菜单项
        menu_item = request.env['wechat.menu.item'].sudo().search([
            ('key', '=', event_key),
            ('menu_id.config_id', '=', config.id),
            ('menu_id.state', '=', 'published'),
            ('active', '=', True)
        ], limit=1)

        if menu_item:
            # 记录菜单点击事件
            self._log_menu_event(config, from_user, 'click', event_key, menu_item.name)

            # 根据菜单项返回相应的响应
            return self._get_menu_response(config, message_data, menu_item)

        # 默认处理一些常见的菜单键
        if event_key == 'ABOUT_US':
            return self._create_text_response(
                config, message_data,
                "关于我们:\n我们是行业领先的公司..."
            )
        elif event_key == 'CONTACT':
            return self._create_text_response(
                config, message_data,
                "联系方式:\n电话: +86 123-456-7890\n邮箱: <EMAIL>"
            )
        elif event_key == 'HELP':
            return self._create_text_response(
                config, message_data,
                "帮助信息:\n如需帮助，请联系我们的客服。"
            )

        return self._create_text_response(
            config, message_data,
            f"您点击了菜单: {event_key}\n感谢您的关注！"
        )
    
    def _handle_menu_view_event(self, config, message_data):
        """Handle menu view event"""
        event_key = message_data.get('EventKey', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Menu view event from {from_user}: {event_key}")

        # 记录菜单访问事件
        self._log_menu_event(config, from_user, 'view', event_key, 'URL访问')

        # No response needed for view events
        return None

    def _handle_scancode_push_event(self, config, message_data):
        """Handle scancode push event"""
        event_key = message_data.get('EventKey', '')
        scan_result = message_data.get('ScanCodeInfo', {})
        scan_type = scan_result.get('ScanType', '')
        scan_result_text = scan_result.get('ScanResult', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Scancode push event from {from_user}: {event_key}, result: {scan_result_text}")

        # 记录扫码事件
        self._log_menu_event(config, from_user, 'scancode_push', event_key, f'扫码结果: {scan_result_text}')

        return self._create_text_response(
            config, message_data,
            f"扫码成功！\n类型: {scan_type}\n结果: {scan_result_text}"
        )

    def _handle_scancode_waitmsg_event(self, config, message_data):
        """Handle scancode waitmsg event"""
        event_key = message_data.get('EventKey', '')
        scan_result = message_data.get('ScanCodeInfo', {})
        scan_type = scan_result.get('ScanType', '')
        scan_result_text = scan_result.get('ScanResult', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Scancode waitmsg event from {from_user}: {event_key}, result: {scan_result_text}")

        # 记录扫码事件
        self._log_menu_event(config, from_user, 'scancode_waitmsg', event_key, f'扫码结果: {scan_result_text}')

        return self._create_text_response(
            config, message_data,
            f"扫码完成，正在处理...\n类型: {scan_type}\n结果: {scan_result_text}"
        )
    
    def _create_text_response(self, config, message_data, content):
        """Create XML text response"""
        from_user = message_data.get('FromUserName', '')
        to_user = message_data.get('ToUserName', '')
        import time
        create_time = str(int(time.time()))
        
        response_xml = f"""<xml>
<ToUserName><![CDATA[{from_user}]]></ToUserName>
<FromUserName><![CDATA[{to_user}]]></FromUserName>
<CreateTime>{create_time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""
        
        return response_xml

    def _handle_pic_sysphoto_event(self, config, message_data):
        """Handle pic sysphoto event"""
        event_key = message_data.get('EventKey', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Pic sysphoto event from {from_user}: {event_key}")

        # 记录拍照事件
        self._log_menu_event(config, from_user, 'pic_sysphoto', event_key, '系统拍照')

        return self._create_text_response(
            config, message_data,
            "感谢您使用系统拍照功能！我们已收到您的照片。"
        )

    def _handle_pic_photo_or_album_event(self, config, message_data):
        """Handle pic photo or album event"""
        event_key = message_data.get('EventKey', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Pic photo or album event from {from_user}: {event_key}")

        # 记录拍照或相册事件
        self._log_menu_event(config, from_user, 'pic_photo_or_album', event_key, '拍照或相册')

        return self._create_text_response(
            config, message_data,
            "感谢您分享照片！我们已收到您的图片。"
        )

    def _handle_pic_weixin_event(self, config, message_data):
        """Handle pic weixin event"""
        event_key = message_data.get('EventKey', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Pic weixin event from {from_user}: {event_key}")

        # 记录微信相册事件
        self._log_menu_event(config, from_user, 'pic_weixin', event_key, '微信相册')

        return self._create_text_response(
            config, message_data,
            "感谢您从微信相册分享照片！我们已收到您的图片。"
        )

    def _handle_location_select_event(self, config, message_data):
        """Handle location select event"""
        event_key = message_data.get('EventKey', '')
        location_info = message_data.get('SendLocationInfo', {})
        location_x = location_info.get('Location_X', '')
        location_y = location_info.get('Location_Y', '')
        scale = location_info.get('Scale', '')
        label = location_info.get('Label', '')
        from_user = message_data.get('FromUserName', '')

        _logger.info(f"Location select event from {from_user}: {event_key}, location: {location_x},{location_y}")

        # 记录位置选择事件
        self._log_menu_event(config, from_user, 'location_select', event_key, f'位置: {label}')

        return self._create_text_response(
            config, message_data,
            f"感谢您分享位置信息！\n位置: {label}\n坐标: {location_x}, {location_y}"
        )

    def _get_menu_response(self, config, message_data, menu_item):
        """根据菜单项获取响应"""
        # 这里可以根据菜单项的配置返回不同的响应
        # 例如：根据菜单项的描述或者关联的内容
        return self._create_text_response(
            config, message_data,
            f"您点击了菜单: {menu_item.name}\n感谢您的关注！"
        )

    def _log_menu_event(self, config, openid, event_type, event_key, description):
        """记录菜单事件"""
        try:
            # 记录菜单事件到数据库，用于统计分析
            _logger.info(f"Menu event logged: {event_type} - {event_key} - {description} from {openid}")

            # 创建菜单事件记录
            request.env['wechat.menu.event'].sudo().create({
                'config_id': config.id,
                'openid': openid,
                'event_type': event_type,
                'event_key': event_key,
                'description': description,
            })

        except Exception as e:
            _logger.error(f"Failed to log menu event: {e}")
