# -*- coding: utf-8 -*-

import json
import logging
import werkzeug
from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError, AccessDenied

_logger = logging.getLogger(__name__)


class WeChatAutoLoginController(http.Controller):

    @http.route('/MP_verify_SxqTFnTAb3E28Ki3.txt', type='http', auth='public')
    def wechat_verify_file(self):
        # 按照微信要求，返回验证文件的内容
        return request.make_response(
            'SxqTFnTAb3E28Ki3',
            headers=[('Content-Type', 'text/plain')]
        )

    @http.route('/wechat/auto/login/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_auto_login(self, config_id, redirect=None, **kwargs):
        """
        Initiate WeChat auto login process
        
        Args:
            config_id: WeChat configuration ID
            redirect: URL to redirect after successful login
        """
        try:
            # Store redirect URL in session
            if redirect:
                request.session['wechat_login_redirect'] = redirect
            else:
                request.session['wechat_login_redirect'] = '/web'
            
            # Generate WeChat OAuth URL for auto login
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = f"{base_url}/wechat/auto/callback/{config_id}"
            
            oauth_url = request.env['wechat.oauth'].sudo().generate_oauth_url(
                config_id=config_id,
                redirect_uri=redirect_uri,
                scope='snsapi_userinfo',
                state='auto_login'
            )
            
            if not oauth_url:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Failed to generate WeChat login URL')
                })
            
            # Log the redirect URL for debugging
            _logger.info(f"Redirecting to WeChat OAuth: {oauth_url}")

            # Use JavaScript redirect instead of HTTP redirect for better compatibility
            return request.render('wechat_official_account.oauth_redirect', {
                'oauth_url': oauth_url,
                'config_id': config_id
            })
            
        except Exception as e:
            _logger.error(f"WeChat auto login initiation error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/auto/callback/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_auto_callback(self, config_id, code=None, state=None, **kwargs):
        """
        Handle WeChat auto login callback
        
        Args:
            config_id: WeChat configuration ID
            code: Authorization code from WeChat
            state: State parameter
        """
        try:
            if not code:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Authorization code not received')
                })

            # 调试日志：记录收到的code
            _logger.info(f"Received WeChat authorization code: {code[:10]}... (length: {len(code)})")

            # Get user info from WeChat
            user_info = self._get_wechat_user_info(code, config_id)
            if not user_info:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Failed to get user information from WeChat')
                })
            
            openid = user_info.get('openid')
            if not openid:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Invalid user information received')
                })

            # 调试日志：记录获得的openid
            _logger.info(f"Extracted OpenID from user info: {openid}")
            _logger.info(f"User info keys: {list(user_info.keys()) if user_info else 'None'}")
            
            # 通过wechat.user查找关联的用户
            wechat_user = request.env['wechat.user'].sudo().search([
                ('openid', '=', openid),
                ('config_id', '=', config_id)
            ], limit=1)

            if wechat_user and wechat_user.user_id:
                # 找到关联的用户，直接登录
                existing_user = wechat_user.user_id
                _logger.info(f"Found linked user: {existing_user.name} for OpenID: {openid}")

                # 检查用户是否活跃
                if not existing_user.active:
                    _logger.warning(f"User {existing_user.name} is not active")
                    return request.render('wechat_official_account.auth_error', {
                        'error': _('User account is not active')
                    })

                # 直接设置session，绕过密码验证
                request.session.uid = existing_user.id
                request.session.login = existing_user.login
                request.session.session_token = existing_user._compute_session_token(request.session.sid)
                request.session.context = dict(existing_user.context_get())

                redirect_url = request.session.pop('wechat_login_redirect', '/web')
                _logger.info(f"WeChat auto login successful for user {existing_user.login} via OpenID {openid}")
                return request.redirect(redirect_url)
            
            # New user or user without phone - need to collect phone number
            return self._handle_new_user_registration(openid, config_id, user_info)
            
        except Exception as e:
            _logger.error(f"WeChat auto login callback error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    def _get_wechat_user_info(self, code, config_id):
        """Get user information from WeChat API"""
        try:
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return None
            
            # Get access token
            import requests
            token_url = 'https://api.weixin.qq.com/sns/oauth2/access_token'
            token_params = {
                'appid': config.app_id,
                'secret': config.app_secret,
                'code': code,
                'grant_type': 'authorization_code'
            }
            
            token_response = requests.get(token_url, params=token_params, timeout=30)
            token_data = token_response.json()

            # 调试日志：记录token响应
            _logger.info(f"WeChat token response: {token_data}")

            if 'errcode' in token_data:
                _logger.error(f"WeChat token error: {token_data}")
                return None

            access_token = token_data.get('access_token')
            openid = token_data.get('openid')

            # 调试日志：记录获得的access_token和openid
            _logger.info(f"Got access_token: {access_token[:20] if access_token else 'None'}...")
            _logger.info(f"Got openid: {openid}")
            
            if not access_token or not openid:
                return None
            
            # Get user info
            if token_data.get('scope') == 'snsapi_userinfo':
                userinfo_url = 'https://api.weixin.qq.com/sns/userinfo'
                userinfo_params = {
                    'access_token': access_token,
                    'openid': openid,
                    'lang': 'zh_CN'
                }
                
                userinfo_response = requests.get(userinfo_url, params=userinfo_params, timeout=30)
                user_info = userinfo_response.json()
                
                if 'errcode' in user_info:
                    _logger.error(f"WeChat userinfo error: {user_info}")
                    return None
                
                return user_info
            else:
                # Basic scope, only have openid
                return {
                    'openid': openid,
                    'unionid': token_data.get('unionid'),
                }
                
        except Exception as e:
            _logger.error(f"Failed to get WeChat user info: {e}")
            return None

    def _handle_new_user_registration(self, openid, config_id, user_info):
        """Handle new user registration with phone collection"""
        try:
            # Store user info in session for phone collection
            request.session['wechat_user_info'] = {
                'openid': openid,
                'config_id': config_id,
                'user_info': user_info,
                'auto_login': True
            }
            
            # Redirect to phone collection
            return request.redirect('/wechat/auto/phone/collect')
            
        except Exception as e:
            _logger.error(f"New user registration error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/auto/phone/collect', type='http', auth='public', methods=['GET'], csrf=False)
    def auto_phone_collect(self, **kwargs):
        """Display phone collection form for auto login setup"""
        try:
            user_info_session = request.session.get('wechat_user_info')
            if not user_info_session:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Session expired, please try again')
                })
            
            return request.render('wechat_official_account.auto_phone_collect_form', {
                'user_info': user_info_session['user_info'],
                'session_data': user_info_session
            })
            
        except Exception as e:
            _logger.error(f"Auto phone collect error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/auto/phone/submit', type='http', auth='public', methods=['POST'], csrf=False)
    def auto_phone_submit(self, phone_number=None, **kwargs):
        """Submit phone number and complete auto login setup"""
        try:
            user_info_session = request.session.get('wechat_user_info')
            if not user_info_session:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Session expired, please try again')
                })
            
            if not phone_number:
                return request.render('wechat_official_account.auto_phone_collect_form', {
                    'user_info': user_info_session['user_info'],
                    'session_data': user_info_session,
                    'error': _('Phone number is required')
                })
            
            # Validate phone number
            import re
            if not re.match(r'^1[3-9]\d{9}$', phone_number):
                return request.render('wechat_official_account.auto_phone_collect_form', {
                    'user_info': user_info_session['user_info'],
                    'session_data': user_info_session,
                    'error': _('Invalid phone number format')
                })
            
            # Get or create user
            auto_login_mgr = request.env['wechat.auto.login'].sudo()
            user = auto_login_mgr.get_or_create_user_for_wechat(
                openid=user_info_session['openid'],
                config_id=user_info_session['config_id'],
                user_info=user_info_session['user_info'],
                phone_number=phone_number
            )
            
            if not user:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Failed to create or find user account')
                })
            
            # Clear session data
            request.session.pop('wechat_user_info', None)
            
            # Auto login the user
            # 直接设置session，绕过密码验证
            request.session.uid = user.id
            request.session.login = user.login
            request.session.session_token = user._compute_session_token(request.session.sid)
            request.session.context = dict(user.context_get())
            
            # Redirect to original destination
            redirect_url = request.session.pop('wechat_login_redirect', '/web')
            
            # Show success message first, then redirect
            return request.render('wechat_official_account.auto_login_success', {
                'user': user,
                'redirect_url': redirect_url,
                'phone_number': phone_number
            })
            
        except Exception as e:
            _logger.error(f"Auto phone submit error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/auto/check/<string:openid>/<int:config_id>', type='json', auth='public', methods=['POST'])
    def check_auto_login_status(self, openid, config_id, **kwargs):
        """Check if user has auto login enabled"""
        try:
            auto_login = request.env['wechat.auto.login'].sudo().search([
                ('openid', '=', openid),
                ('config_id', '=', config_id),
                ('enabled', '=', True),
                ('active', '=', True)
            ], limit=1)
            
            return {
                'success': True,
                'has_auto_login': bool(auto_login),
                'user_name': auto_login.user_id.name if auto_login else None,
                'phone_number': auto_login.phone_number if auto_login else None,
                'last_login': auto_login.last_login.isoformat() if auto_login and auto_login.last_login else None
            }
            
        except Exception as e:
            _logger.error(f"Check auto login status error: {e}")
            return {'success': False, 'error': str(e)}

    @http.route('/wechat/auto/disable', type='json', auth='user', methods=['POST'])
    def disable_auto_login(self, **kwargs):
        """Disable auto login for current user"""
        try:
            current_user = request.env.user
            auto_login_records = request.env['wechat.auto.login'].search([
                ('user_id', '=', current_user.id),
                ('active', '=', True)
            ])
            
            for record in auto_login_records:
                record.disable_auto_login()
            
            return {
                'success': True,
                'message': _('Auto login has been disabled')
            }
            
        except Exception as e:
            _logger.error(f"Disable auto login error: {e}")
            return {'success': False, 'error': str(e)}

    @http.route('/wechat/auto/enable', type='json', auth='user', methods=['POST'])
    def enable_auto_login(self, **kwargs):
        """Enable auto login for current user"""
        try:
            current_user = request.env.user
            auto_login_records = request.env['wechat.auto.login'].search([
                ('user_id', '=', current_user.id),
                ('active', '=', True)
            ])
            
            for record in auto_login_records:
                record.enable_auto_login()
            
            return {
                'success': True,
                'message': _('Auto login has been enabled')
            }
            
        except Exception as e:
            _logger.error(f"Enable auto login error: {e}")
            return {'success': False, 'error': str(e)}

    @http.route('/wechat/auto/qr/<int:config_id>', type='http', auth='public', methods=['GET'])
    def generate_auto_login_qr(self, config_id, **kwargs):
        """Generate QR code for WeChat auto login"""
        try:
            # Generate auto login URL
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            login_url = f"{base_url}/wechat/auto/login/{config_id}"
            
            # Generate QR code
            try:
                import qrcode
                import io
                import base64
                
                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(login_url)
                qr.make(fit=True)
                
                img = qr.make_image(fill_color="black", back_color="white")
                
                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                img_str = base64.b64encode(buffer.getvalue()).decode()
                
                return request.render('wechat_official_account.auto_login_qr', {
                    'qr_code': img_str,
                    'login_url': login_url,
                    'config_id': config_id
                })
                
            except ImportError:
                # Fallback if qrcode library not available
                return request.render('wechat_official_account.auto_login_qr', {
                    'qr_code': None,
                    'login_url': login_url,
                    'config_id': config_id,
                    'error': _('QR code generation requires python-qrcode library')
                })
                
        except Exception as e:
            _logger.error(f"Generate QR code error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })
