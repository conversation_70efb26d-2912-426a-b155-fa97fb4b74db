# -*- coding: utf-8 -*-

import logging
from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WeChatDirectLoginController(http.Controller):
    """微信直接登录控制器"""

    @http.route('/wechat/direct/login/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_direct_login(self, config_id, redirect=None, **kwargs):
        """
        微信直接登录入口
        
        Args:
            config_id: 微信配置ID
            redirect: 登录成功后的重定向URL
        """
        try:
            # 获取微信配置
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return request.render('wechat_official_account.auth_error', {
                    'error': _('微信配置不存在')
                })
            
            # 保存重定向URL
            if redirect:
                request.session['wechat_login_redirect'] = redirect
            else:
                request.session['wechat_login_redirect'] = '/web'
            
            # 检查是否在微信浏览器中
            user_agent = request.httprequest.headers.get('User-Agent', '')
            if 'MicroMessenger' not in user_agent:
                # 不在微信中，显示二维码或提示
                return request.render('wechat_official_account.direct_login_qr', {
                    'config': config,
                    'login_url': f"/wechat/direct/login/{config_id}",
                })
            
            # 在微信中，尝试获取用户信息
            return self._handle_wechat_browser_login(config_id)
            
        except Exception as e:
            _logger.error(f"WeChat direct login error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    def _handle_wechat_browser_login(self, config_id):
        """处理微信浏览器中的登录"""
        try:
            # 显示OpenID输入页面（临时方案）
            return request.render('wechat_official_account.direct_login_form', {
                'config_id': config_id,
            })
            
        except Exception as e:
            _logger.error(f"WeChat browser login error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/direct/auth', type='http', auth='public', methods=['POST'], csrf=False)
    def wechat_direct_auth(self, config_id=None, openid=None, **kwargs):
        """
        处理直接登录认证
        
        Args:
            config_id: 微信配置ID
            openid: 用户OpenID
        """
        try:
            if not config_id or not openid:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('缺少必要参数')
                })
            
            config_id = int(config_id)
            
            # 通过wechat.user查找关联的用户
            wechat_user = request.env['wechat.user'].sudo().search([
                ('openid', '=', openid),
                ('config_id', '=', config_id)
            ], limit=1)

            if not wechat_user or not wechat_user.user_id:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('未找到关联的用户账号，请联系管理员进行绑定')
                })

            user = wechat_user.user_id

            # 检查用户是否活跃
            if not user.active:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('用户账号未激活')
                })
            
            # 执行登录
            # 直接设置session，绕过密码验证
            request.session.uid = user.id
            request.session.login = user.login
            request.session.session_token = user._compute_session_token(request.session.sid)
            request.session.context = dict(user.context_get())
            
            # 获取重定向URL
            redirect_url = request.session.pop('wechat_login_redirect', '/web')
            
            _logger.info(f"WeChat direct login successful: user {user.id} ({user.login})")
            
            # 显示成功页面，然后重定向
            return request.render('wechat_official_account.direct_login_success', {
                'user': user,
                'redirect_url': redirect_url,
            })
            
        except Exception as e:
            _logger.error(f"WeChat direct auth error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/direct/bind', type='http', auth='user', methods=['GET'], csrf=False)
    def wechat_direct_bind(self, **kwargs):
        """用户绑定微信账号页面"""
        try:
            current_user = request.env.user
            configs = request.env['wechat.config'].search([('active', '=', True)])
            
            return request.render('wechat_official_account.direct_bind_form', {
                'user': current_user,
                'configs': configs,
            })
            
        except Exception as e:
            _logger.error(f"WeChat bind page error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/direct/bind/submit', type='http', auth='user', methods=['POST'], csrf=False)
    def wechat_direct_bind_submit(self, config_id=None, openid=None, **kwargs):
        """提交绑定"""
        try:
            if not config_id or not openid:
                raise UserError(_('请填写完整信息'))
            
            current_user = request.env.user
            config_id = int(config_id)
            
            # 创建绑定
            binding = request.env['wechat.direct.login'].create_binding(
                user_id=current_user.id,
                openid=openid,
                config_id=config_id
            )
            
            return request.render('wechat_official_account.bind_success', {
                'user': current_user,
                'binding': binding,
            })
            
        except Exception as e:
            _logger.error(f"WeChat bind submit error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/direct/test/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def test_direct_login(self, config_id, **kwargs):
        """测试直接登录功能"""
        try:
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return "<h1>微信配置不存在</h1>"
            
            # 获取绑定记录
            bindings = request.env['wechat.direct.login'].sudo().search([
                ('config_id', '=', config_id),
                ('enabled', '=', True)
            ])
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>微信直接登录测试</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .binding {{ border: 1px solid #ccc; margin: 10px 0; padding: 10px; }}
                    .test-form {{ background: #f5f5f5; padding: 15px; margin: 10px 0; }}
                </style>
            </head>
            <body>
                <h1>微信直接登录测试</h1>
                
                <h2>配置信息</h2>
                <p><strong>配置名称：</strong>{config.name}</p>
                <p><strong>App ID：</strong>{config.app_id}</p>
                
                <h2>现有绑定 ({len(bindings)} 个)</h2>
            """
            
            for binding in bindings:
                html += f"""
                <div class="binding">
                    <p><strong>用户：</strong>{binding.user_id.name}</p>
                    <p><strong>OpenID：</strong>{binding.openid}</p>
                    <p><strong>登录次数：</strong>{binding.login_count}</p>
                    <p><strong>最后登录：</strong>{binding.last_login_time or '从未'}</p>
                </div>
                """
            
            html += f"""
                <h2>测试登录</h2>
                <div class="test-form">
                    <form action="/wechat/direct/auth" method="post">
                        <input type="hidden" name="config_id" value="{config_id}">
                        <label>OpenID: 
                            <input type="text" name="openid" placeholder="输入要测试的OpenID" style="width: 300px;">
                        </label>
                        <button type="submit">测试登录</button>
                    </form>
                </div>
                
                <h2>登录链接</h2>
                <p><a href="/wechat/direct/login/{config_id}">直接登录链接</a></p>
                
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            return f"<h1>错误：{str(e)}</h1>"
