# -*- coding: utf-8 -*-

import json
import logging
from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WeChatInAppLoginController(http.Controller):
    """微信公众号内登录控制器"""

    @http.route('/wechat/inapp/login/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_inapp_login(self, config_id, redirect=None, **kwargs):
        """
        微信公众号内登录入口
        
        Args:
            config_id: 微信配置ID
            redirect: 登录成功后跳转地址
        """
        try:
            # 检测是否在微信浏览器中
            user_agent = request.httprequest.headers.get('User-Agent', '')
            is_wechat = 'MicroMessenger' in user_agent
            
            if not is_wechat:
                # 不在微信中，显示提示页面
                return request.render('wechat_official_account.not_in_wechat', {
                    'config_id': config_id,
                    'redirect': redirect
                })
            
            # 在微信中，检查是否已经登录
            if request.session.uid:
                # 已登录，直接跳转
                redirect_url = redirect or '/web'
                return request.redirect(redirect_url)
            
            # 未登录，发起微信授权
            return self._initiate_wechat_auth(config_id, redirect)
            
        except Exception as e:
            _logger.error(f"WeChat in-app login error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    def _initiate_wechat_auth(self, config_id, redirect=None):
        """发起微信授权"""
        try:
            # 存储重定向地址
            if redirect:
                request.session['wechat_login_redirect'] = redirect
            else:
                request.session['wechat_login_redirect'] = '/web'
            
            # 生成微信授权URL
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = f"{base_url}/wechat/inapp/callback/{config_id}"
            
            oauth_url = request.env['wechat.oauth'].sudo().generate_oauth_url(
                config_id=config_id,
                redirect_uri=redirect_uri,
                scope='snsapi_userinfo',
                state='inapp_login'
            )
            
            if not oauth_url:
                raise UserError(_('无法生成微信授权链接'))
            
            # 重定向到微信授权页面
            return request.redirect(oauth_url)
            
        except Exception as e:
            _logger.error(f"Failed to initiate WeChat auth: {e}")
            raise

    @http.route('/wechat/inapp/callback/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_inapp_callback(self, config_id, code=None, state=None, **kwargs):
        """
        微信公众号内登录回调
        
        Args:
            config_id: 微信配置ID
            code: 微信授权码
            state: 状态参数
        """
        try:
            if not code:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('未收到微信授权码')
                })
            
            # 获取用户信息
            user_info = self._get_wechat_user_info(code, config_id)
            if not user_info:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('无法获取微信用户信息')
                })
            
            openid = user_info.get('openid')
            if not openid:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('无效的用户信息')
                })
            
            # 检查是否已有自动登录记录
            auto_login_mgr = request.env['wechat.auto.login'].sudo()
            existing_user = auto_login_mgr.authenticate_by_openid(
                openid=openid,
                config_id=config_id,
                ip_address=request.httprequest.environ.get('REMOTE_ADDR')
            )
            
            if existing_user:
                # 用户已存在，直接登录
                # 直接设置session，绕过密码验证
                request.session.uid = existing_user.id
                request.session.login = existing_user.login
                request.session.session_token = existing_user._compute_session_token(request.session.sid)
                request.session.context = dict(existing_user.context_get())
                
                redirect_url = request.session.pop('wechat_login_redirect', '/web')
                return request.redirect(redirect_url)
            
            # 新用户，需要收集手机号
            return self._handle_new_user_in_wechat(openid, config_id, user_info)
            
        except Exception as e:
            _logger.error(f"WeChat in-app callback error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    def _get_wechat_user_info(self, code, config_id):
        """获取微信用户信息"""
        try:
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return None
            
            # 获取access_token
            import requests
            token_url = 'https://api.weixin.qq.com/sns/oauth2/access_token'
            token_params = {
                'appid': config.app_id,
                'secret': config.app_secret,
                'code': code,
                'grant_type': 'authorization_code'
            }
            
            token_response = requests.get(token_url, params=token_params, timeout=30)
            token_data = token_response.json()
            
            if 'errcode' in token_data:
                _logger.error(f"WeChat token error: {token_data}")
                return None
            
            access_token = token_data.get('access_token')
            openid = token_data.get('openid')
            
            if not access_token or not openid:
                return None
            
            # 获取用户详细信息
            if token_data.get('scope') == 'snsapi_userinfo':
                userinfo_url = 'https://api.weixin.qq.com/sns/userinfo'
                userinfo_params = {
                    'access_token': access_token,
                    'openid': openid,
                    'lang': 'zh_CN'
                }
                
                userinfo_response = requests.get(userinfo_url, params=userinfo_params, timeout=30)
                user_info = userinfo_response.json()
                
                if 'errcode' in user_info:
                    _logger.error(f"WeChat userinfo error: {user_info}")
                    return None
                
                return user_info
            else:
                # 基础授权，只有openid
                return {
                    'openid': openid,
                    'unionid': token_data.get('unionid'),
                }
                
        except Exception as e:
            _logger.error(f"Failed to get WeChat user info: {e}")
            return None

    def _handle_new_user_in_wechat(self, openid, config_id, user_info):
        """在微信中处理新用户"""
        try:
            # 存储用户信息到session
            request.session['wechat_inapp_user_info'] = {
                'openid': openid,
                'config_id': config_id,
                'user_info': user_info,
                'in_wechat': True
            }
            
            # 跳转到微信内的手机号收集页面
            return request.redirect('/wechat/inapp/phone/collect')
            
        except Exception as e:
            _logger.error(f"Handle new user in WeChat error: {e}")
            raise

    @http.route('/wechat/inapp/phone/collect', type='http', auth='public', methods=['GET'], csrf=False)
    def inapp_phone_collect(self, **kwargs):
        """微信内手机号收集页面"""
        try:
            user_info_session = request.session.get('wechat_inapp_user_info')
            if not user_info_session:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('会话已过期，请重新登录')
                })
            
            return request.render('wechat_official_account.inapp_phone_collect_form', {
                'user_info': user_info_session['user_info'],
                'session_data': user_info_session
            })
            
        except Exception as e:
            _logger.error(f"In-app phone collect error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/inapp/phone/submit', type='http', auth='public', methods=['POST'], csrf=False)
    def inapp_phone_submit(self, phone_number=None, **kwargs):
        """微信内手机号提交"""
        try:
            user_info_session = request.session.get('wechat_inapp_user_info')
            if not user_info_session:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('会话已过期，请重新登录')
                })
            
            if not phone_number:
                return request.render('wechat_official_account.inapp_phone_collect_form', {
                    'user_info': user_info_session['user_info'],
                    'session_data': user_info_session,
                    'error': _('请输入手机号码')
                })
            
            # 验证手机号格式
            import re
            if not re.match(r'^1[3-9]\d{9}$', phone_number):
                return request.render('wechat_official_account.inapp_phone_collect_form', {
                    'user_info': user_info_session['user_info'],
                    'session_data': user_info_session,
                    'error': _('手机号格式不正确')
                })
            
            # 创建或获取用户
            auto_login_mgr = request.env['wechat.auto.login'].sudo()
            user = auto_login_mgr.get_or_create_user_for_wechat(
                openid=user_info_session['openid'],
                config_id=user_info_session['config_id'],
                user_info=user_info_session['user_info'],
                phone_number=phone_number
            )
            
            if not user:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('创建用户失败')
                })
            
            # 清除session数据
            request.session.pop('wechat_inapp_user_info', None)
            
            # 自动登录
            # 直接设置session，绕过密码验证
            request.session.uid = user.id
            request.session.login = user.login
            request.session.session_token = user._compute_session_token(request.session.sid)
            request.session.context = dict(user.context_get())
            
            # 获取重定向地址
            redirect_url = request.session.pop('wechat_login_redirect', '/web')
            
            # 显示成功页面，然后自动跳转
            return request.render('wechat_official_account.inapp_login_success', {
                'user': user,
                'redirect_url': redirect_url,
                'phone_number': phone_number
            })
            
        except Exception as e:
            _logger.error(f"In-app phone submit error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/inapp/check', type='json', auth='public', methods=['POST'])
    def check_wechat_browser(self, **kwargs):
        """检查是否在微信浏览器中"""
        try:
            user_agent = request.httprequest.headers.get('User-Agent', '')
            is_wechat = 'MicroMessenger' in user_agent
            
            return {
                'success': True,
                'is_wechat': is_wechat,
                'user_agent': user_agent
            }
            
        except Exception as e:
            _logger.error(f"Check WeChat browser error: {e}")
            return {'success': False, 'error': str(e)}
