# -*- coding: utf-8 -*-

import json
import logging
from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WeChatOAuthController(http.Controller):

    @http.route('/wechat/oauth/authorize/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def oauth_authorize(self, config_id, redirect_uri=None, scope='snsapi_userinfo', state=None, **kwargs):
        """
        Initiate WeChat OAuth authorization

        Args:
            config_id: WeChat configuration ID
            redirect_uri: Where to redirect after authorization (optional)
            scope: Authorization scope (snsapi_base or snsapi_userinfo)
            state: Custom state parameter
        """
        try:
            # Log incoming parameters for debugging
            _logger.info(f"OAuth authorize called with: config_id={config_id}, scope='{scope}', state='{state}'")

            # Ensure scope is not empty
            if not scope or scope.strip() == '' or scope in ['None', 'null', 'undefined']:
                scope = 'snsapi_userinfo'
                _logger.warning(f"Empty or invalid scope provided, using default: {scope}")
            # Get WeChat configuration
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return request.render('wechat_official_account.oauth_error', {
                    'error': _('WeChat configuration not found')
                })
            
            # Default redirect URI
            if not redirect_uri:
                base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                redirect_uri = f"{base_url}/wechat/oauth/callback/{config_id}"
            
            # Generate OAuth URL
            oauth_url = request.env['wechat.oauth'].sudo().generate_oauth_url(
                config_id=config_id,
                redirect_uri=redirect_uri,
                scope=scope,
                state=state
            )
            
            # Redirect to WeChat OAuth
            return request.redirect(oauth_url)
            
        except Exception as e:
            _logger.error(f"OAuth authorize error: {e}")
            return request.render('wechat_official_account.oauth_error', {
                'error': str(e)
            })

    @http.route('/wechat/oauth/callback/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def oauth_callback(self, config_id, code=None, state=None, **kwargs):
        """
        Handle WeChat OAuth callback
        
        Args:
            config_id: WeChat configuration ID
            code: Authorization code from WeChat
            state: State parameter
        """
        try:
            if not code:
                return request.render('wechat_official_account.oauth_error', {
                    'error': _('Authorization code not received')
                })
            
            # Handle OAuth callback
            oauth_record = request.env['wechat.oauth'].sudo().handle_oauth_callback(
                code=code,
                state=state,
                config_id=config_id
            )
            
            # Redirect to phone collection form
            return request.redirect(f'/wechat/phone/collect?oauth_id={oauth_record.id}')
            
        except Exception as e:
            _logger.error(f"OAuth callback error: {e}")
            return request.render('wechat_official_account.oauth_error', {
                'error': str(e)
            })

    @http.route('/wechat/phone/collect', type='http', auth='public', methods=['GET'], csrf=False)
    def phone_collect_form(self, oauth_id=None, **kwargs):
        """
        Display phone number collection form
        
        Args:
            oauth_id: OAuth record ID
        """
        try:
            if not oauth_id:
                return request.render('wechat_official_account.oauth_error', {
                    'error': _('OAuth session not found')
                })
            
            oauth_record = request.env['wechat.oauth'].sudo().browse(int(oauth_id))
            if not oauth_record.exists():
                return request.render('wechat_official_account.oauth_error', {
                    'error': _('OAuth session expired')
                })
            
            return request.render('wechat_official_account.phone_collect_form', {
                'oauth_record': oauth_record
            })
            
        except Exception as e:
            _logger.error(f"Phone collect form error: {e}")
            return request.render('wechat_official_account.oauth_error', {
                'error': str(e)
            })

    @http.route('/wechat/phone/submit', type='http', auth='public', methods=['POST'], csrf=False)
    def phone_submit(self, oauth_id=None, phone_number=None, verification_code=None, **kwargs):
        """
        Submit phone number
        
        Args:
            oauth_id: OAuth record ID
            phone_number: User's phone number
            verification_code: SMS verification code (if implemented)
        """
        try:
            if not oauth_id or not phone_number:
                return request.render('wechat_official_account.oauth_error', {
                    'error': _('Missing required information')
                })
            
            oauth_record = request.env['wechat.oauth'].sudo().browse(int(oauth_id))
            if not oauth_record.exists():
                return request.render('wechat_official_account.oauth_error', {
                    'error': _('OAuth session expired')
                })
            
            # Validate phone number format
            import re
            if not re.match(r'^1[3-9]\d{9}$', phone_number):
                return request.render('wechat_official_account.phone_collect_form', {
                    'oauth_record': oauth_record,
                    'error': _('Invalid phone number format')
                })
            
            # TODO: Implement SMS verification if needed
            # if verification_code:
            #     self._verify_sms_code(phone_number, verification_code)
            
            # Update OAuth record with phone number
            oauth_record.write({
                'phone_number': phone_number,
                'phone_verified': True,  # Set to True if SMS verification is implemented
            })
            
            # Update related WeChat user
            if oauth_record.user_id:
                # Add phone field to wechat.user model if not exists
                if hasattr(oauth_record.user_id, 'phone'):
                    oauth_record.user_id.write({'phone': phone_number})
            
            # Create or update partner
            self._create_or_update_partner(oauth_record)
            
            return request.render('wechat_official_account.phone_success', {
                'oauth_record': oauth_record
            })
            
        except Exception as e:
            _logger.error(f"Phone submit error: {e}")
            return request.render('wechat_official_account.oauth_error', {
                'error': str(e)
            })

    def _create_or_update_partner(self, oauth_record):
        """Create or update partner with phone number"""
        try:
            partner_vals = {
                'name': oauth_record.nickname or f'WeChat User {oauth_record.openid[:8]}',
                'phone': oauth_record.phone_number,
                'is_company': False,
                'customer_rank': 1,
                'comment': f'WeChat User - OpenID: {oauth_record.openid}',
            }
            
            if oauth_record.partner_id:
                # Update existing partner
                oauth_record.partner_id.write(partner_vals)
            else:
                # Create new partner
                partner = request.env['res.partner'].sudo().create(partner_vals)
                oauth_record.partner_id = partner.id
                
                # Link with WeChat user if exists
                if oauth_record.user_id:
                    oauth_record.user_id.partner_id = partner.id
            
        except Exception as e:
            _logger.error(f"Partner creation error: {e}")

    @http.route('/wechat/phone/verify', type='json', auth='public', methods=['POST'], csrf=False)
    def send_verification_code(self, phone_number=None, **kwargs):
        """
        Send SMS verification code (optional feature)
        
        Args:
            phone_number: Phone number to send code to
        
        Returns:
            JSON response
        """
        try:
            if not phone_number:
                return {'success': False, 'error': _('Phone number is required')}
            
            # Validate phone number
            import re
            if not re.match(r'^1[3-9]\d{9}$', phone_number):
                return {'success': False, 'error': _('Invalid phone number format')}
            
            # TODO: Implement SMS sending logic
            # This would typically integrate with SMS providers like:
            # - Alibaba Cloud SMS
            # - Tencent Cloud SMS
            # - Twilio
            # - etc.
            
            # For now, return success (implement actual SMS sending as needed)
            _logger.info(f"SMS verification code requested for: {phone_number}")
            
            return {
                'success': True,
                'message': _('Verification code sent successfully')
            }
            
        except Exception as e:
            _logger.error(f"SMS verification error: {e}")
            return {'success': False, 'error': str(e)}

    @http.route('/wechat/user/info/<string:openid>', type='json', auth='public', methods=['GET'], csrf=False)
    def get_user_info(self, openid, config_id=None, **kwargs):
        """
        Get user information by OpenID
        
        Args:
            openid: User's OpenID
            config_id: WeChat configuration ID (optional)
        
        Returns:
            JSON response with user information
        """
        try:
            domain = [('openid', '=', openid)]
            if config_id:
                domain.append(('config_id', '=', int(config_id)))
            
            # Search in OAuth records first (more complete info)
            oauth_record = request.env['wechat.oauth'].sudo().search(domain, limit=1)
            if oauth_record:
                return {
                    'success': True,
                    'data': {
                        'openid': oauth_record.openid,
                        'unionid': oauth_record.unionid,
                        'nickname': oauth_record.nickname,
                        'phone_number': oauth_record.phone_number,
                        'phone_verified': oauth_record.phone_verified,
                        'city': oauth_record.city,
                        'province': oauth_record.province,
                        'country': oauth_record.country,
                        'headimgurl': oauth_record.headimgurl,
                        'partner_id': oauth_record.partner_id.id if oauth_record.partner_id else None,
                    }
                }
            
            # Fallback to WeChat user records
            wechat_user = request.env['wechat.user'].sudo().search(domain, limit=1)
            if wechat_user:
                return {
                    'success': True,
                    'data': {
                        'openid': wechat_user.openid,
                        'unionid': wechat_user.unionid,
                        'nickname': wechat_user.nickname,
                        'phone_number': getattr(wechat_user, 'phone', None),
                        'city': wechat_user.city,
                        'province': wechat_user.province,
                        'country': wechat_user.country,
                        'headimgurl': wechat_user.headimgurl,
                        'partner_id': wechat_user.partner_id.id if wechat_user.partner_id else None,
                    }
                }
            
            return {'success': False, 'error': _('User not found')}
            
        except Exception as e:
            _logger.error(f"Get user info error: {e}")
            return {'success': False, 'error': str(e)}

    @http.route('/wechat/oauth/test/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def test_oauth_url(self, config_id, scope='snsapi_userinfo', **kwargs):
        """Test OAuth URL generation"""
        try:
            # Get WeChat configuration
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return f"<h1>Error: WeChat configuration {config_id} not found</h1>"

            # Generate OAuth URL
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = f"{base_url}/wechat/oauth/callback/{config_id}"

            oauth_url = request.env['wechat.oauth'].sudo().generate_oauth_url(
                config_id=config_id,
                redirect_uri=redirect_uri,
                scope=scope,
                state='test'
            )

            # Check if scope is in the URL
            scope_found = 'scope=' in oauth_url
            if scope_found:
                scope_value = oauth_url.split('scope=')[1].split('&')[0]
            else:
                scope_value = 'NOT FOUND'

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>WeChat OAuth Test</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .success {{ color: green; }}
                    .error {{ color: red; }}
                    .url {{ word-break: break-all; background: #f5f5f5; padding: 10px; margin: 10px 0; }}
                </style>
            </head>
            <body>
                <h1>WeChat OAuth URL Test</h1>
                <p><strong>Config ID:</strong> {config_id}</p>
                <p><strong>Input Scope:</strong> {scope}</p>
                <p><strong>Scope in URL:</strong> <span class="{'success' if scope_found else 'error'}">{scope_value}</span></p>
                <p><strong>Generated URL:</strong></p>
                <div class="url">{oauth_url}</div>
                <p><a href="{oauth_url}" target="_blank">Test This URL</a></p>

                <h2>Test Different Scopes</h2>
                <p><a href="/wechat/oauth/test/{config_id}?scope=snsapi_userinfo">Test snsapi_userinfo</a></p>
                <p><a href="/wechat/oauth/test/{config_id}?scope=snsapi_base">Test snsapi_base</a></p>
                <p><a href="/wechat/oauth/test/{config_id}?scope=">Test empty scope</a></p>
            </body>
            </html>
            """

            return html

        except Exception as e:
            _logger.error(f"Test OAuth URL error: {e}")
            return f"<h1>Error: {str(e)}</h1>"
