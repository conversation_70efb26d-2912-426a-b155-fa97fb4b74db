# -*- coding: utf-8 -*-

import json
import logging
import werkzeug
from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError, AccessDenied
from odoo.addons.web.controllers.home import Home
from odoo.addons.auth_signup.controllers.main import AuthSignupHome

_logger = logging.getLogger(__name__)


class WeChatAuthController(http.Controller):

    @http.route('/wechat/auth/login/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_login(self, config_id, redirect=None, **kwargs):
        """
        Initiate WeChat login process
        
        Args:
            config_id: WeChat configuration ID
            redirect: URL to redirect after successful login
        """
        try:
            # Store redirect URL in session
            if redirect:
                request.session['wechat_login_redirect'] = redirect
            
            # Generate WeChat login URL
            auth_provider = request.env['wechat.auth.provider'].sudo()
            login_url = auth_provider.get_wechat_login_url(config_id, state='login')
            
            if not login_url:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Failed to generate WeChat login URL')
                })
            
            # Redirect to WeChat OAuth
            return request.redirect(login_url)
            
        except Exception as e:
            _logger.error(f"WeChat login initiation error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/auth/login/callback/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def wechat_login_callback(self, config_id, code=None, state=None, **kwargs):
        """
        Handle WeChat login callback
        
        Args:
            config_id: WeChat configuration ID
            code: Authorization code from WeChat
            state: State parameter
        """
        try:
            if not code:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Authorization code not received')
                })
            
            # Handle OAuth callback to get user info
            oauth_record = request.env['wechat.oauth'].sudo().handle_oauth_callback(
                code=code,
                state=state,
                config_id=config_id
            )
            
            # Check if we need to collect phone number
            if not oauth_record.phone_number:
                # Redirect to phone collection with auto-login flag
                return request.redirect(f'/wechat/phone/collect?oauth_id={oauth_record.id}&auto_login=1')
            
            # Authenticate user
            user = self._authenticate_wechat_user(oauth_record)
            
            if user:
                # Login the user
                # 直接设置session，绕过密码验证
                request.session.uid = user.id
                request.session.login = user.login
                request.session.session_token = user._compute_session_token(request.session.sid)
                request.session.context = dict(user.context_get())
                
                # Get redirect URL
                redirect_url = request.session.pop('wechat_login_redirect', '/web')
                
                return request.redirect(redirect_url)
            else:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Failed to authenticate WeChat user')
                })
                
        except Exception as e:
            _logger.error(f"WeChat login callback error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/phone/collect', type='http', auth='public', methods=['GET'], csrf=False)
    def phone_collect_with_login(self, oauth_id=None, auto_login=None, **kwargs):
        """
        Display phone number collection form with auto-login support
        """
        try:
            if not oauth_id:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('OAuth session not found')
                })
            
            oauth_record = request.env['wechat.oauth'].sudo().browse(int(oauth_id))
            if not oauth_record.exists():
                return request.render('wechat_official_account.auth_error', {
                    'error': _('OAuth session expired')
                })
            
            return request.render('wechat_official_account.phone_collect_login_form', {
                'oauth_record': oauth_record,
                'auto_login': bool(auto_login)
            })
            
        except Exception as e:
            _logger.error(f"Phone collect with login error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    @http.route('/wechat/phone/submit', type='http', auth='public', methods=['POST'], csrf=False)
    def phone_submit_with_login(self, oauth_id=None, phone_number=None, auto_login=None, **kwargs):
        """
        Submit phone number and auto-login if requested
        """
        try:
            if not oauth_id or not phone_number:
                return request.render('wechat_official_account.auth_error', {
                    'error': _('Missing required information')
                })
            
            oauth_record = request.env['wechat.oauth'].sudo().browse(int(oauth_id))
            if not oauth_record.exists():
                return request.render('wechat_official_account.auth_error', {
                    'error': _('OAuth session expired')
                })
            
            # Validate phone number format
            import re
            if not re.match(r'^1[3-9]\d{9}$', phone_number):
                return request.render('wechat_official_account.phone_collect_login_form', {
                    'oauth_record': oauth_record,
                    'auto_login': bool(auto_login),
                    'error': _('Invalid phone number format')
                })
            
            # Update OAuth record with phone number
            oauth_record.write({
                'phone_number': phone_number,
                'phone_verified': True,
            })
            
            # If auto_login is requested, authenticate the user
            if auto_login:
                user = self._authenticate_wechat_user(oauth_record)
                
                if user:
                    # Login the user (Odoo 18 compatible)
                    # 直接设置session，绕过密码验证
                    request.session.uid = user.id
                    request.session.login = user.login
                    request.session.session_token = user._compute_session_token(request.session.sid)
                    request.session.context = dict(user.context_get())

                    # Get redirect URL
                    redirect_url = request.session.pop('wechat_login_redirect', '/web')

                    return request.redirect(redirect_url)
                else:
                    return request.render('wechat_official_account.auth_error', {
                        'error': _('Failed to authenticate WeChat user')
                    })
            else:
                # Regular phone collection success
                return request.render('wechat_official_account.phone_success', {
                    'oauth_record': oauth_record
                })
                
        except Exception as e:
            _logger.error(f"Phone submit with login error: {e}")
            return request.render('wechat_official_account.auth_error', {
                'error': str(e)
            })

    def _authenticate_wechat_user(self, oauth_record):
        """Authenticate WeChat user and return Odoo user"""
        try:
            auth_provider = request.env['wechat.auth.provider'].sudo()
            
            # Prepare user info
            user_info = {
                'nickname': oauth_record.nickname,
                'sex': int(oauth_record.sex) if oauth_record.sex else 0,
                'city': oauth_record.city,
                'country': oauth_record.country,
                'province': oauth_record.province,
                'language': oauth_record.language,
                'headimgurl': oauth_record.headimgurl,
                'unionid': oauth_record.unionid,
            }
            
            # Authenticate user
            user = auth_provider.authenticate_wechat_user(
                openid=oauth_record.openid,
                config_id=oauth_record.config_id.id,
                user_info=user_info,
                phone_number=oauth_record.phone_number
            )
            
            return user
            
        except Exception as e:
            _logger.error(f"WeChat user authentication error: {e}")
            return False

    @http.route('/wechat/auth/logout', type='http', auth='user', methods=['GET'], csrf=False)
    def wechat_logout(self, redirect=None, **kwargs):
        """WeChat logout"""
        try:
            # Get current user's WeChat info for logging
            current_user = request.env.user
            wechat_user = request.env['wechat.user'].search([
                ('user_id', '=', current_user.id)
            ], limit=1)
            
            if wechat_user:
                _logger.info(f"WeChat user {wechat_user.openid} logged out")
            
            # Standard logout
            request.session.logout(keep_db=True)
            
            # Redirect
            redirect_url = redirect or '/web/login'
            return request.redirect(redirect_url)
            
        except Exception as e:
            _logger.error(f"WeChat logout error: {e}")
            return request.redirect('/web/login')

    @http.route('/wechat/auth/user/info', type='json', auth='user', methods=['POST'])
    def get_current_user_wechat_info(self, **kwargs):
        """Get current user's WeChat information"""
        try:
            current_user = request.env.user
            wechat_user = request.env['wechat.user'].search([
                ('user_id', '=', current_user.id)
            ], limit=1)
            
            if wechat_user:
                return {
                    'success': True,
                    'data': {
                        'openid': wechat_user.openid,
                        'unionid': wechat_user.unionid,
                        'nickname': wechat_user.nickname,
                        'phone': wechat_user.phone,
                        'city': wechat_user.city,
                        'province': wechat_user.province,
                        'headimgurl': wechat_user.headimgurl,
                        'subscribe_time': wechat_user.subscribe_time.isoformat() if wechat_user.subscribe_time else None,
                    }
                }
            else:
                return {'success': False, 'error': _('No WeChat information found')}
                
        except Exception as e:
            _logger.error(f"Get user WeChat info error: {e}")
            return {'success': False, 'error': str(e)}


class WeChatHomeController(Home):
    """Extend Home controller to add WeChat login option"""
    
    @http.route('/web/login', type='http', auth="none")
    def web_login(self, redirect=None, **kw):
        """Override login page to add WeChat login options"""
        response = super().web_login(redirect=redirect, **kw)
        
        # Add WeChat login options to the context
        if hasattr(response, 'qcontext'):
            # Get available WeChat auth providers
            wechat_providers = request.env['wechat.auth.provider'].sudo().search([
                ('enabled', '=', True)
            ])
            
            response.qcontext['wechat_providers'] = wechat_providers
            response.qcontext['wechat_login_redirect'] = redirect
        
        return response


class WeChatSignupController(AuthSignupHome):
    """Extend Signup controller for WeChat integration"""
    
    @http.route('/web/signup', type='http', auth='public', website=True, sitemap=False)
    def web_auth_signup(self, *args, **kw):
        """Override signup page to add WeChat signup options"""
        response = super().web_auth_signup(*args, **kw)
        
        # Add WeChat signup options
        if hasattr(response, 'qcontext'):
            wechat_providers = request.env['wechat.auth.provider'].sudo().search([
                ('enabled', '=', True),
                ('auto_create_user', '=', True)
            ])
            
            response.qcontext['wechat_providers'] = wechat_providers
        
        return response
