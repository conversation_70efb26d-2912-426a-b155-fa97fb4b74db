# -*- coding: utf-8 -*-

import logging
from odoo import http, _
from odoo.http import request

_logger = logging.getLogger(__name__)


class WeChatDebugController(http.Controller):
    """WeChat Debug Controller for testing OAuth and other features"""

    @http.route('/wechat/debug/oauth/<int:config_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def debug_oauth(self, config_id, scope='snsapi_userinfo', **kwargs):
        """Debug OAuth URL generation"""
        try:
            # Get WeChat configuration
            config = request.env['wechat.config'].sudo().browse(config_id)
            if not config.exists():
                return f"<h1>Error: WeChat configuration {config_id} not found</h1>"
            
            # Generate OAuth URL
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = f"{base_url}/wechat/oauth/callback/{config_id}"
            
            # Test different scope values
            test_scopes = ['snsapi_userinfo', 'snsapi_base', '', None, 'invalid_scope']
            results = []
            
            for test_scope in test_scopes:
                try:
                    oauth_url = request.env['wechat.oauth'].sudo().generate_oauth_url(
                        config_id=config_id,
                        redirect_uri=redirect_uri,
                        scope=test_scope,
                        state='debug_test'
                    )
                    results.append({
                        'scope': test_scope,
                        'url': oauth_url,
                        'status': 'success'
                    })
                except Exception as e:
                    results.append({
                        'scope': test_scope,
                        'url': None,
                        'status': f'error: {str(e)}'
                    })
            
            # Generate HTML response
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>WeChat OAuth Debug</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .config {{ background: #f0f0f0; padding: 10px; margin: 10px 0; }}
                    .test {{ border: 1px solid #ccc; margin: 10px 0; padding: 10px; }}
                    .success {{ background: #d4edda; }}
                    .error {{ background: #f8d7da; }}
                    .url {{ word-break: break-all; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>WeChat OAuth Debug</h1>
                
                <div class="config">
                    <h2>Configuration</h2>
                    <p><strong>Config ID:</strong> {config.id}</p>
                    <p><strong>Name:</strong> {config.name}</p>
                    <p><strong>App ID:</strong> {config.app_id}</p>
                    <p><strong>Active:</strong> {config.active}</p>
                    <p><strong>Redirect URI:</strong> {redirect_uri}</p>
                </div>
                
                <h2>OAuth URL Tests</h2>
            """
            
            for result in results:
                status_class = 'success' if result['status'] == 'success' else 'error'
                html += f"""
                <div class="test {status_class}">
                    <h3>Scope: '{result['scope']}'</h3>
                    <p><strong>Status:</strong> {result['status']}</p>
                """
                
                if result['url']:
                    html += f"""
                    <p><strong>Generated URL:</strong></p>
                    <div class="url">{result['url']}</div>
                    <p><a href="{result['url']}" target="_blank">Test This URL</a></p>
                    """
                
                html += "</div>"
            
            html += """
                <h2>Manual Test</h2>
                <form method="get">
                    <label>Custom Scope: 
                        <input type="text" name="scope" value="snsapi_userinfo" />
                    </label>
                    <button type="submit">Test</button>
                </form>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            _logger.error(f"Debug OAuth error: {e}")
            return f"<h1>Error: {str(e)}</h1>"

    @http.route('/wechat/debug/config', type='http', auth='public', methods=['GET'], csrf=False)
    def debug_config(self, **kwargs):
        """Debug WeChat configurations"""
        try:
            configs = request.env['wechat.config'].sudo().search([])
            
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>WeChat Config Debug</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .config { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
                    .active { background: #d4edda; }
                    .inactive { background: #f8d7da; }
                </style>
            </head>
            <body>
                <h1>WeChat Configurations</h1>
            """
            
            for config in configs:
                status_class = 'active' if config.active else 'inactive'
                html += f"""
                <div class="config {status_class}">
                    <h2>{config.name}</h2>
                    <p><strong>ID:</strong> {config.id}</p>
                    <p><strong>App ID:</strong> {config.app_id}</p>
                    <p><strong>Active:</strong> {config.active}</p>
                    <p><strong>Access Token:</strong> {'Set' if config.access_token else 'Not Set'}</p>
                    <p><strong>Token Expires:</strong> {config.access_token_expires}</p>
                    <p><a href="/wechat/debug/oauth/{config.id}">Test OAuth</a></p>
                </div>
                """
            
            html += """
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            _logger.error(f"Debug config error: {e}")
            return f"<h1>Error: {str(e)}</h1>"

    @http.route('/wechat/debug/test-scope', type='http', auth='public', methods=['GET'], csrf=False)
    def test_scope_issue(self, config_id=None, **kwargs):
        """Test the specific scope issue"""
        try:
            if not config_id:
                configs = request.env['wechat.config'].sudo().search([('active', '=', True)], limit=1)
                if not configs:
                    return "<h1>No active WeChat configuration found</h1>"
                config_id = configs.id
            else:
                config_id = int(config_id)
            
            # Test the exact scenario that might be causing the issue
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = f"{base_url}/wechat/oauth/callback/{config_id}"
            
            # Test with various problematic inputs
            test_cases = [
                {'scope': None, 'description': 'None value'},
                {'scope': '', 'description': 'Empty string'},
                {'scope': ' ', 'description': 'Whitespace only'},
                {'scope': 'null', 'description': 'String "null"'},
                {'scope': 'undefined', 'description': 'String "undefined"'},
                {'scope': 'snsapi_userinfo', 'description': 'Valid scope'},
            ]
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>WeChat Scope Issue Test</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .test {{ border: 1px solid #ccc; margin: 10px 0; padding: 10px; }}
                    .success {{ background: #d4edda; }}
                    .error {{ background: #f8d7da; }}
                    pre {{ background: #f8f9fa; padding: 10px; overflow-x: auto; }}
                </style>
            </head>
            <body>
                <h1>WeChat Scope Issue Test</h1>
                <p><strong>Config ID:</strong> {config_id}</p>
                <p><strong>Redirect URI:</strong> {redirect_uri}</p>
                
                <h2>Test Results</h2>
            """
            
            for test_case in test_cases:
                try:
                    oauth_url = request.env['wechat.oauth'].sudo().generate_oauth_url(
                        config_id=config_id,
                        redirect_uri=redirect_uri,
                        scope=test_case['scope'],
                        state='scope_test'
                    )
                    
                    html += f"""
                    <div class="test success">
                        <h3>✅ {test_case['description']}</h3>
                        <p><strong>Input scope:</strong> {repr(test_case['scope'])}</p>
                        <p><strong>Generated URL:</strong></p>
                        <pre>{oauth_url}</pre>
                    </div>
                    """
                    
                except Exception as e:
                    html += f"""
                    <div class="test error">
                        <h3>❌ {test_case['description']}</h3>
                        <p><strong>Input scope:</strong> {repr(test_case['scope'])}</p>
                        <p><strong>Error:</strong> {str(e)}</p>
                    </div>
                    """
            
            html += """
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            _logger.error(f"Test scope issue error: {e}")
            return f"<h1>Error: {str(e)}</h1>"
