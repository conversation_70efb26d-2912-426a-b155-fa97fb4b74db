# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
from unittest.mock import patch, MagicMock


class TestWeChatMessage(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.WeChatConfig = self.env['wechat.config']
        self.WeChatMessage = self.env['wechat.message']
        self.WeChatUser = self.env['wechat.user']
        
        # Create test config
        self.config = self.WeChatConfig.create({
            'name': 'Test Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret',
            'token': 'test_token',
            'is_default': True,
        })
        
        # Create test user
        self.user = self.WeChatUser.create({
            'openid': 'test_openid_123',
            'nickname': 'Test User',
            'config_id': self.config.id,
            'subscribe': True,
        })
    
    def test_create_text_message(self):
        """Test creating a text message"""
        message = self.WeChatMessage.create({
            'subject': 'Test Text Message',
            'message_type': 'text',
            'content': 'Hello, this is a test message!',
            'recipient_type': 'single',
            'user_id': self.user.id,
            'config_id': self.config.id,
        })
        
        self.assertEqual(message.subject, 'Test Text Message')
        self.assertEqual(message.message_type, 'text')
        self.assertEqual(message.state, 'draft')
        self.assertEqual(message.total_recipients, 1)
    
    def test_message_content_validation(self):
        """Test message content validation"""
        # Test text message without content - should fail
        with self.assertRaises(ValidationError):
            self.WeChatMessage.create({
                'subject': 'Test Message',
                'message_type': 'text',
                'content': '',  # Empty content
                'recipient_type': 'single',
                'user_id': self.user.id,
                'config_id': self.config.id,
            })
        
        # Test image message without media_id - should fail
        with self.assertRaises(ValidationError):
            self.WeChatMessage.create({
                'subject': 'Test Image',
                'message_type': 'image',
                'media_id': '',  # Empty media_id
                'recipient_type': 'single',
                'user_id': self.user.id,
                'config_id': self.config.id,
            })
        
        # Test news message without title - should fail
        with self.assertRaises(ValidationError):
            self.WeChatMessage.create({
                'subject': 'Test News',
                'message_type': 'news',
                'title': '',  # Empty title
                'recipient_type': 'single',
                'user_id': self.user.id,
                'config_id': self.config.id,
            })
    
    def test_recipients_computation(self):
        """Test recipients computation for different types"""
        # Single recipient
        message_single = self.WeChatMessage.create({
            'subject': 'Single Message',
            'message_type': 'text',
            'content': 'Test content',
            'recipient_type': 'single',
            'user_id': self.user.id,
            'config_id': self.config.id,
        })
        self.assertEqual(message_single.total_recipients, 1)
        
        # Multiple recipients
        user2 = self.WeChatUser.create({
            'openid': 'test_openid_456',
            'nickname': 'Test User 2',
            'config_id': self.config.id,
            'subscribe': True,
        })
        
        message_multiple = self.WeChatMessage.create({
            'subject': 'Multiple Message',
            'message_type': 'text',
            'content': 'Test content',
            'recipient_type': 'multiple',
            'user_ids': [(6, 0, [self.user.id, user2.id])],
            'config_id': self.config.id,
        })
        self.assertEqual(message_multiple.total_recipients, 2)
        
        # All subscribers
        message_all = self.WeChatMessage.create({
            'subject': 'Broadcast Message',
            'message_type': 'text',
            'content': 'Test content',
            'recipient_type': 'all',
            'config_id': self.config.id,
        })
        self.assertEqual(message_all.total_recipients, 2)  # 2 subscribed users
    
    def test_user_id_onchange(self):
        """Test user_id onchange updates recipient_openid"""
        message = self.WeChatMessage.create({
            'subject': 'Test Message',
            'message_type': 'text',
            'content': 'Test content',
            'recipient_type': 'single',
            'config_id': self.config.id,
        })
        
        # Simulate onchange
        message.user_id = self.user.id
        message._onchange_user_id()
        
        self.assertEqual(message.recipient_openid, self.user.openid)
    
    def test_user_ids_onchange(self):
        """Test user_ids onchange updates recipient_openids"""
        user2 = self.WeChatUser.create({
            'openid': 'test_openid_456',
            'nickname': 'Test User 2',
            'config_id': self.config.id,
            'subscribe': True,
        })
        
        message = self.WeChatMessage.create({
            'subject': 'Test Message',
            'message_type': 'text',
            'content': 'Test content',
            'recipient_type': 'multiple',
            'config_id': self.config.id,
        })
        
        # Simulate onchange
        message.user_ids = [(6, 0, [self.user.id, user2.id])]
        message._onchange_user_ids()
        
        expected_openids = f"{self.user.openid}\n{user2.openid}"
        self.assertEqual(message.recipient_openids, expected_openids)
    
    @patch('requests.post')
    def test_send_single_message_success(self, mock_post):
        """Test successful single message sending"""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.json.return_value = {'errcode': 0}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Mock access token
        with patch.object(self.config, 'get_access_token', return_value='test_token'):
            message = self.WeChatMessage.create({
                'subject': 'Test Message',
                'message_type': 'text',
                'content': 'Hello World!',
                'recipient_type': 'single',
                'user_id': self.user.id,
                'config_id': self.config.id,
            })
            
            message.send_message()
            
            self.assertEqual(message.state, 'sent')
            self.assertEqual(message.sent_count, 1)
            self.assertEqual(message.failed_count, 0)
    
    @patch('requests.post')
    def test_send_single_message_failure(self, mock_post):
        """Test failed single message sending"""
        # Mock failed API response
        mock_response = MagicMock()
        mock_response.json.return_value = {'errcode': 40001, 'errmsg': 'Invalid access token'}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Mock access token
        with patch.object(self.config, 'get_access_token', return_value='test_token'):
            message = self.WeChatMessage.create({
                'subject': 'Test Message',
                'message_type': 'text',
                'content': 'Hello World!',
                'recipient_type': 'single',
                'user_id': self.user.id,
                'config_id': self.config.id,
            })
            
            message.send_message()
            
            self.assertEqual(message.state, 'failed')
            self.assertEqual(message.sent_count, 0)
            self.assertEqual(message.failed_count, 1)
    
    def test_prepare_custom_message_data(self):
        """Test preparing custom message data for different types"""
        # Test text message
        message_text = self.WeChatMessage.create({
            'subject': 'Text Message',
            'message_type': 'text',
            'content': 'Hello World!',
            'recipient_type': 'single',
            'user_id': self.user.id,
            'config_id': self.config.id,
        })
        
        data = message_text._prepare_custom_message_data('test_openid')
        expected_data = {
            'touser': 'test_openid',
            'msgtype': 'text',
            'text': {'content': 'Hello World!'}
        }
        self.assertEqual(data, expected_data)
        
        # Test image message
        message_image = self.WeChatMessage.create({
            'subject': 'Image Message',
            'message_type': 'image',
            'media_id': 'test_media_id',
            'recipient_type': 'single',
            'user_id': self.user.id,
            'config_id': self.config.id,
        })
        
        data = message_image._prepare_custom_message_data('test_openid')
        expected_data = {
            'touser': 'test_openid',
            'msgtype': 'image',
            'image': {'media_id': 'test_media_id'}
        }
        self.assertEqual(data, expected_data)
        
        # Test news message
        message_news = self.WeChatMessage.create({
            'subject': 'News Message',
            'message_type': 'news',
            'title': 'News Title',
            'description': 'News Description',
            'url': 'https://example.com',
            'pic_url': 'https://example.com/pic.jpg',
            'recipient_type': 'single',
            'user_id': self.user.id,
            'config_id': self.config.id,
        })
        
        data = message_news._prepare_custom_message_data('test_openid')
        expected_data = {
            'touser': 'test_openid',
            'msgtype': 'news',
            'news': {
                'articles': [{
                    'title': 'News Title',
                    'description': 'News Description',
                    'url': 'https://example.com',
                    'picurl': 'https://example.com/pic.jpg'
                }]
            }
        }
        self.assertEqual(data, expected_data)
    
    def test_send_message_state_validation(self):
        """Test that only draft messages can be sent"""
        message = self.WeChatMessage.create({
            'subject': 'Test Message',
            'message_type': 'text',
            'content': 'Hello World!',
            'recipient_type': 'single',
            'user_id': self.user.id,
            'config_id': self.config.id,
            'state': 'sent',  # Already sent
        })
        
        with self.assertRaises(UserError):
            message.send_message()
