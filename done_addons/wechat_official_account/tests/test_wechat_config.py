# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from unittest.mock import patch, MagicMock


class TestWeChatConfig(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.WeChatConfig = self.env['wechat.config']
        
    def test_create_wechat_config(self):
        """Test creating a WeChat configuration"""
        config = self.WeChatConfig.create({
            'name': 'Test WeChat Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret_123',
            'token': 'test_token_123',
            'is_default': True,
        })
        
        self.assertEqual(config.name, 'Test WeChat Config')
        self.assertEqual(config.app_id, 'wx1234567890abcdef')
        self.assertTrue(config.is_default)
        self.assertTrue(config.active)
    
    def test_default_config_constraint(self):
        """Test that only one default configuration is allowed"""
        # Create first default config
        config1 = self.WeChatConfig.create({
            'name': 'Config 1',
            'app_id': 'wx1111111111111111',
            'app_secret': 'secret1',
            'token': 'token1',
            'is_default': True,
        })
        
        # Try to create second default config - should fail
        with self.assertRaises(ValidationError):
            self.WeChatConfig.create({
                'name': 'Config 2',
                'app_id': 'wx2222222222222222',
                'app_secret': 'secret2',
                'token': 'token2',
                'is_default': True,
            })
    
    def test_webhook_url_computation(self):
        """Test webhook URL computation"""
        config = self.WeChatConfig.create({
            'name': 'Test Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret',
            'token': 'test_token',
        })
        
        # Check that webhook URL is computed
        self.assertTrue(config.webhook_url)
        self.assertIn(str(config.id), config.webhook_url)
        self.assertIn('/wechat/webhook/', config.webhook_url)
    
    @patch('requests.get')
    def test_access_token_refresh(self, mock_get):
        """Test access token refresh functionality"""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'access_token': 'test_access_token_123',
            'expires_in': 7200
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        config = self.WeChatConfig.create({
            'name': 'Test Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret',
            'token': 'test_token',
        })
        
        # Test access token refresh
        token = config.get_access_token()
        
        self.assertEqual(token, 'test_access_token_123')
        self.assertEqual(config.access_token, 'test_access_token_123')
        self.assertTrue(config.access_token_expires)
    
    def test_signature_verification(self):
        """Test webhook signature verification"""
        config = self.WeChatConfig.create({
            'name': 'Test Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret',
            'token': 'test_token',
        })
        
        # Test with known values
        timestamp = '1234567890'
        nonce = 'test_nonce'
        
        # Calculate expected signature
        import hashlib
        tmp_list = [config.token, timestamp, nonce]
        tmp_list.sort()
        tmp_str = ''.join(tmp_list)
        expected_signature = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()
        
        # Test verification
        result = config.verify_webhook_signature(expected_signature, timestamp, nonce)
        self.assertTrue(result)
        
        # Test with wrong signature
        result = config.verify_webhook_signature('wrong_signature', timestamp, nonce)
        self.assertFalse(result)
    
    def test_get_default_config(self):
        """Test getting default configuration"""
        # Create non-default config
        config1 = self.WeChatConfig.create({
            'name': 'Config 1',
            'app_id': 'wx1111111111111111',
            'app_secret': 'secret1',
            'token': 'token1',
            'is_default': False,
        })
        
        # Create default config
        config2 = self.WeChatConfig.create({
            'name': 'Config 2',
            'app_id': 'wx2222222222222222',
            'app_secret': 'secret2',
            'token': 'token2',
            'is_default': True,
        })
        
        # Test getting default config
        default_config = self.WeChatConfig.get_default_config()
        self.assertEqual(default_config.id, config2.id)
    
    def test_total_users_computation(self):
        """Test total users computation"""
        config = self.WeChatConfig.create({
            'name': 'Test Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret',
            'token': 'test_token',
        })
        
        # Create some users
        self.env['wechat.user'].create({
            'openid': 'user1',
            'nickname': 'User 1',
            'config_id': config.id,
        })
        
        self.env['wechat.user'].create({
            'openid': 'user2',
            'nickname': 'User 2',
            'config_id': config.id,
        })
        
        # Trigger computation
        config._compute_total_users()
        
        self.assertEqual(config.total_users, 2)
