# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
from unittest.mock import patch, MagicMock


class TestWeChatUser(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.WeChatConfig = self.env['wechat.config']
        self.WeChatUser = self.env['wechat.user']
        
        # Create test config
        self.config = self.WeChatConfig.create({
            'name': 'Test Config',
            'app_id': 'wx1234567890abcdef',
            'app_secret': 'test_secret',
            'token': 'test_token',
            'is_default': True,
        })
    
    def test_create_wechat_user(self):
        """Test creating a WeChat user"""
        user = self.WeChatUser.create({
            'openid': 'test_openid_123',
            'nickname': 'Test User',
            'sex': '1',
            'city': 'Beijing',
            'country': 'China',
            'province': 'Beijing',
            'config_id': self.config.id,
            'subscribe': True,
        })
        
        self.assertEqual(user.openid, 'test_openid_123')
        self.assertEqual(user.nickname, 'Test User')
        self.assertEqual(user.sex, '1')
        self.assertTrue(user.subscribe)
        self.assertEqual(user.config_id.id, self.config.id)
    
    def test_openid_config_unique_constraint(self):
        """Test that OpenID must be unique per configuration"""
        # Create first user
        user1 = self.WeChatUser.create({
            'openid': 'test_openid_123',
            'nickname': 'User 1',
            'config_id': self.config.id,
        })
        
        # Try to create second user with same OpenID and config - should fail
        with self.assertRaises(Exception):  # IntegrityError wrapped in different exceptions
            self.WeChatUser.create({
                'openid': 'test_openid_123',
                'nickname': 'User 2',
                'config_id': self.config.id,
            })
    
    def test_prepare_user_values(self):
        """Test preparing user values from WeChat user info"""
        user_info = {
            'nickname': 'Test User',
            'sex': 1,
            'city': 'Shanghai',
            'country': 'China',
            'province': 'Shanghai',
            'language': 'zh_CN',
            'headimgurl': 'http://example.com/avatar.jpg',
            'subscribe': 1,
            'subscribe_time': 1234567890,
            'unionid': 'test_unionid',
            'tagid_list': [1, 2, 3],
            'groupid': 100,
        }
        
        user = self.WeChatUser.create({
            'openid': 'test_openid',
            'config_id': self.config.id,
        })
        
        values = user._prepare_user_values(user_info)
        
        self.assertEqual(values['nickname'], 'Test User')
        self.assertEqual(values['sex'], 1)
        self.assertEqual(values['city'], 'Shanghai')
        self.assertEqual(values['unionid'], 'test_unionid')
        self.assertEqual(values['tagid_list'], '1,2,3')
        self.assertEqual(values['group_id'], 100)
    
    @patch('requests.get')
    def test_get_user_info_from_wechat_success(self, mock_get):
        """Test getting user info from WeChat API successfully"""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'openid': 'test_openid',
            'nickname': 'Test User',
            'sex': 1,
            'city': 'Beijing',
            'subscribe': 1,
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock access token
        with patch.object(self.config, 'get_access_token', return_value='test_token'):
            user = self.WeChatUser.create({
                'openid': 'test_openid',
                'config_id': self.config.id,
            })
            
            user_info = user._get_user_info_from_wechat('test_openid', self.config.id)
            
            self.assertEqual(user_info['openid'], 'test_openid')
            self.assertEqual(user_info['nickname'], 'Test User')
            self.assertEqual(user_info['sex'], 1)
    
    @patch('requests.get')
    def test_get_user_info_from_wechat_failure(self, mock_get):
        """Test getting user info from WeChat API with failure"""
        # Mock failed API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'errcode': 40001,
            'errmsg': 'Invalid access token'
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock access token
        with patch.object(self.config, 'get_access_token', return_value='test_token'):
            user = self.WeChatUser.create({
                'openid': 'test_openid',
                'config_id': self.config.id,
            })
            
            user_info = user._get_user_info_from_wechat('test_openid', self.config.id)
            
            # Should return minimal info on failure
            self.assertEqual(user_info['openid'], 'test_openid')
            self.assertEqual(user_info['subscribe'], 0)
    
    def test_create_or_update_user_new(self):
        """Test creating a new user via create_or_update_user"""
        user_info = {
            'openid': 'new_openid',
            'nickname': 'New User',
            'subscribe': 1,
        }
        
        with patch.object(self.WeChatUser, '_get_user_info_from_wechat', return_value=user_info):
            user = self.WeChatUser.create_or_update_user('new_openid', self.config.id, user_info)
            
            self.assertEqual(user.openid, 'new_openid')
            self.assertEqual(user.nickname, 'New User')
            self.assertTrue(user.subscribe)
    
    def test_create_or_update_user_existing(self):
        """Test updating an existing user via create_or_update_user"""
        # Create existing user
        existing_user = self.WeChatUser.create({
            'openid': 'existing_openid',
            'nickname': 'Old Name',
            'config_id': self.config.id,
        })
        
        user_info = {
            'openid': 'existing_openid',
            'nickname': 'Updated Name',
            'subscribe': 1,
        }
        
        with patch.object(self.WeChatUser, '_get_user_info_from_wechat', return_value=user_info):
            user = self.WeChatUser.create_or_update_user('existing_openid', self.config.id, user_info)
            
            self.assertEqual(user.id, existing_user.id)  # Same user
            self.assertEqual(user.nickname, 'Updated Name')  # Updated
    
    def test_subscribe_unsubscribe_user(self):
        """Test subscribe and unsubscribe user methods"""
        user = self.WeChatUser.create({
            'openid': 'test_openid',
            'nickname': 'Test User',
            'config_id': self.config.id,
            'subscribe': True,
        })
        
        # Test unsubscribe
        user.unsubscribe_user()
        self.assertFalse(user.subscribe)
        self.assertTrue(user.unsubscribe_time)
        
        # Test subscribe
        user.subscribe_user()
        self.assertTrue(user.subscribe)
        self.assertTrue(user.subscribe_time)
        self.assertFalse(user.unsubscribe_time)
    
    def test_create_partner(self):
        """Test creating a related partner"""
        user = self.WeChatUser.create({
            'openid': 'test_openid',
            'nickname': 'Test User',
            'city': 'Beijing',
            'country': 'China',
            'config_id': self.config.id,
        })
        
        # Test creating partner
        result = user.create_partner()
        
        self.assertTrue(user.partner_id)
        self.assertEqual(user.partner_id.name, 'Test User')
        self.assertIn('test_openid', user.partner_id.comment)
        
        # Test that action returns correct window action
        self.assertEqual(result['type'], 'ir.actions.act_window')
        self.assertEqual(result['res_model'], 'res.partner')
        self.assertEqual(result['res_id'], user.partner_id.id)
    
    def test_create_partner_already_exists(self):
        """Test creating partner when one already exists"""
        partner = self.env['res.partner'].create({
            'name': 'Existing Partner',
        })
        
        user = self.WeChatUser.create({
            'openid': 'test_openid',
            'nickname': 'Test User',
            'config_id': self.config.id,
            'partner_id': partner.id,
        })
        
        # Should raise error when partner already exists
        with self.assertRaises(UserError):
            user.create_partner()
    
    def test_action_view_partner(self):
        """Test action to view related partner"""
        partner = self.env['res.partner'].create({
            'name': 'Test Partner',
        })
        
        user = self.WeChatUser.create({
            'openid': 'test_openid',
            'nickname': 'Test User',
            'config_id': self.config.id,
            'partner_id': partner.id,
        })
        
        result = user.action_view_partner()
        
        self.assertEqual(result['type'], 'ir.actions.act_window')
        self.assertEqual(result['res_model'], 'res.partner')
        self.assertEqual(result['res_id'], partner.id)
    
    def test_action_view_partner_no_partner(self):
        """Test action to view partner when no partner exists"""
        user = self.WeChatUser.create({
            'openid': 'test_openid',
            'nickname': 'Test User',
            'config_id': self.config.id,
        })
        
        with self.assertRaises(UserError):
            user.action_view_partner()
    
    @patch('requests.get')
    def test_sync_users_from_wechat(self, mock_get):
        """Test syncing users from WeChat API"""
        # Mock API response with user list
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'data': {
                'openid': ['openid1', 'openid2', 'openid3']
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock access token and user info
        with patch.object(self.config, 'get_access_token', return_value='test_token'):
            with patch.object(self.WeChatUser, 'create_or_update_user') as mock_create_update:
                self.WeChatUser.sync_users_from_wechat(self.config.id)
                
                # Should call create_or_update_user for each openid
                self.assertEqual(mock_create_update.call_count, 3)
                mock_create_update.assert_any_call('openid1', self.config.id)
                mock_create_update.assert_any_call('openid2', self.config.id)
                mock_create_update.assert_any_call('openid3', self.config.id)
