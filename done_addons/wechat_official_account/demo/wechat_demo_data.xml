<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo WeChat Configuration -->
        <record id="demo_wechat_config" model="wechat.config">
            <field name="name">Demo WeChat Official Account</field>
            <field name="app_id">wx1234567890abcdef</field>
            <field name="app_secret">demo_secret_key_1234567890abcdef</field>
            <field name="token">demo_token_123</field>
            <field name="encoding_aes_key">demo_aes_key_1234567890abcdef1234567890abcdef</field>
            <field name="is_default">True</field>
            <field name="active">True</field>
        </record>
        
        <!-- Demo WeChat Users -->
        <record id="demo_wechat_user_1" model="wechat.user">
            <field name="openid">demo_openid_user_001</field>
            <field name="unionid">demo_unionid_001</field>
            <field name="nickname">张三</field>
            <field name="sex">1</field>
            <field name="city">北京</field>
            <field name="country">中国</field>
            <field name="province">北京</field>
            <field name="language">zh_CN</field>
            <field name="subscribe">True</field>
            <field name="subscribe_time" eval="(datetime.datetime.now() - datetime.timedelta(days=30))"/>
            <field name="config_id" ref="demo_wechat_config"/>
            <field name="message_count">5</field>
            <field name="remark">活跃用户，经常互动</field>
        </record>
        
        <record id="demo_wechat_user_2" model="wechat.user">
            <field name="openid">demo_openid_user_002</field>
            <field name="unionid">demo_unionid_002</field>
            <field name="nickname">李四</field>
            <field name="sex">2</field>
            <field name="city">上海</field>
            <field name="country">中国</field>
            <field name="province">上海</field>
            <field name="language">zh_CN</field>
            <field name="subscribe">True</field>
            <field name="subscribe_time" eval="(datetime.datetime.now() - datetime.timedelta(days=15))"/>
            <field name="config_id" ref="demo_wechat_config"/>
            <field name="message_count">2</field>
            <field name="remark">新用户，潜在客户</field>
        </record>
        
        <record id="demo_wechat_user_3" model="wechat.user">
            <field name="openid">demo_openid_user_003</field>
            <field name="nickname">王五</field>
            <field name="sex">1</field>
            <field name="city">广州</field>
            <field name="country">中国</field>
            <field name="province">广东</field>
            <field name="language">zh_CN</field>
            <field name="subscribe">False</field>
            <field name="subscribe_time" eval="(datetime.datetime.now() - datetime.timedelta(days=60))"/>
            <field name="unsubscribe_time" eval="(datetime.datetime.now() - datetime.timedelta(days=10))"/>
            <field name="config_id" ref="demo_wechat_config"/>
            <field name="message_count">1</field>
            <field name="remark">已取消关注</field>
        </record>
        
        <!-- Demo WeChat Messages -->
        <record id="demo_message_welcome" model="wechat.message">
            <field name="subject">欢迎新用户</field>
            <field name="message_type">text</field>
            <field name="recipient_type">single</field>
            <field name="content">欢迎关注我们的微信公众号！🎉

感谢您的关注，这里您可以：
• 获取最新产品信息
• 享受专属优惠活动
• 获得客服支持

回复"菜单"查看更多功能</field>
            <field name="config_id" ref="demo_wechat_config"/>
            <field name="user_id" ref="demo_wechat_user_2"/>
            <field name="recipient_openid">demo_openid_user_002</field>
            <field name="state">sent</field>
            <field name="send_time" eval="(datetime.datetime.now() - datetime.timedelta(days=15))"/>
            <field name="sent_count">1</field>
            <field name="total_recipients">1</field>
        </record>
        
        <record id="demo_message_promotion" model="wechat.message">
            <field name="subject">春季促销活动</field>
            <field name="message_type">text</field>
            <field name="recipient_type">all</field>
            <field name="content">🌸 春季大促销！🌸

全场商品8折起！
• 满200减50
• 满500减150
• 满1000减300

活动时间：即日起至月底
立即购买：www.example.com</field>
            <field name="config_id" ref="demo_wechat_config"/>
            <field name="state">sent</field>
            <field name="send_time" eval="(datetime.datetime.now() - datetime.timedelta(days=7))"/>
            <field name="sent_count">2</field>
            <field name="total_recipients">2</field>
        </record>
        
        <record id="demo_message_news" model="wechat.message">
            <field name="subject">公司新闻发布</field>
            <field name="message_type">news</field>
            <field name="recipient_type">all</field>
            <field name="title">我们获得了行业大奖！</field>
            <field name="description">很高兴宣布我们公司在本年度行业评选中获得"最佳创新奖"，这是对我们团队努力的认可。</field>
            <field name="url">https://www.example.com/news/award-2024</field>
            <field name="pic_url">https://www.example.com/images/award-banner.jpg</field>
            <field name="config_id" ref="demo_wechat_config"/>
            <field name="state">draft</field>
            <field name="total_recipients">2</field>
        </record>
        
    </data>
</odoo>
