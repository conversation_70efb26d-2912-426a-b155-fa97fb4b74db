# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wechat_official_account
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: 2024-01-01 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"

#. module: wechat_official_account
#: model:ir.model,name:wechat_official_account.model_wechat_config
msgid "WeChat Official Account Configuration"
msgstr "微信公众号配置"

#. module: wechat_official_account
#: model:ir.model,name:wechat_official_account.model_wechat_message
msgid "WeChat Message"
msgstr "微信消息"

#. module: wechat_official_account
#: model:ir.model,name:wechat_official_account.model_wechat_user
msgid "WeChat User"
msgstr "微信用户"

#. module: wechat_official_account
#: model:ir.model,name:wechat_official_account.model_wechat_config_wizard
msgid "WeChat Configuration Wizard"
msgstr "微信配置向导"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__name
msgid "Configuration Name"
msgstr "配置名称"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__app_id
msgid "AppID"
msgstr "应用ID"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__app_secret
msgid "AppSecret"
msgstr "应用密钥"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__token
msgid "Token"
msgstr "令牌"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__encoding_aes_key
msgid "EncodingAESKey"
msgstr "消息加密密钥"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__access_token_expires
msgid "Access Token Expires"
msgstr "访问令牌过期时间"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__active
msgid "Active"
msgstr "启用"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__is_default
msgid "Default Configuration"
msgstr "默认配置"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__webhook_url
msgid "Webhook URL"
msgstr "回调地址"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__total_messages_sent
msgid "Total Messages Sent"
msgstr "已发送消息总数"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__total_users
msgid "Total Users"
msgstr "用户总数"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_config__company_id
msgid "Company"
msgstr "公司"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__name
msgid "Subject"
msgstr "主题"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__config_id
msgid "WeChat Configuration"
msgstr "微信配置"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__message_type
msgid "Message Type"
msgstr "消息类型"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__recipient_type
msgid "Recipient Type"
msgstr "接收者类型"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__content
msgid "Message Content"
msgstr "消息内容"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__state
msgid "Status"
msgstr "状态"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__send_time
msgid "Send Time"
msgstr "发送时间"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__sent_count
msgid "Sent Count"
msgstr "发送成功数"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__failed_count
msgid "Failed Count"
msgstr "发送失败数"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_message__error_message
msgid "Error Message"
msgstr "错误信息"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__openid
msgid "OpenID"
msgstr "用户标识"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__nickname
msgid "Nickname"
msgstr "昵称"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__sex
msgid "Gender"
msgstr "性别"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__city
msgid "City"
msgstr "城市"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__country
msgid "Country"
msgstr "国家"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__province
msgid "Province"
msgstr "省份"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__language
msgid "Language"
msgstr "语言"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__headimgurl
msgid "Avatar URL"
msgstr "头像地址"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__subscribe
msgid "Subscribed"
msgstr "已关注"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__subscribe_time
msgid "Subscribe Time"
msgstr "关注时间"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__unsubscribe_time
msgid "Unsubscribe Time"
msgstr "取消关注时间"

#. module: wechat_official_account
#: model:ir.model.fields,field_description:wechat_official_account.field_wechat_user__partner_id
msgid "Related Partner"
msgstr "关联业务伙伴"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__message_type
msgid "Text"
msgstr "文本"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__message_type
msgid "Image"
msgstr "图片"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__message_type
msgid "Voice"
msgstr "语音"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__message_type
msgid "Video"
msgstr "视频"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__message_type
msgid "News"
msgstr "图文"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__recipient_type
msgid "Single User"
msgstr "单个用户"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__recipient_type
msgid "Multiple Users"
msgstr "多个用户"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__recipient_type
msgid "All Users"
msgstr "全部用户"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__recipient_type
msgid "By Tag"
msgstr "按标签"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__recipient_type
msgid "By Group"
msgstr "按分组"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__state
msgid "Draft"
msgstr "草稿"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__state
msgid "Sent"
msgstr "已发送"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__state
msgid "Failed"
msgstr "发送失败"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_message__state
msgid "Partial"
msgstr "部分发送"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_user__sex
msgid "Unknown"
msgstr "未知"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_user__sex
msgid "Male"
msgstr "男"

#. module: wechat_official_account
#: model:ir.model.fields,selection:wechat_official_account.field_wechat_user__sex
msgid "Female"
msgstr "女"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "Only one default WeChat configuration is allowed."
msgstr "只允许设置一个默认微信配置。"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "Failed to get WeChat access token: %s"
msgstr "获取微信访问令牌失败：%s"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "Failed to connect to WeChat API: %s"
msgstr "连接微信API失败：%s"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "Success"
msgstr "成功"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "WeChat API connection successful!"
msgstr "微信API连接成功！"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "Error"
msgstr "错误"

#. module: wechat_official_account
#: code:addons/wechat_official_account/models/wechat_config.py:0
#, python-format
msgid "WeChat API connection failed: %s"
msgstr "微信API连接失败：%s"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_form_view
msgid "Test Connection"
msgstr "测试连接"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_form_view
msgid "Refresh Token"
msgstr "刷新令牌"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_message_form_view
msgid "Send Message"
msgstr "发送消息"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_message_form_view
msgid "Send Test"
msgstr "测试发送"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_user_form_view
msgid "Create Partner"
msgstr "创建业务伙伴"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_user_form_view
msgid "Partner"
msgstr "业务伙伴"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "WeChat Configuration Wizard"
msgstr "微信配置向导"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Welcome to WeChat Official Account Setup!"
msgstr "欢迎使用微信公众号配置向导！"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Enter Your WeChat Configuration"
msgstr "输入您的微信配置"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Configure Webhook in WeChat"
msgstr "在微信中配置回调地址"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Test Your Configuration"
msgstr "测试您的配置"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Setup Complete!"
msgstr "配置完成！"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Previous"
msgstr "上一步"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Next"
msgstr "下一步"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_wizard_form_view
msgid "Create Configuration"
msgstr "创建配置"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_message_list_view
msgid "WeChat Messages"
msgstr "微信消息"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_user_list_view
msgid "WeChat Users"
msgstr "微信用户"

#. module: wechat_official_account
#: model_terms:ir.ui.view,arch_db:wechat_official_account.wechat_config_list_view
msgid "WeChat Configurations"
msgstr "微信配置"

#. module: wechat_official_account
#: model_terms:ir.actions.act_window,name:wechat_official_account.wechat_config_action
msgid "WeChat Configuration"
msgstr "微信配置"

#. module: wechat_official_account
#: model_terms:ir.actions.act_window,name:wechat_official_account.wechat_message_action
msgid "WeChat Messages"
msgstr "微信消息"

#. module: wechat_official_account
#: model_terms:ir.actions.act_window,name:wechat_official_account.wechat_user_action
msgid "WeChat Users"
msgstr "微信用户"

#. module: wechat_official_account
#: model_terms:ir.ui.menu,name:wechat_official_account.wechat_main_menu
msgid "WeChat"
msgstr "微信"

#. module: wechat_official_account
#: model_terms:ir.ui.menu,name:wechat_official_account.wechat_config_menu
msgid "Configuration"
msgstr "配置"

#. module: wechat_official_account
#: model_terms:ir.ui.menu,name:wechat_official_account.wechat_message_menu
msgid "Messages"
msgstr "消息"

#. module: wechat_official_account
#: model_terms:ir.ui.menu,name:wechat_official_account.wechat_user_menu
msgid "Users"
msgstr "用户"
