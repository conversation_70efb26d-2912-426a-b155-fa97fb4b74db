<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 考勤位置配置列表视图 -->
    <record id="attendance_location_config_list_view" model="ir.ui.view">
        <field name="name">attendance.location.config.list</field>
        <field name="model">attendance.location.config</field>
        <field name="arch" type="xml">
            <list string="考勤位置配置" default_order="sequence, name">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="latitude"/>
                <field name="longitude"/>
                <field name="radius"/>
                <field name="address"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="checkin_count"/>
                <field name="active" widget="boolean_toggle"/>
                <button name="action_test_location" type="object" string="测试" 
                        icon="fa-map-marker" class="btn-link"/>
            </list>
        </field>
    </record>

    <!-- 考勤位置配置表单视图 -->
    <record id="attendance_location_config_form_view" model="ir.ui.view">
        <field name="name">attendance.location.config.form</field>
        <field name="model">attendance.location.config</field>
        <field name="arch" type="xml">
            <form string="考勤位置配置">
                <header>
                    <button name="action_test_location" type="object" string="测试位置" 
                            class="btn-primary" icon="fa-map-marker"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="已停用" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="位置名称"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info" string="基本信息">
                            <field name="active"/>
                            <field name="sequence"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group name="statistics" string="统计信息">
                            <field name="checkin_count"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="位置信息" name="location_info">
                            <group>
                                <group name="coordinates" string="坐标信息">
                                    <field name="latitude" widget="float" digits="[10,7]"/>
                                    <field name="longitude" widget="float" digits="[10,7]"/>
                                    <field name="radius"/>
                                </group>
                                <group name="address_info" string="地址信息">
                                    <field name="address" widget="text"/>
                                    <field name="description" widget="text"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="使用范围" name="usage_scope">
                            <group>
                                <group name="department_scope" string="部门范围">
                                    <field name="department_ids" widget="many2many_tags" 
                                           placeholder="留空则适用所有部门"/>
                                </group>
                                <group name="employee_scope" string="员工范围">
                                    <field name="employee_ids" widget="many2many_tags" 
                                           placeholder="留空则适用所有员工"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="时间限制" name="time_restriction">
                            <group>
                                <group>
                                    <field name="time_restriction"/>
                                    <field name="start_time" widget="float_time" 
                                           invisible="not time_restriction"/>
                                    <field name="end_time" widget="float_time" 
                                           invisible="not time_restriction"/>
                                </group>
                            </group>
                            <div class="alert alert-info" invisible="not time_restriction">
                                <strong>提示：</strong>时间限制仅在启用时生效。时间格式为24小时制，如：9.5表示9:30。
                            </div>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- 考勤位置配置搜索视图 -->
    <record id="attendance_location_config_search_view" model="ir.ui.view">
        <field name="name">attendance.location.config.search</field>
        <field name="model">attendance.location.config</field>
        <field name="arch" type="xml">
            <search string="考勤位置配置">
                <field name="name"/>
                <field name="address"/>
                <field name="company_id"/>
                <field name="department_ids"/>
                <field name="employee_ids"/>
                <separator/>
                <filter string="激活" name="active" domain="[('active', '=', True)]"/>
                <filter string="未激活" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="启用时间限制" name="time_restricted" 
                        domain="[('time_restriction', '=', True)]"/>
                <separator/>
                <filter string="我的公司" name="my_company" 
                        domain="[('company_id', '=', company_id)]"/>
                <group expand="0" string="分组">
                    <filter string="公司" name="group_by_company" 
                            context="{'group_by': 'company_id'}"/>
                    <filter string="激活状态" name="group_by_active" 
                            context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 考勤位置配置动作 -->
    <record id="attendance_location_config_action" model="ir.actions.act_window">
        <field name="name">考勤位置配置</field>
        <field name="res_model">attendance.location.config</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="attendance_location_config_search_view"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建第一个考勤位置配置
            </p>
            <p>
                配置允许员工打卡的地理位置范围，包括经纬度坐标和允许的半径范围。
            </p>
        </field>
    </record>

</odoo>
