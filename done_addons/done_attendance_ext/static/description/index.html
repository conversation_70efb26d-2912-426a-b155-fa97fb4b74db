<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Done Attendance Extension</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .feature i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #28a745;
        }
        .feature h3 {
            margin-bottom: 10px;
        }
        .screenshot {
            text-align: center;
            margin: 30px 0;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>Done Attendance Extension</h1>
        <p class="subtitle">基于GPS位置验证的智能考勤打卡系统</p>
        
        <div class="features">
            <div class="feature">
                <i class="fas fa-map-marker-alt"></i>
                <h3>GPS位置验证</h3>
                <p>精确的GPS定位，确保员工在指定区域内打卡</p>
            </div>
            
            <div class="feature">
                <i class="fas fa-mobile-alt"></i>
                <h3>移动端友好</h3>
                <p>响应式设计，完美支持手机和平板设备</p>
            </div>
            
            <div class="feature">
                <i class="fas fa-cogs"></i>
                <h3>灵活配置</h3>
                <p>支持多个打卡点配置，可设置不同的允许范围</p>
            </div>
            
            <div class="feature">
                <i class="fas fa-chart-line"></i>
                <h3>实时监控</h3>
                <p>实时显示位置状态，异常打卡自动标记</p>
            </div>
            
            <div class="feature">
                <i class="fas fa-shield-alt"></i>
                <h3>安全可靠</h3>
                <p>严格的权限控制，保护员工隐私和数据安全</p>
            </div>
            
            <div class="feature">
                <i class="fas fa-clock"></i>
                <h3>便捷打卡</h3>
                <p>一键打卡，自动记录时间和位置信息</p>
            </div>
        </div>
        
        <div class="screenshot">
            <h2>功能特色</h2>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><strong>智能位置验证</strong>：基于GPS坐标和设定半径进行位置合规性检查</li>
                <li><strong>可视化地图</strong>：实时显示员工当前位置和允许的打卡区域</li>
                <li><strong>异常监控</strong>：自动识别和标记异常位置的打卡记录</li>
                <li><strong>多点配置</strong>：支持配置多个办公地点，适应不同工作场景</li>
                <li><strong>时间限制</strong>：可设置特定时间段内的打卡限制</li>
                <li><strong>部门权限</strong>：支持按部门和员工设置不同的位置权限</li>
                <li><strong>详细记录</strong>：记录设备信息、位置精度等详细打卡数据</li>
                <li><strong>报表分析</strong>：提供位置异常考勤记录的专门视图和分析</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <h2>技术要求</h2>
            <p>• 支持HTML5地理定位的现代浏览器</p>
            <p>• HTTPS连接（生产环境推荐）</p>
            <p>• 互联网连接（用于地图显示）</p>
        </div>
    </div>
</body>
</html>
