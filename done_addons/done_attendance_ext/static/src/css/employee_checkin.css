/* 员工打卡页面样式 */

.employee-checkin-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.checkin-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.checkin-title {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.employee-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.employee-name {
    font-size: 1.2rem;
    font-weight: 500;
}

.current-time {
    font-size: 1.1rem;
    opacity: 0.9;
}

.checkin-content {
    max-width: 800px;
    margin: 0 auto;
}

/* 地图容器样式 */
.map-container {
    position: relative;
    height: 300px;
    margin-bottom: 30px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.map-display {
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 1.1rem;
}

.map-overlay {
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    background: rgba(255,255,255,0.95);
    border-radius: 10px;
    padding: 15px;
    backdrop-filter: blur(10px);
}

.location-info {
    font-size: 0.9rem;
}

.location-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
}

.location-status.success {
    color: #28a745;
}

.location-status.warning {
    color: #ffc107;
}

.location-status.error {
    color: #dc3545;
}

.location-address {
    color: #666;
    margin-bottom: 5px;
}

.location-accuracy {
    color: #888;
    font-size: 0.8rem;
}

/* 打卡按钮区域 */
.checkin-actions {
    text-align: center;
    margin-bottom: 40px;
}

.checkin-button {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: none;
    font-size: 1.2rem;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    background: linear-gradient(135deg, #28a745, #20c997);
}

.checkin-button:hover:not(:disabled) {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.3);
}

.checkin-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.checkin-button i {
    font-size: 2rem;
}

.checkin-status {
    margin-top: 15px;
    font-size: 1.1rem;
    color: white;
    min-height: 25px;
}

.location-validation {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    color: white;
}

.validation-result {
    font-weight: 500;
    margin-bottom: 8px;
}

.validation-result.success {
    color: #28a745;
}

.validation-result.error {
    color: #dc3545;
}

.validation-details {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 最近打卡记录 */
.recent-attendance {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 20px;
    color: white;
}

.recent-attendance h3 {
    margin-bottom: 15px;
    font-weight: 500;
}

.attendance-list {
    max-height: 200px;
    overflow-y: auto;
}

.attendance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.attendance-item:last-child {
    border-bottom: none;
}

.attendance-time {
    font-weight: 500;
}

.attendance-type {
    font-size: 0.9rem;
    opacity: 0.8;
}

.attendance-status {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.attendance-status.normal {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.attendance-status.outside {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.attendance-status.unknown {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
}

/* 设置按钮 */
.checkin-settings {
    position: fixed;
    top: 20px;
    right: 20px;
}

.checkin-settings .btn {
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 25px;
    padding: 8px 15px;
}

.checkin-settings .btn:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 20px;
    color: rgba(255,255,255,0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .employee-checkin-container {
        padding: 15px;
    }
    
    .checkin-title {
        font-size: 2rem;
    }
    
    .employee-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .map-container {
        height: 250px;
    }
    
    .checkin-button {
        width: 150px;
        height: 150px;
        font-size: 1rem;
    }
    
    .checkin-button i {
        font-size: 1.5rem;
    }
    
    .checkin-settings {
        position: static;
        text-align: center;
        margin-top: 20px;
    }
}

/* 模态框样式调整 */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
