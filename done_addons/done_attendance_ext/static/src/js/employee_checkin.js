/** @odoo-module **/

import { registry } from "@web/core/registry";
import { _t } from "@web/core/l10n/translation";

// 简化的客户端动作实现
class EmployeeCheckinAction {
    constructor(parent, action, options) {
        this.parent = parent;
        this.action = action;
        this.options = options;
        this.currentLocation = null;
        this.employee = null;
    }

    async start() {
        // 创建HTML结构
        this.renderHTML();

        // 初始化功能
        await this.initialize();

        return this;
    }

    renderHTML() {
        const html = `
            <div class="employee-checkin-container">
                <div class="checkin-header">
                    <h1 class="checkin-title">员工打卡</h1>
                    <div class="employee-info">
                        <span class="employee-name" id="employee-name">加载中...</span>
                        <span class="current-time" id="current-time"></span>
                    </div>
                </div>

                <div class="checkin-content">
                    <div class="map-container">
                        <div id="map" class="map-display">正在加载地图...</div>
                        <div class="map-overlay">
                            <div class="location-info" id="location-info">
                                <div class="location-status" id="location-status">
                                    <i class="fa fa-spinner fa-spin"></i>
                                    <span>正在获取位置...</span>
                                </div>
                                <div class="location-address" id="location-address"></div>
                                <div class="location-accuracy" id="location-accuracy"></div>
                            </div>
                        </div>
                    </div>

                    <div class="checkin-actions">
                        <button id="checkin-btn" class="btn btn-primary btn-lg checkin-button" disabled="true">
                            <i class="fa fa-clock-o"></i>
                            <span class="btn-text">获取位置中...</span>
                        </button>

                        <div class="checkin-status" id="checkin-status"></div>

                        <div class="location-validation" id="location-validation" style="display: none;">
                            <div class="validation-result" id="validation-result"></div>
                            <div class="validation-details" id="validation-details"></div>
                        </div>
                    </div>

                    <div class="recent-attendance">
                        <h3>最近打卡记录</h3>
                        <div class="attendance-list" id="attendance-list">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>

                <div class="checkin-settings">
                    <button class="btn btn-link" id="settings-btn">
                        <i class="fa fa-cog"></i>
                        设置
                    </button>
                </div>
            </div>
        `;

        document.body.innerHTML = html;
    }

    async initialize() {
        // 初始化时间显示
        this.updateCurrentTime();
        this.timeInterval = setInterval(() => {
            this.updateCurrentTime();
        }, 1000);

        // 获取当前员工信息
        await this.loadEmployeeInfo();

        // 获取位置权限并开始定位
        await this.requestLocationPermission();

        // 加载最近的打卡记录
        await this.loadRecentAttendance();

        // 绑定事件
        this.bindEvents();
    }

    destroy() {
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
        }
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
        }
    }

    // RPC调用辅助方法
    async rpc(params) {
        return new Promise((resolve, reject) => {
            odoo.define('web.ajax', function(require) {
                const ajax = require('web.ajax');
                ajax.rpc('/web/dataset/call_kw', {
                    model: params.model,
                    method: params.method,
                    args: params.args || [],
                    kwargs: params.kwargs || {}
                }).then(resolve).catch(reject);
            });
        });
    }

    // 显示通知
    showNotification(message, type = 'info') {
        if (window.odoo && window.odoo.notification) {
            window.odoo.notification.add(message, { type });
        } else {
            alert(message);
        }
    }

    updateCurrentTime() {
        const currentTime = new Date();
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = currentTime.toLocaleString('zh-CN');
        }
    }

    async loadEmployeeInfo() {
        try {
            // 获取当前用户信息
            const sessionInfo = await fetch('/web/session/get_session_info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            }).then(r => r.json());

            if (sessionInfo.uid) {
                const employee = await this.rpc({
                    model: 'hr.employee',
                    method: 'search_read',
                    args: [[['user_id', '=', sessionInfo.uid]]],
                    kwargs: {
                        fields: ['name', 'department_id', 'job_title'],
                        limit: 1
                    }
                });

                if (employee.length > 0) {
                    this.employee = employee[0];
                    const nameElement = document.getElementById('employee-name');
                    if (nameElement) {
                        nameElement.textContent = employee[0].name;
                    }
                }
            }
        } catch (error) {
            console.error('Failed to load employee info:', error);
            this.showNotification('获取员工信息失败', 'danger');
        }
    }

    async requestLocationPermission() {
        if (!navigator.geolocation) {
            this.updateLocationStatus('error', '您的浏览器不支持地理定位');
            return;
        }

        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        };

        try {
            // 先获取一次位置
            navigator.geolocation.getCurrentPosition(
                (position) => this.onLocationSuccess(position),
                (error) => this.onLocationError(error),
                options
            );

            // 持续监听位置变化
            this.watchId = navigator.geolocation.watchPosition(
                (position) => this.onLocationSuccess(position),
                (error) => this.onLocationError(error),
                options
            );
        } catch (error) {
            this.onLocationError(error);
        }
    }

    onLocationSuccess(position) {
        const { latitude, longitude, accuracy } = position.coords;

        this.currentLocation = {
            latitude,
            longitude,
            accuracy,
            timestamp: new Date(position.timestamp)
        };

        this.updateLocationDisplay();
        this.validateLocation();
    }

    onLocationError(error) {
        let message;
        switch (error.code) {
            case error.PERMISSION_DENIED:
                message = '位置访问被拒绝，请允许位置权限后刷新页面';
                break;
            case error.POSITION_UNAVAILABLE:
                message = '无法获取位置信息';
                break;
            case error.TIMEOUT:
                message = '获取位置超时，请检查网络连接';
                break;
            default:
                message = '获取位置时发生未知错误';
                break;
        }

        this.updateLocationStatus('error', message);
    }

    updateLocationDisplay() {
        const { latitude, longitude, accuracy } = this.currentLocation;

        // 更新地图显示（这里可以集成实际的地图API）
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <i class="fa fa-map-marker" style="font-size: 3rem; color: #007bff; margin-bottom: 10px;"></i>
                    <div>纬度: ${latitude.toFixed(6)}</div>
                    <div>经度: ${longitude.toFixed(6)}</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                        精度: ±${Math.round(accuracy)}米
                    </div>
                </div>
            `;
        }

        // 更新位置精度显示
        const accuracyElement = document.getElementById('location-accuracy');
        if (accuracyElement) {
            accuracyElement.textContent = `位置精度: ±${Math.round(accuracy)}米`;
        }
    }

    async validateLocation() {
        if (!this.currentLocation || !this.employee) {
            return;
        }

        try {
            const result = await this.rpc({
                model: 'attendance.location.config',
                method: 'check_location_allowed',
                args: [
                    this.currentLocation.latitude,
                    this.currentLocation.longitude,
                    this.employee.id,
                    new Date()
                ]
            });

            if (result.allowed) {
                this.updateLocationStatus('success', result.reason);
                this.isCheckinEnabled = true;
            } else {
                this.updateLocationStatus('warning', result.reason);
                this.isCheckinEnabled = true; // 仍然允许打卡，但会标记为异常
            }

            this.updateCheckinButton();
            this.showLocationValidation(result);

        } catch (error) {
            console.error('Location validation failed:', error);
            this.updateLocationStatus('error', '位置验证失败');
        }
    }

    updateLocationStatus(status, message) {
        this.state.locationStatus = status;
        this.state.locationMessage = message;

        const statusElement = document.getElementById('location-status');
        if (statusElement) {
            statusElement.className = `location-status ${status}`;
            
            let icon;
            switch (status) {
                case 'success':
                    icon = 'fa-check-circle';
                    break;
                case 'warning':
                    icon = 'fa-exclamation-triangle';
                    break;
                case 'error':
                    icon = 'fa-times-circle';
                    break;
                default:
                    icon = 'fa-spinner fa-spin';
            }
            
            statusElement.innerHTML = `<i class="fa ${icon}"></i><span>${message}</span>`;
        }
    }

    updateCheckinButton() {
        const button = document.getElementById('checkin-btn');
        const buttonText = button?.querySelector('.btn-text');
        
        if (!button || !buttonText) return;

        if (this.state.isCheckinEnabled) {
            button.disabled = false;
            button.className = 'btn btn-primary btn-lg checkin-button';
            buttonText.textContent = _t('立即打卡');
        } else {
            button.disabled = true;
            button.className = 'btn btn-secondary btn-lg checkin-button';
            buttonText.textContent = _t('获取位置中...');
        }
    }

    showLocationValidation(result) {
        const validationElement = document.getElementById('location-validation');
        const resultElement = document.getElementById('validation-result');
        const detailsElement = document.getElementById('validation-details');
        
        if (validationElement && resultElement && detailsElement) {
            validationElement.style.display = 'block';
            
            resultElement.className = `validation-result ${result.allowed ? 'success' : 'error'}`;
            resultElement.textContent = result.allowed ? _t('位置验证通过') : _t('位置验证异常');
            
            detailsElement.textContent = result.reason;
            
            if (result.distance !== null) {
                detailsElement.textContent += ` (距离: ${Math.round(result.distance)}米)`;
            }
        }
    }

    async performCheckin() {
        if (!this.state.isCheckinEnabled || !this.state.currentLocation || !this.state.employee) {
            return;
        }

        const button = document.getElementById('checkin-btn');
        const buttonText = button?.querySelector('.btn-text');
        
        if (button && buttonText) {
            button.disabled = true;
            buttonText.textContent = _t('打卡中...');
        }

        try {
            const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                timestamp: new Date().toISOString()
            };

            const result = await this.rpc({
                model: 'hr.attendance',
                method: 'create_attendance_with_location',
                args: [
                    this.state.employee.id,
                    this.state.currentLocation.latitude,
                    this.state.currentLocation.longitude,
                    this.state.currentLocation.accuracy,
                    null, // address - 可以通过反向地理编码获取
                    JSON.stringify(deviceInfo),
                    navigator.userAgent
                ]
            });

            this.notification.add(_t('打卡成功！'), { type: 'success' });
            
            // 刷新最近打卡记录
            await this.loadRecentAttendance();
            
            // 更新打卡状态显示
            const statusElement = document.getElementById('checkin-status');
            if (statusElement) {
                const isCheckOut = result.check_out;
                statusElement.innerHTML = `
                    <div style="color: #28a745; font-weight: 500;">
                        <i class="fa fa-check-circle"></i>
                        ${isCheckOut ? _t('下班打卡成功') : _t('上班打卡成功')}
                    </div>
                    <div style="font-size: 0.9rem; margin-top: 5px;">
                        ${new Date().toLocaleString('zh-CN')}
                    </div>
                `;
            }

        } catch (error) {
            console.error('Checkin failed:', error);
            this.notification.add(_t('打卡失败: ') + error.message, { type: 'danger' });
        } finally {
            if (button && buttonText) {
                button.disabled = false;
                buttonText.textContent = _t('立即打卡');
            }
        }
    }

    async loadRecentAttendance() {
        if (!this.state.employee) return;

        try {
            const records = await this.rpc({
                model: 'hr.attendance',
                method: 'search_read',
                args: [[['employee_id', '=', this.state.employee.id]]],
                kwargs: {
                    fields: ['check_in', 'check_out', 'location_status', 'location_reason'],
                    order: 'check_in desc',
                    limit: 5
                }
            });

            this.state.recentAttendance = records;
            this.updateAttendanceList();

        } catch (error) {
            console.error('Failed to load recent attendance:', error);
        }
    }

    updateAttendanceList() {
        const listElement = document.getElementById('attendance-list');
        if (!listElement) return;

        if (this.state.recentAttendance.length === 0) {
            listElement.innerHTML = '<div class="loading">暂无打卡记录</div>';
            return;
        }

        const html = this.state.recentAttendance.map(record => {
            const checkIn = new Date(record.check_in);
            const checkOut = record.check_out ? new Date(record.check_out) : null;
            
            return `
                <div class="attendance-item">
                    <div>
                        <div class="attendance-time">
                            ${checkIn.toLocaleDateString('zh-CN')} ${checkIn.toLocaleTimeString('zh-CN')}
                        </div>
                        ${checkOut ? `
                            <div class="attendance-type">
                                下班: ${checkOut.toLocaleTimeString('zh-CN')}
                            </div>
                        ` : '<div class="attendance-type">进行中</div>'}
                    </div>
                    <div class="attendance-status ${record.location_status}">
                        ${this.getLocationStatusText(record.location_status)}
                    </div>
                </div>
            `;
        }).join('');

        listElement.innerHTML = html;
    }

    getLocationStatusText(status) {
        switch (status) {
            case 'normal':
                return _t('正常');
            case 'outside':
                return _t('异常');
            case 'unknown':
                return _t('未知');
            default:
                return _t('未知');
        }
    }

    bindEvents() {
        // 打卡按钮事件
        const checkinBtn = document.getElementById('checkin-btn');
        if (checkinBtn) {
            checkinBtn.addEventListener('click', () => this.performCheckin());
        }

        // 设置按钮事件
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showSettings());
        }
    }

    showSettings() {
        // 这里可以实现设置对话框
        this.notification.add(_t('设置功能开发中...'), { type: 'info' });
    }
}

// 注册组件
registry.category("actions").add("employee_checkin", EmployeeCheckinComponent);
