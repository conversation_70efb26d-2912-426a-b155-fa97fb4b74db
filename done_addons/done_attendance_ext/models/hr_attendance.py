# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class HrAttendance(models.Model):
    """扩展HR考勤模型，添加位置信息"""
    _inherit = 'hr.attendance'

    # 位置信息
    location_latitude = fields.Float('打卡纬度', digits=(10, 7),
                                    help='打卡时的纬度坐标')
    location_longitude = fields.Float('打卡经度', digits=(10, 7),
                                     help='打卡时的经度坐标')
    location_accuracy = fields.Float('位置精度', help='GPS位置精度（米）')
    location_address = fields.Char('打卡地址', help='打卡时的地址信息')
    
    # 位置状态
    location_status = fields.Selection([
        ('normal', '正常'),
        ('outside', '异常'),
        ('unknown', '未知'),
    ], string='位置状态', default='unknown', 
       help='打卡位置的合规状态')
    
    location_config_id = fields.Many2one('attendance.location.config', 
                                        string='匹配的位置配置',
                                        help='打卡时匹配的位置配置')
    location_distance = fields.Float('距离配置点', help='与最近配置点的距离（米）')
    location_reason = fields.Text('位置说明', help='位置状态的详细说明')
    
    # 设备信息
    device_info = fields.Text('设备信息', help='打卡设备的相关信息')
    user_agent = fields.Text('浏览器信息', help='打卡时的浏览器信息')
    
    @api.model_create_multi
    def create(self, vals_list):
        """创建考勤记录时验证位置"""
        for vals in vals_list:
            if 'location_latitude' in vals and 'location_longitude' in vals:
                self._validate_location(vals)
        return super().create(vals_list)
    
    def write(self, vals):
        """更新考勤记录时验证位置"""
        if 'location_latitude' in vals or 'location_longitude' in vals:
            for record in self:
                # 合并当前值和新值
                merged_vals = {
                    'location_latitude': vals.get('location_latitude', record.location_latitude),
                    'location_longitude': vals.get('location_longitude', record.location_longitude),
                    'employee_id': vals.get('employee_id', record.employee_id.id),
                    'check_in': vals.get('check_in', record.check_in),
                }
                self._validate_location(merged_vals)
        return super().write(vals)
    
    def _validate_location(self, vals):
        """验证位置信息"""
        latitude = vals.get('location_latitude')
        longitude = vals.get('location_longitude')
        employee_id = vals.get('employee_id')
        check_time = vals.get('check_in')
        
        if not latitude or not longitude:
            vals.update({
                'location_status': 'unknown',
                'location_reason': '未提供位置信息',
            })
            return
        
        # 转换check_time为datetime对象
        if isinstance(check_time, str):
            check_time = datetime.fromisoformat(check_time.replace('Z', '+00:00'))
        
        # 检查位置是否允许
        location_check = self.env['attendance.location.config'].check_location_allowed(
            latitude, longitude, employee_id, check_time
        )
        
        # 更新位置状态
        vals.update({
            'location_status': 'normal' if location_check['allowed'] else 'outside',
            'location_config_id': location_check['location_config'].id if location_check['location_config'] else False,
            'location_distance': location_check['distance'],
            'location_reason': location_check['reason'],
        })
    
    @api.model
    def create_attendance_with_location(self, employee_id, latitude, longitude, 
                                       accuracy=None, address=None, device_info=None, user_agent=None):
        """
        创建带位置信息的考勤记录
        
        :param employee_id: 员工ID
        :param latitude: 纬度
        :param longitude: 经度
        :param accuracy: 位置精度
        :param address: 地址
        :param device_info: 设备信息
        :param user_agent: 浏览器信息
        :return: 创建的考勤记录
        """
        employee = self.env['hr.employee'].browse(employee_id)
        if not employee.exists():
            raise ValidationError(_('员工不存在'))
        
        # 检查是否已经打卡
        last_attendance = self.search([
            ('employee_id', '=', employee_id)
        ], order='check_in desc', limit=1)
        
        vals = {
            'employee_id': employee_id,
            'check_in': fields.Datetime.now(),
            'location_latitude': latitude,
            'location_longitude': longitude,
            'location_accuracy': accuracy,
            'location_address': address,
            'device_info': device_info,
            'user_agent': user_agent,
        }
        
        # 如果上次记录没有check_out，则这次是check_out
        if last_attendance and not last_attendance.check_out:
            vals.update({
                'check_in': last_attendance.check_in,
                'check_out': fields.Datetime.now(),
            })
            attendance = last_attendance
            attendance.write(vals)
        else:
            # 创建新的check_in记录
            attendance = self.create(vals)
        
        return attendance
    
    def action_view_location_map(self):
        """查看打卡位置地图"""
        self.ensure_one()
        if not self.location_latitude or not self.location_longitude:
            raise ValidationError(_('此记录没有位置信息'))
        
        return {
            'type': 'ir.actions.client',
            'tag': 'attendance_location_map',
            'params': {
                'latitude': self.location_latitude,
                'longitude': self.location_longitude,
                'address': self.location_address or '',
                'employee_name': self.employee_id.name,
                'check_time': self.check_in.strftime('%Y-%m-%d %H:%M:%S') if self.check_in else '',
                'location_status': self.location_status,
                'location_reason': self.location_reason or '',
            }
        }
    
    def action_validate_location(self):
        """重新验证位置"""
        for record in self:
            if record.location_latitude and record.location_longitude:
                vals = {
                    'location_latitude': record.location_latitude,
                    'location_longitude': record.location_longitude,
                    'employee_id': record.employee_id.id,
                    'check_in': record.check_in,
                }
                record._validate_location(vals)
                record.write({
                    'location_status': vals['location_status'],
                    'location_config_id': vals['location_config_id'],
                    'location_distance': vals['location_distance'],
                    'location_reason': vals['location_reason'],
                })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('位置验证'),
                'message': _('位置验证完成'),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_manual_checkin(self):
        """手动打卡操作"""
        self.ensure_one()

        # 获取当前用户的员工记录
        current_employee = self.env.user.employee_id
        if not current_employee:
            raise ValidationError(_('当前用户没有关联的员工记录，请联系管理员'))

        # 检查员工是否已经有未完成的考勤记录
        last_attendance = self.search([
            ('employee_id', '=', current_employee.id)
        ], order='check_in desc', limit=1)

        current_time = fields.Datetime.now()

        if last_attendance and not last_attendance.check_out:
            # 如果有未完成的记录，这次是下班打卡
            last_attendance.write({
                'check_out': current_time,
            })
            message = _('下班打卡成功！时间：%s') % current_time.strftime('%Y-%m-%d %H:%M:%S')
            action_type = '下班'
        else:
            # 创建新的上班打卡记录
            new_attendance = self.create({
                'employee_id': current_employee.id,
                'check_in': current_time,
            })
            message = _('上班打卡成功！时间：%s') % current_time.strftime('%Y-%m-%d %H:%M:%S')
            action_type = '上班'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('打卡成功'),
                'message': message,
                'type': 'success',
                'sticky': False,
            }
        }
