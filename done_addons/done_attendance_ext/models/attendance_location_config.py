# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import math
import logging

_logger = logging.getLogger(__name__)


class AttendanceLocationConfig(models.Model):
    """考勤位置配置模型"""
    _name = 'attendance.location.config'
    _description = 'Attendance Location Configuration'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'name'
    _order = 'sequence, name'

    name = fields.Char('位置名称', required=True, tracking=True,
                      help='打卡位置的名称，如：总部大楼、分公司等')
    active = fields.Boolean('激活', default=True, tracking=True)
    sequence = fields.Integer('序号', default=10)
    
    # 位置信息
    latitude = fields.Float('纬度', required=True, digits=(10, 7), tracking=True,
                           help='位置的纬度坐标')
    longitude = fields.Float('经度', required=True, digits=(10, 7), tracking=True,
                            help='位置的经度坐标')
    radius = fields.Float('允许半径(米)', required=True, default=100.0, tracking=True,
                         help='允许打卡的半径范围，单位：米')
    
    # 地址信息
    address = fields.Text('地址', help='位置的详细地址')
    description = fields.Text('描述', help='位置的详细描述')
    
    # 使用范围
    company_id = fields.Many2one('res.company', string='公司', 
                                default=lambda self: self.env.company,
                                help='此位置配置适用的公司')
    department_ids = fields.Many2many('hr.department', string='适用部门',
                                     help='此位置配置适用的部门，留空则适用所有部门')
    employee_ids = fields.Many2many('hr.employee', string='适用员工',
                                   help='此位置配置适用的员工，留空则适用所有员工')
    
    # 时间限制
    time_restriction = fields.Boolean('启用时间限制', default=False,
                                     help='是否启用时间限制')
    start_time = fields.Float('开始时间', help='允许打卡的开始时间')
    end_time = fields.Float('结束时间', help='允许打卡的结束时间')
    
    # 统计信息
    checkin_count = fields.Integer('打卡次数', compute='_compute_checkin_count',
                                  help='在此位置的打卡次数')
    
    @api.constrains('latitude', 'longitude')
    def _check_coordinates(self):
        """验证经纬度坐标"""
        for record in self:
            if not (-90 <= record.latitude <= 90):
                raise ValidationError(_('纬度必须在-90到90之间'))
            if not (-180 <= record.longitude <= 180):
                raise ValidationError(_('经度必须在-180到180之间'))
    
    @api.constrains('radius')
    def _check_radius(self):
        """验证半径"""
        for record in self:
            if record.radius <= 0:
                raise ValidationError(_('半径必须大于0'))
            if record.radius > 10000:  # 10公里
                raise ValidationError(_('半径不能超过10000米'))
    
    @api.constrains('start_time', 'end_time')
    def _check_time_range(self):
        """验证时间范围"""
        for record in self:
            if record.time_restriction:
                if not (0 <= record.start_time < 24):
                    raise ValidationError(_('开始时间必须在0-24之间'))
                if not (0 <= record.end_time < 24):
                    raise ValidationError(_('结束时间必须在0-24之间'))
    
    def _compute_checkin_count(self):
        """计算打卡次数"""
        for record in self:
            # 计算在此位置范围内的打卡次数
            count = self.env['hr.attendance'].search_count([
                ('location_latitude', '!=', False),
                ('location_longitude', '!=', False),
                ('location_status', '=', 'normal'),
            ])
            record.checkin_count = count
    
    @api.model
    def check_location_allowed(self, latitude, longitude, employee_id=None, check_time=None):
        """
        检查指定位置是否允许打卡
        
        :param latitude: 纬度
        :param longitude: 经度
        :param employee_id: 员工ID
        :param check_time: 打卡时间
        :return: dict with 'allowed', 'location_config', 'distance', 'reason'
        """
        if not latitude or not longitude:
            return {
                'allowed': False,
                'location_config': None,
                'distance': None,
                'reason': '无法获取位置信息'
            }
        
        # 构建搜索域
        domain = [('active', '=', True)]
        
        # 如果指定了员工，检查员工和部门限制
        if employee_id:
            employee = self.env['hr.employee'].browse(employee_id)
            domain.extend([
                '|', ('employee_ids', '=', False),
                     ('employee_ids', 'in', [employee_id]),
                '|', ('department_ids', '=', False),
                     ('department_ids', 'in', employee.department_id.ids if employee.department_id else []),
            ])
        
        configs = self.search(domain)
        
        for config in configs:
            distance = self._calculate_distance(
                latitude, longitude,
                config.latitude, config.longitude
            )
            
            if distance <= config.radius:
                # 检查时间限制
                if config.time_restriction and check_time:
                    hour = check_time.hour + check_time.minute / 60.0
                    if not (config.start_time <= hour <= config.end_time):
                        continue
                
                return {
                    'allowed': True,
                    'location_config': config,
                    'distance': distance,
                    'reason': f'在允许范围内（距离{config.name} {distance:.1f}米）'
                }
        
        # 找到最近的配置点
        nearest_config = None
        nearest_distance = float('inf')
        
        for config in configs:
            distance = self._calculate_distance(
                latitude, longitude,
                config.latitude, config.longitude
            )
            if distance < nearest_distance:
                nearest_distance = distance
                nearest_config = config
        
        if nearest_config:
            reason = f'超出允许范围（距离{nearest_config.name} {nearest_distance:.1f}米，允许范围{nearest_config.radius}米）'
        else:
            reason = '没有配置的打卡位置'
        
        return {
            'allowed': False,
            'location_config': nearest_config,
            'distance': nearest_distance if nearest_config else None,
            'reason': reason
        }
    
    @api.model
    def _calculate_distance(self, lat1, lon1, lat2, lon2):
        """
        计算两个经纬度点之间的距离（米）
        使用Haversine公式
        """
        # 转换为弧度
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine公式
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # 地球半径（米）
        r = 6371000
        
        return c * r
    
    def action_test_location(self):
        """测试位置配置"""
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('位置测试'),
                'message': _('位置配置：%s\n纬度：%s\n经度：%s\n半径：%s米') % (
                    self.name, self.latitude, self.longitude, self.radius
                ),
                'type': 'info',
                'sticky': False,
            }
        }
