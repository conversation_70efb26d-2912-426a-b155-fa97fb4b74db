<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- 考勤位置管理权限组 -->
    <record id="group_attendance_location_manager" model="res.groups">
        <field name="name">考勤位置管理员</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('hr.group_hr_manager'))]"/>
        <field name="comment">可以配置和管理考勤位置设置</field>
    </record>

    <!-- 员工打卡权限组 -->
    <record id="group_employee_checkin" model="res.groups">
        <field name="name">员工打卡</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">可以使用员工打卡功能</field>
    </record>



</odoo>
