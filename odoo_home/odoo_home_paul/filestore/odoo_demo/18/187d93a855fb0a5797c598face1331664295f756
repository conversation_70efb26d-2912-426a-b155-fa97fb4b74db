
/* <inline asset> */
@charset "UTF-8"; 

/* /web/static/src/scss/functions.scss */
 

/* /web/static/src/scss/utils.scss */
 .o_nocontent_help .o_empty_folder_image:before{content: ""; display: block; margin: auto; background-size: cover;}.o_nocontent_help .o_empty_folder_image:before{width: 120px; height: 80px; margin-top: 30px; margin-bottom: 30px; background: transparent url(/web/static/img/empty_folder.svg) no-repeat center;}

/* /web_enterprise/static/src/scss/primary_variables.scss */
 

/* /web/static/src/scss/primary_variables.scss */
 

/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */
 

/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */
 

/* /web/static/src/core/avatar/avatar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/fields/statusbar/statusbar_field.variables.scss */
 

/* /web/static/src/views/fields/translation_button.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /mail/static/src/core/common/primary_variables.scss */
 

/* /mail/static/src/discuss/typing/common/primary_variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /onboarding/static/src/scss/onboarding.variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /account/static/src/scss/variables.scss */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/primary_variables.scss */
 

/* /website/static/src/scss/options/user_values.scss */
 

/* /website/static/src/scss/options/colors/user_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */
 

/* /website_sale/static/src/scss/primary_variables.scss */
 

/* /web_gantt/static/src/gantt_view.variables.scss */
 

/* /hr_org_chart/static/src/scss/variables.scss */
 

/* /website/static/src/snippets/s_badge/000_variables.scss */
 

/* /website/static/src/scss/secondary_variables.scss */
 

/* /web_enterprise/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/webclient/actions/reports/bootstrap_overridden_report.scss */
 

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 .o_report_reception .bg-dark-light h1, .o_colored_level .o_report_reception .bg-dark-light h1, .o_report_reception .bg-light-light h1, .o_colored_level .o_report_reception .bg-light-light h1, .o_report_reception .bg-danger-light h1, .o_colored_level .o_report_reception .bg-danger-light h1, .o_report_reception .bg-warning-light h1, .o_colored_level .o_report_reception .bg-warning-light h1, .o_report_reception .bg-info-light h1, .o_colored_level .o_report_reception .bg-info-light h1, .o_report_reception .bg-success-light h1, .o_colored_level .o_report_reception .bg-success-light h1, .o_report_reception .bg-secondary-light h1, .o_colored_level .o_report_reception .bg-secondary-light h1, .o_report_reception .bg-primary-light h1, .o_colored_level .o_report_reception .bg-primary-light h1, .o_cc5 h1, .o_colored_level .o_cc5 h1, .o_cc4 h1, .o_colored_level .o_cc4 h1, .o_cc3 h1, .o_colored_level .o_cc3 h1, .o_cc2 h1, .o_colored_level .o_cc2 h1, .o_cc1 h1, .o_colored_level .o_cc1 h1, .bg-o-color-5 h1, .o_colored_level .bg-o-color-5 h1, .bg-o-color-4 h1, .o_colored_level .bg-o-color-4 h1, .bg-o-color-3 h1, .o_colored_level .bg-o-color-3 h1, .bg-o-color-2 h1, .o_colored_level .bg-o-color-2 h1, .bg-o-color-1 h1, .o_colored_level .bg-o-color-1 h1, .o_report_reception .bg-dark-light .h1, .o_colored_level .o_report_reception .bg-dark-light .h1, .o_report_reception .bg-light-light .h1, .o_colored_level .o_report_reception .bg-light-light .h1, .o_report_reception .bg-danger-light .h1, .o_colored_level .o_report_reception .bg-danger-light .h1, .o_report_reception .bg-warning-light .h1, .o_colored_level .o_report_reception .bg-warning-light .h1, .o_report_reception .bg-info-light .h1, .o_colored_level .o_report_reception .bg-info-light .h1, .o_report_reception .bg-success-light .h1, .o_colored_level .o_report_reception .bg-success-light .h1, .o_report_reception .bg-secondary-light .h1, .o_colored_level .o_report_reception .bg-secondary-light .h1, .o_report_reception .bg-primary-light .h1, .o_colored_level .o_report_reception .bg-primary-light .h1, .o_cc5 .h1, .o_colored_level .o_cc5 .h1, .o_cc4 .h1, .o_colored_level .o_cc4 .h1, .o_cc3 .h1, .o_colored_level .o_cc3 .h1, .o_cc2 .h1, .o_colored_level .o_cc2 .h1, .o_cc1 .h1, .o_colored_level .o_cc1 .h1, .bg-o-color-5 .h1, .o_colored_level .bg-o-color-5 .h1, .bg-o-color-4 .h1, .bg-o-color-3 .h1, .bg-o-color-2 .h1, .bg-o-color-1 .h1, .o_report_reception .bg-dark-light h2, .o_colored_level .o_report_reception .bg-dark-light h2, .o_report_reception .bg-light-light h2, .o_colored_level .o_report_reception .bg-light-light h2, .o_report_reception .bg-danger-light h2, .o_colored_level .o_report_reception .bg-danger-light h2, .o_report_reception .bg-warning-light h2, .o_colored_level .o_report_reception .bg-warning-light h2, .o_report_reception .bg-info-light h2, .o_colored_level .o_report_reception .bg-info-light h2, .o_report_reception .bg-success-light h2, .o_colored_level .o_report_reception .bg-success-light h2, .o_report_reception .bg-secondary-light h2, .o_colored_level .o_report_reception .bg-secondary-light h2, .o_report_reception .bg-primary-light h2, .o_colored_level .o_report_reception .bg-primary-light h2, .o_cc5 h2, .o_colored_level .o_cc5 h2, .o_cc4 h2, .o_colored_level .o_cc4 h2, .o_cc3 h2, .o_colored_level .o_cc3 h2, .o_cc2 h2, .o_colored_level .o_cc2 h2, .o_cc1 h2, .o_colored_level .o_cc1 h2, .bg-o-color-5 h2, .o_colored_level .bg-o-color-5 h2, .bg-o-color-4 h2, .o_colored_level .bg-o-color-4 h2, .bg-o-color-3 h2, .o_colored_level .bg-o-color-3 h2, .bg-o-color-2 h2, .o_colored_level .bg-o-color-2 h2, .bg-o-color-1 h2, .o_colored_level .bg-o-color-1 h2, .o_report_reception .bg-dark-light .h2, .o_colored_level .o_report_reception .bg-dark-light .h2, .o_report_reception .bg-light-light .h2, .o_colored_level .o_report_reception .bg-light-light .h2, .o_report_reception .bg-danger-light .h2, .o_colored_level .o_report_reception .bg-danger-light .h2, .o_report_reception .bg-warning-light .h2, .o_colored_level .o_report_reception .bg-warning-light .h2, .o_report_reception .bg-info-light .h2, .o_colored_level .o_report_reception .bg-info-light .h2, .o_report_reception .bg-success-light .h2, .o_colored_level .o_report_reception .bg-success-light .h2, .o_report_reception .bg-secondary-light .h2, .o_colored_level .o_report_reception .bg-secondary-light .h2, .o_report_reception .bg-primary-light .h2, .o_colored_level .o_report_reception .bg-primary-light .h2, .o_cc5 .h2, .o_colored_level .o_cc5 .h2, .o_cc4 .h2, .o_colored_level .o_cc4 .h2, .o_cc3 .h2, .o_colored_level .o_cc3 .h2, .o_cc2 .h2, .o_colored_level .o_cc2 .h2, .o_cc1 .h2, .o_colored_level .o_cc1 .h2, .bg-o-color-5 .h2, .o_colored_level .bg-o-color-5 .h2, .bg-o-color-4 .h2, .bg-o-color-3 .h2, .bg-o-color-2 .h2, .bg-o-color-1 .h2, .o_report_reception .bg-dark-light h3, .o_colored_level .o_report_reception .bg-dark-light h3, .o_report_reception .bg-light-light h3, .o_colored_level .o_report_reception .bg-light-light h3, .o_report_reception .bg-danger-light h3, .o_colored_level .o_report_reception .bg-danger-light h3, .o_report_reception .bg-warning-light h3, .o_colored_level .o_report_reception .bg-warning-light h3, .o_report_reception .bg-info-light h3, .o_colored_level .o_report_reception .bg-info-light h3, .o_report_reception .bg-success-light h3, .o_colored_level .o_report_reception .bg-success-light h3, .o_report_reception .bg-secondary-light h3, .o_colored_level .o_report_reception .bg-secondary-light h3, .o_report_reception .bg-primary-light h3, .o_colored_level .o_report_reception .bg-primary-light h3, .o_cc5 h3, .o_colored_level .o_cc5 h3, .o_cc4 h3, .o_colored_level .o_cc4 h3, .o_cc3 h3, .o_colored_level .o_cc3 h3, .o_cc2 h3, .o_colored_level .o_cc2 h3, .o_cc1 h3, .o_colored_level .o_cc1 h3, .bg-o-color-5 h3, .o_colored_level .bg-o-color-5 h3, .bg-o-color-4 h3, .o_colored_level .bg-o-color-4 h3, .bg-o-color-3 h3, .o_colored_level .bg-o-color-3 h3, .bg-o-color-2 h3, .o_colored_level .bg-o-color-2 h3, .bg-o-color-1 h3, .o_colored_level .bg-o-color-1 h3, .o_report_reception .bg-dark-light .h3, .o_colored_level .o_report_reception .bg-dark-light .h3, .o_report_reception .bg-light-light .h3, .o_colored_level .o_report_reception .bg-light-light .h3, .o_report_reception .bg-danger-light .h3, .o_colored_level .o_report_reception .bg-danger-light .h3, .o_report_reception .bg-warning-light .h3, .o_colored_level .o_report_reception .bg-warning-light .h3, .o_report_reception .bg-info-light .h3, .o_colored_level .o_report_reception .bg-info-light .h3, .o_report_reception .bg-success-light .h3, .o_colored_level .o_report_reception .bg-success-light .h3, .o_report_reception .bg-secondary-light .h3, .o_colored_level .o_report_reception .bg-secondary-light .h3, .o_report_reception .bg-primary-light .h3, .o_colored_level .o_report_reception .bg-primary-light .h3, .o_cc5 .h3, .o_colored_level .o_cc5 .h3, .o_cc4 .h3, .o_colored_level .o_cc4 .h3, .o_cc3 .h3, .o_colored_level .o_cc3 .h3, .o_cc2 .h3, .o_colored_level .o_cc2 .h3, .o_cc1 .h3, .o_colored_level .o_cc1 .h3, .bg-o-color-5 .h3, .o_colored_level .bg-o-color-5 .h3, .bg-o-color-4 .h3, .bg-o-color-3 .h3, .bg-o-color-2 .h3, .bg-o-color-1 .h3, .o_report_reception .bg-dark-light h4, .o_colored_level .o_report_reception .bg-dark-light h4, .o_report_reception .bg-light-light h4, .o_colored_level .o_report_reception .bg-light-light h4, .o_report_reception .bg-danger-light h4, .o_colored_level .o_report_reception .bg-danger-light h4, .o_report_reception .bg-warning-light h4, .o_colored_level .o_report_reception .bg-warning-light h4, .o_report_reception .bg-info-light h4, .o_colored_level .o_report_reception .bg-info-light h4, .o_report_reception .bg-success-light h4, .o_colored_level .o_report_reception .bg-success-light h4, .o_report_reception .bg-secondary-light h4, .o_colored_level .o_report_reception .bg-secondary-light h4, .o_report_reception .bg-primary-light h4, .o_colored_level .o_report_reception .bg-primary-light h4, .o_cc5 h4, .o_colored_level .o_cc5 h4, .o_cc4 h4, .o_colored_level .o_cc4 h4, .o_cc3 h4, .o_colored_level .o_cc3 h4, .o_cc2 h4, .o_colored_level .o_cc2 h4, .o_cc1 h4, .o_colored_level .o_cc1 h4, .bg-o-color-5 h4, .o_colored_level .bg-o-color-5 h4, .bg-o-color-4 h4, .o_colored_level .bg-o-color-4 h4, .bg-o-color-3 h4, .o_colored_level .bg-o-color-3 h4, .bg-o-color-2 h4, .o_colored_level .bg-o-color-2 h4, .bg-o-color-1 h4, .o_colored_level .bg-o-color-1 h4, .o_report_reception .bg-dark-light .h4, .o_colored_level .o_report_reception .bg-dark-light .h4, .o_report_reception .bg-light-light .h4, .o_colored_level .o_report_reception .bg-light-light .h4, .o_report_reception .bg-danger-light .h4, .o_colored_level .o_report_reception .bg-danger-light .h4, .o_report_reception .bg-warning-light .h4, .o_colored_level .o_report_reception .bg-warning-light .h4, .o_report_reception .bg-info-light .h4, .o_colored_level .o_report_reception .bg-info-light .h4, .o_report_reception .bg-success-light .h4, .o_colored_level .o_report_reception .bg-success-light .h4, .o_report_reception .bg-secondary-light .h4, .o_colored_level .o_report_reception .bg-secondary-light .h4, .o_report_reception .bg-primary-light .h4, .o_colored_level .o_report_reception .bg-primary-light .h4, .o_cc5 .h4, .o_colored_level .o_cc5 .h4, .o_cc4 .h4, .o_colored_level .o_cc4 .h4, .o_cc3 .h4, .o_colored_level .o_cc3 .h4, .o_cc2 .h4, .o_colored_level .o_cc2 .h4, .o_cc1 .h4, .o_colored_level .o_cc1 .h4, .bg-o-color-5 .h4, .o_colored_level .bg-o-color-5 .h4, .bg-o-color-4 .h4, .bg-o-color-3 .h4, .bg-o-color-2 .h4, .bg-o-color-1 .h4, .o_report_reception .bg-dark-light h5, .o_colored_level .o_report_reception .bg-dark-light h5, .o_report_reception .bg-light-light h5, .o_colored_level .o_report_reception .bg-light-light h5, .o_report_reception .bg-danger-light h5, .o_colored_level .o_report_reception .bg-danger-light h5, .o_report_reception .bg-warning-light h5, .o_colored_level .o_report_reception .bg-warning-light h5, .o_report_reception .bg-info-light h5, .o_colored_level .o_report_reception .bg-info-light h5, .o_report_reception .bg-success-light h5, .o_colored_level .o_report_reception .bg-success-light h5, .o_report_reception .bg-secondary-light h5, .o_colored_level .o_report_reception .bg-secondary-light h5, .o_report_reception .bg-primary-light h5, .o_colored_level .o_report_reception .bg-primary-light h5, .o_cc5 h5, .o_colored_level .o_cc5 h5, .o_cc4 h5, .o_colored_level .o_cc4 h5, .o_cc3 h5, .o_colored_level .o_cc3 h5, .o_cc2 h5, .o_colored_level .o_cc2 h5, .o_cc1 h5, .o_colored_level .o_cc1 h5, .bg-o-color-5 h5, .o_colored_level .bg-o-color-5 h5, .bg-o-color-4 h5, .o_colored_level .bg-o-color-4 h5, .bg-o-color-3 h5, .o_colored_level .bg-o-color-3 h5, .bg-o-color-2 h5, .o_colored_level .bg-o-color-2 h5, .bg-o-color-1 h5, .o_colored_level .bg-o-color-1 h5, .o_report_reception .bg-dark-light .h5, .o_colored_level .o_report_reception .bg-dark-light .h5, .o_report_reception .bg-light-light .h5, .o_colored_level .o_report_reception .bg-light-light .h5, .o_report_reception .bg-danger-light .h5, .o_colored_level .o_report_reception .bg-danger-light .h5, .o_report_reception .bg-warning-light .h5, .o_colored_level .o_report_reception .bg-warning-light .h5, .o_report_reception .bg-info-light .h5, .o_colored_level .o_report_reception .bg-info-light .h5, .o_report_reception .bg-success-light .h5, .o_colored_level .o_report_reception .bg-success-light .h5, .o_report_reception .bg-secondary-light .h5, .o_colored_level .o_report_reception .bg-secondary-light .h5, .o_report_reception .bg-primary-light .h5, .o_colored_level .o_report_reception .bg-primary-light .h5, .o_cc5 .h5, .o_colored_level .o_cc5 .h5, .o_cc4 .h5, .o_colored_level .o_cc4 .h5, .o_cc3 .h5, .o_colored_level .o_cc3 .h5, .o_cc2 .h5, .o_colored_level .o_cc2 .h5, .o_cc1 .h5, .o_colored_level .o_cc1 .h5, .bg-o-color-5 .h5, .o_colored_level .bg-o-color-5 .h5, .bg-o-color-4 .h5, .bg-o-color-3 .h5, .bg-o-color-2 .h5, .bg-o-color-1 .h5, .o_report_reception .bg-dark-light h6, .o_colored_level .o_report_reception .bg-dark-light h6, .o_report_reception .bg-light-light h6, .o_colored_level .o_report_reception .bg-light-light h6, .o_report_reception .bg-danger-light h6, .o_colored_level .o_report_reception .bg-danger-light h6, .o_report_reception .bg-warning-light h6, .o_colored_level .o_report_reception .bg-warning-light h6, .o_report_reception .bg-info-light h6, .o_colored_level .o_report_reception .bg-info-light h6, .o_report_reception .bg-success-light h6, .o_colored_level .o_report_reception .bg-success-light h6, .o_report_reception .bg-secondary-light h6, .o_colored_level .o_report_reception .bg-secondary-light h6, .o_report_reception .bg-primary-light h6, .o_colored_level .o_report_reception .bg-primary-light h6, .o_cc5 h6, .o_colored_level .o_cc5 h6, .o_cc4 h6, .o_colored_level .o_cc4 h6, .o_cc3 h6, .o_colored_level .o_cc3 h6, .o_cc2 h6, .o_colored_level .o_cc2 h6, .o_cc1 h6, .o_colored_level .o_cc1 h6, .bg-o-color-5 h6, .o_colored_level .bg-o-color-5 h6, .bg-o-color-4 h6, .o_colored_level .bg-o-color-4 h6, .bg-o-color-3 h6, .o_colored_level .bg-o-color-3 h6, .bg-o-color-2 h6, .o_colored_level .bg-o-color-2 h6, .bg-o-color-1 h6, .o_colored_level .bg-o-color-1 h6, .o_report_reception .bg-dark-light .h6, .o_colored_level .o_report_reception .bg-dark-light .h6, .o_report_reception .bg-light-light .h6, .o_colored_level .o_report_reception .bg-light-light .h6, .o_report_reception .bg-danger-light .h6, .o_colored_level .o_report_reception .bg-danger-light .h6, .o_report_reception .bg-warning-light .h6, .o_colored_level .o_report_reception .bg-warning-light .h6, .o_report_reception .bg-info-light .h6, .o_colored_level .o_report_reception .bg-info-light .h6, .o_report_reception .bg-success-light .h6, .o_colored_level .o_report_reception .bg-success-light .h6, .o_report_reception .bg-secondary-light .h6, .o_colored_level .o_report_reception .bg-secondary-light .h6, .o_report_reception .bg-primary-light .h6, .o_colored_level .o_report_reception .bg-primary-light .h6, .o_cc5 .h6, .o_colored_level .o_cc5 .h6, .o_cc4 .h6, .o_colored_level .o_cc4 .h6, .o_cc3 .h6, .o_colored_level .o_cc3 .h6, .o_cc2 .h6, .o_colored_level .o_cc2 .h6, .o_cc1 .h6, .o_colored_level .o_cc1 .h6, .bg-o-color-5 .h6, .o_colored_level .bg-o-color-5 .h6, .bg-o-color-4 .h6, .bg-o-color-3 .h6, .bg-o-color-2 .h6, .bg-o-color-1 .h6{color: inherit;}

/* /web_enterprise/static/src/scss/bootstrap_overridden.scss */
 

/* /web/static/src/scss/bootstrap_overridden.scss */
 .user-select-none{-webkit-user-select: none !important;}

/* /web/static/src/scss/bs_mixins_overrides_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables-dark.scss */
 

/* /web/static/lib/bootstrap/scss/_maps.scss */
 

/* /web/static/src/scss/import_bootstrap.scss */
 :root, [data-bs-theme="light"]{--blue: #007bff; --indigo: #6610f2; --purple: #6f42c1; --pink: #e83e8c; --red: #dc3545; --orange: #fd7e14; --yellow: #ffc107; --green: #28a745; --teal: #20c997; --cyan: #17a2b8; --white: #FFF; --gray: #5f636f; --gray-dark: #1F2937; --o-cc5-btn-secondary-border: ; --o-cc5-btn-secondary: #F3F2F2; --o-cc5-btn-primary-border: ; --o-cc5-btn-primary: ; --o-cc5-link: ; --o-cc5-h6: ; --o-cc5-h5: ; --o-cc5-h4: ; --o-cc5-h3: ; --o-cc5-h2: ; --o-cc5-headings: #FFFFFF; --o-cc5-text: ; --o-cc5-bg: #111827; --o-cc4-btn-secondary-border: ; --o-cc4-btn-secondary: #F3F2F2; --o-cc4-btn-primary-border: ; --o-cc4-btn-primary: #111827; --o-cc4-link: #111827; --o-cc4-h6: ; --o-cc4-h5: ; --o-cc4-h4: ; --o-cc4-h3: ; --o-cc4-h2: ; --o-cc4-headings: ; --o-cc4-text: ; --o-cc4-bg: #714B67; --o-cc3-btn-secondary-border: ; --o-cc3-btn-secondary: #F3F2F2; --o-cc3-btn-primary-border: ; --o-cc3-btn-primary: ; --o-cc3-link: ; --o-cc3-h6: ; --o-cc3-h5: ; --o-cc3-h4: ; --o-cc3-h3: ; --o-cc3-h2: ; --o-cc3-headings: ; --o-cc3-text: ; --o-cc3-bg: #2D3142; --o-cc2-btn-secondary-border: ; --o-cc2-btn-secondary: ; --o-cc2-btn-primary-border: ; --o-cc2-btn-primary: ; --o-cc2-link: ; --o-cc2-h6: ; --o-cc2-h5: ; --o-cc2-h4: ; --o-cc2-h3: ; --o-cc2-h2: ; --o-cc2-headings: #111827; --o-cc2-text: ; --o-cc2-bg: #F3F2F2; --o-cc1-btn-secondary-border: ; --o-cc1-btn-secondary: ; --o-cc1-btn-primary-border: ; --o-cc1-btn-primary: ; --o-cc1-link: ; --o-cc1-h6: ; --o-cc1-h5: ; --o-cc1-h4: ; --o-cc1-h3: ; --o-cc1-h2: ; --o-cc1-headings: ; --o-cc1-text: ; --o-cc1-bg: #FFFFFF; --copyright-custom: rgba(0, 0, 0, 0.15); --copyright: ; --footer-custom: ; --footer: #111827; --header-sales_four-custom: ; --header-sales_four: #FFFFFF; --header-sales_three-custom: ; --header-sales_three: #F3F2F2; --header-sales_two-custom: ; --header-sales_two: #111827; --header-sales_one-custom: ; --header-sales_one: #F3F2F2; --menu-border-color: ; --menu-custom: ; --menu: #FFFFFF; --input: ; --body: white; --o-color-5: #111827; --o-color-4: #FFFFFF; --o-color-3: #F3F2F2; --o-color-2: #2D3142; --o-color-1: #714B67; --gray-100: #F9FAFB; --gray-200: #e7e9ed; --gray-300: #d8dadd; --gray-400: #9a9ca5; --gray-500: #7c7f89; --gray-600: #5f636f; --gray-700: #374151; --gray-800: #1F2937; --gray-900: #111827; --gray-white-85: rgba(255, 255, 255, 0.85); --gray-white-75: rgba(255, 255, 255, 0.75); --gray-white-50: rgba(255, 255, 255, 0.5); --gray-white-25: rgba(255, 255, 255, 0.25); --gray-black-75: rgba(0, 0, 0, 0.75); --gray-black-50: rgba(0, 0, 0, 0.5); --gray-black-25: rgba(0, 0, 0, 0.25); --gray-black-15: rgba(0, 0, 0, 0.15); --primary: #714B67; --secondary: #d8dadd; --success: #28a745; --info: #17a2b8; --warning: #e99d00; --danger: #d44c59; --light: #FFF; --dark: #111827; --primary-rgb: 113, 75, 103; --secondary-rgb: 216, 218, 221; --success-rgb: 40, 167, 69; --info-rgb: 23, 162, 184; --warning-rgb: 233, 157, 0; --danger-rgb: 212, 76, 89; --light-rgb: 255, 255, 255; --dark-rgb: 17, 24, 39; --primary-text-emphasis: #2d1e29; --secondary-text-emphasis: #565758; --success-text-emphasis: #10431c; --info-text-emphasis: #09414a; --warning-text-emphasis: #5d3f00; --danger-text-emphasis: #551e24; --light-text-emphasis: #374151; --dark-text-emphasis: #374151; --primary-bg-subtle: #e3dbe1; --secondary-bg-subtle: #f7f8f8; --success-bg-subtle: #d4edda; --info-bg-subtle: #d1ecf1; --warning-bg-subtle: #fbebcc; --danger-bg-subtle: #f6dbde; --light-bg-subtle: #fcfdfd; --dark-bg-subtle: #9a9ca5; --primary-border-subtle: #c6b7c2; --secondary-border-subtle: #eff0f1; --success-border-subtle: #a9dcb5; --info-border-subtle: #a2dae3; --warning-border-subtle: #f6d899; --danger-border-subtle: #eeb7bd; --light-border-subtle: #e7e9ed; --dark-border-subtle: #7c7f89; --white-rgb: 255, 255, 255; --black-rgb: 0, 0, 0; --font-sans-serif: "Odoo Unicode Support Noto", "Lucida Grande", Helvetica, Verdana, Arial, "Odoo Unicode Support Noto", sans-serif; --font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; --gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)); --body-font-family: var(--font-sans-serif); --body-font-size: 1rem; --body-font-weight: 400; --body-line-height: 1.5; --body-color: #111827; --body-color-rgb: 17, 24, 39; --body-bg: #FFF; --body-bg-rgb: 255, 255, 255; --emphasis-color: #000; --emphasis-color-rgb: 0, 0, 0; --secondary-color: rgba(17, 24, 39, 0.75); --secondary-color-rgb: 17, 24, 39; --secondary-bg: #e7e9ed; --secondary-bg-rgb: 231, 233, 237; --tertiary-color: rgba(17, 24, 39, 0.5); --tertiary-color-rgb: 17, 24, 39; --tertiary-bg: #F9FAFB; --tertiary-bg-rgb: 249, 250, 251; --heading-color: #111827; --link-color: #017e84; --link-color-rgb: 1, 126, 132; --link-decoration: none; --link-hover-color: #01585c; --link-hover-color-rgb: 1, 88, 92; --link-hover-decoration: none; --code-color: #d2317b; --highlight-color: #111827; --highlight-bg: #fff3cd; --border-width: 1px; --border-style: solid; --border-color: #111827; --border-color-translucent: rgba(0, 0, 0, 0.175); --border-radius: 0.25rem; --border-radius-sm: 0.1875rem; --border-radius-lg: 0.75rem; --border-radius-xl: 1rem; --border-radius-xxl: 2rem; --border-radius-2xl: var(--border-radius-xxl); --border-radius-pill: 50rem; --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175); --box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075); --focus-ring-width: 0.25rem; --focus-ring-opacity: 0.25; --focus-ring-color: rgba(113, 75, 103, 0.25); --form-valid-color: #28a745; --form-valid-border-color: #28a745; --form-invalid-color: #d44c59; --form-invalid-border-color: #d44c59;}*, *::before, *::after{box-sizing: border-box;}body{margin: 0; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight); line-height: var(--body-line-height); color: var(--body-color); text-align: var(--body-text-align); background-color: var(--body-bg); -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}hr{margin: 16px 0; color: inherit; border: 0; border-top: var(--border-width) solid; opacity: 0.25;}h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1{margin-top: 0; margin-bottom: 0.75rem; font-family: inherit; font-weight: 500; line-height: 1.2; color: var(--heading-color);}h1, .h1{font-size: 2.5rem;}h2, .h2{font-size: 2rem;}h3, .h3{font-size: 1.75rem;}h4, .h4{font-size: 1.5rem;}h5, .h5{font-size: 1.25rem;}h6, .h6{font-size: 1rem;}p{margin-top: 0; margin-bottom: 1rem;}abbr[title]{text-decoration: underline dotted; cursor: help; text-decoration-skip-ink: none;}address{margin-bottom: 1rem; font-style: normal; line-height: inherit;}ol, ul{padding-left: 2rem;}ol, ul, dl{margin-top: 0; margin-bottom: 1rem;}ol ol, ul ul, ol ul, ul ol{margin-bottom: 0;}dt{font-weight: 700;}dd{margin-bottom: .5rem; margin-left: 0;}blockquote{margin: 0 0 1rem;}b, strong{font-weight: bolder;}small, .small{font-size: 0.8125rem;}mark, .mark{padding: 0.1875em; color: var(--highlight-color); background-color: var(--highlight-bg);}sub, sup{position: relative; font-size: 0.75em; line-height: 0; vertical-align: baseline;}sub{bottom: -.25em;}sup{top: -.5em;}a{color: rgba(var(--link-color-rgb), var(--link-opacity, 1)); text-decoration: none;}a:hover{--link-color-rgb: var(--link-hover-color-rgb); text-decoration: none;}a:not([href]):not([class]), a:not([href]):not([class]):hover{color: inherit; text-decoration: none;}pre, code, kbd, samp{font-family: var(--font-monospace); font-size: 1em;}pre{display: block; margin-top: 0; margin-bottom: 1rem; overflow: auto; font-size: 0.8125rem;}pre code{font-size: inherit; color: inherit; word-break: normal;}code{font-size: 0.8125rem; color: var(--code-color); word-wrap: break-word;}a > code{color: inherit;}kbd{padding: 0.1875rem 0.375rem; font-size: 0.8125rem; color: #374151; background-color: #F9FAFB; border-radius: 0.1875rem;}kbd kbd{padding: 0; font-size: 1em;}figure{margin: 0 0 1rem;}img, svg{vertical-align: middle;}table{caption-side: bottom; border-collapse: collapse;}caption{padding-top: 0.5rem; padding-bottom: 0.5rem; color: var(--secondary-color); text-align: left;}th{font-weight: 500; text-align: inherit; text-align: -webkit-match-parent;}thead, tbody, tfoot, tr, td, th{border-color: inherit; border-style: solid; border-width: 0;}label{display: inline-block;}button{border-radius: 0;}button:focus:not(:focus-visible){outline: 0;}input, button, select, optgroup, textarea{margin: 0; font-family: inherit; font-size: inherit; line-height: inherit;}button, select{text-transform: none;}[role="button"]{cursor: pointer;}select{word-wrap: normal;}select:disabled{opacity: 1;}[list]:not([type="date"]):not([type="datetime-local"]):not([type="month"]):not([type="week"]):not([type="time"])::-webkit-calendar-picker-indicator{display: none !important;}button, [type="button"], [type="reset"], [type="submit"]{-webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled){cursor: pointer;}::-moz-focus-inner{padding: 0; border-style: none;}textarea{resize: vertical;}fieldset{min-width: 0; padding: 0; margin: 0; border: 0;}legend{float: left; width: 100%; padding: 0; margin-bottom: 0.5rem; font-size: 1.5rem; line-height: inherit;}legend + *{clear: left;}::-webkit-datetime-edit-fields-wrapper, ::-webkit-datetime-edit-text, ::-webkit-datetime-edit-minute, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-year-field{padding: 0;}::-webkit-inner-spin-button{height: auto;}[type="search"]{-webkit--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield; outline-offset: -2px;}::-webkit-search-decoration{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;}::-webkit-color-swatch-wrapper{padding: 0;}::file-selector-button{font: inherit; -webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}output{display: inline-block;}iframe{border: 0;}summary{display: list-item; cursor: pointer;}progress{vertical-align: baseline;}[hidden]{display: none !important;}.lead{font-size: 1.25rem; font-weight: 300;}.display-1{font-size: 5rem; font-weight: 300; line-height: 1.2;}.display-2{font-size: 4.5rem; font-weight: 300; line-height: 1.2;}.display-3{font-size: 4rem; font-weight: 300; line-height: 1.2;}.display-4{font-size: 3.5rem; font-weight: 300; line-height: 1.2;}.display-5{font-size: 3rem; font-weight: 300; line-height: 1.2;}.display-6{font-size: 2.5rem; font-weight: 300; line-height: 1.2;}.list-unstyled{padding-left: 0; list-style: none;}.list-inline{padding-left: 0; list-style: none;}.list-inline-item{display: inline-block;}.list-inline-item:not(:last-child){margin-right: 0.5rem;}.initialism{font-size: 0.8125rem; text-transform: uppercase;}.blockquote{margin-bottom: 16px; font-size: 1.25rem;}.blockquote > :last-child{margin-bottom: 0;}.blockquote-footer{margin-top: -16px; margin-bottom: 16px; font-size: 0.8125rem; color: #5f636f;}.blockquote-footer::before{content: "\2014\00A0";}.img-fluid{max-width: 100%; height: auto;}.img-thumbnail{padding: 0.25rem; background-color: var(--body-bg); border: var(--border-width) solid var(--border-color); border-radius: 0; box-shadow: 0; max-width: 100%; height: auto;}.figure{display: inline-block;}.figure-img{margin-bottom: 8px; line-height: 1;}.figure-caption{font-size: 0.8125rem; color: var(--secondary-color);}.container, .o_container_small, .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm{--gutter-x: 16px; --gutter-y: 0; width: 100%; padding-right: calc(var(--gutter-x) * .5); padding-left: calc(var(--gutter-x) * .5); margin-right: auto; margin-left: auto;}@media (min-width: 576px){.container-sm, .container, .o_container_small{max-width: 540px;}}@media (min-width: 768px){.container-md, .container-sm, .container, .o_container_small{max-width: 720px;}}@media (min-width: 992px){.container-lg, .container-md, .container-sm, .container, .o_container_small{max-width: 960px;}}@media (min-width: 1200px){.container-xl, .container-lg, .container-md, .container-sm, .container, .o_container_small{max-width: 1140px;}}@media (min-width: 1400px){.container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container, .o_container_small{max-width: 1320px;}}:root{--breakpoint-xs: 0; --breakpoint-sm: 576px; --breakpoint-md: 768px; --breakpoint-lg: 992px; --breakpoint-xl: 1200px; --breakpoint-xxl: 1400px;}.row{--gutter-x: 32px; --gutter-y: 0; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-top: calc(-1 * var(--gutter-y)); margin-right: calc(-.5 * var(--gutter-x)); margin-left: calc(-.5 * var(--gutter-x));}.row > *{flex-shrink: 0; width: 100%; max-width: 100%; padding-right: calc(var(--gutter-x) * .5); padding-left: calc(var(--gutter-x) * .5); margin-top: var(--gutter-y);}.grid{display: grid; grid-template-rows: repeat(var(--rows, 1), 1fr); grid-template-columns: repeat(var(--columns, 12), 1fr); gap: var(--gap, 32px);}.grid .g-col-1{grid-column: auto/span 1;}.grid .g-col-2{grid-column: auto/span 2;}.grid .g-col-3{grid-column: auto/span 3;}.grid .g-col-4{grid-column: auto/span 4;}.grid .g-col-5{grid-column: auto/span 5;}.grid .g-col-6{grid-column: auto/span 6;}.grid .g-col-7{grid-column: auto/span 7;}.grid .g-col-8{grid-column: auto/span 8;}.grid .g-col-9{grid-column: auto/span 9;}.grid .g-col-10{grid-column: auto/span 10;}.grid .g-col-11{grid-column: auto/span 11;}.grid .g-col-12{grid-column: auto/span 12;}.grid .g-start-1{grid-column-start: 1;}.grid .g-start-2{grid-column-start: 2;}.grid .g-start-3{grid-column-start: 3;}.grid .g-start-4{grid-column-start: 4;}.grid .g-start-5{grid-column-start: 5;}.grid .g-start-6{grid-column-start: 6;}.grid .g-start-7{grid-column-start: 7;}.grid .g-start-8{grid-column-start: 8;}.grid .g-start-9{grid-column-start: 9;}.grid .g-start-10{grid-column-start: 10;}.grid .g-start-11{grid-column-start: 11;}@media (min-width: 576px){.grid .g-col-sm-1{grid-column: auto/span 1;}.grid .g-col-sm-2{grid-column: auto/span 2;}.grid .g-col-sm-3{grid-column: auto/span 3;}.grid .g-col-sm-4{grid-column: auto/span 4;}.grid .g-col-sm-5{grid-column: auto/span 5;}.grid .g-col-sm-6{grid-column: auto/span 6;}.grid .g-col-sm-7{grid-column: auto/span 7;}.grid .g-col-sm-8{grid-column: auto/span 8;}.grid .g-col-sm-9{grid-column: auto/span 9;}.grid .g-col-sm-10{grid-column: auto/span 10;}.grid .g-col-sm-11{grid-column: auto/span 11;}.grid .g-col-sm-12{grid-column: auto/span 12;}.grid .g-start-sm-1{grid-column-start: 1;}.grid .g-start-sm-2{grid-column-start: 2;}.grid .g-start-sm-3{grid-column-start: 3;}.grid .g-start-sm-4{grid-column-start: 4;}.grid .g-start-sm-5{grid-column-start: 5;}.grid .g-start-sm-6{grid-column-start: 6;}.grid .g-start-sm-7{grid-column-start: 7;}.grid .g-start-sm-8{grid-column-start: 8;}.grid .g-start-sm-9{grid-column-start: 9;}.grid .g-start-sm-10{grid-column-start: 10;}.grid .g-start-sm-11{grid-column-start: 11;}}@media (min-width: 768px){.grid .g-col-md-1{grid-column: auto/span 1;}.grid .g-col-md-2{grid-column: auto/span 2;}.grid .g-col-md-3{grid-column: auto/span 3;}.grid .g-col-md-4{grid-column: auto/span 4;}.grid .g-col-md-5{grid-column: auto/span 5;}.grid .g-col-md-6{grid-column: auto/span 6;}.grid .g-col-md-7{grid-column: auto/span 7;}.grid .g-col-md-8{grid-column: auto/span 8;}.grid .g-col-md-9{grid-column: auto/span 9;}.grid .g-col-md-10{grid-column: auto/span 10;}.grid .g-col-md-11{grid-column: auto/span 11;}.grid .g-col-md-12{grid-column: auto/span 12;}.grid .g-start-md-1{grid-column-start: 1;}.grid .g-start-md-2{grid-column-start: 2;}.grid .g-start-md-3{grid-column-start: 3;}.grid .g-start-md-4{grid-column-start: 4;}.grid .g-start-md-5{grid-column-start: 5;}.grid .g-start-md-6{grid-column-start: 6;}.grid .g-start-md-7{grid-column-start: 7;}.grid .g-start-md-8{grid-column-start: 8;}.grid .g-start-md-9{grid-column-start: 9;}.grid .g-start-md-10{grid-column-start: 10;}.grid .g-start-md-11{grid-column-start: 11;}}@media (min-width: 992px){.grid .g-col-lg-1{grid-column: auto/span 1;}.grid .g-col-lg-2{grid-column: auto/span 2;}.grid .g-col-lg-3{grid-column: auto/span 3;}.grid .g-col-lg-4{grid-column: auto/span 4;}.grid .g-col-lg-5{grid-column: auto/span 5;}.grid .g-col-lg-6{grid-column: auto/span 6;}.grid .g-col-lg-7{grid-column: auto/span 7;}.grid .g-col-lg-8{grid-column: auto/span 8;}.grid .g-col-lg-9{grid-column: auto/span 9;}.grid .g-col-lg-10{grid-column: auto/span 10;}.grid .g-col-lg-11{grid-column: auto/span 11;}.grid .g-col-lg-12{grid-column: auto/span 12;}.grid .g-start-lg-1{grid-column-start: 1;}.grid .g-start-lg-2{grid-column-start: 2;}.grid .g-start-lg-3{grid-column-start: 3;}.grid .g-start-lg-4{grid-column-start: 4;}.grid .g-start-lg-5{grid-column-start: 5;}.grid .g-start-lg-6{grid-column-start: 6;}.grid .g-start-lg-7{grid-column-start: 7;}.grid .g-start-lg-8{grid-column-start: 8;}.grid .g-start-lg-9{grid-column-start: 9;}.grid .g-start-lg-10{grid-column-start: 10;}.grid .g-start-lg-11{grid-column-start: 11;}}@media (min-width: 1200px){.grid .g-col-xl-1{grid-column: auto/span 1;}.grid .g-col-xl-2{grid-column: auto/span 2;}.grid .g-col-xl-3{grid-column: auto/span 3;}.grid .g-col-xl-4{grid-column: auto/span 4;}.grid .g-col-xl-5{grid-column: auto/span 5;}.grid .g-col-xl-6{grid-column: auto/span 6;}.grid .g-col-xl-7{grid-column: auto/span 7;}.grid .g-col-xl-8{grid-column: auto/span 8;}.grid .g-col-xl-9{grid-column: auto/span 9;}.grid .g-col-xl-10{grid-column: auto/span 10;}.grid .g-col-xl-11{grid-column: auto/span 11;}.grid .g-col-xl-12{grid-column: auto/span 12;}.grid .g-start-xl-1{grid-column-start: 1;}.grid .g-start-xl-2{grid-column-start: 2;}.grid .g-start-xl-3{grid-column-start: 3;}.grid .g-start-xl-4{grid-column-start: 4;}.grid .g-start-xl-5{grid-column-start: 5;}.grid .g-start-xl-6{grid-column-start: 6;}.grid .g-start-xl-7{grid-column-start: 7;}.grid .g-start-xl-8{grid-column-start: 8;}.grid .g-start-xl-9{grid-column-start: 9;}.grid .g-start-xl-10{grid-column-start: 10;}.grid .g-start-xl-11{grid-column-start: 11;}}@media (min-width: 1400px){.grid .g-col-xxl-1{grid-column: auto/span 1;}.grid .g-col-xxl-2{grid-column: auto/span 2;}.grid .g-col-xxl-3{grid-column: auto/span 3;}.grid .g-col-xxl-4{grid-column: auto/span 4;}.grid .g-col-xxl-5{grid-column: auto/span 5;}.grid .g-col-xxl-6{grid-column: auto/span 6;}.grid .g-col-xxl-7{grid-column: auto/span 7;}.grid .g-col-xxl-8{grid-column: auto/span 8;}.grid .g-col-xxl-9{grid-column: auto/span 9;}.grid .g-col-xxl-10{grid-column: auto/span 10;}.grid .g-col-xxl-11{grid-column: auto/span 11;}.grid .g-col-xxl-12{grid-column: auto/span 12;}.grid .g-start-xxl-1{grid-column-start: 1;}.grid .g-start-xxl-2{grid-column-start: 2;}.grid .g-start-xxl-3{grid-column-start: 3;}.grid .g-start-xxl-4{grid-column-start: 4;}.grid .g-start-xxl-5{grid-column-start: 5;}.grid .g-start-xxl-6{grid-column-start: 6;}.grid .g-start-xxl-7{grid-column-start: 7;}.grid .g-start-xxl-8{grid-column-start: 8;}.grid .g-start-xxl-9{grid-column-start: 9;}.grid .g-start-xxl-10{grid-column-start: 10;}.grid .g-start-xxl-11{grid-column-start: 11;}}.col{flex: 1 0 0%;}.row-cols-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-1{margin-left: 8.33333333%;}.offset-2{margin-left: 16.66666667%;}.offset-3{margin-left: 25%;}.offset-4{margin-left: 33.33333333%;}.offset-5{margin-left: 41.66666667%;}.offset-6{margin-left: 50%;}.offset-7{margin-left: 58.33333333%;}.offset-8{margin-left: 66.66666667%;}.offset-9{margin-left: 75%;}.offset-10{margin-left: 83.33333333%;}.offset-11{margin-left: 91.66666667%;}.g-0, .gx-0{--gutter-x: 0;}.g-0, .gy-0{--gutter-y: 0;}.g-1, .gx-1{--gutter-x: 4px;}.g-1, .gy-1{--gutter-y: 4px;}.g-2, .gx-2{--gutter-x: 8px;}.g-2, .gy-2{--gutter-y: 8px;}.g-3, .gx-3{--gutter-x: 16px;}.g-3, .gy-3{--gutter-y: 16px;}.g-4, .gx-4{--gutter-x: 24px;}.g-4, .gy-4{--gutter-y: 24px;}.g-5, .gx-5{--gutter-x: 48px;}.g-5, .gy-5{--gutter-y: 48px;}@media (min-width: 576px){.col-sm{flex: 1 0 0%;}.row-cols-sm-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-sm-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-sm-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-sm-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-sm-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-sm-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-sm-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-sm-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-sm-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-sm-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-sm-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-sm-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-sm-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-sm-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-sm-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-sm-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-sm-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-sm-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-sm-0{margin-left: 0;}.offset-sm-1{margin-left: 8.33333333%;}.offset-sm-2{margin-left: 16.66666667%;}.offset-sm-3{margin-left: 25%;}.offset-sm-4{margin-left: 33.33333333%;}.offset-sm-5{margin-left: 41.66666667%;}.offset-sm-6{margin-left: 50%;}.offset-sm-7{margin-left: 58.33333333%;}.offset-sm-8{margin-left: 66.66666667%;}.offset-sm-9{margin-left: 75%;}.offset-sm-10{margin-left: 83.33333333%;}.offset-sm-11{margin-left: 91.66666667%;}.g-sm-0, .gx-sm-0{--gutter-x: 0;}.g-sm-0, .gy-sm-0{--gutter-y: 0;}.g-sm-1, .gx-sm-1{--gutter-x: 4px;}.g-sm-1, .gy-sm-1{--gutter-y: 4px;}.g-sm-2, .gx-sm-2{--gutter-x: 8px;}.g-sm-2, .gy-sm-2{--gutter-y: 8px;}.g-sm-3, .gx-sm-3{--gutter-x: 16px;}.g-sm-3, .gy-sm-3{--gutter-y: 16px;}.g-sm-4, .gx-sm-4{--gutter-x: 24px;}.g-sm-4, .gy-sm-4{--gutter-y: 24px;}.g-sm-5, .gx-sm-5{--gutter-x: 48px;}.g-sm-5, .gy-sm-5{--gutter-y: 48px;}}@media (min-width: 768px){.col-md{flex: 1 0 0%;}.row-cols-md-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-md-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-md-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-md-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-md-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-md-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-md-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-md-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-md-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-md-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-md-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-md-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-md-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-md-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-md-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-md-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-md-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-md-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-md-0{margin-left: 0;}.offset-md-1{margin-left: 8.33333333%;}.offset-md-2{margin-left: 16.66666667%;}.offset-md-3{margin-left: 25%;}.offset-md-4{margin-left: 33.33333333%;}.offset-md-5{margin-left: 41.66666667%;}.offset-md-6{margin-left: 50%;}.offset-md-7{margin-left: 58.33333333%;}.offset-md-8{margin-left: 66.66666667%;}.offset-md-9{margin-left: 75%;}.offset-md-10{margin-left: 83.33333333%;}.offset-md-11{margin-left: 91.66666667%;}.g-md-0, .gx-md-0{--gutter-x: 0;}.g-md-0, .gy-md-0{--gutter-y: 0;}.g-md-1, .gx-md-1{--gutter-x: 4px;}.g-md-1, .gy-md-1{--gutter-y: 4px;}.g-md-2, .gx-md-2{--gutter-x: 8px;}.g-md-2, .gy-md-2{--gutter-y: 8px;}.g-md-3, .gx-md-3{--gutter-x: 16px;}.g-md-3, .gy-md-3{--gutter-y: 16px;}.g-md-4, .gx-md-4{--gutter-x: 24px;}.g-md-4, .gy-md-4{--gutter-y: 24px;}.g-md-5, .gx-md-5{--gutter-x: 48px;}.g-md-5, .gy-md-5{--gutter-y: 48px;}}@media (min-width: 992px){.col-lg{flex: 1 0 0%;}.row-cols-lg-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-lg-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-lg-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-lg-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-lg-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-lg-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-lg-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-lg-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-lg-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-lg-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-lg-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-lg-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-lg-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-lg-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-lg-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-lg-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-lg-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-lg-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-lg-0{margin-left: 0;}.offset-lg-1{margin-left: 8.33333333%;}.offset-lg-2{margin-left: 16.66666667%;}.offset-lg-3{margin-left: 25%;}.offset-lg-4{margin-left: 33.33333333%;}.offset-lg-5{margin-left: 41.66666667%;}.offset-lg-6{margin-left: 50%;}.offset-lg-7{margin-left: 58.33333333%;}.offset-lg-8{margin-left: 66.66666667%;}.offset-lg-9{margin-left: 75%;}.offset-lg-10{margin-left: 83.33333333%;}.offset-lg-11{margin-left: 91.66666667%;}.g-lg-0, .gx-lg-0{--gutter-x: 0;}.g-lg-0, .gy-lg-0{--gutter-y: 0;}.g-lg-1, .gx-lg-1{--gutter-x: 4px;}.g-lg-1, .gy-lg-1{--gutter-y: 4px;}.g-lg-2, .gx-lg-2{--gutter-x: 8px;}.g-lg-2, .gy-lg-2{--gutter-y: 8px;}.g-lg-3, .gx-lg-3{--gutter-x: 16px;}.g-lg-3, .gy-lg-3{--gutter-y: 16px;}.g-lg-4, .gx-lg-4{--gutter-x: 24px;}.g-lg-4, .gy-lg-4{--gutter-y: 24px;}.g-lg-5, .gx-lg-5{--gutter-x: 48px;}.g-lg-5, .gy-lg-5{--gutter-y: 48px;}}@media (min-width: 1200px){.col-xl{flex: 1 0 0%;}.row-cols-xl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xl-0{margin-left: 0;}.offset-xl-1{margin-left: 8.33333333%;}.offset-xl-2{margin-left: 16.66666667%;}.offset-xl-3{margin-left: 25%;}.offset-xl-4{margin-left: 33.33333333%;}.offset-xl-5{margin-left: 41.66666667%;}.offset-xl-6{margin-left: 50%;}.offset-xl-7{margin-left: 58.33333333%;}.offset-xl-8{margin-left: 66.66666667%;}.offset-xl-9{margin-left: 75%;}.offset-xl-10{margin-left: 83.33333333%;}.offset-xl-11{margin-left: 91.66666667%;}.g-xl-0, .gx-xl-0{--gutter-x: 0;}.g-xl-0, .gy-xl-0{--gutter-y: 0;}.g-xl-1, .gx-xl-1{--gutter-x: 4px;}.g-xl-1, .gy-xl-1{--gutter-y: 4px;}.g-xl-2, .gx-xl-2{--gutter-x: 8px;}.g-xl-2, .gy-xl-2{--gutter-y: 8px;}.g-xl-3, .gx-xl-3{--gutter-x: 16px;}.g-xl-3, .gy-xl-3{--gutter-y: 16px;}.g-xl-4, .gx-xl-4{--gutter-x: 24px;}.g-xl-4, .gy-xl-4{--gutter-y: 24px;}.g-xl-5, .gx-xl-5{--gutter-x: 48px;}.g-xl-5, .gy-xl-5{--gutter-y: 48px;}}@media (min-width: 1400px){.col-xxl{flex: 1 0 0%;}.row-cols-xxl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xxl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xxl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xxl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xxl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xxl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xxl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xxl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xxl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xxl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xxl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xxl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xxl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xxl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xxl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xxl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xxl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xxl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xxl-0{margin-left: 0;}.offset-xxl-1{margin-left: 8.33333333%;}.offset-xxl-2{margin-left: 16.66666667%;}.offset-xxl-3{margin-left: 25%;}.offset-xxl-4{margin-left: 33.33333333%;}.offset-xxl-5{margin-left: 41.66666667%;}.offset-xxl-6{margin-left: 50%;}.offset-xxl-7{margin-left: 58.33333333%;}.offset-xxl-8{margin-left: 66.66666667%;}.offset-xxl-9{margin-left: 75%;}.offset-xxl-10{margin-left: 83.33333333%;}.offset-xxl-11{margin-left: 91.66666667%;}.g-xxl-0, .gx-xxl-0{--gutter-x: 0;}.g-xxl-0, .gy-xxl-0{--gutter-y: 0;}.g-xxl-1, .gx-xxl-1{--gutter-x: 4px;}.g-xxl-1, .gy-xxl-1{--gutter-y: 4px;}.g-xxl-2, .gx-xxl-2{--gutter-x: 8px;}.g-xxl-2, .gy-xxl-2{--gutter-y: 8px;}.g-xxl-3, .gx-xxl-3{--gutter-x: 16px;}.g-xxl-3, .gy-xxl-3{--gutter-y: 16px;}.g-xxl-4, .gx-xxl-4{--gutter-x: 24px;}.g-xxl-4, .gy-xxl-4{--gutter-y: 24px;}.g-xxl-5, .gx-xxl-5{--gutter-x: 48px;}.g-xxl-5, .gy-xxl-5{--gutter-y: 48px;}}.table{--table-color-type: initial; --table-bg-type: initial; --table-color-state: initial; --table-bg-state: initial; --table-color: #111827; --table-bg: transparent; --table-border-color: #111827; --table-accent-bg: transparent; --table-striped-color: inherit; --table-striped-bg: rgba(var(--emphasis-color-rgb), 0.01); --table-active-color: #111827; --table-active-bg: rgba(var(--emphasis-color-rgb), 0.05); --table-hover-color: #111827; --table-hover-bg: rgba(var(--emphasis-color-rgb), 0.055); width: 100%; margin-bottom: 16px; vertical-align: top; border-color: var(--table-border-color);}.table > :not(caption) > * > *{padding: 0.5rem 0.5rem; color: var(--table-color-state, var(--table-color-type, var(--table-color))); background-color: var(--table-bg); border-bottom-width: var(--border-width); box-shadow: inset 0 0 0 9999px var(--table-bg-state, var(--table-bg-type, var(--table-accent-bg)));}.table > tbody{vertical-align: inherit;}.table > thead{vertical-align: bottom;}.table-group-divider{border-top: calc(var(--border-width) * 2) solid #e7e9ed;}.caption-top{caption-side: top;}.table-sm > :not(caption) > * > *{padding: 0.25rem 0.25rem;}.table-bordered > :not(caption) > *{border-width: var(--border-width) 0;}.table-bordered > :not(caption) > * > *{border-width: 0 var(--border-width);}.table-borderless > :not(caption) > * > *{border-bottom-width: 0;}.table-borderless > :not(:first-child){border-top-width: 0;}.table-striped > tbody > tr:nth-of-type(even) > *{--table-color-type: var(--table-striped-color); --table-bg-type: var(--table-striped-bg);}.table-striped-columns > :not(caption) > tr > :nth-child(even){--table-color-type: var(--table-striped-color); --table-bg-type: var(--table-striped-bg);}.table-active{--table-color-state: var(--table-active-color); --table-bg-state: var(--table-active-bg);}.table-hover > tbody > tr:hover > *{--table-color-state: var(--table-hover-color); --table-bg-state: var(--table-hover-bg);}.table-primary{--table-color: #000; --table-bg: #e3dbe1; --table-border-color: #b6afb4; --table-striped-bg: #e1d9df; --table-striped-color: #000; --table-active-bg: #d8d0d6; --table-active-color: #000; --table-hover-bg: #d7cfd5; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-secondary{--table-color: #000; --table-bg: #f7f8f8; --table-border-color: #c6c6c6; --table-striped-bg: #f5f6f6; --table-striped-color: #000; --table-active-bg: #ebecec; --table-active-color: #000; --table-hover-bg: #e9eaea; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-success{--table-color: #000; --table-bg: #d4edda; --table-border-color: #aabeae; --table-striped-bg: #d2ebd8; --table-striped-color: #000; --table-active-bg: #c9e1cf; --table-active-color: #000; --table-hover-bg: #c8e0ce; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-info{--table-color: #000; --table-bg: #d1ecf1; --table-border-color: #a7bdc1; --table-striped-bg: #cfeaef; --table-striped-color: #000; --table-active-bg: #c7e0e5; --table-active-color: #000; --table-hover-bg: #c6dfe4; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-warning{--table-color: #000; --table-bg: #fbebcc; --table-border-color: #c9bca3; --table-striped-bg: #f8e9ca; --table-striped-color: #000; --table-active-bg: #eedfc2; --table-active-color: #000; --table-hover-bg: #eddec1; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-danger{--table-color: #000; --table-bg: #f6dbde; --table-border-color: #c5afb2; --table-striped-bg: #f4d9dc; --table-striped-color: #000; --table-active-bg: #ead0d3; --table-active-color: #000; --table-hover-bg: #e8cfd2; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-light{--table-color: #000; --table-bg: #FFF; --table-border-color: #cccccc; --table-striped-bg: #fcfcfc; --table-striped-color: #000; --table-active-bg: #f2f2f2; --table-active-color: #000; --table-hover-bg: #f1f1f1; --table-hover-color: #000; color: var(--table-color); border-color: var(--table-border-color);}.table-dark{--table-color: #FFF; --table-bg: #111827; --table-border-color: #414652; --table-striped-bg: #131a29; --table-striped-color: #FFF; --table-active-bg: #1d2432; --table-active-color: #FFF; --table-hover-bg: #1e2533; --table-hover-color: #FFF; color: var(--table-color); border-color: var(--table-border-color);}.table-responsive{overflow-x: auto; -webkit-overflow-scrolling: touch;}@media (max-width: 575.98px){.table-responsive-sm{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 767.98px){.table-responsive-md{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 991.98px){.table-responsive-lg{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1199.98px){.table-responsive-xl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1399.98px){.table-responsive-xxl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}.form-label{margin-bottom: 0.5rem;}.col-form-label{padding-top: calc(0.3125rem + var(--border-width)); padding-bottom: calc(0.3125rem + var(--border-width)); margin-bottom: 0; font-size: inherit; line-height: 1.5;}.col-form-label-lg{padding-top: calc(0.375rem + var(--border-width)); padding-bottom: calc(0.375rem + var(--border-width)); font-size: 1.25rem;}.col-form-label-sm{padding-top: calc(0.1875rem + var(--border-width)); padding-bottom: calc(0.1875rem + var(--border-width)); font-size: 0.8125rem;}.form-text{margin-top: 0.25rem; font-size: 0.8125rem; color: var(--secondary-color);}.form-control{display: block; width: 100%; padding: 0.3125rem 0.625rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: var(--body-color); -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: transparent; background-clip: padding-box; border: var(--border-width) solid #e7e9ed; border-radius: var(--border-radius); box-shadow: 0; transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control{transition: none;}}.form-control[type="file"]{overflow: hidden;}.form-control[type="file"]:not(:disabled):not([readonly]){cursor: pointer;}.form-control:focus{color: var(--body-color); background-color: #FFF; border-color: #74b4b9; outline: 0; box-shadow: 0, 0;}.form-control::-webkit-date-and-time-value{min-width: 85px; height: 1.5em; margin: 0;}.form-control::-webkit-datetime-edit{display: block; padding: 0;}.form-control::placeholder{color: #8b8e97; opacity: 1;}.form-control:disabled{background-color: var(--secondary-bg); opacity: 1;}.form-control::file-selector-button{padding: 0.3125rem 0.625rem; margin: -0.3125rem -0.625rem; margin-inline-end: 0.625rem; color: var(--body-color); background-color: var(--tertiary-bg); pointer-events: none; border-color: inherit; border-style: solid; border-width: 0; border-inline-end-width: var(--border-width); border-radius: 0; transition: none;}.form-control:hover:not(:disabled):not([readonly])::file-selector-button{background-color: var(--secondary-bg);}.form-control-plaintext{display: block; width: 100%; padding: 0.3125rem 0; margin-bottom: 0; line-height: 1.5; color: var(--body-color); background-color: transparent; border: solid transparent; border-width: var(--border-width) 0;}.form-control-plaintext:focus{outline: 0;}.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg{padding-right: 0; padding-left: 0;}.form-control-sm{min-height: calc(1.5em + 0.375rem + calc(var(--border-width) * 2)); padding: 0.1875rem 0.5rem; font-size: 0.8125rem; border-radius: var(--border-radius-sm);}.form-control-sm::file-selector-button{padding: 0.1875rem 0.5rem; margin: -0.1875rem -0.5rem; margin-inline-end: 0.5rem;}.form-control-lg{min-height: calc(1.5em + 0.75rem + calc(var(--border-width) * 2)); padding: 0.375rem 0.75rem; font-size: 1.25rem; border-radius: var(--border-radius-lg);}.form-control-lg::file-selector-button{padding: 0.375rem 0.75rem; margin: -0.375rem -0.75rem; margin-inline-end: 0.75rem;}textarea.form-control{min-height: calc(1.5em + 0.625rem + calc(var(--border-width) * 2));}textarea.form-control-sm{min-height: calc(1.5em + 0.375rem + calc(var(--border-width) * 2));}textarea.form-control-lg{min-height: calc(1.5em + 0.75rem + calc(var(--border-width) * 2));}.form-control-color{width: 3rem; height: calc(1.5em + 0.625rem + calc(var(--border-width) * 2)); padding: 0.3125rem;}.form-control-color:not(:disabled):not([readonly]){cursor: pointer;}.form-control-color::-moz-color-swatch{border: 0 !important; border-radius: var(--border-radius);}.form-control-color::-webkit-color-swatch{border: 0 !important; border-radius: var(--border-radius);}.form-control-color.form-control-sm{height: calc(1.5em + 0.375rem + calc(var(--border-width) * 2));}.form-control-color.form-control-lg{height: calc(1.5em + 0.75rem + calc(var(--border-width) * 2));}.form-select{--form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231F2937' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"); display: block; width: 100%; padding: 0.3125rem 1.875rem 0.3125rem 0.625rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: var(--body-color); -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: transparent; background-image: var(--form-select-bg-img), var(--form-select-bg-icon, none); background-repeat: no-repeat; background-position: right 0.625rem center; background-size: 16px 12px; border: var(--border-width) solid #e7e9ed; border-radius: var(--border-radius); box-shadow: var(--box-shadow-inset); transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-select{transition: none;}}.form-select:focus{border-color: #74b4b9; outline: 0; box-shadow: var(--box-shadow-inset), 0;}.form-select[multiple], .form-select[size]:not([size="1"]){padding-right: 0.625rem; background-image: none;}.form-select:disabled{background-color: var(--secondary-bg);}.form-select:-moz-focusring{color: transparent; text-shadow: 0 0 0 var(--body-color);}.form-select-sm{padding-top: 0.1875rem; padding-bottom: 0.1875rem; padding-left: 0.5rem; font-size: 0.8125rem; border-radius: var(--border-radius-sm);}.form-select-lg{padding-top: 0.375rem; padding-bottom: 0.375rem; padding-left: 0.75rem; font-size: 1.25rem; border-radius: var(--border-radius-lg);}.form-check{display: block; min-height: 1.5rem; padding-left: 1.5em; margin-bottom: 0.125rem;}.form-check .form-check-input{float: left; margin-left: -1.5em;}.form-check-reverse{padding-right: 1.5em; padding-left: 0; text-align: right;}.form-check-reverse .form-check-input{float: right; margin-right: -1.5em; margin-left: 0;}.form-check-input{--form-check-bg: transparent; flex-shrink: 0; width: 1em; height: 1em; margin-top: 0.25em; vertical-align: top; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: var(--form-check-bg); background-image: var(--form-check-bg-image); background-repeat: no-repeat; background-position: center; background-size: contain; border: var(--border-width) solid var(--border-color); print-color-adjust: exact;}.form-check-input[type="checkbox"]{border-radius: 0;}.form-check-input[type="radio"]{border-radius: 50%;}.form-check-input:active{filter: brightness(90%);}.form-check-input:focus{border-color: #74b4b9; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.form-check-input:checked{background-color: #017e84; border-color: #017e84;}.form-check-input:checked[type="checkbox"]{--form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23FFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");}.form-check-input:checked[type="radio"]{--form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23FFF'/%3e%3c/svg%3e");}.form-check-input[type="checkbox"]:indeterminate{background-color: #e6f2f3; border-color: #e6f2f3; --form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='unset' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");}.form-check-input:disabled{pointer-events: none; filter: none; opacity: 0.5;}.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label{cursor: default; opacity: 0.5;}.form-switch{padding-left: 2.5em;}.form-switch .form-check-input{--form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e"); width: 2em; margin-left: -2.5em; background-image: var(--form-switch-bg); background-position: left center; border-radius: 2em; transition: background-position 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-switch .form-check-input{transition: none;}}.form-switch .form-check-input:focus{--form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%2840, 167, 69, 0.5%29'/%3e%3c/svg%3e");}.form-switch .form-check-input:checked{background-position: right center; --form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23FFF'/%3e%3c/svg%3e");}.form-switch.form-check-reverse{padding-right: 2.5em; padding-left: 0;}.form-switch.form-check-reverse .form-check-input{margin-right: -2.5em; margin-left: 0;}.form-check-inline{display: inline-block; margin-right: 1rem;}.btn-check{position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none;}.btn-check[disabled] + .btn, .btn-check:disabled + .btn{pointer-events: none; filter: none; opacity: 0.5;}.form-range{width: 100%; height: 1.5rem; padding: 0; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: transparent;}.form-range:focus{outline: 0;}.form-range:focus::-webkit-slider-thumb{box-shadow: 0 0 0 1px #714B67;}.form-range:focus::-moz-range-thumb{box-shadow: 0 0 0 1px #714B67;}.form-range::-moz-focus-outer{border: 0;}.form-range::-webkit-slider-thumb{width: 1rem; height: 1rem; margin-top: -0.25rem; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: #714B67; border: 0; border-radius: 1rem; box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1); transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-range::-webkit-slider-thumb{transition: none;}}.form-range::-webkit-slider-thumb:active{background-color: #f8fbfb;}.form-range::-webkit-slider-runnable-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: var(--secondary-bg); border-color: transparent; border-radius: 1rem; box-shadow: 0;}.form-range::-moz-range-thumb{width: 1rem; height: 1rem; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: #714B67; border: 0; border-radius: 1rem; box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1); transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-range::-moz-range-thumb{transition: none;}}.form-range::-moz-range-thumb:active{background-color: #f8fbfb;}.form-range::-moz-range-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: var(--secondary-bg); border-color: transparent; border-radius: 1rem; box-shadow: 0;}.form-range:disabled{pointer-events: none;}.form-range:disabled::-webkit-slider-thumb{background-color: var(--secondary-color);}.form-range:disabled::-moz-range-thumb{background-color: var(--secondary-color);}.form-floating{position: relative;}.form-floating > .form-control, .form-floating > .form-control-plaintext, .form-floating > .form-select{height: calc(3.5rem + calc(var(--border-width) * 2)); min-height: calc(3.5rem + calc(var(--border-width) * 2)); line-height: 1.25;}.form-floating > label{position: absolute; top: 0; left: 0; z-index: 2; height: 100%; padding: 1rem 0.625rem; overflow: hidden; text-align: start; text-overflow: ellipsis; white-space: nowrap; pointer-events: none; border: var(--border-width) solid transparent; transform-origin: 0 0; transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-floating > label{transition: none;}}.form-floating > .form-control, .form-floating > .form-control-plaintext{padding: 1rem 0.625rem;}.form-floating > .form-control::placeholder, .form-floating > .form-control-plaintext::placeholder{color: transparent;}.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown), .form-floating > .form-control-plaintext:focus, .form-floating > .form-control-plaintext:not(:placeholder-shown){padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:-webkit-autofill, .form-floating > .form-control-plaintext:-webkit-autofill{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-select{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:focus ~ label, .form-floating > .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-control-plaintext ~ label, .form-floating > .form-select ~ label{color: rgba(var(--body-color-rgb), 0.65); transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.form-floating > .form-control:focus ~ label::after, .form-floating > .form-control:not(:placeholder-shown) ~ label::after, .form-floating > .form-control-plaintext ~ label::after, .form-floating > .form-select ~ label::after{position: absolute; inset: 1rem 0.3125rem; z-index: -1; height: 1.5em; content: ""; background-color: transparent; border-radius: var(--border-radius);}.form-floating > .form-control:-webkit-autofill ~ label{color: rgba(var(--body-color-rgb), 0.65); transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.form-floating > .form-control-plaintext ~ label{border-width: var(--border-width) 0;}.form-floating > :disabled ~ label, .form-floating > .form-control:disabled ~ label{color: #5f636f;}.form-floating > :disabled ~ label::after, .form-floating > .form-control:disabled ~ label::after{background-color: var(--secondary-bg);}.input-group{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: stretch; width: 100%;}.input-group > .form-control, .input-group > .form-select, .input-group > .form-floating{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 1%; min-width: 0;}.input-group > .form-control:focus, .input-group > .form-select:focus, .input-group > .form-floating:focus-within{z-index: 5;}.input-group .btn{position: relative; z-index: 2;}.input-group .btn:focus{z-index: 5;}.input-group-text{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.3125rem 0.625rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: var(--body-color); text-align: center; white-space: nowrap; background-color: var(--tertiary-bg); border: var(--border-width) solid #e7e9ed; border-radius: var(--border-radius);}.input-group-lg > .form-control, .input-group-lg > .form-select, .input-group-lg > .input-group-text, .input-group-lg > .btn{padding: 0.375rem 0.75rem; font-size: 1.25rem; border-radius: var(--border-radius-lg);}.input-group-sm > .form-control, .input-group-sm > .form-select, .input-group-sm > .input-group-text, .input-group-sm > .btn{padding: 0.1875rem 0.5rem; font-size: 0.8125rem; border-radius: var(--border-radius-sm);}.input-group-lg > .form-select, .input-group-sm > .form-select{padding-right: 2.5rem;}.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating), .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3), .input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control, .input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select{border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group.has-validation > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating), .input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4), .input-group.has-validation > .form-floating:nth-last-child(n + 3) > .form-control, .input-group.has-validation > .form-floating:nth-last-child(n + 3) > .form-select{border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback){margin-left: calc(var(--border-width) * -1); border-top-left-radius: 0; border-bottom-left-radius: 0;}.input-group > .form-floating:not(:first-child) > .form-control, .input-group > .form-floating:not(:first-child) > .form-select{border-top-left-radius: 0; border-bottom-left-radius: 0;}.valid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.8125rem; color: var(--form-valid-color);}.valid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 4px 8px; margin-top: .1rem; font-size: 0.8125rem; color: #fff; background-color: var(--success); border-radius: var(--border-radius);}.was-validated :valid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip, .is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip{display: block;}.was-validated .form-control:valid, .form-control.is-valid{border-color: var(--form-valid-border-color); padding-right: calc(1.5em + 0.625rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.15625rem) center; background-size: calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-control:valid:focus, .form-control.is-valid:focus{border-color: var(--form-valid-border-color); box-shadow: 0, 0 0 0 0.25rem rgba(var(--success-rgb), 0.25);}.was-validated textarea.form-control:valid, textarea.form-control.is-valid{padding-right: calc(1.5em + 0.625rem); background-position: top calc(0.375em + 0.15625rem) right calc(0.375em + 0.15625rem);}.was-validated .form-select:valid, .form-select.is-valid{border-color: var(--form-valid-border-color);}.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"]{--form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); padding-right: 3.4375rem; background-position: right 0.625rem center, center right 1.875rem; background-size: 16px 12px, calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-select:valid:focus, .form-select.is-valid:focus{border-color: var(--form-valid-border-color); box-shadow: var(--box-shadow-inset), 0 0 0 0.25rem rgba(var(--success-rgb), 0.25);}.was-validated .form-control-color:valid, .form-control-color.is-valid{width: calc(3rem + calc(1.5em + 0.625rem));}.was-validated .form-check-input:valid, .form-check-input.is-valid{border-color: var(--form-valid-border-color);}.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked{background-color: var(--form-valid-color);}.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus{box-shadow: 0 0 0 0.25rem rgba(var(--success-rgb), 0.25);}.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label{color: var(--form-valid-color);}.form-check-inline .form-check-input ~ .valid-feedback{margin-left: .5em;}.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid, .was-validated .input-group > .form-select:not(:focus):valid, .input-group > .form-select:not(:focus).is-valid, .was-validated .input-group > .form-floating:not(:focus-within):valid, .input-group > .form-floating:not(:focus-within).is-valid{z-index: 3;}.invalid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.8125rem; color: var(--form-invalid-color);}.invalid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 4px 8px; margin-top: .1rem; font-size: 0.8125rem; color: #fff; background-color: var(--danger); border-radius: var(--border-radius);}.was-validated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-tooltip, .is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip{display: block;}.was-validated .form-control:invalid, .form-control.is-invalid{border-color: var(--form-invalid-border-color); padding-right: calc(1.5em + 0.625rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d44c59'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d44c59' stroke='none'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.15625rem) center; background-size: calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus{border-color: var(--form-invalid-border-color); box-shadow: 0, 0 0 0 0.25rem rgba(var(--danger-rgb), 0.25);}.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid{padding-right: calc(1.5em + 0.625rem); background-position: top calc(0.375em + 0.15625rem) right calc(0.375em + 0.15625rem);}.was-validated .form-select:invalid, .form-select.is-invalid{border-color: var(--form-invalid-border-color);}.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"]{--form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d44c59'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d44c59' stroke='none'/%3e%3c/svg%3e"); padding-right: 3.4375rem; background-position: right 0.625rem center, center right 1.875rem; background-size: 16px 12px, calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus{border-color: var(--form-invalid-border-color); box-shadow: var(--box-shadow-inset), 0 0 0 0.25rem rgba(var(--danger-rgb), 0.25);}.was-validated .form-control-color:invalid, .form-control-color.is-invalid{width: calc(3rem + calc(1.5em + 0.625rem));}.was-validated .form-check-input:invalid, .form-check-input.is-invalid{border-color: var(--form-invalid-border-color);}.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked{background-color: var(--form-invalid-color);}.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus{box-shadow: 0 0 0 0.25rem rgba(var(--danger-rgb), 0.25);}.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label{color: var(--form-invalid-color);}.form-check-inline .form-check-input ~ .invalid-feedback{margin-left: .5em;}.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid, .was-validated .input-group > .form-select:not(:focus):invalid, .input-group > .form-select:not(:focus).is-invalid, .was-validated .input-group > .form-floating:not(:focus-within):invalid, .input-group > .form-floating:not(:focus-within).is-invalid{z-index: 4;}.btn{--btn-padding-x: 0.625rem; --btn-padding-y: 0.3125rem; --btn-font-family: ; --btn-font-size: 1rem; --btn-font-weight: 700; --btn-line-height: 1.5; --btn-color: var(--body-color); --btn-bg: transparent; --btn-border-width: var(--border-width); --btn-border-color: transparent; --btn-border-radius: var(--border-radius); --btn-hover-border-color: transparent; --btn-box-shadow: 0; --btn-disabled-opacity: 0.5; --btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--btn-focus-shadow-rgb), .5); display: inline-block; padding: var(--btn-padding-y) var(--btn-padding-x); font-family: var(--btn-font-family); font-size: var(--btn-font-size); font-weight: var(--btn-font-weight); line-height: var(--btn-line-height); color: var(--btn-color); text-align: center; vertical-align: middle; cursor: pointer; user-select: none; border: var(--btn-border-width) solid var(--btn-border-color); border-radius: var(--btn-border-radius); background-color: var(--btn-bg); box-shadow: var(--btn-box-shadow); transition: none;}.btn:hover{color: var(--btn-hover-color); background-color: var(--btn-hover-bg); border-color: var(--btn-hover-border-color);}.btn-check + .btn:hover{color: var(--btn-color); background-color: var(--btn-bg); border-color: var(--btn-border-color);}.btn:focus-visible{color: var(--btn-hover-color); background-color: var(--btn-hover-bg); border-color: var(--btn-hover-border-color); outline: 0; box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);}.btn-check:focus-visible + .btn{border-color: var(--btn-hover-border-color); outline: 0; box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);}.btn-check:checked + .btn, :not(.btn-check) + .btn:active, .btn:first-child:active, .btn.active, .btn.show{color: var(--btn-active-color); background-color: var(--btn-active-bg); border-color: var(--btn-active-border-color); box-shadow: var(--btn-active-shadow);}.btn-check:checked + .btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible{box-shadow: var(--btn-active-shadow), var(--btn-focus-box-shadow);}.btn-check:checked:focus-visible + .btn{box-shadow: var(--btn-active-shadow), var(--btn-focus-box-shadow);}.btn:disabled, .btn.disabled, fieldset:disabled .btn{color: var(--btn-disabled-color); pointer-events: none; background-color: var(--btn-disabled-bg); border-color: var(--btn-disabled-border-color); opacity: var(--btn-disabled-opacity); box-shadow: none;}.btn-link{--btn-font-weight: 400; --btn-color: var(--link-color); --btn-bg: transparent; --btn-border-color: transparent; --btn-hover-color: var(--link-hover-color); --btn-hover-border-color: transparent; --btn-active-color: var(--link-hover-color); --btn-active-border-color: transparent; --btn-disabled-color: #5f636f; --btn-disabled-border-color: transparent; --btn-box-shadow: 0 0 0 #000; --btn-focus-shadow-rgb: 39, 145, 150; text-decoration: none;}.btn-link:hover, .btn-link:focus-visible{text-decoration: none;}.btn-link:focus-visible{color: var(--btn-color);}.btn-link:hover{color: var(--btn-hover-color);}.btn-lg, .btn-group-lg > .btn{--btn-padding-y: 0.375rem; --btn-padding-x: 0.75rem; --btn-font-size: 1.25rem; --btn-border-radius: 0.25rem;}.btn-sm, .btn-group-sm > .btn{--btn-padding-y: 0.1875rem; --btn-padding-x: 0.5rem; --btn-font-size: 0.8125rem; --btn-border-radius: var(--border-radius-sm);}.fade{transition: opacity 0.15s linear;}@media (prefers-reduced-motion: reduce){.fade{transition: none;}}.fade:not(.show){opacity: 0;}.collapse:not(.show){display: none;}.collapsing{height: 0; overflow: hidden; transition: height 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing{transition: none;}}.collapsing.collapse-horizontal{width: 0; height: auto; transition: width 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing.collapse-horizontal{transition: none;}}.dropup, .dropend, .dropdown, .dropstart, .dropup-center, .dropdown-center{position: relative;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid; border-right: 4px solid transparent; border-bottom: 0; border-left: 4px solid transparent;}.dropdown-toggle:empty::after{margin-left: 0;}.dropdown-menu{--dropdown-zindex: 1000; --dropdown-min-width: 10rem; --dropdown-padding-x: 0; --dropdown-padding-y: 0.5rem; --dropdown-spacer: 0.125rem; --dropdown-font-size: 1rem; --dropdown-color: var(--body-color); --dropdown-bg: var(--body-bg); --dropdown-border-color: #111827; --dropdown-border-radius: var(--border-radius); --dropdown-border-width: var(--border-width); --dropdown-inner-border-radius: calc(var(--border-radius) - var(--border-width)); --dropdown-divider-bg: #111827; --dropdown-divider-margin-y: 8px; --dropdown-box-shadow: 0 0.3rem 1rem rgba(0, 0, 0, 0.1); --dropdown-link-color: #374151; --dropdown-link-hover-color: #111827; --dropdown-link-hover-bg: rgba(0, 0, 0, 0.08); --dropdown-link-active-color: #111827; --dropdown-link-active-bg: transparent; --dropdown-link-disabled-color: rgba(55, 65, 81, 0.76); --dropdown-item-padding-x: 20px; --dropdown-item-padding-y: 3px; --dropdown-header-color: #5f636f; --dropdown-header-padding-x: 20px; --dropdown-header-padding-y: 0.5rem; position: absolute; z-index: var(--dropdown-zindex); display: none; min-width: var(--dropdown-min-width); padding: var(--dropdown-padding-y) var(--dropdown-padding-x); margin: 0; font-size: var(--dropdown-font-size); color: var(--dropdown-color); text-align: left; list-style: none; background-color: var(--dropdown-bg); background-clip: padding-box; border: var(--dropdown-border-width) solid var(--dropdown-border-color); border-radius: var(--dropdown-border-radius); box-shadow: var(--dropdown-box-shadow);}.dropdown-menu[data-bs-popper]{top: 100%; left: 0; margin-top: var(--dropdown-spacer);}.dropdown-menu-start{--bs-position: start;}.dropdown-menu-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-end{--bs-position: end;}.dropdown-menu-end[data-bs-popper]{right: 0; left: auto;}@media (min-width: 576px){.dropdown-menu-sm-start{--bs-position: start;}.dropdown-menu-sm-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-sm-end{--bs-position: end;}.dropdown-menu-sm-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 768px){.dropdown-menu-md-start{--bs-position: start;}.dropdown-menu-md-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-md-end{--bs-position: end;}.dropdown-menu-md-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 992px){.dropdown-menu-lg-start{--bs-position: start;}.dropdown-menu-lg-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-lg-end{--bs-position: end;}.dropdown-menu-lg-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1200px){.dropdown-menu-xl-start{--bs-position: start;}.dropdown-menu-xl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xl-end{--bs-position: end;}.dropdown-menu-xl-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1400px){.dropdown-menu-xxl-start{--bs-position: start;}.dropdown-menu-xxl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xxl-end{--bs-position: end;}.dropdown-menu-xxl-end[data-bs-popper]{right: 0; left: auto;}}.dropup .dropdown-menu[data-bs-popper]{top: auto; bottom: 100%; margin-top: 0; margin-bottom: var(--dropdown-spacer);}.dropup .dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 0; border-right: 4px solid transparent; border-bottom: 4px solid; border-left: 4px solid transparent;}.dropup .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-menu[data-bs-popper]{top: 0; right: auto; left: 100%; margin-top: 0; margin-left: var(--dropdown-spacer);}.dropend .dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid transparent; border-right: 0; border-bottom: 4px solid transparent; border-left: 4px solid;}.dropend .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-toggle::after{vertical-align: 0;}.dropstart .dropdown-menu[data-bs-popper]{top: 0; right: 100%; left: auto; margin-top: 0; margin-right: var(--dropdown-spacer);}.dropstart .dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: "";}.dropstart .dropdown-toggle::after{display: none;}.dropstart .dropdown-toggle::before{display: inline-block; margin-right: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid transparent; border-right: 4px solid; border-bottom: 4px solid transparent;}.dropstart .dropdown-toggle:empty::after{margin-left: 0;}.dropstart .dropdown-toggle::before{vertical-align: 0;}.dropdown-divider{height: 0; margin: var(--dropdown-divider-margin-y) 0; overflow: hidden; border-top: 1px solid var(--dropdown-divider-bg); opacity: 1;}.dropdown-item{display: block; width: 100%; padding: var(--dropdown-item-padding-y) var(--dropdown-item-padding-x); clear: both; font-weight: 400; color: var(--dropdown-link-color); text-align: inherit; white-space: nowrap; background-color: transparent; border: 0; border-radius: var(--dropdown-item-border-radius, 0);}.dropdown-item:hover, .dropdown-item:focus{color: var(--dropdown-link-hover-color); background-color: var(--dropdown-link-hover-bg);}.dropdown-item.active, .dropdown-item:active{color: var(--dropdown-link-active-color); text-decoration: none; background-color: var(--dropdown-link-active-bg);}.dropdown-item.disabled, .dropdown-item:disabled{color: var(--dropdown-link-disabled-color); pointer-events: none; background-color: transparent;}.dropdown-menu.show{display: block;}.dropdown-header{display: block; padding: var(--dropdown-header-padding-y) var(--dropdown-header-padding-x); margin-bottom: 0; font-size: 0.8125rem; color: var(--dropdown-header-color); white-space: nowrap;}.dropdown-item-text{display: block; padding: var(--dropdown-item-padding-y) var(--dropdown-item-padding-x); color: var(--dropdown-link-color);}.dropdown-menu-dark{--dropdown-color: #d8dadd; --dropdown-bg: #1F2937; --dropdown-border-color: #111827; --dropdown-box-shadow: ; --dropdown-link-color: #d8dadd; --dropdown-link-hover-color: #FFF; --dropdown-divider-bg: #111827; --dropdown-link-hover-bg: rgba(255, 255, 255, 0.15); --dropdown-link-active-color: #111827; --dropdown-link-active-bg: transparent; --dropdown-link-disabled-color: #7c7f89; --dropdown-header-color: #7c7f89;}.btn-group, .btn-group-vertical{position: relative; display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; vertical-align: middle;}.btn-group > .btn, .btn-group-vertical > .btn{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:focus + .btn, .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn-check:checked + .btn, .btn-group-vertical > .btn-check:focus + .btn, .btn-group-vertical > .btn:hover, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active{z-index: 1;}.btn-toolbar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-pack: start; justify-content: flex-start;}.btn-toolbar .input-group{width: auto;}.btn-group{border-radius: var(--border-radius);}.btn-group > :not(.btn-check:first-child) + .btn, .btn-group > .btn-group:not(:first-child){margin-left: calc(var(--border-width) * -1);}.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .btn.dropdown-toggle-split:first-child, .btn-group > .btn-group:not(:last-child) > .btn{border-top-right-radius: 0; border-bottom-right-radius: 0;}.btn-group > .btn:nth-child(n + 3), .btn-group > :not(.btn-check) + .btn, .btn-group > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-bottom-left-radius: 0;}.dropdown-toggle-split{padding-right: 0.46875rem; padding-left: 0.46875rem;}.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after{margin-left: 0;}.dropstart .dropdown-toggle-split::before{margin-right: 0;}.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split{padding-right: 0.375rem; padding-left: 0.375rem;}.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split{padding-right: 0.5625rem; padding-left: 0.5625rem;}.btn-group.show .dropdown-toggle{box-shadow: 0;}.btn-group.show .dropdown-toggle.btn-link{box-shadow: none;}.btn-group-vertical{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-items: flex-start; justify-content: center;}.btn-group-vertical > .btn, .btn-group-vertical > .btn-group{width: 100%;}.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .btn-group:not(:first-child){margin-top: calc(var(--border-width) * -1);}.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .btn-group:not(:last-child) > .btn{border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.btn-group-vertical > .btn ~ .btn, .btn-group-vertical > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-top-right-radius: 0;}.nav{--nav-link-padding-x: 1rem; --nav-link-padding-y: 0.5rem; --nav-link-font-weight: ; --nav-link-color: #374151; --nav-link-hover-color: var(--link-hover-color); --nav-link-disabled-color: var(--secondary-color); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding-left: 0; margin-bottom: 0; list-style: none;}.nav-link{display: block; padding: var(--nav-link-padding-y) var(--nav-link-padding-x); font-size: var(--nav-link-font-size); font-weight: var(--nav-link-font-weight); color: var(--nav-link-color); background: none; border: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.nav-link{transition: none;}}.nav-link:hover, .nav-link:focus{color: var(--nav-link-hover-color);}.nav-link:focus-visible{outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.nav-link.disabled, .nav-link:disabled{color: var(--nav-link-disabled-color); pointer-events: none; cursor: default;}.nav-tabs{--nav-tabs-border-width: var(--border-width); --nav-tabs-border-color: var(--border-color); --nav-tabs-border-radius: 0; --nav-tabs-link-hover-border-color: var(--secondary-bg) var(--secondary-bg) var(--border-color); --nav-tabs-link-active-color: #111827; --nav-tabs-link-active-bg: transparent; --nav-tabs-link-active-border-color: var(--border-color) var(--border-color) transparent; border-bottom: var(--nav-tabs-border-width) solid var(--nav-tabs-border-color);}.nav-tabs .nav-link{margin-bottom: calc(-1 * var(--nav-tabs-border-width)); border: var(--nav-tabs-border-width) solid transparent; border-top-left-radius: var(--nav-tabs-border-radius); border-top-right-radius: var(--nav-tabs-border-radius);}.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus{isolation: isolate; border-color: var(--nav-tabs-link-hover-border-color);}.nav-tabs .nav-link.active, .nav-tabs .nav-item.show .nav-link{color: var(--nav-tabs-link-active-color); background-color: var(--nav-tabs-link-active-bg); border-color: var(--nav-tabs-link-active-border-color);}.nav-tabs .dropdown-menu{margin-top: calc(-1 * var(--nav-tabs-border-width)); border-top-left-radius: 0; border-top-right-radius: 0;}.nav-pills{--nav-pills-border-radius: 0; --nav-pills-link-active-color: #FFF; --nav-pills-link-active-bg: #714B67;}.nav-pills .nav-link{border-radius: var(--nav-pills-border-radius);}.nav-pills .nav-link.active, .nav-pills .show > .nav-link{color: var(--nav-pills-link-active-color); background-color: var(--nav-pills-link-active-bg);}.nav-underline{--nav-underline-gap: 1rem; --nav-underline-border-width: 0.125rem; --nav-underline-link-active-color: var(--emphasis-color); gap: var(--nav-underline-gap);}.nav-underline .nav-link{padding-right: 0; padding-left: 0; border-bottom: var(--nav-underline-border-width) solid transparent;}.nav-underline .nav-link:hover, .nav-underline .nav-link:focus{border-bottom-color: currentcolor;}.nav-underline .nav-link.active, .nav-underline .show > .nav-link{font-weight: 700; color: var(--nav-underline-link-active-color); border-bottom-color: currentcolor;}.nav-fill > .nav-link, .nav-fill .nav-item{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; text-align: center;}.nav-justified > .nav-link, .nav-justified .nav-item{flex-basis: 0; flex-grow: 1; text-align: center;}.nav-fill .nav-item .nav-link, .nav-justified .nav-item .nav-link{width: 100%;}.tab-content > .tab-pane{display: none;}.tab-content > .active{display: block;}.navbar{--navbar-padding-x: 0; --navbar-padding-y: 8px; --navbar-color: rgba(var(--emphasis-color-rgb), 0.65); --navbar-hover-color: rgba(var(--emphasis-color-rgb), 0.8); --navbar-disabled-color: rgba(var(--emphasis-color-rgb), 0.3); --navbar-active-color: rgba(var(--emphasis-color-rgb), 1); --navbar-brand-padding-y: 0.3125rem; --navbar-brand-margin-end: 1rem; --navbar-brand-font-size: 1.25rem; --navbar-brand-color: rgba(var(--emphasis-color-rgb), 1); --navbar-brand-hover-color: rgba(var(--emphasis-color-rgb), 1); --navbar-nav-link-padding-x: 0.5rem; --navbar-toggler-padding-y: 0.25rem; --navbar-toggler-padding-x: 0.75rem; --navbar-toggler-font-size: 1.25rem; --navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2817, 24, 39, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e"); --navbar-toggler-border-color: rgba(var(--emphasis-color-rgb), 0.15); --navbar-toggler-border-radius: var(--border-radius); --navbar-toggler-focus-width: 0.25rem; --navbar-toggler-transition: box-shadow 0.15s ease-in-out; position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; justify-content: space-between; padding: var(--navbar-padding-y) var(--navbar-padding-x);}.navbar > .container, .navbar > .o_container_small, .navbar > .container-fluid, .navbar > .container-sm, .navbar > .container-md, .navbar > .container-lg, .navbar > .container-xl, .navbar > .container-xxl{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: inherit; flex-wrap: inherit; align-items: center; justify-content: space-between;}.navbar-brand{padding-top: var(--navbar-brand-padding-y); padding-bottom: var(--navbar-brand-padding-y); margin-right: var(--navbar-brand-margin-end); font-size: var(--navbar-brand-font-size); color: var(--navbar-brand-color); white-space: nowrap;}.navbar-brand:hover, .navbar-brand:focus{color: var(--navbar-brand-hover-color);}.navbar-nav{--nav-link-padding-x: 0; --nav-link-padding-y: 0.5rem; --nav-link-font-weight: ; --nav-link-color: var(--navbar-color); --nav-link-hover-color: var(--navbar-hover-color); --nav-link-disabled-color: var(--navbar-disabled-color); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; list-style: none;}.navbar-nav .nav-link.active, .navbar-nav .nav-link.show{color: var(--navbar-active-color);}.navbar-nav .dropdown-menu{position: static;}.navbar-text{padding-top: 0.5rem; padding-bottom: 0.5rem; color: var(--navbar-color);}.navbar-text a, .navbar-text a:hover, .navbar-text a:focus{color: var(--navbar-active-color);}.navbar-collapse{flex-basis: 100%; flex-grow: 1; align-items: center;}.navbar-toggler{padding: var(--navbar-toggler-padding-y) var(--navbar-toggler-padding-x); font-size: var(--navbar-toggler-font-size); line-height: 1; color: var(--navbar-color); background-color: transparent; border: var(--border-width) solid var(--navbar-toggler-border-color); border-radius: var(--navbar-toggler-border-radius); transition: var(--navbar-toggler-transition);}@media (prefers-reduced-motion: reduce){.navbar-toggler{transition: none;}}.navbar-toggler:hover{text-decoration: none;}.navbar-toggler:focus{text-decoration: none; outline: 0; box-shadow: 0 0 0 var(--navbar-toggler-focus-width);}.navbar-toggler-icon{display: inline-block; width: 1.5em; height: 1.5em; vertical-align: middle; background-image: var(--navbar-toggler-icon-bg); background-repeat: no-repeat; background-position: center; background-size: 100%;}.navbar-nav-scroll{max-height: var(--scroll-height, 75vh); overflow-y: auto;}@media (min-width: 576px){.navbar-expand-sm{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-sm .navbar-nav{flex-direction: row;}.navbar-expand-sm .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-sm .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-sm .navbar-nav-scroll{overflow: visible;}.navbar-expand-sm .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-sm .navbar-toggler{display: none;}.navbar-expand-sm .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; box-shadow: none; transition: none;}.navbar-expand-sm .offcanvas .offcanvas-header{display: none;}.navbar-expand-sm .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 768px){.navbar-expand-md{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-md .navbar-nav{flex-direction: row;}.navbar-expand-md .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-md .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-md .navbar-nav-scroll{overflow: visible;}.navbar-expand-md .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-md .navbar-toggler{display: none;}.navbar-expand-md .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; box-shadow: none; transition: none;}.navbar-expand-md .offcanvas .offcanvas-header{display: none;}.navbar-expand-md .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 992px){.navbar-expand-lg{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-lg .navbar-nav{flex-direction: row;}.navbar-expand-lg .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-lg .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-lg .navbar-nav-scroll{overflow: visible;}.navbar-expand-lg .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-lg .navbar-toggler{display: none;}.navbar-expand-lg .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; box-shadow: none; transition: none;}.navbar-expand-lg .offcanvas .offcanvas-header{display: none;}.navbar-expand-lg .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1200px){.navbar-expand-xl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xl .navbar-nav{flex-direction: row;}.navbar-expand-xl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xl .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-xl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xl .navbar-toggler{display: none;}.navbar-expand-xl .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; box-shadow: none; transition: none;}.navbar-expand-xl .offcanvas .offcanvas-header{display: none;}.navbar-expand-xl .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1400px){.navbar-expand-xxl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xxl .navbar-nav{flex-direction: row;}.navbar-expand-xxl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xxl .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-xxl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xxl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xxl .navbar-toggler{display: none;}.navbar-expand-xxl .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; box-shadow: none; transition: none;}.navbar-expand-xxl .offcanvas .offcanvas-header{display: none;}.navbar-expand-xxl .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}.navbar-expand{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand .navbar-nav{flex-direction: row;}.navbar-expand .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand .navbar-nav-scroll{overflow: visible;}.navbar-expand .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand .navbar-toggler{display: none;}.navbar-expand .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; box-shadow: none; transition: none;}.navbar-expand .offcanvas .offcanvas-header{display: none;}.navbar-expand .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}.navbar-dark, .navbar[data-bs-theme="dark"]{--navbar-color: rgba(255, 255, 255, 0.55); --navbar-hover-color: rgba(255, 255, 255, 0.75); --navbar-disabled-color: rgba(255, 255, 255, 0.25); --navbar-active-color: #FFF; --navbar-brand-color: #FFF; --navbar-brand-hover-color: #FFF; --navbar-toggler-border-color: rgba(255, 255, 255, 0.1); --navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.card{--card-spacer-y: 16px; --card-spacer-x: 16px; --card-title-spacer-y: 8px; --card-title-color: ; --card-subtitle-color: ; --card-border-width: var(--border-width); --card-border-color: var(--border-color-translucent); --card-border-radius: 0; --card-box-shadow: ; --card-inner-border-radius: calc(0 - (var(--border-width))); --card-cap-padding-y: 16px; --card-cap-padding-x: 16px; --card-cap-bg: rgba(var(--body-color-rgb), 0.03); --card-cap-color: ; --card-height: ; --card-color: ; --card-bg: white; --card-img-overlay-padding: 16px; --card-group-margin: 16px; position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; min-width: 0; height: var(--card-height); color: var(--body-color); word-wrap: break-word; background-color: var(--card-bg); background-clip: border-box; border: var(--card-border-width) solid var(--card-border-color); border-radius: var(--card-border-radius); box-shadow: var(--card-box-shadow);}.card > hr{margin-right: 0; margin-left: 0;}.card > .list-group{border-top: inherit; border-bottom: inherit;}.card > .list-group:first-child{border-top-width: 0; border-top-left-radius: var(--card-inner-border-radius); border-top-right-radius: var(--card-inner-border-radius);}.card > .list-group:last-child{border-bottom-width: 0; border-bottom-right-radius: var(--card-inner-border-radius); border-bottom-left-radius: var(--card-inner-border-radius);}.card > .card-header + .list-group, .card > .list-group + .card-footer{border-top: 0;}.card-body{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: var(--card-spacer-y) var(--card-spacer-x); color: var(--card-color);}.card-title{margin-bottom: var(--card-title-spacer-y); color: var(--card-title-color);}.card-subtitle{margin-top: calc(-.5 * var(--card-title-spacer-y)); margin-bottom: 0; color: var(--card-subtitle-color);}.card-text:last-child{margin-bottom: 0;}.card-link + .card-link{margin-left: var(--card-spacer-x);}.card-header{padding: var(--card-cap-padding-y) var(--card-cap-padding-x); margin-bottom: 0; color: var(--card-cap-color); background-color: var(--card-cap-bg); border-bottom: var(--card-border-width) solid var(--card-border-color);}.card-header:first-child{border-radius: var(--card-inner-border-radius) var(--card-inner-border-radius) 0 0;}.card-footer{padding: var(--card-cap-padding-y) var(--card-cap-padding-x); color: var(--card-cap-color); background-color: var(--card-cap-bg); border-top: var(--card-border-width) solid var(--card-border-color);}.card-footer:last-child{border-radius: 0 0 var(--card-inner-border-radius) var(--card-inner-border-radius);}.card-header-tabs{margin-right: calc(-.5 * var(--card-cap-padding-x)); margin-bottom: calc(-1 * var(--card-cap-padding-y)); margin-left: calc(-.5 * var(--card-cap-padding-x)); border-bottom: 0;}.card-header-tabs .nav-link.active{background-color: var(--card-bg); border-bottom-color: var(--card-bg);}.card-header-pills{margin-right: calc(-.5 * var(--card-cap-padding-x)); margin-left: calc(-.5 * var(--card-cap-padding-x));}.card-img-overlay{position: absolute; top: 0; right: 0; bottom: 0; left: 0; padding: var(--card-img-overlay-padding); border-radius: var(--card-inner-border-radius);}.card-img, .card-img-top, .card-img-bottom{width: 100%;}.card-img, .card-img-top{border-top-left-radius: var(--card-inner-border-radius); border-top-right-radius: var(--card-inner-border-radius);}.card-img, .card-img-bottom{border-bottom-right-radius: var(--card-inner-border-radius); border-bottom-left-radius: var(--card-inner-border-radius);}.card-group > .card{margin-bottom: var(--card-group-margin);}@media (min-width: 576px){.card-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}.card-group > .card{flex: 1 0 0%; margin-bottom: 0;}.card-group > .card + .card{margin-left: 0; border-left: 0;}.card-group > .card:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-top, .card-group > .card:not(:last-child) .card-header{border-top-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-bottom, .card-group > .card:not(:last-child) .card-footer{border-bottom-right-radius: 0;}.card-group > .card:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-top, .card-group > .card:not(:first-child) .card-header{border-top-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-bottom, .card-group > .card:not(:first-child) .card-footer{border-bottom-left-radius: 0;}}.accordion{--accordion-color: var(--body-color); --accordion-bg: var(--body-bg); --accordion-transition: none, border-radius 0.15s ease; --accordion-border-color: var(--border-color); --accordion-border-width: var(--border-width); --accordion-border-radius: 0; --accordion-inner-border-radius: calc(0 - (var(--border-width))); --accordion-btn-padding-x: 1.25rem; --accordion-btn-padding-y: 1rem; --accordion-btn-color: var(--body-color); --accordion-btn-bg: var(--accordion-bg); --accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23111827' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e"); --accordion-btn-icon-width: 1.25rem; --accordion-btn-icon-transform: rotate(-180deg); --accordion-btn-icon-transition: transform 0.2s ease-in-out; --accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%232d1e29' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e"); --accordion-btn-focus-box-shadow: 0; --accordion-body-padding-x: 1.25rem; --accordion-body-padding-y: 1rem; --accordion-active-color: var(--primary-text-emphasis); --accordion-active-bg: var(--primary-bg-subtle);}.accordion-button{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 100%; padding: var(--accordion-btn-padding-y) var(--accordion-btn-padding-x); font-size: 1rem; color: var(--accordion-btn-color); text-align: left; background-color: var(--accordion-btn-bg); border: 0; border-radius: 0; overflow-anchor: none; transition: var(--accordion-transition);}@media (prefers-reduced-motion: reduce){.accordion-button{transition: none;}}.accordion-button:not(.collapsed){color: var(--accordion-active-color); background-color: var(--accordion-active-bg); box-shadow: inset 0 calc(-1 * var(--accordion-border-width)) 0 var(--accordion-border-color);}.accordion-button:not(.collapsed)::after{background-image: var(--accordion-btn-active-icon); transform: var(--accordion-btn-icon-transform);}.accordion-button::after{flex-shrink: 0; width: var(--accordion-btn-icon-width); height: var(--accordion-btn-icon-width); margin-left: auto; content: ""; background-image: var(--accordion-btn-icon); background-repeat: no-repeat; background-size: var(--accordion-btn-icon-width); transition: var(--accordion-btn-icon-transition);}@media (prefers-reduced-motion: reduce){.accordion-button::after{transition: none;}}.accordion-button:hover{z-index: 2;}.accordion-button:focus{z-index: 3; outline: 0; box-shadow: var(--accordion-btn-focus-box-shadow);}.accordion-header{margin-bottom: 0;}.accordion-item{color: var(--accordion-color); background-color: var(--accordion-bg); border: var(--accordion-border-width) solid var(--accordion-border-color);}.accordion-item:first-of-type{border-top-left-radius: var(--accordion-border-radius); border-top-right-radius: var(--accordion-border-radius);}.accordion-item:first-of-type > .accordion-header .accordion-button{border-top-left-radius: var(--accordion-inner-border-radius); border-top-right-radius: var(--accordion-inner-border-radius);}.accordion-item:not(:first-of-type){border-top: 0;}.accordion-item:last-of-type{border-bottom-right-radius: var(--accordion-border-radius); border-bottom-left-radius: var(--accordion-border-radius);}.accordion-item:last-of-type > .accordion-header .accordion-button.collapsed{border-bottom-right-radius: var(--accordion-inner-border-radius); border-bottom-left-radius: var(--accordion-inner-border-radius);}.accordion-item:last-of-type > .accordion-collapse{border-bottom-right-radius: var(--accordion-border-radius); border-bottom-left-radius: var(--accordion-border-radius);}.accordion-body{padding: var(--accordion-body-padding-y) var(--accordion-body-padding-x);}.accordion-flush > .accordion-item{border-right: 0; border-left: 0; border-radius: 0;}.accordion-flush > .accordion-item:first-child{border-top: 0;}.accordion-flush > .accordion-item:last-child{border-bottom: 0;}.accordion-flush > .accordion-item > .accordion-header .accordion-button, .accordion-flush > .accordion-item > .accordion-header .accordion-button.collapsed{border-radius: 0;}.accordion-flush > .accordion-item > .accordion-collapse{border-radius: 0;}.breadcrumb{--breadcrumb-padding-x: 0; --breadcrumb-padding-y: 0; --breadcrumb-margin-bottom: 0; --breadcrumb-bg: white; --breadcrumb-border-radius: ; --breadcrumb-divider-color: rgba(55, 65, 81, 0.76); --breadcrumb-item-padding-x: 0.5rem; --breadcrumb-item-active-color: #374151; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding: var(--breadcrumb-padding-y) var(--breadcrumb-padding-x); margin-bottom: var(--breadcrumb-margin-bottom); font-size: var(--breadcrumb-font-size); list-style: none; background-color: var(--breadcrumb-bg); border-radius: var(--breadcrumb-border-radius);}.breadcrumb-item + .breadcrumb-item{padding-left: var(--breadcrumb-item-padding-x);}.breadcrumb-item + .breadcrumb-item::before{float: left; padding-right: var(--breadcrumb-item-padding-x); color: var(--breadcrumb-divider-color); content: var(--breadcrumb-divider, "/") ;}.breadcrumb-item.active{color: var(--breadcrumb-item-active-color);}.pagination{--pagination-padding-x: 0.75rem; --pagination-padding-y: 0.375rem; --pagination-font-size: 1rem; --pagination-color: var(--link-color); --pagination-bg: var(--body-bg); --pagination-border-width: var(--border-width); --pagination-border-color: var(--border-color); --pagination-border-radius: var(--border-radius); --pagination-hover-color: var(--link-hover-color); --pagination-hover-bg: var(--tertiary-bg); --pagination-hover-border-color: var(--border-color); --pagination-focus-color: var(--link-hover-color); --pagination-focus-bg: var(--secondary-bg); --pagination-focus-box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25); --pagination-active-color: unset; --pagination-active-bg: #e6f2f3; --pagination-active-border-color: #e6f2f3; --pagination-disabled-color: var(--secondary-color); --pagination-disabled-bg: var(--secondary-bg); --pagination-disabled-border-color: var(--border-color); display: -webkit-box; display: -webkit-flex; display: flex; padding-left: 0; list-style: none;}.page-link{position: relative; display: block; padding: var(--pagination-padding-y) var(--pagination-padding-x); font-size: var(--pagination-font-size); color: var(--pagination-color); background-color: var(--pagination-bg); border: var(--pagination-border-width) solid var(--pagination-border-color); transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.page-link{transition: none;}}.page-link:hover{z-index: 2; color: var(--pagination-hover-color); background-color: var(--pagination-hover-bg); border-color: var(--pagination-hover-border-color);}.page-link:focus{z-index: 3; color: var(--pagination-focus-color); background-color: var(--pagination-focus-bg); outline: 0; box-shadow: var(--pagination-focus-box-shadow);}.page-link.active, .active > .page-link{z-index: 3; color: var(--pagination-active-color); background-color: var(--pagination-active-bg); border-color: var(--pagination-active-border-color);}.page-link.disabled, .disabled > .page-link{color: var(--pagination-disabled-color); pointer-events: none; background-color: var(--pagination-disabled-bg); border-color: var(--pagination-disabled-border-color);}.page-item:not(:first-child) .page-link{margin-left: calc(var(--border-width) * -1);}.page-item:first-child .page-link{border-top-left-radius: var(--pagination-border-radius); border-bottom-left-radius: var(--pagination-border-radius);}.page-item:last-child .page-link{border-top-right-radius: var(--pagination-border-radius); border-bottom-right-radius: var(--pagination-border-radius);}.pagination-lg{--pagination-padding-x: 1.5rem; --pagination-padding-y: 0.75rem; --pagination-font-size: 1.25rem; --pagination-border-radius: var(--border-radius-lg);}.pagination-sm{--pagination-padding-x: 0.5rem; --pagination-padding-y: 0.25rem; --pagination-font-size: 0.8125rem; --pagination-border-radius: var(--border-radius-sm);}.badge{--badge-padding-x: 0.82em; --badge-padding-y: 0.25em; --badge-font-size: 0.75em; --badge-font-weight: normal; --badge-color: inherit; --badge-border-radius: 0; display: inline-block; padding: var(--badge-padding-y) var(--badge-padding-x); font-size: var(--badge-font-size); font-weight: var(--badge-font-weight); line-height: 1; color: var(--badge-color); text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: var(--badge-border-radius);}.badge:empty{display: none;}.btn .badge{position: relative; top: -1px;}.alert{--alert-bg: transparent; --alert-padding-x: 16px; --alert-padding-y: 16px; --alert-margin-bottom: 1rem; --alert-color: inherit; --alert-border-color: transparent; --alert-border: 0 solid var(--alert-border-color); --alert-border-radius: var(--border-radius); --alert-link-color: inherit; position: relative; padding: var(--alert-padding-y) var(--alert-padding-x); margin-bottom: var(--alert-margin-bottom); color: var(--alert-color); background-color: var(--alert-bg); border: var(--alert-border); border-radius: var(--alert-border-radius);}.alert-heading{color: inherit;}.alert-link{font-weight: 700; color: var(--alert-link-color);}.alert-dismissible{padding-right: 48px;}.alert-dismissible .btn-close{position: absolute; top: 0; right: 0; z-index: 2; padding: 20px 16px;}.alert-primary{--alert-color: var(--primary-text-emphasis); --alert-bg: var(--primary-bg-subtle); --alert-border-color: var(--primary-border-subtle); --alert-link-color: var(--primary-text-emphasis);}.alert-secondary{--alert-color: var(--secondary-text-emphasis); --alert-bg: var(--secondary-bg-subtle); --alert-border-color: var(--secondary-border-subtle); --alert-link-color: var(--secondary-text-emphasis);}.alert-success{--alert-color: var(--success-text-emphasis); --alert-bg: var(--success-bg-subtle); --alert-border-color: var(--success-border-subtle); --alert-link-color: var(--success-text-emphasis);}.alert-info{--alert-color: var(--info-text-emphasis); --alert-bg: var(--info-bg-subtle); --alert-border-color: var(--info-border-subtle); --alert-link-color: var(--info-text-emphasis);}.alert-warning{--alert-color: var(--warning-text-emphasis); --alert-bg: var(--warning-bg-subtle); --alert-border-color: var(--warning-border-subtle); --alert-link-color: var(--warning-text-emphasis);}.alert-danger{--alert-color: var(--danger-text-emphasis); --alert-bg: var(--danger-bg-subtle); --alert-border-color: var(--danger-border-subtle); --alert-link-color: var(--danger-text-emphasis);}.alert-light{--alert-color: var(--light-text-emphasis); --alert-bg: var(--light-bg-subtle); --alert-border-color: var(--light-border-subtle); --alert-link-color: var(--light-text-emphasis);}.alert-dark{--alert-color: var(--dark-text-emphasis); --alert-bg: var(--dark-bg-subtle); --alert-border-color: var(--dark-border-subtle); --alert-link-color: var(--dark-text-emphasis);}@keyframes progress-bar-stripes{0%{background-position-x: 1rem;}}.progress, .progress-stacked{--progress-height: 1rem; --progress-font-size: 0.75rem; --progress-bg: var(--secondary-bg); --progress-border-radius: 0; --progress-box-shadow: 0; --progress-bar-color: #FFF; --progress-bar-bg: #714B67; --progress-bar-transition: width 0.6s ease; display: -webkit-box; display: -webkit-flex; display: flex; height: var(--progress-height); overflow: hidden; font-size: var(--progress-font-size); background-color: var(--progress-bg); border-radius: var(--progress-border-radius); box-shadow: var(--progress-box-shadow);}.progress-bar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; overflow: hidden; color: var(--progress-bar-color); text-align: center; white-space: nowrap; background-color: var(--progress-bar-bg); transition: var(--progress-bar-transition);}@media (prefers-reduced-motion: reduce){.progress-bar{transition: none;}}.progress-bar-striped{background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: var(--progress-height) var(--progress-height);}.progress-stacked > .progress{overflow: visible;}.progress-stacked > .progress > .progress-bar{width: 100%;}.progress-bar-animated{animation: 1s linear infinite progress-bar-stripes;}@media (prefers-reduced-motion: reduce){.progress-bar-animated{animation: none;}}.list-group{--list-group-color: var(--body-color); --list-group-bg: white; --list-group-border-color: var(--border-color); --list-group-border-width: var(--border-width); --list-group-border-radius: 0; --list-group-item-padding-x: 16px; --list-group-item-padding-y: 8px; --list-group-action-color: var(--secondary-color); --list-group-action-hover-color: #111827; --list-group-action-hover-bg: rgba(0, 0, 0, 0.08); --list-group-action-active-color: var(--body-color); --list-group-action-active-bg: var(--secondary-bg); --list-group-disabled-color: var(--secondary-color); --list-group-disabled-bg: white; --list-group-active-color: #017e84; --list-group-active-bg: #e6f2f3; --list-group-active-border-color: #017e84; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; border-radius: var(--list-group-border-radius);}.list-group-numbered{list-style-type: none; counter-reset: section;}.list-group-numbered > .list-group-item::before{content: counters(section, ".") ". "; counter-increment: section;}.list-group-item-action{width: 100%; color: var(--list-group-action-color); text-align: inherit;}.list-group-item-action:hover, .list-group-item-action:focus{z-index: 1; color: var(--list-group-action-hover-color); text-decoration: none; background-color: var(--list-group-action-hover-bg);}.list-group-item-action:active{color: var(--list-group-action-active-color); background-color: var(--list-group-action-active-bg);}.list-group-item{position: relative; display: block; padding: var(--list-group-item-padding-y) var(--list-group-item-padding-x); color: var(--list-group-color); background-color: var(--list-group-bg); border: var(--list-group-border-width) solid var(--list-group-border-color);}.list-group-item:first-child{border-top-left-radius: inherit; border-top-right-radius: inherit;}.list-group-item:last-child{border-bottom-right-radius: inherit; border-bottom-left-radius: inherit;}.list-group-item.disabled, .list-group-item:disabled{color: var(--list-group-disabled-color); pointer-events: none; background-color: var(--list-group-disabled-bg);}.list-group-item.active{z-index: 2; color: var(--list-group-active-color); background-color: var(--list-group-active-bg); border-color: var(--list-group-active-border-color);}.list-group-item + .list-group-item{border-top-width: 0;}.list-group-item + .list-group-item.active{margin-top: calc(-1 * var(--list-group-border-width)); border-top-width: var(--list-group-border-width);}.list-group-horizontal{flex-direction: row;}.list-group-horizontal > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal > .list-group-item.active{margin-top: 0;}.list-group-horizontal > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}@media (min-width: 576px){.list-group-horizontal-sm{flex-direction: row;}.list-group-horizontal-sm > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-sm > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-sm > .list-group-item.active{margin-top: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 768px){.list-group-horizontal-md{flex-direction: row;}.list-group-horizontal-md > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-md > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-md > .list-group-item.active{margin-top: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 992px){.list-group-horizontal-lg{flex-direction: row;}.list-group-horizontal-lg > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-lg > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-lg > .list-group-item.active{margin-top: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 1200px){.list-group-horizontal-xl{flex-direction: row;}.list-group-horizontal-xl > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-xl > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-xl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 1400px){.list-group-horizontal-xxl{flex-direction: row;}.list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-xxl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}.list-group-flush{border-radius: 0;}.list-group-flush > .list-group-item{border-width: 0 0 var(--list-group-border-width);}.list-group-flush > .list-group-item:last-child{border-bottom-width: 0;}.list-group-item-primary{--list-group-color: var(--primary-text-emphasis); --list-group-bg: var(--primary-bg-subtle); --list-group-border-color: var(--primary-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--primary-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--primary-border-subtle); --list-group-active-color: var(--primary-bg-subtle); --list-group-active-bg: var(--primary-text-emphasis); --list-group-active-border-color: var(--primary-text-emphasis);}.list-group-item-secondary{--list-group-color: var(--secondary-text-emphasis); --list-group-bg: var(--secondary-bg-subtle); --list-group-border-color: var(--secondary-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--secondary-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--secondary-border-subtle); --list-group-active-color: var(--secondary-bg-subtle); --list-group-active-bg: var(--secondary-text-emphasis); --list-group-active-border-color: var(--secondary-text-emphasis);}.list-group-item-success{--list-group-color: var(--success-text-emphasis); --list-group-bg: var(--success-bg-subtle); --list-group-border-color: var(--success-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--success-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--success-border-subtle); --list-group-active-color: var(--success-bg-subtle); --list-group-active-bg: var(--success-text-emphasis); --list-group-active-border-color: var(--success-text-emphasis);}.list-group-item-info{--list-group-color: var(--info-text-emphasis); --list-group-bg: var(--info-bg-subtle); --list-group-border-color: var(--info-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--info-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--info-border-subtle); --list-group-active-color: var(--info-bg-subtle); --list-group-active-bg: var(--info-text-emphasis); --list-group-active-border-color: var(--info-text-emphasis);}.list-group-item-warning{--list-group-color: var(--warning-text-emphasis); --list-group-bg: var(--warning-bg-subtle); --list-group-border-color: var(--warning-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--warning-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--warning-border-subtle); --list-group-active-color: var(--warning-bg-subtle); --list-group-active-bg: var(--warning-text-emphasis); --list-group-active-border-color: var(--warning-text-emphasis);}.list-group-item-danger{--list-group-color: var(--danger-text-emphasis); --list-group-bg: var(--danger-bg-subtle); --list-group-border-color: var(--danger-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--danger-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--danger-border-subtle); --list-group-active-color: var(--danger-bg-subtle); --list-group-active-bg: var(--danger-text-emphasis); --list-group-active-border-color: var(--danger-text-emphasis);}.list-group-item-light{--list-group-color: var(--light-text-emphasis); --list-group-bg: var(--light-bg-subtle); --list-group-border-color: var(--light-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--light-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--light-border-subtle); --list-group-active-color: var(--light-bg-subtle); --list-group-active-bg: var(--light-text-emphasis); --list-group-active-border-color: var(--light-text-emphasis);}.list-group-item-dark{--list-group-color: var(--dark-text-emphasis); --list-group-bg: var(--dark-bg-subtle); --list-group-border-color: var(--dark-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--dark-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--dark-border-subtle); --list-group-active-color: var(--dark-bg-subtle); --list-group-active-bg: var(--dark-text-emphasis); --list-group-active-border-color: var(--dark-text-emphasis);}.btn-close{--btn-close-color: #000; --btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e"); --btn-close-opacity: 0.5; --btn-close-hover-opacity: 0.75; --btn-close-focus-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25); --btn-close-focus-opacity: 1; --btn-close-disabled-opacity: 0.25; --btn-close-white-filter: invert(1) grayscale(100%) brightness(200%); box-sizing: content-box; width: 1em; height: 1em; padding: 0.25em 0.25em; color: var(--btn-close-color); background: transparent var(--btn-close-bg) center/1em auto no-repeat; border: 0; border-radius: 0.25rem; opacity: var(--btn-close-opacity);}.btn-close:hover{color: var(--btn-close-color); text-decoration: none; opacity: var(--btn-close-hover-opacity);}.btn-close:focus{outline: 0; box-shadow: var(--btn-close-focus-shadow); opacity: var(--btn-close-focus-opacity);}.btn-close:disabled, .btn-close.disabled{pointer-events: none; user-select: none; opacity: var(--btn-close-disabled-opacity);}.btn-close-white{filter: var(--btn-close-white-filter);}.toast{--toast-zindex: 1090; --toast-padding-x: 1.5rem; --toast-padding-y: 0.5rem; --toast-spacing: 16px; --toast-max-width: 320px; --toast-font-size: 1rem; --toast-color: ; --toast-bg: rgba(255, 255, 255, 0.7); --toast-border-width: var(--border-width); --toast-border-color: var(--border-color-translucent); --toast-border-radius: 0; --toast-box-shadow: var(--box-shadow); --toast-header-color: var(--secondary-color); --toast-header-bg: rgba(255, 255, 255, 0.7); --toast-header-border-color: var(--border-color-translucent); width: var(--toast-max-width); max-width: 100%; font-size: var(--toast-font-size); color: var(--toast-color); pointer-events: auto; background-color: var(--toast-bg); background-clip: padding-box; border: var(--toast-border-width) solid var(--toast-border-color); box-shadow: var(--toast-box-shadow); border-radius: var(--toast-border-radius);}.toast.showing{opacity: 0;}.toast:not(.show){display: none;}.toast-container{--toast-zindex: 1090; position: absolute; z-index: var(--toast-zindex); width: max-content; max-width: 100%; pointer-events: none;}.toast-container > :not(:last-child){margin-bottom: var(--toast-spacing);}.toast-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: var(--toast-padding-y) var(--toast-padding-x); color: var(--toast-header-color); background-color: var(--toast-header-bg); background-clip: padding-box; border-bottom: var(--toast-border-width) solid var(--toast-header-border-color); border-top-left-radius: calc(var(--toast-border-radius) - var(--toast-border-width)); border-top-right-radius: calc(var(--toast-border-radius) - var(--toast-border-width));}.toast-header .btn-close{margin-right: calc(-.5 * var(--toast-padding-x)); margin-left: var(--toast-padding-x);}.toast-body{padding: var(--toast-padding-x); word-wrap: break-word;}.modal{--modal-zindex: 1055; --modal-width: 650px; --modal-padding: 16px; --modal-margin: 0.5rem; --modal-color: ; --modal-bg: white; --modal-border-color: var(--border-color-translucent); --modal-border-width: var(--border-width); --modal-border-radius: 0.25rem; --modal-box-shadow: var(--box-shadow-sm); --modal-inner-border-radius: calc(0.25rem - (var(--border-width))); --modal-header-padding-x: 16px; --modal-header-padding-y: 16px; --modal-header-padding: 16px 16px; --modal-header-border-color: var(--border-color); --modal-header-border-width: var(--border-width); --modal-title-line-height: 1.5; --modal-footer-gap: 1px; --modal-footer-bg: ; --modal-footer-border-color: var(--border-color); --modal-footer-border-width: var(--border-width); position: fixed; top: 0; left: 0; z-index: var(--modal-zindex); display: none; width: 100%; height: 100%; overflow-x: hidden; overflow-y: auto; outline: 0;}.modal-dialog{position: relative; width: auto; margin: var(--modal-margin); pointer-events: none;}.modal.fade .modal-dialog{transition: transform 0.3s ease-out; transform: translate(0, -50px);}@media (prefers-reduced-motion: reduce){.modal.fade .modal-dialog{transition: none;}}.modal.show .modal-dialog{transform: none;}.modal.modal-static .modal-dialog{transform: none;}.modal-dialog-scrollable{height: calc(100% - var(--modal-margin) * 2);}.modal-dialog-scrollable .modal-content{max-height: 100%; overflow: hidden;}.modal-dialog-scrollable .modal-body{overflow-y: auto;}.modal-dialog-centered{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: calc(100% - var(--modal-margin) * 2);}.modal-content{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; color: var(--modal-color); pointer-events: auto; background-color: var(--modal-bg); background-clip: padding-box; border: var(--modal-border-width) solid var(--modal-border-color); border-radius: var(--modal-border-radius); box-shadow: var(--modal-box-shadow); outline: 0;}.modal-backdrop{--backdrop-zindex: 1050; --backdrop-bg: #000; --backdrop-opacity: 0.5; position: fixed; top: 0; left: 0; z-index: var(--backdrop-zindex); width: 100vw; height: 100vh; background-color: var(--backdrop-bg);}.modal-backdrop.fade{opacity: 0;}.modal-backdrop.show{opacity: var(--backdrop-opacity);}.modal-header{display: -webkit-box; display: -webkit-flex; display: flex; flex-shrink: 0; align-items: center; padding: var(--modal-header-padding); border-bottom: var(--modal-header-border-width) solid var(--modal-header-border-color); border-top-left-radius: var(--modal-inner-border-radius); border-top-right-radius: var(--modal-inner-border-radius);}.modal-header .btn-close{padding: calc(var(--modal-header-padding-y) * .5) calc(var(--modal-header-padding-x) * .5); margin: calc(-.5 * var(--modal-header-padding-y)) calc(-.5 * var(--modal-header-padding-x)) calc(-.5 * var(--modal-header-padding-y)) auto;}.modal-title{margin-bottom: 0; line-height: var(--modal-title-line-height);}.modal-body{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: var(--modal-padding);}.modal-footer{display: -webkit-box; display: -webkit-flex; display: flex; flex-shrink: 0; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; -webkit-box-pack: end; justify-content: flex-end; padding: calc(var(--modal-padding) - var(--modal-footer-gap) * .5); background-color: var(--modal-footer-bg); border-top: var(--modal-footer-border-width) solid var(--modal-footer-border-color); border-bottom-right-radius: var(--modal-inner-border-radius); border-bottom-left-radius: var(--modal-inner-border-radius);}.modal-footer > *{margin: calc(var(--modal-footer-gap) * .5);}@media (min-width: 576px){.modal{--modal-margin: 1.75rem; --modal-box-shadow: var(--box-shadow);}.modal-dialog{max-width: var(--modal-width); margin-right: auto; margin-left: auto;}.modal-sm{--modal-width: 300px;}}@media (min-width: 992px){.modal-lg, .modal-xl{--modal-width: 980px;}}@media (min-width: 1200px){.modal-xl{--modal-width: 1140px;}}.modal-fullscreen{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen .modal-header, .modal-fullscreen .modal-footer{border-radius: 0;}.modal-fullscreen .modal-body{overflow-y: auto;}@media (max-width: 575.98px){.modal-fullscreen-sm-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-sm-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-sm-down .modal-header, .modal-fullscreen-sm-down .modal-footer{border-radius: 0;}.modal-fullscreen-sm-down .modal-body{overflow-y: auto;}}@media (max-width: 767.98px){.modal-fullscreen-md-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-md-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-md-down .modal-header, .modal-fullscreen-md-down .modal-footer{border-radius: 0;}.modal-fullscreen-md-down .modal-body{overflow-y: auto;}}@media (max-width: 991.98px){.modal-fullscreen-lg-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-lg-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-lg-down .modal-header, .modal-fullscreen-lg-down .modal-footer{border-radius: 0;}.modal-fullscreen-lg-down .modal-body{overflow-y: auto;}}@media (max-width: 1199.98px){.modal-fullscreen-xl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xl-down .modal-header, .modal-fullscreen-xl-down .modal-footer{border-radius: 0;}.modal-fullscreen-xl-down .modal-body{overflow-y: auto;}}@media (max-width: 1399.98px){.modal-fullscreen-xxl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xxl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xxl-down .modal-header, .modal-fullscreen-xxl-down .modal-footer{border-radius: 0;}.modal-fullscreen-xxl-down .modal-body{overflow-y: auto;}}.tooltip{--tooltip-zindex: 1080; --tooltip-max-width: 400px; --tooltip-padding-x: 8px; --tooltip-padding-y: 4px; --tooltip-margin: ; --tooltip-font-size: 0.8125rem; --tooltip-color: #e7e9ed; --tooltip-bg: var(--emphasis-color); --tooltip-border-radius: var(--border-radius); --tooltip-opacity: 1; --tooltip-arrow-width: 0.8rem; --tooltip-arrow-height: 0.4rem; z-index: var(--tooltip-zindex); display: block; margin: var(--tooltip-margin); font-family: var(--font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; white-space: normal; word-spacing: normal; line-break: auto; font-size: var(--tooltip-font-size); word-wrap: break-word; opacity: 0;}.tooltip.show{opacity: var(--tooltip-opacity);}.tooltip .tooltip-arrow{display: block; width: var(--tooltip-arrow-width); height: var(--tooltip-arrow-height);}.tooltip .tooltip-arrow::before{position: absolute; content: ""; border-color: transparent; border-style: solid;}.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow{bottom: calc(-1 * var(--tooltip-arrow-height));}.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before{top: -1px; border-width: var(--tooltip-arrow-height) calc(var(--tooltip-arrow-width) * .5) 0; border-top-color: var(--tooltip-bg);}.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow{left: calc(-1 * var(--tooltip-arrow-height)); width: var(--tooltip-arrow-height); height: var(--tooltip-arrow-width);}.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before{right: -1px; border-width: calc(var(--tooltip-arrow-width) * .5) var(--tooltip-arrow-height) calc(var(--tooltip-arrow-width) * .5) 0; border-right-color: var(--tooltip-bg);}.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow{top: calc(-1 * var(--tooltip-arrow-height));}.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before{bottom: -1px; border-width: 0 calc(var(--tooltip-arrow-width) * .5) var(--tooltip-arrow-height); border-bottom-color: var(--tooltip-bg);}.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow{right: calc(-1 * var(--tooltip-arrow-height)); width: var(--tooltip-arrow-height); height: var(--tooltip-arrow-width);}.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before{left: -1px; border-width: calc(var(--tooltip-arrow-width) * .5) 0 calc(var(--tooltip-arrow-width) * .5) var(--tooltip-arrow-height); border-left-color: var(--tooltip-bg);}.tooltip-inner{max-width: var(--tooltip-max-width); padding: var(--tooltip-padding-y) var(--tooltip-padding-x); color: var(--tooltip-color); text-align: center; background-color: var(--tooltip-bg); border-radius: var(--tooltip-border-radius);}.popover{--popover-zindex: 1070; --popover-max-width: 276px; --popover-font-size: 0.8125rem; --popover-bg: #FFF; --popover-border-width: var(--border-width); --popover-border-color: #111827; --popover-border-radius: 0.25rem; --popover-inner-border-radius: calc(0.25rem - var(--border-width)); --popover-box-shadow: var(--box-shadow); --popover-header-padding-x: 16px; --popover-header-padding-y: 0.5rem; --popover-header-font-size: 1rem; --popover-header-color: #111827; --popover-header-bg: var(--secondary-bg); --popover-body-padding-x: 16px; --popover-body-padding-y: 16px; --popover-body-color: var(--body-color); --popover-arrow-width: 1rem; --popover-arrow-height: 0.5rem; --popover-arrow-border: var(--popover-border-color); z-index: var(--popover-zindex); display: block; max-width: var(--popover-max-width); font-family: var(--font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; white-space: normal; word-spacing: normal; line-break: auto; font-size: var(--popover-font-size); word-wrap: break-word; background-color: var(--popover-bg); background-clip: padding-box; border: var(--popover-border-width) solid var(--popover-border-color); border-radius: var(--popover-border-radius); box-shadow: var(--popover-box-shadow);}.popover .popover-arrow{display: block; width: var(--popover-arrow-width); height: var(--popover-arrow-height);}.popover .popover-arrow::before, .popover .popover-arrow::after{position: absolute; display: block; content: ""; border-color: transparent; border-style: solid; border-width: 0;}.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow{bottom: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));}.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before, .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after{border-width: var(--popover-arrow-height) calc(var(--popover-arrow-width) * .5) 0;}.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before{bottom: 0; border-top-color: var(--popover-arrow-border);}.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after{bottom: var(--popover-border-width); border-top-color: var(--popover-bg);}.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow{left: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width)); width: var(--popover-arrow-height); height: var(--popover-arrow-width);}.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before, .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after{border-width: calc(var(--popover-arrow-width) * .5) var(--popover-arrow-height) calc(var(--popover-arrow-width) * .5) 0;}.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before{left: 0; border-right-color: var(--popover-arrow-border);}.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after{left: var(--popover-border-width); border-right-color: var(--popover-bg);}.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow{top: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));}.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before, .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after{border-width: 0 calc(var(--popover-arrow-width) * .5) var(--popover-arrow-height);}.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before{top: 0; border-bottom-color: var(--popover-arrow-border);}.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after{top: var(--popover-border-width); border-bottom-color: var(--popover-bg);}.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before{position: absolute; top: 0; left: 50%; display: block; width: var(--popover-arrow-width); margin-left: calc(-.5 * var(--popover-arrow-width)); content: ""; border-bottom: var(--popover-border-width) solid var(--popover-header-bg);}.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow{right: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width)); width: var(--popover-arrow-height); height: var(--popover-arrow-width);}.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before, .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after{border-width: calc(var(--popover-arrow-width) * .5) 0 calc(var(--popover-arrow-width) * .5) var(--popover-arrow-height);}.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before{right: 0; border-left-color: var(--popover-arrow-border);}.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after{right: var(--popover-border-width); border-left-color: var(--popover-bg);}.popover-header{padding: var(--popover-header-padding-y) var(--popover-header-padding-x); margin-bottom: 0; font-size: var(--popover-header-font-size); color: var(--popover-header-color); background-color: var(--popover-header-bg); border-bottom: var(--popover-border-width) solid var(--popover-border-color); border-top-left-radius: var(--popover-inner-border-radius); border-top-right-radius: var(--popover-inner-border-radius);}.popover-header:empty{display: none;}.popover-body{padding: var(--popover-body-padding-y) var(--popover-body-padding-x); color: var(--popover-body-color);}.carousel{position: relative;}.carousel.pointer-event{touch-action: pan-y;}.carousel-inner{position: relative; width: 100%; overflow: hidden;}.carousel-inner::after{display: block; clear: both; content: "";}.carousel-item{position: relative; display: none; float: left; width: 100%; margin-right: -100%; backface-visibility: hidden; transition: transform 0.6s ease-in-out;}@media (prefers-reduced-motion: reduce){.carousel-item{transition: none;}}.carousel-item.active, .carousel-item-next, .carousel-item-prev{display: block;}.carousel-item-next:not(.carousel-item-start), .active.carousel-item-end{transform: translateX(100%);}.carousel-item-prev:not(.carousel-item-end), .active.carousel-item-start{transform: translateX(-100%);}.carousel-fade .carousel-item{opacity: 0; transition-property: opacity; transform: none;}.carousel-fade .carousel-item.active, .carousel-fade .carousel-item-next.carousel-item-start, .carousel-fade .carousel-item-prev.carousel-item-end{z-index: 1; opacity: 1;}.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{z-index: 0; opacity: 0; transition: opacity 0s 0.6s;}@media (prefers-reduced-motion: reduce){.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{transition: none;}}.carousel-control-prev, .carousel-control-next{position: absolute; top: 0; bottom: 0; z-index: 1; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; width: 15%; padding: 0; color: #FFF; text-align: center; background: none; border: 0; opacity: 0.5; transition: opacity 0.15s ease;}@media (prefers-reduced-motion: reduce){.carousel-control-prev, .carousel-control-next{transition: none;}}.carousel-control-prev:hover, .carousel-control-prev:focus, .carousel-control-next:hover, .carousel-control-next:focus{color: #FFF; text-decoration: none; outline: 0; opacity: 0.9;}.carousel-control-prev{left: 0;}.carousel-control-next{right: 0;}.carousel-control-prev-icon, .carousel-control-next-icon{display: inline-block; width: 2rem; height: 2rem; background-repeat: no-repeat; background-position: 50%; background-size: 100% 100%;}.carousel-control-prev-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFF'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e") ;}.carousel-control-next-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFF'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") ;}.carousel-indicators{position: absolute; right: 0; bottom: 0; left: 0; z-index: 2; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; padding: 0; margin-right: 15%; margin-bottom: 1rem; margin-left: 15%;}.carousel-indicators [data-bs-target]{box-sizing: content-box; -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 30px; height: 3px; padding: 0; margin-right: 3px; margin-left: 3px; text-indent: -999px; cursor: pointer; background-color: #FFF; background-clip: padding-box; border: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; opacity: 0.5; transition: opacity 0.6s ease;}@media (prefers-reduced-motion: reduce){.carousel-indicators [data-bs-target]{transition: none;}}.carousel-indicators .active{opacity: 1;}.carousel-caption{position: absolute; right: 15%; bottom: 1.25rem; left: 15%; padding-top: 1.25rem; padding-bottom: 1.25rem; color: #FFF; text-align: center;}.carousel-dark .carousel-control-prev-icon, .carousel-dark .carousel-control-next-icon{filter: invert(1) grayscale(100);}.carousel-dark .carousel-indicators [data-bs-target]{background-color: #000;}.carousel-dark .carousel-caption{color: #000;}.spinner-grow, .spinner-border{display: inline-block; width: var(--spinner-width); height: var(--spinner-height); vertical-align: var(--spinner-vertical-align); border-radius: 50%; animation: var(--spinner-animation-speed) linear infinite var(--spinner-animation-name);}@keyframes spinner-border{to{transform: rotate(360deg) ;}}.spinner-border{--spinner-width: 2rem; --spinner-height: 2rem; --spinner-vertical-align: -0.125em; --spinner-border-width: 0.25em; --spinner-animation-speed: 0.75s; --spinner-animation-name: spinner-border; border: var(--spinner-border-width) solid currentcolor; border-right-color: transparent;}.spinner-border-sm{--spinner-width: 1rem; --spinner-height: 1rem; --spinner-border-width: 0.2em;}@keyframes spinner-grow{0%{transform: scale(0);}50%{opacity: 1; transform: none;}}.spinner-grow{--spinner-width: 2rem; --spinner-height: 2rem; --spinner-vertical-align: -0.125em; --spinner-animation-speed: 0.75s; --spinner-animation-name: spinner-grow; background-color: currentcolor; opacity: 0;}.spinner-grow-sm{--spinner-width: 1rem; --spinner-height: 1rem;}@media (prefers-reduced-motion: reduce){.spinner-border, .spinner-grow{--spinner-animation-speed: 1.5s;}}.offcanvas, .offcanvas-xxl, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm{--offcanvas-zindex: 1045; --offcanvas-width: 400px; --offcanvas-height: 30vh; --offcanvas-padding-x: 16px; --offcanvas-padding-y: 16px; --offcanvas-color: var(--body-color); --offcanvas-bg: var(--body-bg); --offcanvas-border-width: var(--border-width); --offcanvas-border-color: var(--border-color-translucent); --offcanvas-box-shadow: var(--box-shadow-sm); --offcanvas-transition: transform 0.3s ease-in-out; --offcanvas-title-line-height: 1.5;}@media (max-width: 575.98px){.offcanvas-sm{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; box-shadow: var(--offcanvas-box-shadow); transition: var(--offcanvas-transition);}}@media (max-width: 575.98px) and (prefers-reduced-motion: reduce){.offcanvas-sm{transition: none;}}@media (max-width: 575.98px){.offcanvas-sm.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-sm.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-sm.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-sm.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding){transform: none;}.offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show{visibility: visible;}}@media (min-width: 576px){.offcanvas-sm{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-sm .offcanvas-header{display: none;}.offcanvas-sm .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 767.98px){.offcanvas-md{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; box-shadow: var(--offcanvas-box-shadow); transition: var(--offcanvas-transition);}}@media (max-width: 767.98px) and (prefers-reduced-motion: reduce){.offcanvas-md{transition: none;}}@media (max-width: 767.98px){.offcanvas-md.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-md.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-md.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-md.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-md.showing, .offcanvas-md.show:not(.hiding){transform: none;}.offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show{visibility: visible;}}@media (min-width: 768px){.offcanvas-md{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-md .offcanvas-header{display: none;}.offcanvas-md .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 991.98px){.offcanvas-lg{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; box-shadow: var(--offcanvas-box-shadow); transition: var(--offcanvas-transition);}}@media (max-width: 991.98px) and (prefers-reduced-motion: reduce){.offcanvas-lg{transition: none;}}@media (max-width: 991.98px){.offcanvas-lg.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-lg.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-lg.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-lg.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding){transform: none;}.offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show{visibility: visible;}}@media (min-width: 992px){.offcanvas-lg{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-lg .offcanvas-header{display: none;}.offcanvas-lg .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 1199.98px){.offcanvas-xl{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; box-shadow: var(--offcanvas-box-shadow); transition: var(--offcanvas-transition);}}@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce){.offcanvas-xl{transition: none;}}@media (max-width: 1199.98px){.offcanvas-xl.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-xl.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-xl.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-xl.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding){transform: none;}.offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show{visibility: visible;}}@media (min-width: 1200px){.offcanvas-xl{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-xl .offcanvas-header{display: none;}.offcanvas-xl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 1399.98px){.offcanvas-xxl{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; box-shadow: var(--offcanvas-box-shadow); transition: var(--offcanvas-transition);}}@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce){.offcanvas-xxl{transition: none;}}@media (max-width: 1399.98px){.offcanvas-xxl.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-xxl.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-xxl.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-xxl.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding){transform: none;}.offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show{visibility: visible;}}@media (min-width: 1400px){.offcanvas-xxl{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-xxl .offcanvas-header{display: none;}.offcanvas-xxl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}.offcanvas{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; box-shadow: var(--offcanvas-box-shadow); transition: var(--offcanvas-transition);}@media (prefers-reduced-motion: reduce){.offcanvas{transition: none;}}.offcanvas.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas.showing, .offcanvas.show:not(.hiding){transform: none;}.offcanvas.showing, .offcanvas.hiding, .offcanvas.show{visibility: visible;}.offcanvas-backdrop{position: fixed; top: 0; left: 0; z-index: 1040; width: 100vw; height: 100vh; background-color: #000;}.offcanvas-backdrop.fade{opacity: 0;}.offcanvas-backdrop.show{opacity: 0.5;}.offcanvas-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: var(--offcanvas-padding-y) var(--offcanvas-padding-x);}.offcanvas-header .btn-close{padding: calc(var(--offcanvas-padding-y) * .5) calc(var(--offcanvas-padding-x) * .5); margin: calc(-.5 * var(--offcanvas-padding-y)) calc(-.5 * var(--offcanvas-padding-x)) calc(-.5 * var(--offcanvas-padding-y)) auto;}.offcanvas-title{margin-bottom: 0; line-height: var(--offcanvas-title-line-height);}.offcanvas-body{flex-grow: 1; padding: var(--offcanvas-padding-y) var(--offcanvas-padding-x); overflow-y: auto;}.placeholder{display: inline-block; min-height: 1em; vertical-align: middle; cursor: wait; background-color: currentcolor; opacity: 0.5;}.placeholder.btn::before{display: inline-block; content: "";}.placeholder-xs{min-height: .6em;}.placeholder-sm{min-height: .8em;}.placeholder-lg{min-height: 1.2em;}.placeholder-glow .placeholder{animation: placeholder-glow 2s ease-in-out infinite;}@keyframes placeholder-glow{50%{opacity: 0.2;}}.placeholder-wave{mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%); mask-size: 200% 100%; animation: placeholder-wave 2s linear infinite;}@keyframes placeholder-wave{100%{mask-position: -200% 0%;}}.clearfix::after{display: block; clear: both; content: "";}.text-bg-primary{color: #FFF !important; background-color: RGBA(var(--primary-rgb), var(--bg-opacity, 1)) !important;}.text-bg-secondary{color: #000 !important; background-color: RGBA(var(--secondary-rgb), var(--bg-opacity, 1)) !important;}.text-bg-success{color: #FFF !important; background-color: RGBA(var(--success-rgb), var(--bg-opacity, 1)) !important;}.text-bg-info{color: #FFF !important; background-color: RGBA(var(--info-rgb), var(--bg-opacity, 1)) !important;}.text-bg-warning{color: #000 !important; background-color: RGBA(var(--warning-rgb), var(--bg-opacity, 1)) !important;}.text-bg-danger{color: #FFF !important; background-color: RGBA(var(--danger-rgb), var(--bg-opacity, 1)) !important;}.text-bg-light{color: #000 !important; background-color: RGBA(var(--light-rgb), var(--bg-opacity, 1)) !important;}.text-bg-dark{color: #FFF !important; background-color: RGBA(var(--dark-rgb), var(--bg-opacity, 1)) !important;}.link-primary{color: RGBA(var(--primary-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--primary-rgb), var(--link-underline-opacity, 1)) !important;}.link-primary:hover, .link-primary:focus{color: RGBA(79, 53, 72, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(79, 53, 72, var(--link-underline-opacity, 1)) !important;}.link-secondary{color: RGBA(var(--secondary-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--secondary-rgb), var(--link-underline-opacity, 1)) !important;}.link-secondary:hover, .link-secondary:focus{color: RGBA(228, 229, 231, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(228, 229, 231, var(--link-underline-opacity, 1)) !important;}.link-success{color: RGBA(var(--success-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--success-rgb), var(--link-underline-opacity, 1)) !important;}.link-success:hover, .link-success:focus{color: RGBA(28, 117, 48, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(28, 117, 48, var(--link-underline-opacity, 1)) !important;}.link-info{color: RGBA(var(--info-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--info-rgb), var(--link-underline-opacity, 1)) !important;}.link-info:hover, .link-info:focus{color: RGBA(16, 113, 129, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(16, 113, 129, var(--link-underline-opacity, 1)) !important;}.link-warning{color: RGBA(var(--warning-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--warning-rgb), var(--link-underline-opacity, 1)) !important;}.link-warning:hover, .link-warning:focus{color: RGBA(240, 186, 77, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(240, 186, 77, var(--link-underline-opacity, 1)) !important;}.link-danger{color: RGBA(var(--danger-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--danger-rgb), var(--link-underline-opacity, 1)) !important;}.link-danger:hover, .link-danger:focus{color: RGBA(148, 53, 62, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(148, 53, 62, var(--link-underline-opacity, 1)) !important;}.link-light{color: RGBA(var(--light-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--light-rgb), var(--link-underline-opacity, 1)) !important;}.link-light:hover, .link-light:focus{color: RGBA(255, 255, 255, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(255, 255, 255, var(--link-underline-opacity, 1)) !important;}.link-dark{color: RGBA(var(--dark-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--dark-rgb), var(--link-underline-opacity, 1)) !important;}.link-dark:hover, .link-dark:focus{color: RGBA(12, 17, 27, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(12, 17, 27, var(--link-underline-opacity, 1)) !important;}.link-body-emphasis{color: RGBA(var(--emphasis-color-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--emphasis-color-rgb), var(--link-underline-opacity, 1)) !important;}.link-body-emphasis:hover, .link-body-emphasis:focus{color: RGBA(var(--emphasis-color-rgb), var(--link-opacity, 0.75)) !important; text-decoration-color: RGBA(var(--emphasis-color-rgb), var(--link-underline-opacity, 0.75)) !important;}.focus-ring:focus{outline: 0; box-shadow: var(--focus-ring-x, 0) var(--focus-ring-y, 0) var(--focus-ring-blur, 0) var(--focus-ring-width) var(--focus-ring-color);}.icon-link{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; gap: 0.375rem; align-items: center; text-decoration-color: rgba(var(--link-color-rgb), var(--link-opacity, 0.5)); text-underline-offset: 0.25em; backface-visibility: hidden;}.icon-link > .bi{flex-shrink: 0; width: 1em; height: 1em; fill: currentcolor; transition: 0.2s ease-in-out transform;}@media (prefers-reduced-motion: reduce){.icon-link > .bi{transition: none;}}.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi{transform: var(--icon-link-transform, translate3d(0.25em, 0, 0));}.ratio{position: relative; width: 100%;}.ratio::before{display: block; padding-top: var(--aspect-ratio); content: "";}.ratio > *{position: absolute; top: 0; left: 0; width: 100%; height: 100%;}.ratio-1x1{--aspect-ratio: 100%;}.ratio-4x3{--aspect-ratio: calc(3 / 4 * 100%);}.ratio-16x9{--aspect-ratio: calc(9 / 16 * 100%);}.ratio-21x9{--aspect-ratio: calc(9 / 21 * 100%);}.fixed-top{position: fixed; top: 0; right: 0; left: 0; z-index: 1030;}.fixed-bottom{position: fixed; right: 0; bottom: 0; left: 0; z-index: 1030;}.sticky-top{position: sticky; top: 0; z-index: 1020;}.sticky-bottom{position: sticky; bottom: 0; z-index: 1020;}@media (min-width: 576px){.sticky-sm-top{position: sticky; top: 0; z-index: 1020;}.sticky-sm-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 768px){.sticky-md-top{position: sticky; top: 0; z-index: 1020;}.sticky-md-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 992px){.sticky-lg-top{position: sticky; top: 0; z-index: 1020;}.sticky-lg-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 1200px){.sticky-xl-top{position: sticky; top: 0; z-index: 1020;}.sticky-xl-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 1400px){.sticky-xxl-top{position: sticky; top: 0; z-index: 1020;}.sticky-xxl-bottom{position: sticky; bottom: 0; z-index: 1020;}}.hstack{display: -webkit-box; display: -webkit-flex; display: flex; flex-direction: row; align-items: center; align-self: stretch;}.vstack{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-self: stretch;}.visually-hidden, .visually-hidden-focusable:not(:focus):not(:focus-within){width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0, 0, 0, 0) !important; white-space: nowrap !important; border: 0 !important;}.visually-hidden:not(caption), .visually-hidden-focusable:not(:focus):not(:focus-within):not(caption){position: absolute !important;}.stretched-link::after{position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1; content: "";}.text-truncate{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.vr{display: inline-block; align-self: stretch; width: var(--border-width); min-height: 1em; background-color: currentcolor; opacity: 0.25;}

/* /web/static/src/scss/utilities_custom.scss */
 .opacity-0-hover:hover, .opacity-trigger-hover:hover .opacity-0-hover{opacity: 0 !important;}.opacity-25-hover:hover, .opacity-trigger-hover:hover .opacity-25-hover{opacity: 0.25 !important;}.opacity-50-hover:hover, .opacity-trigger-hover:hover .opacity-50-hover{opacity: 0.5 !important;}.opacity-75-hover:hover, .opacity-trigger-hover:hover .opacity-75-hover{opacity: 0.75 !important;}.opacity-100-hover:hover, .opacity-trigger-hover:hover .opacity-100-hover{opacity: 1 !important;}.opacity-disabled-hover:hover, .opacity-trigger-hover:hover .opacity-disabled-hover{opacity: 0.5 !important;}.opacity-muted-hover:hover, .opacity-trigger-hover:hover .opacity-muted-hover{opacity: 0.76 !important;}.d-empty-none:empty{display: none !important;}.smaller{font-size: 0.75rem;}

/* /web/static/src/webclient/actions/reports/utilities_custom_report.scss */
 

/* /web/static/lib/bootstrap/scss/utilities/_api.scss */
 .align-baseline{vertical-align: baseline !important;}.align-top{vertical-align: top !important;}.align-middle{vertical-align: middle !important;}.align-bottom{vertical-align: bottom !important;}.align-text-bottom{vertical-align: text-bottom !important;}.align-text-top{vertical-align: text-top !important;}.float-start{float: left !important;}.float-end{float: right !important;}.float-none{float: none !important;}.object-fit-contain{object-fit: contain !important;}.object-fit-cover{object-fit: cover !important;}.object-fit-fill{object-fit: fill !important;}.object-fit-scale{object-fit: scale-down !important;}.object-fit-none{object-fit: none !important;}.opacity-0{opacity: 0 !important;}.opacity-25{opacity: 0.25 !important;}.opacity-50{opacity: 0.5 !important;}.opacity-75{opacity: 0.75 !important;}.opacity-100{opacity: 1 !important;}.opacity-disabled{opacity: 0.5 !important;}.opacity-muted{opacity: 0.76 !important;}.overflow-auto{overflow: auto !important;}.overflow-hidden{overflow: hidden !important;}.overflow-visible{overflow: visible !important;}.overflow-scroll{overflow: scroll !important;}.overflow-x-auto{overflow-x: auto !important;}.overflow-x-hidden{overflow-x: hidden !important;}.overflow-x-visible{overflow-x: visible !important;}.overflow-x-scroll{overflow-x: scroll !important;}.overflow-y-auto{overflow-y: auto !important;}.overflow-y-hidden{overflow-y: hidden !important;}.overflow-y-visible{overflow-y: visible !important;}.overflow-y-scroll{overflow-y: scroll !important;}.d-inline{display: inline !important;}.d-inline-block{display: inline-block !important;}.d-block{display: block !important;}.d-grid{display: grid !important;}.d-inline-grid{display: inline-grid !important;}.d-table{display: table !important;}.d-table-row{display: table-row !important;}.d-table-cell{display: table-cell !important;}.d-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-none{display: none !important;}.d-contents{display: contents !important;}.shadow{box-shadow: var(--box-shadow) !important;}.shadow-sm{box-shadow: var(--box-shadow-sm) !important;}.shadow-lg{box-shadow: var(--box-shadow-lg) !important;}.shadow-none{box-shadow: none !important;}.focus-ring-primary{--focus-ring-color: rgba(var(--primary-rgb), var(--focus-ring-opacity));}.focus-ring-secondary{--focus-ring-color: rgba(var(--secondary-rgb), var(--focus-ring-opacity));}.focus-ring-success{--focus-ring-color: rgba(var(--success-rgb), var(--focus-ring-opacity));}.focus-ring-info{--focus-ring-color: rgba(var(--info-rgb), var(--focus-ring-opacity));}.focus-ring-warning{--focus-ring-color: rgba(var(--warning-rgb), var(--focus-ring-opacity));}.focus-ring-danger{--focus-ring-color: rgba(var(--danger-rgb), var(--focus-ring-opacity));}.focus-ring-light{--focus-ring-color: rgba(var(--light-rgb), var(--focus-ring-opacity));}.focus-ring-dark{--focus-ring-color: rgba(var(--dark-rgb), var(--focus-ring-opacity));}.position-static{position: static !important;}.position-relative{position: relative !important;}.position-absolute{position: absolute !important;}.position-fixed{position: fixed !important;}.position-sticky{position: sticky !important;}.top-0{top: 0 !important;}.top-50{top: 50% !important;}.top-100{top: 100% !important;}.bottom-0{bottom: 0 !important;}.bottom-50{bottom: 50% !important;}.bottom-100{bottom: 100% !important;}.start-0{left: 0 !important;}.start-50{left: 50% !important;}.start-100{left: 100% !important;}.end-0{right: 0 !important;}.end-50{right: 50% !important;}.end-100{right: 100% !important;}.translate-middle{transform: translate(-50%, -50%) !important;}.translate-middle-x{transform: translateX(-50%) !important;}.translate-middle-y{transform: translateY(-50%) !important;}.border{border: 1px solid #111827 !important;}.border-0{border: 0 !important;}.border-top{border-top: 1px solid #111827 !important;}.border-top-0{border-top: 0 !important;}.border-end{border-right: 1px solid #111827 !important;}.border-end-0{border-right: 0 !important;}.border-bottom{border-bottom: 1px solid #111827 !important;}.border-bottom-0{border-bottom: 0 !important;}.border-start{border-left: 1px solid #111827 !important;}.border-start-0{border-left: 0 !important;}.border-primary{--border-opacity: 1; border-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;}.border-secondary{--border-opacity: 1; border-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;}.border-success{--border-opacity: 1; border-color: rgba(var(--success-rgb), var(--border-opacity)) !important;}.border-info{--border-opacity: 1; border-color: rgba(var(--info-rgb), var(--border-opacity)) !important;}.border-warning{--border-opacity: 1; border-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;}.border-danger{--border-opacity: 1; border-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;}.border-light{--border-opacity: 1; border-color: rgba(var(--light-rgb), var(--border-opacity)) !important;}.border-dark{--border-opacity: 1; border-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;}.border-black{--border-opacity: 1; border-color: rgba(var(--black-rgb), var(--border-opacity)) !important;}.border-white{--border-opacity: 1; border-color: rgba(var(--white-rgb), var(--border-opacity)) !important;}.border-transparent{--border-opacity: 1; border-color: transparent !important;}.border-primary-subtle{border-color: var(--primary-border-subtle) !important;}.border-secondary-subtle{border-color: var(--secondary-border-subtle) !important;}.border-success-subtle{border-color: var(--success-border-subtle) !important;}.border-info-subtle{border-color: var(--info-border-subtle) !important;}.border-warning-subtle{border-color: var(--warning-border-subtle) !important;}.border-danger-subtle{border-color: var(--danger-border-subtle) !important;}.border-light-subtle{border-color: var(--light-border-subtle) !important;}.border-dark-subtle{border-color: var(--dark-border-subtle) !important;}.border-1{border-width: 1px !important;}.border-2{border-width: 2px !important;}.border-3{border-width: 3px !important;}.border-4{border-width: 4px !important;}.border-5{border-width: 5px !important;}.border-opacity-10{--border-opacity: 0.1;}.border-opacity-25{--border-opacity: 0.25;}.border-opacity-50{--border-opacity: 0.5;}.border-opacity-75{--border-opacity: 0.75;}.border-opacity-100{--border-opacity: 1;}.w-0{width: 0 !important;}.w-25{width: 25% !important;}.w-50{width: 50% !important;}.w-75{width: 75% !important;}.w-100{width: 100% !important;}.w-auto{width: auto !important;}.mw-0{max-width: 0 !important;}.mw-25{max-width: 25% !important;}.mw-50{max-width: 50% !important;}.mw-75{max-width: 75% !important;}.mw-100{max-width: 100% !important;}.mw-auto{max-width: auto !important;}.vw-100{width: 100vw !important;}.min-vw-100{min-width: 100vw !important;}.h-0{height: 0 !important;}.h-25{height: 25% !important;}.h-50{height: 50% !important;}.h-75{height: 75% !important;}.h-100{height: 100% !important;}.h-auto{height: auto !important;}.mh-0{max-height: 0 !important;}.mh-25{max-height: 25% !important;}.mh-50{max-height: 50% !important;}.mh-75{max-height: 75% !important;}.mh-100{max-height: 100% !important;}.mh-auto{max-height: auto !important;}.vh-100{height: 100vh !important;}.min-vh-100{min-height: 100vh !important;}.flex-fill{flex: 1 1 auto !important;}.flex-row{flex-direction: row !important;}.flex-column{flex-direction: column !important;}.flex-row-reverse{flex-direction: row-reverse !important;}.flex-column-reverse{flex-direction: column-reverse !important;}.flex-grow-0{flex-grow: 0 !important;}.flex-grow-1{flex-grow: 1 !important;}.flex-shrink-0{flex-shrink: 0 !important;}.flex-shrink-1{flex-shrink: 1 !important;}.flex-wrap{flex-wrap: wrap !important;}.flex-nowrap{flex-wrap: nowrap !important;}.flex-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-center{justify-content: center !important;}.justify-content-between{justify-content: space-between !important;}.justify-content-around{justify-content: space-around !important;}.justify-content-evenly{justify-content: space-evenly !important;}.align-items-start{align-items: flex-start !important;}.align-items-end{align-items: flex-end !important;}.align-items-center{align-items: center !important;}.align-items-baseline{align-items: baseline !important;}.align-items-stretch{align-items: stretch !important;}.align-content-start{align-content: flex-start !important;}.align-content-end{align-content: flex-end !important;}.align-content-center{align-content: center !important;}.align-content-between{align-content: space-between !important;}.align-content-around{align-content: space-around !important;}.align-content-stretch{align-content: stretch !important;}.align-self-auto{align-self: auto !important;}.align-self-start{align-self: flex-start !important;}.align-self-end{align-self: flex-end !important;}.align-self-center{align-self: center !important;}.align-self-baseline{align-self: baseline !important;}.align-self-stretch{align-self: stretch !important;}.order-first{order: -1 !important;}.order-last{order: 13 !important;}.order-0{order: 0 !important;}.order-1{order: 1 !important;}.order-2{order: 2 !important;}.order-3{order: 3 !important;}.order-4{order: 4 !important;}.order-5{order: 5 !important;}.order-6{order: 6 !important;}.order-7{order: 7 !important;}.order-8{order: 8 !important;}.order-9{order: 9 !important;}.order-10{order: 10 !important;}.order-11{order: 11 !important;}.order-12{order: 12 !important;}.m-0{margin: 0 !important;}.m-1{margin: 4px !important;}.m-2{margin: 8px !important;}.m-3{margin: 16px !important;}.m-4{margin: 24px !important;}.m-5{margin: 48px !important;}.m-auto{margin: auto !important;}.mx-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-auto{margin-right: auto !important; margin-left: auto !important;}.my-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-0{margin-top: 0 !important;}.mt-1{margin-top: 4px !important;}.mt-2{margin-top: 8px !important;}.mt-3{margin-top: 16px !important;}.mt-4{margin-top: 24px !important;}.mt-5{margin-top: 48px !important;}.mt-auto{margin-top: auto !important;}.me-0{margin-right: 0 !important;}.me-1{margin-right: 4px !important;}.me-2{margin-right: 8px !important;}.me-3{margin-right: 16px !important;}.me-4{margin-right: 24px !important;}.me-5{margin-right: 48px !important;}.me-auto{margin-right: auto !important;}.mb-0{margin-bottom: 0 !important;}.mb-1{margin-bottom: 4px !important;}.mb-2{margin-bottom: 8px !important;}.mb-3{margin-bottom: 16px !important;}.mb-4{margin-bottom: 24px !important;}.mb-5{margin-bottom: 48px !important;}.mb-auto{margin-bottom: auto !important;}.ms-0{margin-left: 0 !important;}.ms-1{margin-left: 4px !important;}.ms-2{margin-left: 8px !important;}.ms-3{margin-left: 16px !important;}.ms-4{margin-left: 24px !important;}.ms-5{margin-left: 48px !important;}.ms-auto{margin-left: auto !important;}.m-n1{margin: -4px !important;}.m-n2{margin: -8px !important;}.m-n3{margin: -16px !important;}.m-n4{margin: -24px !important;}.m-n5{margin: -48px !important;}.mx-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-n1{margin-top: -4px !important;}.mt-n2{margin-top: -8px !important;}.mt-n3{margin-top: -16px !important;}.mt-n4{margin-top: -24px !important;}.mt-n5{margin-top: -48px !important;}.me-n1{margin-right: -4px !important;}.me-n2{margin-right: -8px !important;}.me-n3{margin-right: -16px !important;}.me-n4{margin-right: -24px !important;}.me-n5{margin-right: -48px !important;}.mb-n1{margin-bottom: -4px !important;}.mb-n2{margin-bottom: -8px !important;}.mb-n3{margin-bottom: -16px !important;}.mb-n4{margin-bottom: -24px !important;}.mb-n5{margin-bottom: -48px !important;}.ms-n1{margin-left: -4px !important;}.ms-n2{margin-left: -8px !important;}.ms-n3{margin-left: -16px !important;}.ms-n4{margin-left: -24px !important;}.ms-n5{margin-left: -48px !important;}.p-0{padding: 0 !important;}.p-1{padding: 4px !important;}.p-2{padding: 8px !important;}.p-3{padding: 16px !important;}.p-4{padding: 24px !important;}.p-5{padding: 48px !important;}.px-0{padding-right: 0 !important; padding-left: 0 !important;}.px-1{padding-right: 4px !important; padding-left: 4px !important;}.px-2{padding-right: 8px !important; padding-left: 8px !important;}.px-3{padding-right: 16px !important; padding-left: 16px !important;}.px-4{padding-right: 24px !important; padding-left: 24px !important;}.px-5{padding-right: 48px !important; padding-left: 48px !important;}.py-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-0{padding-top: 0 !important;}.pt-1{padding-top: 4px !important;}.pt-2{padding-top: 8px !important;}.pt-3{padding-top: 16px !important;}.pt-4{padding-top: 24px !important;}.pt-5{padding-top: 48px !important;}.pe-0{padding-right: 0 !important;}.pe-1{padding-right: 4px !important;}.pe-2{padding-right: 8px !important;}.pe-3{padding-right: 16px !important;}.pe-4{padding-right: 24px !important;}.pe-5{padding-right: 48px !important;}.pb-0{padding-bottom: 0 !important;}.pb-1{padding-bottom: 4px !important;}.pb-2{padding-bottom: 8px !important;}.pb-3{padding-bottom: 16px !important;}.pb-4{padding-bottom: 24px !important;}.pb-5{padding-bottom: 48px !important;}.ps-0{padding-left: 0 !important;}.ps-1{padding-left: 4px !important;}.ps-2{padding-left: 8px !important;}.ps-3{padding-left: 16px !important;}.ps-4{padding-left: 24px !important;}.ps-5{padding-left: 48px !important;}.gap-0{gap: 0 !important;}.gap-1{gap: 4px !important;}.gap-2{gap: 8px !important;}.gap-3{gap: 16px !important;}.gap-4{gap: 24px !important;}.gap-5{gap: 48px !important;}.row-gap-0{row-gap: 0 !important;}.row-gap-1{row-gap: 4px !important;}.row-gap-2{row-gap: 8px !important;}.row-gap-3{row-gap: 16px !important;}.row-gap-4{row-gap: 24px !important;}.row-gap-5{row-gap: 48px !important;}.column-gap-0{column-gap: 0 !important;}.column-gap-1{column-gap: 4px !important;}.column-gap-2{column-gap: 8px !important;}.column-gap-3{column-gap: 16px !important;}.column-gap-4{column-gap: 24px !important;}.column-gap-5{column-gap: 48px !important;}.font-monospace{font-family: var(--font-monospace) !important;}.font-sans-serif{font-family: var(--font-sans-serif) !important;}.fs-1{font-size: 2.5rem !important;}.fs-2{font-size: 2rem !important;}.fs-3{font-size: 1.75rem !important;}.fs-4{font-size: 1.5rem !important;}.fs-5{font-size: 1.25rem !important;}.fs-6{font-size: 1rem !important;}.fst-italic{font-style: italic !important;}.fst-normal{font-style: normal !important;}.fw-lighter{font-weight: lighter !important;}.fw-light{font-weight: 300 !important;}.fw-normal{font-weight: 400 !important;}.fw-medium{font-weight: 500 !important;}.fw-semibold{font-weight: 600 !important;}.fw-bold{font-weight: 700 !important;}.fw-bolder{font-weight: bolder !important;}.lh-1{line-height: 1 !important;}.lh-sm{line-height: 1.25 !important;}.lh-base{line-height: 1.5 !important;}.lh-lg{line-height: 2 !important;}.text-start{text-align: left !important;}.text-end{text-align: right !important;}.text-center{text-align: center !important;}.text-decoration-none{text-decoration: none !important;}.text-decoration-underline{text-decoration: underline !important;}.text-decoration-line-through{text-decoration: line-through !important;}.text-lowercase{text-transform: lowercase !important;}.text-uppercase{text-transform: uppercase !important;}.text-capitalize{text-transform: capitalize !important;}.text-wrap{white-space: normal !important;}.text-nowrap{white-space: nowrap !important;}.text-prewrap{white-space: pre-wrap !important;}.text-break{word-wrap: break-word !important; word-break: break-word !important;}.text-opacity-25{--text-opacity: 0.25;}.text-opacity-50{--text-opacity: 0.5;}.text-opacity-75{--text-opacity: 0.75;}.text-opacity-100{--text-opacity: 1;}.text-primary-emphasis{color: var(--primary-text-emphasis) !important;}.text-secondary-emphasis{color: var(--secondary-text-emphasis) !important;}.text-success-emphasis{color: var(--success-text-emphasis) !important;}.text-info-emphasis{color: var(--info-text-emphasis) !important;}.text-warning-emphasis{color: var(--warning-text-emphasis) !important;}.text-danger-emphasis{color: var(--danger-text-emphasis) !important;}.text-light-emphasis{color: var(--light-text-emphasis) !important;}.text-dark-emphasis{color: var(--dark-text-emphasis) !important;}.link-opacity-10{--link-opacity: 0.1;}.link-opacity-10-hover:hover{--link-opacity: 0.1;}.link-opacity-25{--link-opacity: 0.25;}.link-opacity-25-hover:hover{--link-opacity: 0.25;}.link-opacity-50{--link-opacity: 0.5;}.link-opacity-50-hover:hover{--link-opacity: 0.5;}.link-opacity-75{--link-opacity: 0.75;}.link-opacity-75-hover:hover{--link-opacity: 0.75;}.link-opacity-100{--link-opacity: 1;}.link-opacity-100-hover:hover{--link-opacity: 1;}.link-offset-1{text-underline-offset: 0.125em !important;}.link-offset-1-hover:hover{text-underline-offset: 0.125em !important;}.link-offset-2{text-underline-offset: 0.25em !important;}.link-offset-2-hover:hover{text-underline-offset: 0.25em !important;}.link-offset-3{text-underline-offset: 0.375em !important;}.link-offset-3-hover:hover{text-underline-offset: 0.375em !important;}.link-underline-primary{--link-underline-opacity: 1; text-decoration-color: rgba(var(--primary-rgb), var(--link-underline-opacity)) !important;}.link-underline-secondary{--link-underline-opacity: 1; text-decoration-color: rgba(var(--secondary-rgb), var(--link-underline-opacity)) !important;}.link-underline-success{--link-underline-opacity: 1; text-decoration-color: rgba(var(--success-rgb), var(--link-underline-opacity)) !important;}.link-underline-info{--link-underline-opacity: 1; text-decoration-color: rgba(var(--info-rgb), var(--link-underline-opacity)) !important;}.link-underline-warning{--link-underline-opacity: 1; text-decoration-color: rgba(var(--warning-rgb), var(--link-underline-opacity)) !important;}.link-underline-danger{--link-underline-opacity: 1; text-decoration-color: rgba(var(--danger-rgb), var(--link-underline-opacity)) !important;}.link-underline-light{--link-underline-opacity: 1; text-decoration-color: rgba(var(--light-rgb), var(--link-underline-opacity)) !important;}.link-underline-dark{--link-underline-opacity: 1; text-decoration-color: rgba(var(--dark-rgb), var(--link-underline-opacity)) !important;}.link-underline{--link-underline-opacity: 1; text-decoration-color: rgba(var(--link-color-rgb), var(--link-underline-opacity, 1)) !important;}.link-underline-opacity-0{--link-underline-opacity: 0;}.link-underline-opacity-0-hover:hover{--link-underline-opacity: 0;}.link-underline-opacity-10{--link-underline-opacity: 0.1;}.link-underline-opacity-10-hover:hover{--link-underline-opacity: 0.1;}.link-underline-opacity-25{--link-underline-opacity: 0.25;}.link-underline-opacity-25-hover:hover{--link-underline-opacity: 0.25;}.link-underline-opacity-50{--link-underline-opacity: 0.5;}.link-underline-opacity-50-hover:hover{--link-underline-opacity: 0.5;}.link-underline-opacity-75{--link-underline-opacity: 0.75;}.link-underline-opacity-75-hover:hover{--link-underline-opacity: 0.75;}.link-underline-opacity-100{--link-underline-opacity: 1;}.link-underline-opacity-100-hover:hover{--link-underline-opacity: 1;}.bg-opacity-0{--bg-opacity: 0;}.bg-opacity-25{--bg-opacity: 0.25;}.bg-opacity-50{--bg-opacity: 0.5;}.bg-opacity-75{--bg-opacity: 0.75;}.bg-opacity-100{--bg-opacity: 1;}.bg-opacity-disabled{--bg-opacity: 0.5;}.bg-opacity-muted{--bg-opacity: 0.76;}.bg-primary-subtle{background-color: var(--primary-bg-subtle) !important;}.bg-secondary-subtle{background-color: var(--secondary-bg-subtle) !important;}.bg-success-subtle{background-color: var(--success-bg-subtle) !important;}.bg-info-subtle{background-color: var(--info-bg-subtle) !important;}.bg-warning-subtle{background-color: var(--warning-bg-subtle) !important;}.bg-danger-subtle{background-color: var(--danger-bg-subtle) !important;}.bg-light-subtle{background-color: var(--light-bg-subtle) !important;}.bg-dark-subtle{background-color: var(--dark-bg-subtle) !important;}.bg-gradient{background-image: var(--gradient) !important;}.user-select-all{user-select: all !important;}.user-select-auto{user-select: auto !important;}.user-select-none{user-select: none !important;}.pe-none{pointer-events: none !important;}.pe-auto{pointer-events: auto !important;}.rounded{border-radius: var(--border-radius) !important;}.rounded-0{border-radius: 0 !important;}.rounded-1{border-radius: var(--border-radius-sm) !important;}.rounded-2{border-radius: var(--border-radius) !important;}.rounded-3{border-radius: var(--border-radius-lg) !important;}.rounded-4{border-radius: var(--border-radius-xl) !important;}.rounded-5{border-radius: var(--border-radius-xxl) !important;}.rounded-circle{border-radius: 50% !important;}.rounded-pill{border-radius: var(--border-radius-pill) !important;}.rounded-top{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-0{border-top-left-radius: 0 !important; border-top-right-radius: 0 !important;}.rounded-top-1{border-top-left-radius: 0.1875rem !important; border-top-right-radius: 0.1875rem !important;}.rounded-top-2{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-3{border-top-left-radius: 0.75rem !important; border-top-right-radius: 0.75rem !important;}.rounded-top-circle{border-top-left-radius: 50% !important; border-top-right-radius: 50% !important;}.rounded-top-pill{border-top-left-radius: 50rem !important; border-top-right-radius: 50rem !important;}.rounded-end{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-0{border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;}.rounded-end-1{border-top-right-radius: 0.1875rem !important; border-bottom-right-radius: 0.1875rem !important;}.rounded-end-2{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-3{border-top-right-radius: 0.75rem !important; border-bottom-right-radius: 0.75rem !important;}.rounded-end-circle{border-top-right-radius: 50% !important; border-bottom-right-radius: 50% !important;}.rounded-end-pill{border-top-right-radius: 50rem !important; border-bottom-right-radius: 50rem !important;}.rounded-bottom{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-0{border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important;}.rounded-bottom-1{border-bottom-right-radius: 0.1875rem !important; border-bottom-left-radius: 0.1875rem !important;}.rounded-bottom-2{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-3{border-bottom-right-radius: 0.75rem !important; border-bottom-left-radius: 0.75rem !important;}.rounded-bottom-circle{border-bottom-right-radius: 50% !important; border-bottom-left-radius: 50% !important;}.rounded-bottom-pill{border-bottom-right-radius: 50rem !important; border-bottom-left-radius: 50rem !important;}.rounded-start{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-0{border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important;}.rounded-start-1{border-bottom-left-radius: 0.1875rem !important; border-top-left-radius: 0.1875rem !important;}.rounded-start-2{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-3{border-bottom-left-radius: 0.75rem !important; border-top-left-radius: 0.75rem !important;}.rounded-start-circle{border-bottom-left-radius: 50% !important; border-top-left-radius: 50% !important;}.rounded-start-pill{border-bottom-left-radius: 50rem !important; border-top-left-radius: 50rem !important;}.visible{visibility: visible !important;}.invisible{visibility: hidden !important;}.z-n1{z-index: -1 !important;}.z-0{z-index: 0 !important;}.z-1{z-index: 1 !important;}.z-2{z-index: 2 !important;}.z-3{z-index: 3 !important;}.cursor-default{cursor: default !important;}.cursor-pointer{cursor: pointer !important;}.flex-basis-0{flex-basis: 0 !important;}.flex-basis-25{flex-basis: 25% !important;}.flex-basis-50{flex-basis: 50% !important;}.flex-basis-75{flex-basis: 75% !important;}.flex-basis-100{flex-basis: 100% !important;}.flex-basis-auto{flex-basis: auto !important;}.transition-none{transition: none !important;}.transition-base{transition: all 0.2s ease-in-out !important;}.transition-fade{transition: opacity 0.15s linear !important;}.min-w-0{min-width: 0 !important;}@media (min-width: 576px){.float-sm-start{float: left !important;}.float-sm-end{float: right !important;}.float-sm-none{float: none !important;}.object-fit-sm-contain{object-fit: contain !important;}.object-fit-sm-cover{object-fit: cover !important;}.object-fit-sm-fill{object-fit: fill !important;}.object-fit-sm-scale{object-fit: scale-down !important;}.object-fit-sm-none{object-fit: none !important;}.d-sm-inline{display: inline !important;}.d-sm-inline-block{display: inline-block !important;}.d-sm-block{display: block !important;}.d-sm-grid{display: grid !important;}.d-sm-inline-grid{display: inline-grid !important;}.d-sm-table{display: table !important;}.d-sm-table-row{display: table-row !important;}.d-sm-table-cell{display: table-cell !important;}.d-sm-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-sm-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-sm-none{display: none !important;}.d-sm-contents{display: contents !important;}.position-sm-static{position: static !important;}.position-sm-relative{position: relative !important;}.position-sm-absolute{position: absolute !important;}.position-sm-fixed{position: fixed !important;}.position-sm-sticky{position: sticky !important;}.w-sm-0{width: 0 !important;}.w-sm-25{width: 25% !important;}.w-sm-50{width: 50% !important;}.w-sm-75{width: 75% !important;}.w-sm-100{width: 100% !important;}.w-sm-auto{width: auto !important;}.mw-sm-0{max-width: 0 !important;}.mw-sm-25{max-width: 25% !important;}.mw-sm-50{max-width: 50% !important;}.mw-sm-75{max-width: 75% !important;}.mw-sm-100{max-width: 100% !important;}.mw-sm-auto{max-width: auto !important;}.h-sm-0{height: 0 !important;}.h-sm-25{height: 25% !important;}.h-sm-50{height: 50% !important;}.h-sm-75{height: 75% !important;}.h-sm-100{height: 100% !important;}.h-sm-auto{height: auto !important;}.mh-sm-0{max-height: 0 !important;}.mh-sm-25{max-height: 25% !important;}.mh-sm-50{max-height: 50% !important;}.mh-sm-75{max-height: 75% !important;}.mh-sm-100{max-height: 100% !important;}.mh-sm-auto{max-height: auto !important;}.flex-sm-fill{flex: 1 1 auto !important;}.flex-sm-row{flex-direction: row !important;}.flex-sm-column{flex-direction: column !important;}.flex-sm-row-reverse{flex-direction: row-reverse !important;}.flex-sm-column-reverse{flex-direction: column-reverse !important;}.flex-sm-grow-0{flex-grow: 0 !important;}.flex-sm-grow-1{flex-grow: 1 !important;}.flex-sm-shrink-0{flex-shrink: 0 !important;}.flex-sm-shrink-1{flex-shrink: 1 !important;}.flex-sm-wrap{flex-wrap: wrap !important;}.flex-sm-nowrap{flex-wrap: nowrap !important;}.flex-sm-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-sm-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-sm-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-sm-center{justify-content: center !important;}.justify-content-sm-between{justify-content: space-between !important;}.justify-content-sm-around{justify-content: space-around !important;}.justify-content-sm-evenly{justify-content: space-evenly !important;}.align-items-sm-start{align-items: flex-start !important;}.align-items-sm-end{align-items: flex-end !important;}.align-items-sm-center{align-items: center !important;}.align-items-sm-baseline{align-items: baseline !important;}.align-items-sm-stretch{align-items: stretch !important;}.align-content-sm-start{align-content: flex-start !important;}.align-content-sm-end{align-content: flex-end !important;}.align-content-sm-center{align-content: center !important;}.align-content-sm-between{align-content: space-between !important;}.align-content-sm-around{align-content: space-around !important;}.align-content-sm-stretch{align-content: stretch !important;}.align-self-sm-auto{align-self: auto !important;}.align-self-sm-start{align-self: flex-start !important;}.align-self-sm-end{align-self: flex-end !important;}.align-self-sm-center{align-self: center !important;}.align-self-sm-baseline{align-self: baseline !important;}.align-self-sm-stretch{align-self: stretch !important;}.order-sm-first{order: -1 !important;}.order-sm-last{order: 13 !important;}.order-sm-0{order: 0 !important;}.order-sm-1{order: 1 !important;}.order-sm-2{order: 2 !important;}.order-sm-3{order: 3 !important;}.order-sm-4{order: 4 !important;}.order-sm-5{order: 5 !important;}.order-sm-6{order: 6 !important;}.order-sm-7{order: 7 !important;}.order-sm-8{order: 8 !important;}.order-sm-9{order: 9 !important;}.order-sm-10{order: 10 !important;}.order-sm-11{order: 11 !important;}.order-sm-12{order: 12 !important;}.m-sm-0{margin: 0 !important;}.m-sm-1{margin: 4px !important;}.m-sm-2{margin: 8px !important;}.m-sm-3{margin: 16px !important;}.m-sm-4{margin: 24px !important;}.m-sm-5{margin: 48px !important;}.m-sm-auto{margin: auto !important;}.mx-sm-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-sm-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-sm-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-sm-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-sm-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-sm-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-sm-auto{margin-right: auto !important; margin-left: auto !important;}.my-sm-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-sm-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-sm-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-sm-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-sm-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-sm-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-sm-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-sm-0{margin-top: 0 !important;}.mt-sm-1{margin-top: 4px !important;}.mt-sm-2{margin-top: 8px !important;}.mt-sm-3{margin-top: 16px !important;}.mt-sm-4{margin-top: 24px !important;}.mt-sm-5{margin-top: 48px !important;}.mt-sm-auto{margin-top: auto !important;}.me-sm-0{margin-right: 0 !important;}.me-sm-1{margin-right: 4px !important;}.me-sm-2{margin-right: 8px !important;}.me-sm-3{margin-right: 16px !important;}.me-sm-4{margin-right: 24px !important;}.me-sm-5{margin-right: 48px !important;}.me-sm-auto{margin-right: auto !important;}.mb-sm-0{margin-bottom: 0 !important;}.mb-sm-1{margin-bottom: 4px !important;}.mb-sm-2{margin-bottom: 8px !important;}.mb-sm-3{margin-bottom: 16px !important;}.mb-sm-4{margin-bottom: 24px !important;}.mb-sm-5{margin-bottom: 48px !important;}.mb-sm-auto{margin-bottom: auto !important;}.ms-sm-0{margin-left: 0 !important;}.ms-sm-1{margin-left: 4px !important;}.ms-sm-2{margin-left: 8px !important;}.ms-sm-3{margin-left: 16px !important;}.ms-sm-4{margin-left: 24px !important;}.ms-sm-5{margin-left: 48px !important;}.ms-sm-auto{margin-left: auto !important;}.m-sm-n1{margin: -4px !important;}.m-sm-n2{margin: -8px !important;}.m-sm-n3{margin: -16px !important;}.m-sm-n4{margin: -24px !important;}.m-sm-n5{margin: -48px !important;}.mx-sm-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-sm-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-sm-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-sm-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-sm-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-sm-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-sm-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-sm-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-sm-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-sm-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-sm-n1{margin-top: -4px !important;}.mt-sm-n2{margin-top: -8px !important;}.mt-sm-n3{margin-top: -16px !important;}.mt-sm-n4{margin-top: -24px !important;}.mt-sm-n5{margin-top: -48px !important;}.me-sm-n1{margin-right: -4px !important;}.me-sm-n2{margin-right: -8px !important;}.me-sm-n3{margin-right: -16px !important;}.me-sm-n4{margin-right: -24px !important;}.me-sm-n5{margin-right: -48px !important;}.mb-sm-n1{margin-bottom: -4px !important;}.mb-sm-n2{margin-bottom: -8px !important;}.mb-sm-n3{margin-bottom: -16px !important;}.mb-sm-n4{margin-bottom: -24px !important;}.mb-sm-n5{margin-bottom: -48px !important;}.ms-sm-n1{margin-left: -4px !important;}.ms-sm-n2{margin-left: -8px !important;}.ms-sm-n3{margin-left: -16px !important;}.ms-sm-n4{margin-left: -24px !important;}.ms-sm-n5{margin-left: -48px !important;}.p-sm-0{padding: 0 !important;}.p-sm-1{padding: 4px !important;}.p-sm-2{padding: 8px !important;}.p-sm-3{padding: 16px !important;}.p-sm-4{padding: 24px !important;}.p-sm-5{padding: 48px !important;}.px-sm-0{padding-right: 0 !important; padding-left: 0 !important;}.px-sm-1{padding-right: 4px !important; padding-left: 4px !important;}.px-sm-2{padding-right: 8px !important; padding-left: 8px !important;}.px-sm-3{padding-right: 16px !important; padding-left: 16px !important;}.px-sm-4{padding-right: 24px !important; padding-left: 24px !important;}.px-sm-5{padding-right: 48px !important; padding-left: 48px !important;}.py-sm-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-sm-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-sm-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-sm-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-sm-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-sm-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-sm-0{padding-top: 0 !important;}.pt-sm-1{padding-top: 4px !important;}.pt-sm-2{padding-top: 8px !important;}.pt-sm-3{padding-top: 16px !important;}.pt-sm-4{padding-top: 24px !important;}.pt-sm-5{padding-top: 48px !important;}.pe-sm-0{padding-right: 0 !important;}.pe-sm-1{padding-right: 4px !important;}.pe-sm-2{padding-right: 8px !important;}.pe-sm-3{padding-right: 16px !important;}.pe-sm-4{padding-right: 24px !important;}.pe-sm-5{padding-right: 48px !important;}.pb-sm-0{padding-bottom: 0 !important;}.pb-sm-1{padding-bottom: 4px !important;}.pb-sm-2{padding-bottom: 8px !important;}.pb-sm-3{padding-bottom: 16px !important;}.pb-sm-4{padding-bottom: 24px !important;}.pb-sm-5{padding-bottom: 48px !important;}.ps-sm-0{padding-left: 0 !important;}.ps-sm-1{padding-left: 4px !important;}.ps-sm-2{padding-left: 8px !important;}.ps-sm-3{padding-left: 16px !important;}.ps-sm-4{padding-left: 24px !important;}.ps-sm-5{padding-left: 48px !important;}.gap-sm-0{gap: 0 !important;}.gap-sm-1{gap: 4px !important;}.gap-sm-2{gap: 8px !important;}.gap-sm-3{gap: 16px !important;}.gap-sm-4{gap: 24px !important;}.gap-sm-5{gap: 48px !important;}.row-gap-sm-0{row-gap: 0 !important;}.row-gap-sm-1{row-gap: 4px !important;}.row-gap-sm-2{row-gap: 8px !important;}.row-gap-sm-3{row-gap: 16px !important;}.row-gap-sm-4{row-gap: 24px !important;}.row-gap-sm-5{row-gap: 48px !important;}.column-gap-sm-0{column-gap: 0 !important;}.column-gap-sm-1{column-gap: 4px !important;}.column-gap-sm-2{column-gap: 8px !important;}.column-gap-sm-3{column-gap: 16px !important;}.column-gap-sm-4{column-gap: 24px !important;}.column-gap-sm-5{column-gap: 48px !important;}.text-sm-start{text-align: left !important;}.text-sm-end{text-align: right !important;}.text-sm-center{text-align: center !important;}.flex-basis-sm-0{flex-basis: 0 !important;}.flex-basis-sm-25{flex-basis: 25% !important;}.flex-basis-sm-50{flex-basis: 50% !important;}.flex-basis-sm-75{flex-basis: 75% !important;}.flex-basis-sm-100{flex-basis: 100% !important;}.flex-basis-sm-auto{flex-basis: auto !important;}}@media (min-width: 768px){.float-md-start{float: left !important;}.float-md-end{float: right !important;}.float-md-none{float: none !important;}.object-fit-md-contain{object-fit: contain !important;}.object-fit-md-cover{object-fit: cover !important;}.object-fit-md-fill{object-fit: fill !important;}.object-fit-md-scale{object-fit: scale-down !important;}.object-fit-md-none{object-fit: none !important;}.d-md-inline{display: inline !important;}.d-md-inline-block{display: inline-block !important;}.d-md-block{display: block !important;}.d-md-grid{display: grid !important;}.d-md-inline-grid{display: inline-grid !important;}.d-md-table{display: table !important;}.d-md-table-row{display: table-row !important;}.d-md-table-cell{display: table-cell !important;}.d-md-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-md-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-md-none{display: none !important;}.d-md-contents{display: contents !important;}.position-md-static{position: static !important;}.position-md-relative{position: relative !important;}.position-md-absolute{position: absolute !important;}.position-md-fixed{position: fixed !important;}.position-md-sticky{position: sticky !important;}.w-md-0{width: 0 !important;}.w-md-25{width: 25% !important;}.w-md-50{width: 50% !important;}.w-md-75{width: 75% !important;}.w-md-100{width: 100% !important;}.w-md-auto{width: auto !important;}.mw-md-0{max-width: 0 !important;}.mw-md-25{max-width: 25% !important;}.mw-md-50{max-width: 50% !important;}.mw-md-75{max-width: 75% !important;}.mw-md-100{max-width: 100% !important;}.mw-md-auto{max-width: auto !important;}.h-md-0{height: 0 !important;}.h-md-25{height: 25% !important;}.h-md-50{height: 50% !important;}.h-md-75{height: 75% !important;}.h-md-100{height: 100% !important;}.h-md-auto{height: auto !important;}.mh-md-0{max-height: 0 !important;}.mh-md-25{max-height: 25% !important;}.mh-md-50{max-height: 50% !important;}.mh-md-75{max-height: 75% !important;}.mh-md-100{max-height: 100% !important;}.mh-md-auto{max-height: auto !important;}.flex-md-fill{flex: 1 1 auto !important;}.flex-md-row{flex-direction: row !important;}.flex-md-column{flex-direction: column !important;}.flex-md-row-reverse{flex-direction: row-reverse !important;}.flex-md-column-reverse{flex-direction: column-reverse !important;}.flex-md-grow-0{flex-grow: 0 !important;}.flex-md-grow-1{flex-grow: 1 !important;}.flex-md-shrink-0{flex-shrink: 0 !important;}.flex-md-shrink-1{flex-shrink: 1 !important;}.flex-md-wrap{flex-wrap: wrap !important;}.flex-md-nowrap{flex-wrap: nowrap !important;}.flex-md-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-md-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-md-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-md-center{justify-content: center !important;}.justify-content-md-between{justify-content: space-between !important;}.justify-content-md-around{justify-content: space-around !important;}.justify-content-md-evenly{justify-content: space-evenly !important;}.align-items-md-start{align-items: flex-start !important;}.align-items-md-end{align-items: flex-end !important;}.align-items-md-center{align-items: center !important;}.align-items-md-baseline{align-items: baseline !important;}.align-items-md-stretch{align-items: stretch !important;}.align-content-md-start{align-content: flex-start !important;}.align-content-md-end{align-content: flex-end !important;}.align-content-md-center{align-content: center !important;}.align-content-md-between{align-content: space-between !important;}.align-content-md-around{align-content: space-around !important;}.align-content-md-stretch{align-content: stretch !important;}.align-self-md-auto{align-self: auto !important;}.align-self-md-start{align-self: flex-start !important;}.align-self-md-end{align-self: flex-end !important;}.align-self-md-center{align-self: center !important;}.align-self-md-baseline{align-self: baseline !important;}.align-self-md-stretch{align-self: stretch !important;}.order-md-first{order: -1 !important;}.order-md-last{order: 13 !important;}.order-md-0{order: 0 !important;}.order-md-1{order: 1 !important;}.order-md-2{order: 2 !important;}.order-md-3{order: 3 !important;}.order-md-4{order: 4 !important;}.order-md-5{order: 5 !important;}.order-md-6{order: 6 !important;}.order-md-7{order: 7 !important;}.order-md-8{order: 8 !important;}.order-md-9{order: 9 !important;}.order-md-10{order: 10 !important;}.order-md-11{order: 11 !important;}.order-md-12{order: 12 !important;}.m-md-0{margin: 0 !important;}.m-md-1{margin: 4px !important;}.m-md-2{margin: 8px !important;}.m-md-3{margin: 16px !important;}.m-md-4{margin: 24px !important;}.m-md-5{margin: 48px !important;}.m-md-auto{margin: auto !important;}.mx-md-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-md-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-md-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-md-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-md-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-md-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-md-auto{margin-right: auto !important; margin-left: auto !important;}.my-md-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-md-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-md-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-md-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-md-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-md-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-md-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-md-0{margin-top: 0 !important;}.mt-md-1{margin-top: 4px !important;}.mt-md-2{margin-top: 8px !important;}.mt-md-3{margin-top: 16px !important;}.mt-md-4{margin-top: 24px !important;}.mt-md-5{margin-top: 48px !important;}.mt-md-auto{margin-top: auto !important;}.me-md-0{margin-right: 0 !important;}.me-md-1{margin-right: 4px !important;}.me-md-2{margin-right: 8px !important;}.me-md-3{margin-right: 16px !important;}.me-md-4{margin-right: 24px !important;}.me-md-5{margin-right: 48px !important;}.me-md-auto{margin-right: auto !important;}.mb-md-0{margin-bottom: 0 !important;}.mb-md-1{margin-bottom: 4px !important;}.mb-md-2{margin-bottom: 8px !important;}.mb-md-3{margin-bottom: 16px !important;}.mb-md-4{margin-bottom: 24px !important;}.mb-md-5{margin-bottom: 48px !important;}.mb-md-auto{margin-bottom: auto !important;}.ms-md-0{margin-left: 0 !important;}.ms-md-1{margin-left: 4px !important;}.ms-md-2{margin-left: 8px !important;}.ms-md-3{margin-left: 16px !important;}.ms-md-4{margin-left: 24px !important;}.ms-md-5{margin-left: 48px !important;}.ms-md-auto{margin-left: auto !important;}.m-md-n1{margin: -4px !important;}.m-md-n2{margin: -8px !important;}.m-md-n3{margin: -16px !important;}.m-md-n4{margin: -24px !important;}.m-md-n5{margin: -48px !important;}.mx-md-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-md-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-md-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-md-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-md-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-md-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-md-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-md-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-md-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-md-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-md-n1{margin-top: -4px !important;}.mt-md-n2{margin-top: -8px !important;}.mt-md-n3{margin-top: -16px !important;}.mt-md-n4{margin-top: -24px !important;}.mt-md-n5{margin-top: -48px !important;}.me-md-n1{margin-right: -4px !important;}.me-md-n2{margin-right: -8px !important;}.me-md-n3{margin-right: -16px !important;}.me-md-n4{margin-right: -24px !important;}.me-md-n5{margin-right: -48px !important;}.mb-md-n1{margin-bottom: -4px !important;}.mb-md-n2{margin-bottom: -8px !important;}.mb-md-n3{margin-bottom: -16px !important;}.mb-md-n4{margin-bottom: -24px !important;}.mb-md-n5{margin-bottom: -48px !important;}.ms-md-n1{margin-left: -4px !important;}.ms-md-n2{margin-left: -8px !important;}.ms-md-n3{margin-left: -16px !important;}.ms-md-n4{margin-left: -24px !important;}.ms-md-n5{margin-left: -48px !important;}.p-md-0{padding: 0 !important;}.p-md-1{padding: 4px !important;}.p-md-2{padding: 8px !important;}.p-md-3{padding: 16px !important;}.p-md-4{padding: 24px !important;}.p-md-5{padding: 48px !important;}.px-md-0{padding-right: 0 !important; padding-left: 0 !important;}.px-md-1{padding-right: 4px !important; padding-left: 4px !important;}.px-md-2{padding-right: 8px !important; padding-left: 8px !important;}.px-md-3{padding-right: 16px !important; padding-left: 16px !important;}.px-md-4{padding-right: 24px !important; padding-left: 24px !important;}.px-md-5{padding-right: 48px !important; padding-left: 48px !important;}.py-md-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-md-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-md-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-md-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-md-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-md-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-md-0{padding-top: 0 !important;}.pt-md-1{padding-top: 4px !important;}.pt-md-2{padding-top: 8px !important;}.pt-md-3{padding-top: 16px !important;}.pt-md-4{padding-top: 24px !important;}.pt-md-5{padding-top: 48px !important;}.pe-md-0{padding-right: 0 !important;}.pe-md-1{padding-right: 4px !important;}.pe-md-2{padding-right: 8px !important;}.pe-md-3{padding-right: 16px !important;}.pe-md-4{padding-right: 24px !important;}.pe-md-5{padding-right: 48px !important;}.pb-md-0{padding-bottom: 0 !important;}.pb-md-1{padding-bottom: 4px !important;}.pb-md-2{padding-bottom: 8px !important;}.pb-md-3{padding-bottom: 16px !important;}.pb-md-4{padding-bottom: 24px !important;}.pb-md-5{padding-bottom: 48px !important;}.ps-md-0{padding-left: 0 !important;}.ps-md-1{padding-left: 4px !important;}.ps-md-2{padding-left: 8px !important;}.ps-md-3{padding-left: 16px !important;}.ps-md-4{padding-left: 24px !important;}.ps-md-5{padding-left: 48px !important;}.gap-md-0{gap: 0 !important;}.gap-md-1{gap: 4px !important;}.gap-md-2{gap: 8px !important;}.gap-md-3{gap: 16px !important;}.gap-md-4{gap: 24px !important;}.gap-md-5{gap: 48px !important;}.row-gap-md-0{row-gap: 0 !important;}.row-gap-md-1{row-gap: 4px !important;}.row-gap-md-2{row-gap: 8px !important;}.row-gap-md-3{row-gap: 16px !important;}.row-gap-md-4{row-gap: 24px !important;}.row-gap-md-5{row-gap: 48px !important;}.column-gap-md-0{column-gap: 0 !important;}.column-gap-md-1{column-gap: 4px !important;}.column-gap-md-2{column-gap: 8px !important;}.column-gap-md-3{column-gap: 16px !important;}.column-gap-md-4{column-gap: 24px !important;}.column-gap-md-5{column-gap: 48px !important;}.text-md-start{text-align: left !important;}.text-md-end{text-align: right !important;}.text-md-center{text-align: center !important;}.flex-basis-md-0{flex-basis: 0 !important;}.flex-basis-md-25{flex-basis: 25% !important;}.flex-basis-md-50{flex-basis: 50% !important;}.flex-basis-md-75{flex-basis: 75% !important;}.flex-basis-md-100{flex-basis: 100% !important;}.flex-basis-md-auto{flex-basis: auto !important;}}@media (min-width: 992px){.float-lg-start{float: left !important;}.float-lg-end{float: right !important;}.float-lg-none{float: none !important;}.object-fit-lg-contain{object-fit: contain !important;}.object-fit-lg-cover{object-fit: cover !important;}.object-fit-lg-fill{object-fit: fill !important;}.object-fit-lg-scale{object-fit: scale-down !important;}.object-fit-lg-none{object-fit: none !important;}.d-lg-inline{display: inline !important;}.d-lg-inline-block{display: inline-block !important;}.d-lg-block{display: block !important;}.d-lg-grid{display: grid !important;}.d-lg-inline-grid{display: inline-grid !important;}.d-lg-table{display: table !important;}.d-lg-table-row{display: table-row !important;}.d-lg-table-cell{display: table-cell !important;}.d-lg-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-lg-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-lg-none{display: none !important;}.d-lg-contents{display: contents !important;}.position-lg-static{position: static !important;}.position-lg-relative{position: relative !important;}.position-lg-absolute{position: absolute !important;}.position-lg-fixed{position: fixed !important;}.position-lg-sticky{position: sticky !important;}.w-lg-0{width: 0 !important;}.w-lg-25{width: 25% !important;}.w-lg-50{width: 50% !important;}.w-lg-75{width: 75% !important;}.w-lg-100{width: 100% !important;}.w-lg-auto{width: auto !important;}.mw-lg-0{max-width: 0 !important;}.mw-lg-25{max-width: 25% !important;}.mw-lg-50{max-width: 50% !important;}.mw-lg-75{max-width: 75% !important;}.mw-lg-100{max-width: 100% !important;}.mw-lg-auto{max-width: auto !important;}.h-lg-0{height: 0 !important;}.h-lg-25{height: 25% !important;}.h-lg-50{height: 50% !important;}.h-lg-75{height: 75% !important;}.h-lg-100{height: 100% !important;}.h-lg-auto{height: auto !important;}.mh-lg-0{max-height: 0 !important;}.mh-lg-25{max-height: 25% !important;}.mh-lg-50{max-height: 50% !important;}.mh-lg-75{max-height: 75% !important;}.mh-lg-100{max-height: 100% !important;}.mh-lg-auto{max-height: auto !important;}.flex-lg-fill{flex: 1 1 auto !important;}.flex-lg-row{flex-direction: row !important;}.flex-lg-column{flex-direction: column !important;}.flex-lg-row-reverse{flex-direction: row-reverse !important;}.flex-lg-column-reverse{flex-direction: column-reverse !important;}.flex-lg-grow-0{flex-grow: 0 !important;}.flex-lg-grow-1{flex-grow: 1 !important;}.flex-lg-shrink-0{flex-shrink: 0 !important;}.flex-lg-shrink-1{flex-shrink: 1 !important;}.flex-lg-wrap{flex-wrap: wrap !important;}.flex-lg-nowrap{flex-wrap: nowrap !important;}.flex-lg-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-lg-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-lg-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-lg-center{justify-content: center !important;}.justify-content-lg-between{justify-content: space-between !important;}.justify-content-lg-around{justify-content: space-around !important;}.justify-content-lg-evenly{justify-content: space-evenly !important;}.align-items-lg-start{align-items: flex-start !important;}.align-items-lg-end{align-items: flex-end !important;}.align-items-lg-center{align-items: center !important;}.align-items-lg-baseline{align-items: baseline !important;}.align-items-lg-stretch{align-items: stretch !important;}.align-content-lg-start{align-content: flex-start !important;}.align-content-lg-end{align-content: flex-end !important;}.align-content-lg-center{align-content: center !important;}.align-content-lg-between{align-content: space-between !important;}.align-content-lg-around{align-content: space-around !important;}.align-content-lg-stretch{align-content: stretch !important;}.align-self-lg-auto{align-self: auto !important;}.align-self-lg-start{align-self: flex-start !important;}.align-self-lg-end{align-self: flex-end !important;}.align-self-lg-center{align-self: center !important;}.align-self-lg-baseline{align-self: baseline !important;}.align-self-lg-stretch{align-self: stretch !important;}.order-lg-first{order: -1 !important;}.order-lg-last{order: 13 !important;}.order-lg-0{order: 0 !important;}.order-lg-1{order: 1 !important;}.order-lg-2{order: 2 !important;}.order-lg-3{order: 3 !important;}.order-lg-4{order: 4 !important;}.order-lg-5{order: 5 !important;}.order-lg-6{order: 6 !important;}.order-lg-7{order: 7 !important;}.order-lg-8{order: 8 !important;}.order-lg-9{order: 9 !important;}.order-lg-10{order: 10 !important;}.order-lg-11{order: 11 !important;}.order-lg-12{order: 12 !important;}.m-lg-0{margin: 0 !important;}.m-lg-1{margin: 4px !important;}.m-lg-2{margin: 8px !important;}.m-lg-3{margin: 16px !important;}.m-lg-4{margin: 24px !important;}.m-lg-5{margin: 48px !important;}.m-lg-auto{margin: auto !important;}.mx-lg-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-lg-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-lg-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-lg-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-lg-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-lg-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-lg-auto{margin-right: auto !important; margin-left: auto !important;}.my-lg-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-lg-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-lg-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-lg-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-lg-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-lg-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-lg-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-lg-0{margin-top: 0 !important;}.mt-lg-1{margin-top: 4px !important;}.mt-lg-2{margin-top: 8px !important;}.mt-lg-3{margin-top: 16px !important;}.mt-lg-4{margin-top: 24px !important;}.mt-lg-5{margin-top: 48px !important;}.mt-lg-auto{margin-top: auto !important;}.me-lg-0{margin-right: 0 !important;}.me-lg-1{margin-right: 4px !important;}.me-lg-2{margin-right: 8px !important;}.me-lg-3{margin-right: 16px !important;}.me-lg-4{margin-right: 24px !important;}.me-lg-5{margin-right: 48px !important;}.me-lg-auto{margin-right: auto !important;}.mb-lg-0{margin-bottom: 0 !important;}.mb-lg-1{margin-bottom: 4px !important;}.mb-lg-2{margin-bottom: 8px !important;}.mb-lg-3{margin-bottom: 16px !important;}.mb-lg-4{margin-bottom: 24px !important;}.mb-lg-5{margin-bottom: 48px !important;}.mb-lg-auto{margin-bottom: auto !important;}.ms-lg-0{margin-left: 0 !important;}.ms-lg-1{margin-left: 4px !important;}.ms-lg-2{margin-left: 8px !important;}.ms-lg-3{margin-left: 16px !important;}.ms-lg-4{margin-left: 24px !important;}.ms-lg-5{margin-left: 48px !important;}.ms-lg-auto{margin-left: auto !important;}.m-lg-n1{margin: -4px !important;}.m-lg-n2{margin: -8px !important;}.m-lg-n3{margin: -16px !important;}.m-lg-n4{margin: -24px !important;}.m-lg-n5{margin: -48px !important;}.mx-lg-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-lg-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-lg-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-lg-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-lg-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-lg-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-lg-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-lg-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-lg-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-lg-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-lg-n1{margin-top: -4px !important;}.mt-lg-n2{margin-top: -8px !important;}.mt-lg-n3{margin-top: -16px !important;}.mt-lg-n4{margin-top: -24px !important;}.mt-lg-n5{margin-top: -48px !important;}.me-lg-n1{margin-right: -4px !important;}.me-lg-n2{margin-right: -8px !important;}.me-lg-n3{margin-right: -16px !important;}.me-lg-n4{margin-right: -24px !important;}.me-lg-n5{margin-right: -48px !important;}.mb-lg-n1{margin-bottom: -4px !important;}.mb-lg-n2{margin-bottom: -8px !important;}.mb-lg-n3{margin-bottom: -16px !important;}.mb-lg-n4{margin-bottom: -24px !important;}.mb-lg-n5{margin-bottom: -48px !important;}.ms-lg-n1{margin-left: -4px !important;}.ms-lg-n2{margin-left: -8px !important;}.ms-lg-n3{margin-left: -16px !important;}.ms-lg-n4{margin-left: -24px !important;}.ms-lg-n5{margin-left: -48px !important;}.p-lg-0{padding: 0 !important;}.p-lg-1{padding: 4px !important;}.p-lg-2{padding: 8px !important;}.p-lg-3{padding: 16px !important;}.p-lg-4{padding: 24px !important;}.p-lg-5{padding: 48px !important;}.px-lg-0{padding-right: 0 !important; padding-left: 0 !important;}.px-lg-1{padding-right: 4px !important; padding-left: 4px !important;}.px-lg-2{padding-right: 8px !important; padding-left: 8px !important;}.px-lg-3{padding-right: 16px !important; padding-left: 16px !important;}.px-lg-4{padding-right: 24px !important; padding-left: 24px !important;}.px-lg-5{padding-right: 48px !important; padding-left: 48px !important;}.py-lg-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-lg-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-lg-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-lg-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-lg-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-lg-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-lg-0{padding-top: 0 !important;}.pt-lg-1{padding-top: 4px !important;}.pt-lg-2{padding-top: 8px !important;}.pt-lg-3{padding-top: 16px !important;}.pt-lg-4{padding-top: 24px !important;}.pt-lg-5{padding-top: 48px !important;}.pe-lg-0{padding-right: 0 !important;}.pe-lg-1{padding-right: 4px !important;}.pe-lg-2{padding-right: 8px !important;}.pe-lg-3{padding-right: 16px !important;}.pe-lg-4{padding-right: 24px !important;}.pe-lg-5{padding-right: 48px !important;}.pb-lg-0{padding-bottom: 0 !important;}.pb-lg-1{padding-bottom: 4px !important;}.pb-lg-2{padding-bottom: 8px !important;}.pb-lg-3{padding-bottom: 16px !important;}.pb-lg-4{padding-bottom: 24px !important;}.pb-lg-5{padding-bottom: 48px !important;}.ps-lg-0{padding-left: 0 !important;}.ps-lg-1{padding-left: 4px !important;}.ps-lg-2{padding-left: 8px !important;}.ps-lg-3{padding-left: 16px !important;}.ps-lg-4{padding-left: 24px !important;}.ps-lg-5{padding-left: 48px !important;}.gap-lg-0{gap: 0 !important;}.gap-lg-1{gap: 4px !important;}.gap-lg-2{gap: 8px !important;}.gap-lg-3{gap: 16px !important;}.gap-lg-4{gap: 24px !important;}.gap-lg-5{gap: 48px !important;}.row-gap-lg-0{row-gap: 0 !important;}.row-gap-lg-1{row-gap: 4px !important;}.row-gap-lg-2{row-gap: 8px !important;}.row-gap-lg-3{row-gap: 16px !important;}.row-gap-lg-4{row-gap: 24px !important;}.row-gap-lg-5{row-gap: 48px !important;}.column-gap-lg-0{column-gap: 0 !important;}.column-gap-lg-1{column-gap: 4px !important;}.column-gap-lg-2{column-gap: 8px !important;}.column-gap-lg-3{column-gap: 16px !important;}.column-gap-lg-4{column-gap: 24px !important;}.column-gap-lg-5{column-gap: 48px !important;}.text-lg-start{text-align: left !important;}.text-lg-end{text-align: right !important;}.text-lg-center{text-align: center !important;}.flex-basis-lg-0{flex-basis: 0 !important;}.flex-basis-lg-25{flex-basis: 25% !important;}.flex-basis-lg-50{flex-basis: 50% !important;}.flex-basis-lg-75{flex-basis: 75% !important;}.flex-basis-lg-100{flex-basis: 100% !important;}.flex-basis-lg-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.float-xl-start{float: left !important;}.float-xl-end{float: right !important;}.float-xl-none{float: none !important;}.object-fit-xl-contain{object-fit: contain !important;}.object-fit-xl-cover{object-fit: cover !important;}.object-fit-xl-fill{object-fit: fill !important;}.object-fit-xl-scale{object-fit: scale-down !important;}.object-fit-xl-none{object-fit: none !important;}.d-xl-inline{display: inline !important;}.d-xl-inline-block{display: inline-block !important;}.d-xl-block{display: block !important;}.d-xl-grid{display: grid !important;}.d-xl-inline-grid{display: inline-grid !important;}.d-xl-table{display: table !important;}.d-xl-table-row{display: table-row !important;}.d-xl-table-cell{display: table-cell !important;}.d-xl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xl-none{display: none !important;}.d-xl-contents{display: contents !important;}.position-xl-static{position: static !important;}.position-xl-relative{position: relative !important;}.position-xl-absolute{position: absolute !important;}.position-xl-fixed{position: fixed !important;}.position-xl-sticky{position: sticky !important;}.w-xl-0{width: 0 !important;}.w-xl-25{width: 25% !important;}.w-xl-50{width: 50% !important;}.w-xl-75{width: 75% !important;}.w-xl-100{width: 100% !important;}.w-xl-auto{width: auto !important;}.mw-xl-0{max-width: 0 !important;}.mw-xl-25{max-width: 25% !important;}.mw-xl-50{max-width: 50% !important;}.mw-xl-75{max-width: 75% !important;}.mw-xl-100{max-width: 100% !important;}.mw-xl-auto{max-width: auto !important;}.h-xl-0{height: 0 !important;}.h-xl-25{height: 25% !important;}.h-xl-50{height: 50% !important;}.h-xl-75{height: 75% !important;}.h-xl-100{height: 100% !important;}.h-xl-auto{height: auto !important;}.mh-xl-0{max-height: 0 !important;}.mh-xl-25{max-height: 25% !important;}.mh-xl-50{max-height: 50% !important;}.mh-xl-75{max-height: 75% !important;}.mh-xl-100{max-height: 100% !important;}.mh-xl-auto{max-height: auto !important;}.flex-xl-fill{flex: 1 1 auto !important;}.flex-xl-row{flex-direction: row !important;}.flex-xl-column{flex-direction: column !important;}.flex-xl-row-reverse{flex-direction: row-reverse !important;}.flex-xl-column-reverse{flex-direction: column-reverse !important;}.flex-xl-grow-0{flex-grow: 0 !important;}.flex-xl-grow-1{flex-grow: 1 !important;}.flex-xl-shrink-0{flex-shrink: 0 !important;}.flex-xl-shrink-1{flex-shrink: 1 !important;}.flex-xl-wrap{flex-wrap: wrap !important;}.flex-xl-nowrap{flex-wrap: nowrap !important;}.flex-xl-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-xl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xl-center{justify-content: center !important;}.justify-content-xl-between{justify-content: space-between !important;}.justify-content-xl-around{justify-content: space-around !important;}.justify-content-xl-evenly{justify-content: space-evenly !important;}.align-items-xl-start{align-items: flex-start !important;}.align-items-xl-end{align-items: flex-end !important;}.align-items-xl-center{align-items: center !important;}.align-items-xl-baseline{align-items: baseline !important;}.align-items-xl-stretch{align-items: stretch !important;}.align-content-xl-start{align-content: flex-start !important;}.align-content-xl-end{align-content: flex-end !important;}.align-content-xl-center{align-content: center !important;}.align-content-xl-between{align-content: space-between !important;}.align-content-xl-around{align-content: space-around !important;}.align-content-xl-stretch{align-content: stretch !important;}.align-self-xl-auto{align-self: auto !important;}.align-self-xl-start{align-self: flex-start !important;}.align-self-xl-end{align-self: flex-end !important;}.align-self-xl-center{align-self: center !important;}.align-self-xl-baseline{align-self: baseline !important;}.align-self-xl-stretch{align-self: stretch !important;}.order-xl-first{order: -1 !important;}.order-xl-last{order: 13 !important;}.order-xl-0{order: 0 !important;}.order-xl-1{order: 1 !important;}.order-xl-2{order: 2 !important;}.order-xl-3{order: 3 !important;}.order-xl-4{order: 4 !important;}.order-xl-5{order: 5 !important;}.order-xl-6{order: 6 !important;}.order-xl-7{order: 7 !important;}.order-xl-8{order: 8 !important;}.order-xl-9{order: 9 !important;}.order-xl-10{order: 10 !important;}.order-xl-11{order: 11 !important;}.order-xl-12{order: 12 !important;}.m-xl-0{margin: 0 !important;}.m-xl-1{margin: 4px !important;}.m-xl-2{margin: 8px !important;}.m-xl-3{margin: 16px !important;}.m-xl-4{margin: 24px !important;}.m-xl-5{margin: 48px !important;}.m-xl-auto{margin: auto !important;}.mx-xl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xl-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-xl-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-xl-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-xl-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-xl-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-xl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xl-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-xl-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-xl-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-xl-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-xl-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-xl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xl-0{margin-top: 0 !important;}.mt-xl-1{margin-top: 4px !important;}.mt-xl-2{margin-top: 8px !important;}.mt-xl-3{margin-top: 16px !important;}.mt-xl-4{margin-top: 24px !important;}.mt-xl-5{margin-top: 48px !important;}.mt-xl-auto{margin-top: auto !important;}.me-xl-0{margin-right: 0 !important;}.me-xl-1{margin-right: 4px !important;}.me-xl-2{margin-right: 8px !important;}.me-xl-3{margin-right: 16px !important;}.me-xl-4{margin-right: 24px !important;}.me-xl-5{margin-right: 48px !important;}.me-xl-auto{margin-right: auto !important;}.mb-xl-0{margin-bottom: 0 !important;}.mb-xl-1{margin-bottom: 4px !important;}.mb-xl-2{margin-bottom: 8px !important;}.mb-xl-3{margin-bottom: 16px !important;}.mb-xl-4{margin-bottom: 24px !important;}.mb-xl-5{margin-bottom: 48px !important;}.mb-xl-auto{margin-bottom: auto !important;}.ms-xl-0{margin-left: 0 !important;}.ms-xl-1{margin-left: 4px !important;}.ms-xl-2{margin-left: 8px !important;}.ms-xl-3{margin-left: 16px !important;}.ms-xl-4{margin-left: 24px !important;}.ms-xl-5{margin-left: 48px !important;}.ms-xl-auto{margin-left: auto !important;}.m-xl-n1{margin: -4px !important;}.m-xl-n2{margin: -8px !important;}.m-xl-n3{margin: -16px !important;}.m-xl-n4{margin: -24px !important;}.m-xl-n5{margin: -48px !important;}.mx-xl-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-xl-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-xl-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-xl-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-xl-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-xl-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-xl-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-xl-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-xl-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-xl-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-xl-n1{margin-top: -4px !important;}.mt-xl-n2{margin-top: -8px !important;}.mt-xl-n3{margin-top: -16px !important;}.mt-xl-n4{margin-top: -24px !important;}.mt-xl-n5{margin-top: -48px !important;}.me-xl-n1{margin-right: -4px !important;}.me-xl-n2{margin-right: -8px !important;}.me-xl-n3{margin-right: -16px !important;}.me-xl-n4{margin-right: -24px !important;}.me-xl-n5{margin-right: -48px !important;}.mb-xl-n1{margin-bottom: -4px !important;}.mb-xl-n2{margin-bottom: -8px !important;}.mb-xl-n3{margin-bottom: -16px !important;}.mb-xl-n4{margin-bottom: -24px !important;}.mb-xl-n5{margin-bottom: -48px !important;}.ms-xl-n1{margin-left: -4px !important;}.ms-xl-n2{margin-left: -8px !important;}.ms-xl-n3{margin-left: -16px !important;}.ms-xl-n4{margin-left: -24px !important;}.ms-xl-n5{margin-left: -48px !important;}.p-xl-0{padding: 0 !important;}.p-xl-1{padding: 4px !important;}.p-xl-2{padding: 8px !important;}.p-xl-3{padding: 16px !important;}.p-xl-4{padding: 24px !important;}.p-xl-5{padding: 48px !important;}.px-xl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xl-1{padding-right: 4px !important; padding-left: 4px !important;}.px-xl-2{padding-right: 8px !important; padding-left: 8px !important;}.px-xl-3{padding-right: 16px !important; padding-left: 16px !important;}.px-xl-4{padding-right: 24px !important; padding-left: 24px !important;}.px-xl-5{padding-right: 48px !important; padding-left: 48px !important;}.py-xl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xl-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-xl-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-xl-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-xl-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-xl-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-xl-0{padding-top: 0 !important;}.pt-xl-1{padding-top: 4px !important;}.pt-xl-2{padding-top: 8px !important;}.pt-xl-3{padding-top: 16px !important;}.pt-xl-4{padding-top: 24px !important;}.pt-xl-5{padding-top: 48px !important;}.pe-xl-0{padding-right: 0 !important;}.pe-xl-1{padding-right: 4px !important;}.pe-xl-2{padding-right: 8px !important;}.pe-xl-3{padding-right: 16px !important;}.pe-xl-4{padding-right: 24px !important;}.pe-xl-5{padding-right: 48px !important;}.pb-xl-0{padding-bottom: 0 !important;}.pb-xl-1{padding-bottom: 4px !important;}.pb-xl-2{padding-bottom: 8px !important;}.pb-xl-3{padding-bottom: 16px !important;}.pb-xl-4{padding-bottom: 24px !important;}.pb-xl-5{padding-bottom: 48px !important;}.ps-xl-0{padding-left: 0 !important;}.ps-xl-1{padding-left: 4px !important;}.ps-xl-2{padding-left: 8px !important;}.ps-xl-3{padding-left: 16px !important;}.ps-xl-4{padding-left: 24px !important;}.ps-xl-5{padding-left: 48px !important;}.gap-xl-0{gap: 0 !important;}.gap-xl-1{gap: 4px !important;}.gap-xl-2{gap: 8px !important;}.gap-xl-3{gap: 16px !important;}.gap-xl-4{gap: 24px !important;}.gap-xl-5{gap: 48px !important;}.row-gap-xl-0{row-gap: 0 !important;}.row-gap-xl-1{row-gap: 4px !important;}.row-gap-xl-2{row-gap: 8px !important;}.row-gap-xl-3{row-gap: 16px !important;}.row-gap-xl-4{row-gap: 24px !important;}.row-gap-xl-5{row-gap: 48px !important;}.column-gap-xl-0{column-gap: 0 !important;}.column-gap-xl-1{column-gap: 4px !important;}.column-gap-xl-2{column-gap: 8px !important;}.column-gap-xl-3{column-gap: 16px !important;}.column-gap-xl-4{column-gap: 24px !important;}.column-gap-xl-5{column-gap: 48px !important;}.text-xl-start{text-align: left !important;}.text-xl-end{text-align: right !important;}.text-xl-center{text-align: center !important;}.flex-basis-xl-0{flex-basis: 0 !important;}.flex-basis-xl-25{flex-basis: 25% !important;}.flex-basis-xl-50{flex-basis: 50% !important;}.flex-basis-xl-75{flex-basis: 75% !important;}.flex-basis-xl-100{flex-basis: 100% !important;}.flex-basis-xl-auto{flex-basis: auto !important;}}@media (min-width: 1400px){.float-xxl-start{float: left !important;}.float-xxl-end{float: right !important;}.float-xxl-none{float: none !important;}.object-fit-xxl-contain{object-fit: contain !important;}.object-fit-xxl-cover{object-fit: cover !important;}.object-fit-xxl-fill{object-fit: fill !important;}.object-fit-xxl-scale{object-fit: scale-down !important;}.object-fit-xxl-none{object-fit: none !important;}.d-xxl-inline{display: inline !important;}.d-xxl-inline-block{display: inline-block !important;}.d-xxl-block{display: block !important;}.d-xxl-grid{display: grid !important;}.d-xxl-inline-grid{display: inline-grid !important;}.d-xxl-table{display: table !important;}.d-xxl-table-row{display: table-row !important;}.d-xxl-table-cell{display: table-cell !important;}.d-xxl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xxl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xxl-none{display: none !important;}.d-xxl-contents{display: contents !important;}.position-xxl-static{position: static !important;}.position-xxl-relative{position: relative !important;}.position-xxl-absolute{position: absolute !important;}.position-xxl-fixed{position: fixed !important;}.position-xxl-sticky{position: sticky !important;}.w-xxl-0{width: 0 !important;}.w-xxl-25{width: 25% !important;}.w-xxl-50{width: 50% !important;}.w-xxl-75{width: 75% !important;}.w-xxl-100{width: 100% !important;}.w-xxl-auto{width: auto !important;}.mw-xxl-0{max-width: 0 !important;}.mw-xxl-25{max-width: 25% !important;}.mw-xxl-50{max-width: 50% !important;}.mw-xxl-75{max-width: 75% !important;}.mw-xxl-100{max-width: 100% !important;}.mw-xxl-auto{max-width: auto !important;}.h-xxl-0{height: 0 !important;}.h-xxl-25{height: 25% !important;}.h-xxl-50{height: 50% !important;}.h-xxl-75{height: 75% !important;}.h-xxl-100{height: 100% !important;}.h-xxl-auto{height: auto !important;}.mh-xxl-0{max-height: 0 !important;}.mh-xxl-25{max-height: 25% !important;}.mh-xxl-50{max-height: 50% !important;}.mh-xxl-75{max-height: 75% !important;}.mh-xxl-100{max-height: 100% !important;}.mh-xxl-auto{max-height: auto !important;}.flex-xxl-fill{flex: 1 1 auto !important;}.flex-xxl-row{flex-direction: row !important;}.flex-xxl-column{flex-direction: column !important;}.flex-xxl-row-reverse{flex-direction: row-reverse !important;}.flex-xxl-column-reverse{flex-direction: column-reverse !important;}.flex-xxl-grow-0{flex-grow: 0 !important;}.flex-xxl-grow-1{flex-grow: 1 !important;}.flex-xxl-shrink-0{flex-shrink: 0 !important;}.flex-xxl-shrink-1{flex-shrink: 1 !important;}.flex-xxl-wrap{flex-wrap: wrap !important;}.flex-xxl-nowrap{flex-wrap: nowrap !important;}.flex-xxl-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-xxl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xxl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xxl-center{justify-content: center !important;}.justify-content-xxl-between{justify-content: space-between !important;}.justify-content-xxl-around{justify-content: space-around !important;}.justify-content-xxl-evenly{justify-content: space-evenly !important;}.align-items-xxl-start{align-items: flex-start !important;}.align-items-xxl-end{align-items: flex-end !important;}.align-items-xxl-center{align-items: center !important;}.align-items-xxl-baseline{align-items: baseline !important;}.align-items-xxl-stretch{align-items: stretch !important;}.align-content-xxl-start{align-content: flex-start !important;}.align-content-xxl-end{align-content: flex-end !important;}.align-content-xxl-center{align-content: center !important;}.align-content-xxl-between{align-content: space-between !important;}.align-content-xxl-around{align-content: space-around !important;}.align-content-xxl-stretch{align-content: stretch !important;}.align-self-xxl-auto{align-self: auto !important;}.align-self-xxl-start{align-self: flex-start !important;}.align-self-xxl-end{align-self: flex-end !important;}.align-self-xxl-center{align-self: center !important;}.align-self-xxl-baseline{align-self: baseline !important;}.align-self-xxl-stretch{align-self: stretch !important;}.order-xxl-first{order: -1 !important;}.order-xxl-last{order: 13 !important;}.order-xxl-0{order: 0 !important;}.order-xxl-1{order: 1 !important;}.order-xxl-2{order: 2 !important;}.order-xxl-3{order: 3 !important;}.order-xxl-4{order: 4 !important;}.order-xxl-5{order: 5 !important;}.order-xxl-6{order: 6 !important;}.order-xxl-7{order: 7 !important;}.order-xxl-8{order: 8 !important;}.order-xxl-9{order: 9 !important;}.order-xxl-10{order: 10 !important;}.order-xxl-11{order: 11 !important;}.order-xxl-12{order: 12 !important;}.m-xxl-0{margin: 0 !important;}.m-xxl-1{margin: 4px !important;}.m-xxl-2{margin: 8px !important;}.m-xxl-3{margin: 16px !important;}.m-xxl-4{margin: 24px !important;}.m-xxl-5{margin: 48px !important;}.m-xxl-auto{margin: auto !important;}.mx-xxl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xxl-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-xxl-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-xxl-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-xxl-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-xxl-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-xxl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xxl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xxl-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-xxl-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-xxl-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-xxl-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-xxl-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-xxl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xxl-0{margin-top: 0 !important;}.mt-xxl-1{margin-top: 4px !important;}.mt-xxl-2{margin-top: 8px !important;}.mt-xxl-3{margin-top: 16px !important;}.mt-xxl-4{margin-top: 24px !important;}.mt-xxl-5{margin-top: 48px !important;}.mt-xxl-auto{margin-top: auto !important;}.me-xxl-0{margin-right: 0 !important;}.me-xxl-1{margin-right: 4px !important;}.me-xxl-2{margin-right: 8px !important;}.me-xxl-3{margin-right: 16px !important;}.me-xxl-4{margin-right: 24px !important;}.me-xxl-5{margin-right: 48px !important;}.me-xxl-auto{margin-right: auto !important;}.mb-xxl-0{margin-bottom: 0 !important;}.mb-xxl-1{margin-bottom: 4px !important;}.mb-xxl-2{margin-bottom: 8px !important;}.mb-xxl-3{margin-bottom: 16px !important;}.mb-xxl-4{margin-bottom: 24px !important;}.mb-xxl-5{margin-bottom: 48px !important;}.mb-xxl-auto{margin-bottom: auto !important;}.ms-xxl-0{margin-left: 0 !important;}.ms-xxl-1{margin-left: 4px !important;}.ms-xxl-2{margin-left: 8px !important;}.ms-xxl-3{margin-left: 16px !important;}.ms-xxl-4{margin-left: 24px !important;}.ms-xxl-5{margin-left: 48px !important;}.ms-xxl-auto{margin-left: auto !important;}.m-xxl-n1{margin: -4px !important;}.m-xxl-n2{margin: -8px !important;}.m-xxl-n3{margin: -16px !important;}.m-xxl-n4{margin: -24px !important;}.m-xxl-n5{margin: -48px !important;}.mx-xxl-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-xxl-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-xxl-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-xxl-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-xxl-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-xxl-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-xxl-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-xxl-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-xxl-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-xxl-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-xxl-n1{margin-top: -4px !important;}.mt-xxl-n2{margin-top: -8px !important;}.mt-xxl-n3{margin-top: -16px !important;}.mt-xxl-n4{margin-top: -24px !important;}.mt-xxl-n5{margin-top: -48px !important;}.me-xxl-n1{margin-right: -4px !important;}.me-xxl-n2{margin-right: -8px !important;}.me-xxl-n3{margin-right: -16px !important;}.me-xxl-n4{margin-right: -24px !important;}.me-xxl-n5{margin-right: -48px !important;}.mb-xxl-n1{margin-bottom: -4px !important;}.mb-xxl-n2{margin-bottom: -8px !important;}.mb-xxl-n3{margin-bottom: -16px !important;}.mb-xxl-n4{margin-bottom: -24px !important;}.mb-xxl-n5{margin-bottom: -48px !important;}.ms-xxl-n1{margin-left: -4px !important;}.ms-xxl-n2{margin-left: -8px !important;}.ms-xxl-n3{margin-left: -16px !important;}.ms-xxl-n4{margin-left: -24px !important;}.ms-xxl-n5{margin-left: -48px !important;}.p-xxl-0{padding: 0 !important;}.p-xxl-1{padding: 4px !important;}.p-xxl-2{padding: 8px !important;}.p-xxl-3{padding: 16px !important;}.p-xxl-4{padding: 24px !important;}.p-xxl-5{padding: 48px !important;}.px-xxl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xxl-1{padding-right: 4px !important; padding-left: 4px !important;}.px-xxl-2{padding-right: 8px !important; padding-left: 8px !important;}.px-xxl-3{padding-right: 16px !important; padding-left: 16px !important;}.px-xxl-4{padding-right: 24px !important; padding-left: 24px !important;}.px-xxl-5{padding-right: 48px !important; padding-left: 48px !important;}.py-xxl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xxl-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-xxl-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-xxl-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-xxl-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-xxl-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-xxl-0{padding-top: 0 !important;}.pt-xxl-1{padding-top: 4px !important;}.pt-xxl-2{padding-top: 8px !important;}.pt-xxl-3{padding-top: 16px !important;}.pt-xxl-4{padding-top: 24px !important;}.pt-xxl-5{padding-top: 48px !important;}.pe-xxl-0{padding-right: 0 !important;}.pe-xxl-1{padding-right: 4px !important;}.pe-xxl-2{padding-right: 8px !important;}.pe-xxl-3{padding-right: 16px !important;}.pe-xxl-4{padding-right: 24px !important;}.pe-xxl-5{padding-right: 48px !important;}.pb-xxl-0{padding-bottom: 0 !important;}.pb-xxl-1{padding-bottom: 4px !important;}.pb-xxl-2{padding-bottom: 8px !important;}.pb-xxl-3{padding-bottom: 16px !important;}.pb-xxl-4{padding-bottom: 24px !important;}.pb-xxl-5{padding-bottom: 48px !important;}.ps-xxl-0{padding-left: 0 !important;}.ps-xxl-1{padding-left: 4px !important;}.ps-xxl-2{padding-left: 8px !important;}.ps-xxl-3{padding-left: 16px !important;}.ps-xxl-4{padding-left: 24px !important;}.ps-xxl-5{padding-left: 48px !important;}.gap-xxl-0{gap: 0 !important;}.gap-xxl-1{gap: 4px !important;}.gap-xxl-2{gap: 8px !important;}.gap-xxl-3{gap: 16px !important;}.gap-xxl-4{gap: 24px !important;}.gap-xxl-5{gap: 48px !important;}.row-gap-xxl-0{row-gap: 0 !important;}.row-gap-xxl-1{row-gap: 4px !important;}.row-gap-xxl-2{row-gap: 8px !important;}.row-gap-xxl-3{row-gap: 16px !important;}.row-gap-xxl-4{row-gap: 24px !important;}.row-gap-xxl-5{row-gap: 48px !important;}.column-gap-xxl-0{column-gap: 0 !important;}.column-gap-xxl-1{column-gap: 4px !important;}.column-gap-xxl-2{column-gap: 8px !important;}.column-gap-xxl-3{column-gap: 16px !important;}.column-gap-xxl-4{column-gap: 24px !important;}.column-gap-xxl-5{column-gap: 48px !important;}.text-xxl-start{text-align: left !important;}.text-xxl-end{text-align: right !important;}.text-xxl-center{text-align: center !important;}.flex-basis-xxl-0{flex-basis: 0 !important;}.flex-basis-xxl-25{flex-basis: 25% !important;}.flex-basis-xxl-50{flex-basis: 50% !important;}.flex-basis-xxl-75{flex-basis: 75% !important;}.flex-basis-xxl-100{flex-basis: 100% !important;}.flex-basis-xxl-auto{flex-basis: auto !important;}}@media print{.d-print-inline{display: inline !important;}.d-print-inline-block{display: inline-block !important;}.d-print-block{display: block !important;}.d-print-grid{display: grid !important;}.d-print-inline-grid{display: inline-grid !important;}.d-print-table{display: table !important;}.d-print-table-row{display: table-row !important;}.d-print-table-cell{display: table-cell !important;}.d-print-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-print-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-print-none{display: none !important;}.d-print-contents{display: contents !important;}}

/* /web/static/src/scss/bootstrap_review.scss */
 .alert{clear: both;}.accordion .collapsing > .card-body:first-child, .accordion .collapse.show > .card-body:first-child{margin-top: var(--border-width);}.toast-header{background-clip: border-box;}@media (min-width: 576px){:not(.s_popup) > .modal .modal-dialog{height: 100%; padding: 1.75rem 0; margin: 0 auto;}:not(.s_popup) > .modal .modal-content{max-height: 100%;}:not(.s_popup) > .modal .modal-header, :not(.s_popup) > .modal .modal-footer{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}:not(.s_popup) > .modal .modal-body{overflow: auto; min-height: 0;}}.modal-backdrop{display: none;}.modal:not([data-bs-backdrop="false"]){background-color: rgba(0, 0, 0, 0.5);}.form-check .form-check-input:not(:disabled), .form-check .form-check-input:not(:disabled) + label{cursor: pointer;}.form-check:hover, .form-check:hover .form-check-input:not(:disabled){border-color: #017e84;}.form-select:where(:not(:disabled)):hover{border-color: #017e84;}.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"]{right: auto;}.popover{right: auto;}.carousel-indicators{list-style: none;}@keyframes progress-bar-stripes{0%{background-position-x: var(--progress-height, 1rem);}}

/* /base/static/src/css/description.css */
 .openerp .oe_form_sheet_width{max-width: 960px;}.o_web_client .o_form_view .oe_styling_v8 .container{width: 100%;}.openerp .oe_form .oe_styling_v8{width: 100%; padding: 0; margin: 0; font-family: "Open Sans", "Helvetica", Sans; font-weight: 300; color: #646464; background: white; font-size: 16px;}.openerp .oe_form .oe_styling_v8 .container{width: 100%;}.openerp .oe_form .oe_styling_v8 .oe_websiteonly{display: none;}.openerp .oe_form .oe_styling_v8 .oe_website_contents{background: whitesmoke; padding-bottom: 1px;}.openerp .oe_form .oe_styling_v8 b{font-weight: 600;}.openerp .oe_form .oe_styling_v8 a{color: #6D57E0; text-decoration: none;}.openerp .oe_form .oe_styling_v8 a:visited{color: #5b284f;}.openerp .oe_form .oe_styling_v8 a:hover{color: #0096EB;}.openerp .oe_form .oe_styling_v8 .oe_title_font{font-family: "Lato", "Open Sans", "Helvetica", Sans;}.openerp .oe_form .oe_styling_v8 .oe_page{background: white; overflow: hidden; -moz-border-radius: 1px; -webkit-border-radius: 1px; border-radius: 1px; -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35); -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);}.openerp .oe_form .oe_styling_v8 .oe_emph{font-weight: 400;}.openerp .oe_form .oe_styling_v8 .oe_dark{overflow: hidden; background: #FCFCFC; -moz-box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset; -webkit-box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset; box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset;}.oe_page{margin: 0px auto 64px auto; max-width: 100%;}.oe_row{width: 100%; margin-top: 16px; margin-bottom: 16px; margin-left: auto; margin-right: auto;}.oe_row.oe_fit{width: auto;}.oe_clearfix:after, .oe_row:after{content: "."; display: block; clear: both; visibility: hidden; line-height: 0; height: 0;}[class*='oe_span']{float: left; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; padding: 0 16px;}.oe_span12{width: 100%;}.oe_span10{width: 83.33333%;}.oe_span9{width: 75%;}.oe_span8{width: 66.66667%;}.oe_span6{width: 50%;}.oe_span4{width: 33.33333%;}.oe_span3{width: 25%;}.oe_span2{width: 16.66667%;}[class*='oe_span'].oe_fit{padding-left: 0px !important; padding-right: 0px !important;}.oe_row.oe_flex [class*='oe_span']{display: inline-block; float: none; vertical-align: top; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; padding: 0 16px; width: auto;}.oe_row.oe_flex .oe_span12{max-width: 100%;}.oe_row.oe_flex .oe_span10{max-width: 83.33333%;}.oe_row.oe_flex .oe_span9{max-width: 75%;}.oe_row.oe_flex .oe_span8{max-width: 66.66667%;}.oe_row.oe_flex .oe_span6{max-width: 50%;}.oe_row.oe_flex .oe_span4{max-width: 33.33333%;}.oe_row.oe_flex .oe_span3{max-width: 25%;}.oe_row.oe_flex .oe_span2{max-width: 16.66667%;}.oe_mb0{margin-bottom: 0px !important;}.oe_mb4{margin-bottom: 4px !important;}.oe_mb8{margin-bottom: 8px !important;}.oe_mb16{margin-bottom: 16px !important;}.oe_mb32{margin-bottom: 32px !important;}.oe_mb48{margin-bottom: 48px !important;}.oe_mb64{margin-bottom: 64px !important;}.oe_mt0{margin-top: 0px !important;}.oe_mt4{margin-top: 4px !important;}.oe_mt8{margin-top: 8px !important;}.oe_mt16{margin-top: 16px !important;}.oe_mt32{margin-top: 32px !important;}.oe_mt48{margin-top: 48px !important;}.oe_mt64{margin-top: 64px !important;}.oe_rightfit{padding-right: 0px !important;}.oe_leftfit{padding-left: 0px !important;}.oe_leftalign{text-align: left;}.oe_rightalign{text-align: right;}.oe_centeralign{text-align: center;}.oe_centered{margin-left: auto; margin-right: auto;}.oe_hidden{display: none !important; opacity: 0 !important;}.oe_invisible{visibility: hidden !important;}.oe_transparent{opacity: 0 !important;}.oe_mb0{margin-bottom: 0px !important;}.oe_mb4{margin-bottom: 4px !important;}.oe_mb8{margin-bottom: 8px !important;}.oe_mb16{margin-bottom: 16px !important;}.oe_mb32{margin-bottom: 32px !important;}.oe_mb64{margin-bottom: 64px !important;}.oe_spaced{margin-top: 32px; margin-bottom: 32px;}.oe_more_spaced{margin-top: 64px; margin-bottom: 64px;}.oe_padded{padding-top: 16px; padding-bottom: 16px;}.oe_more_padded{padding-top: 32px; padding-bottom: 32px;}.oe_button{position: relative; bottom: 0; display: inline-block; cursor: pointer; -moz-user-select: -moz-none; -ms-user-select: none; -webkit-user-select: none; user-select: none;}.oe_styling_v8 .oe_button, .oe_styling_v8 a.oe_button{padding: 8px 14px; background: #8b72b6; color: white; -moz-border-radius: 2px; -webkit-border-radius: 2px; border-radius: 2px; -moz-box-shadow: 0px 2px 0px #afa8cc; -webkit-box-shadow: 0px 2px 0px #afa8cc; box-shadow: 0px 2px 0px #afa8cc; text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.44); border: solid 1px rgba(0, 0, 0, 0.09); -moz-transition-property: bottom, background; -o-transition-property: bottom, background; -webkit-transition-property: bottom, background; transition-property: bottom, background; -moz-transition-duration: 250ms; -o-transition-duration: 250ms; -webkit-transition-duration: 250ms; transition-duration: 250ms;}.oe_styling_v8 .oe_button:hover, .oe_styling_v8 a.oe_button:hover{background: #8b5bdd; color: white;}.oe_styling_v8 .oe_button:active, .oe_styling_v8 a.oe_button:active{background: #333333; bottom: -3px;}.oe_styling_v8 .oe_button.oe_big, .oe_styling_v8 a.oe_button.oe_big{font-size: 24px;}.oe_styling_v8 .oe_button.oe_bigger, .oe_styling_v8 a.oe_button.oe_bigger{font-size: 32px;}.oe_styling_v8 .oe_button.oe_small, .oe_styling_v8 a.oe_button.oe_small{font-size: 13px; padding: 2px 4px;}.oe_styling_v8 .oe_button.oe_small:active, .oe_styling_v8 a.oe_button.oe_small:active{bottom: -1px;}.oe_styling_v8 .oe_button.oe_medium, .oe_styling_v8 a.oe_button.oe_medium{padding: 5px 12px; font-size: 16px;}.oe_styling_v8 .oe_button.oe_tacky, .oe_styling_v8 a.oe_button.oe_tacky{background: #ff4444; -moz-box-shadow: 0px 2px 0px #eba8a8; -webkit-box-shadow: 0px 2px 0px #eba8a8; box-shadow: 0px 2px 0px #eba8a8;}.oe_styling_v8 .oe_button.oe_tacky:hover, .oe_styling_v8 a.oe_button.oe_tacky:hover{background: #ff1010;}.oe_styling_v8 .oe_button.oe_tacky:active, .oe_styling_v8 a.oe_button.oe_tacky:active{background: black;}.oe_styling_v8 .oe_button.oe_disabled, .oe_styling_v8 a.oe_button.oe_disabled{background: #c8c8c8; -moz-box-shadow: 0px 2px 0px #b4b4b4; -webkit-box-shadow: 0px 2px 0px #b4b4b4; box-shadow: 0px 2px 0px #b4b4b4; cursor: default;}.oe_styling_v8 .oe_button.oe_disabled:hover, .oe_styling_v8 a.oe_button.oe_disabled:hover{background: #c8c8c8; -moz-box-shadow: 0px 2px 0px #b4b4b4; -webkit-box-shadow: 0px 2px 0px #b4b4b4; box-shadow: 0px 2px 0px #b4b4b4;}.oe_styling_v8 .oe_button.oe_disabled:active, .oe_styling_v8 a.oe_button.oe_disabled:active{background: #c8c8c8; bottom: 0px; -moz-box-shadow: 0px 2px 0px #b4b4b4; -webkit-box-shadow: 0px 2px 0px #b4b4b4; box-shadow: 0px 2px 0px #b4b4b4;}.oe_styling_v8.oe_styling_black .oe_button{-moz-box-shadow: 0px 2px 0px #463555; -webkit-box-shadow: 0px 2px 0px #463555; box-shadow: 0px 2px 0px #463555;}.oe_styling_v8{}.oe_styling_v8 .oe_input{padding: 4px 7px; border-radius: 3px; border: solid 1px #d6d6d6; box-shadow: 0px 2px #e6e6e6; background: #fafafa; font-weight: 300; outline: none; -moz-transition: all 150ms linear; -o-transition: all 150ms linear; -webkit-transition: all 150ms linear; transition: all 150ms linear;}.oe_styling_v8 .oe_input:focus{border: solid 1px #969696; box-shadow: 0px 2px #d2d2d2;}.oe_styling_v8 .oe_input.oe_valid{background: #F2FFEC; border-color: #b1ebb6; box-shadow: 0px 2px #e1f8e1; color: #0f610f;}.oe_styling_v8 .oe_input.oe_invalid{background: #fff2f2; border-color: #EBB1B1; box-shadow: 0px 2px #F8E1E1; color: #610F0F;}.oe_styling_v8 .oe_input.oe_big{padding: 8px 14px;}.oe_styling_v8 .oe_input_label{font-weight: 300; font-size: 16px;}.oe_styling_v8 .oe_input_label.oe_big{font-size: 20px;}.oe_styling_v8 .oe_textarea{width: 300px; height: 80px;}.oe_styling_v8 .oe_form_layout_table{width: 100%;}.oe_styling_v8 .oe_form_layout_table td{padding-bottom: 16px;}.oe_styling_v8 .oe_form_layout_table td:first-child{text-align: right; padding-right: 16px;}.oe_styling_v8 .oe_slogan{color: #333333; font-family: "Lato", "Open Sans", "Helvetica", Sans; text-align: center; margin-top: 32px; margin-bottom: 32px;}.oe_styling_v8 h1.oe_slogan{font-size: 64px; font-weight: 900; margin-top: 48px; margin-bottom: 48px;}.oe_styling_v8 h2.oe_slogan{font-size: 40px; font-weight: 300;}.oe_styling_v8 h3.oe_slogan{font-size: 26px; font-weight: 300; filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50); opacity: 0.5;}.oe_styling_v8 h4.oe_slogan{font-size: 24px; font-weight: 300;}.oe_styling_v8 h4.oe_slogan:before, .oe_styling_v8 h4.oe_slogan:after{margin: 0 20px; content: ""; display: inline-block; width: 100px; height: 0px; border-top: solid 1px; vertical-align: middle; filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=30); opacity: 0.3;}.oe_styling_v8 h5.oe_slogan{font-weight: 300;}.oe_quote{margin: 8px; padding: 16px; background: rgba(0, 0, 0, 0.02); border: solid 1px rgba(0, 0, 0, 0.06); -moz-border-radius: 2px; -webkit-border-radius: 2px; border-radius: 2px;}.oe_quote .oe_q, .oe_quote q{margin: 10px; display: block; font-style: italic; text-align: center; font-size: 20px; color: #4e66e7;}.oe_quote .oe_q:before, .oe_quote .oe_q:after, .oe_quote q:before, .oe_quote q:after{content: '"'; font-weight: 900; filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=20); opacity: 0.2;}.oe_quote cite{display: block; font-style: normal; margin-top: 16px;}.oe_quote .oe_photo{float: left; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; margin-right: 16px;}.oe_quote .oe_author{font-size: 20px; padding-top: 6px; color: #8d7bac;}.oe_dark .oe_quote{background: white; border: 1px solid #f0f0ff;}.oe_picture{display: block; max-width: 84%; max-height: 400px; margin: 16px 8%;}.oe_screenshot{-moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2); -webkit-box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2); box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);}.oe_pic_ctr{position: relative;}.oe_pic_ctr > img.oe_picture{width: 100%; max-width: none; max-height: none; margin: 0;}.oe_pic_ctr > .oe_title{position: absolute; top: 15px; right: 38px;}.oe_styling_v8 .oe_pic_ctr > .oe_title{font-size: 64px; color: white; font-weight: 600; margin: 0; text-shadow: 0px 2px 0px #494949, 0px 2px 5px rgba(0, 0, 0, 0.33), 0px 0px 60px rgba(0, 0, 0, 0.22);}div.oe_demo{position: relative; border: 1px solid #dedede;}div.oe_demo span.oe_demo_play{top: 50%; left: 50%; width: 80px; height: 60px; margin-top: -30px; margin-left: -40px; display: block; position: absolute; background: url("/base/static/src/css/../img/layout/play-button.png") no-repeat left top transparent; pointer-events: none;}div.oe_demo img{max-width: 100%; width: 100%;}div.oe_demo div.oe_demo_footer{position: absolute; left: 0; background-color: rgba(0, 0, 0, 0.4); opacity: 0.85; bottom: -1px; width: 100%; padding-top: 7px; padding-bottom: 7px; color: white; font-size: 14px; font-weight: bold; border-bottom-left-radius: 3px; border-bottom-right-radius: 3px; pointer-events: none;}div.oe_demo:hover span.oe_demo_play{background: url("/base/static/src/css/../img/layout/play-button-over.png") no-repeat left top transparent;}.oe_styling_v8 .oe_container.oe_separator{height: 64px; margin-bottom: 16px; background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwLjAiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMC4wMiIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA=='); background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, rgba(0, 0, 0, 0)), color-stop(100%, rgba(0, 0, 0, 0.02))); background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); -moz-box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; -webkit-box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; overflow-y: hidden;}.oe_row_tabs{text-align: center; margin-top: 0px; margin-bottom: 0px; padding-top: 21px;}.oe_row_tab{position: relative; min-width: 120px; padding: 8px; font-size: 20px; display: inline-block; margin: 0px -2px; border-top-left-radius: 4px; border-top-right-radius: 4px; border: solid 1px rgba(0, 0, 0, 0.1); border-bottom: none; background: #fafafa; background-image: +linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; cursor: pointer; -moz-transition: all 250ms linear; -o-transition: all 250ms linear; -webkit-transition: all 250ms linear; transition: all 250ms linear;}.oe_row_tab:hover{padding-bottom: 12px; top: -4px; background-color: white;}.oe_row_tab.oe_active{background-color: white; background-image: none; box-shadow: none; border-top-color: #8272b6; border-top-width: 2px; cursor: default;}.oe_row_tab.oe_active:hover{padding-bottom: 8px; top: 0asx;}.oe_calltoaction{height: 32px; margin-top: -32px; position: relative;}

/* /web/static/src/libs/fontawesome/css/font-awesome.css */
 @font-face{font-family: 'FontAwesome'; src: url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.fa{display: inline-block; font: normal normal normal 14px/1 FontAwesome; font-size: inherit; text-rendering: auto; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fa-lg{font-size: 1.315em; vertical-align: -6%;}.fa-2x{font-size: 2em;}.fa-3x{font-size: 3em;}.fa-4x{font-size: 4em;}.fa-5x{font-size: 5em;}.fa-fw{width: 1.28571429em; text-align: center;}.fa-ul{padding-left: 0; margin-left: 2.14285714em; list-style-type: none;}.fa-ul > li{position: relative;}.fa-li{position: absolute; left: -2.14285714em; width: 2.14285714em; top: 0.14285714em; text-align: center;}.fa-li.fa-lg{left: -1.85714286em;}.fa-border{padding: .2em .25em .15em; border: solid 0.08em #eeeeee; border-radius: .1em;}.fa-pull-left{float: left;}.fa-pull-right{float: right;}.fa.fa-pull-left{margin-right: .3em;}.fa.fa-pull-right{margin-left: .3em;}.fa-spin{animation: fa-spin 2s infinite linear;}.fa-pulse{animation: fa-spin 1s infinite steps(8);}@keyframes fa-spin{0%{transform: rotate(0deg);}100%{transform: rotate(359deg);}}.fa-rotate-90{transform: rotate(90deg);}.fa-rotate-180{transform: rotate(180deg);}.fa-rotate-270{transform: rotate(270deg);}.fa-flip-horizontal{transform: scale(-1, 1);}.fa-flip-vertical{transform: scale(1, -1);}:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical{filter: none;}.fa-stack{position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle;}.fa-stack-1x, .fa-stack-2x{position: absolute; left: 0; width: 100%; text-align: center;}.fa-stack-1x{line-height: inherit;}.fa-stack-2x{font-size: 2em;}.fa-inverse{color: #ffffff;}.fa-glass:before{content: "\f000";}.fa-music:before{content: "\f001";}.fa-search:before{content: "\f002";}.fa-envelope-o:before{content: "\f003";}.fa-heart:before{content: "\f004";}.fa-star:before{content: "\f005";}.fa-star-o:before{content: "\f006";}.fa-user:before{content: "\f007";}.fa-film:before{content: "\f008";}.fa-th-large:before{content: "\f009";}.fa-th:before{content: "\f00a";}.fa-th-list:before{content: "\f00b";}.fa-check:before{content: "\f00c";}.fa-remove:before, .fa-close:before, .fa-times:before{content: "\f00d";}.fa-search-plus:before{content: "\f00e";}.fa-search-minus:before{content: "\f010";}.fa-power-off:before{content: "\f011";}.fa-signal:before{content: "\f012";}.fa-gear:before, .fa-cog:before{content: "\f013";}.fa-trash-o:before{content: "\f014";}.fa-home:before{content: "\f015";}.fa-file-o:before{content: "\f016";}.fa-clock-o:before{content: "\f017";}.fa-road:before{content: "\f018";}.fa-download:before{content: "\f019";}.fa-arrow-circle-o-down:before{content: "\f01a";}.fa-arrow-circle-o-up:before{content: "\f01b";}.fa-inbox:before{content: "\f01c";}.fa-play-circle-o:before{content: "\f01d";}.fa-rotate-right:before, .fa-repeat:before{content: "\f01e";}.fa-refresh:before{content: "\f021";}.fa-list-alt:before{content: "\f022";}.fa-lock:before{content: "\f023";}.fa-flag:before{content: "\f024";}.fa-headphones:before{content: "\f025";}.fa-volume-off:before{content: "\f026";}.fa-volume-down:before{content: "\f027";}.fa-volume-up:before{content: "\f028";}.fa-qrcode:before{content: "\f029";}.fa-barcode:before{content: "\f02a";}.fa-tag:before{content: "\f02b";}.fa-tags:before{content: "\f02c";}.fa-book:before{content: "\f02d";}.fa-bookmark:before{content: "\f02e";}.fa-print:before{content: "\f02f";}.fa-camera:before{content: "\f030";}.fa-font:before{content: "\f031";}.fa-bold:before{content: "\f032";}.fa-italic:before{content: "\f033";}.fa-text-height:before{content: "\f034";}.fa-text-width:before{content: "\f035";}.fa-align-left:before{content: "\f036";}.fa-align-center:before{content: "\f037";}.fa-align-right:before{content: "\f038";}.fa-align-justify:before{content: "\f039";}.fa-list:before{content: "\f03a";}.fa-dedent:before, .fa-outdent:before{content: "\f03b";}.fa-indent:before{content: "\f03c";}.fa-video-camera:before{content: "\f03d";}.fa-photo:before, .fa-image:before, .fa-picture-o:before{content: "\f03e";}.fa-pencil:before{content: "\f040";}.fa-map-marker:before{content: "\f041";}.fa-adjust:before{content: "\f042";}.fa-tint:before{content: "\f043";}.fa-edit:before, .fa-pencil-square-o:before{content: "\f044";}.fa-share-square-o:before{content: "\f045";}.fa-check-square-o:before{content: "\f046";}.fa-arrows:before{content: "\f047";}.fa-step-backward:before{content: "\f048";}.fa-fast-backward:before{content: "\f049";}.fa-backward:before{content: "\f04a";}.fa-play:before{content: "\f04b";}.fa-pause:before{content: "\f04c";}.fa-stop:before{content: "\f04d";}.fa-forward:before{content: "\f04e";}.fa-fast-forward:before{content: "\f050";}.fa-step-forward:before{content: "\f051";}.fa-eject:before{content: "\f052";}.fa-chevron-left:before{content: "\f053";}.fa-chevron-right:before{content: "\f054";}.fa-plus-circle:before{content: "\f055";}.fa-minus-circle:before{content: "\f056";}.fa-times-circle:before{content: "\f057";}.fa-check-circle:before{content: "\f058";}.fa-question-circle:before{content: "\f059";}.fa-info-circle:before{content: "\f05a";}.fa-crosshairs:before{content: "\f05b";}.fa-times-circle-o:before{content: "\f05c";}.fa-check-circle-o:before{content: "\f05d";}.fa-ban:before{content: "\f05e";}.fa-arrow-left:before{content: "\f060";}.fa-arrow-right:before{content: "\f061";}.fa-arrow-up:before{content: "\f062";}.fa-arrow-down:before{content: "\f063";}.fa-mail-forward:before, .fa-share:before{content: "\f064";}.fa-expand:before{content: "\f065";}.fa-compress:before{content: "\f066";}.fa-plus:before{content: "\f067";}.fa-minus:before{content: "\f068";}.fa-asterisk:before{content: "\f069";}.fa-exclamation-circle:before{content: "\f06a";}.fa-gift:before{content: "\f06b";}.fa-leaf:before{content: "\f06c";}.fa-fire:before{content: "\f06d";}.fa-eye:before{content: "\f06e";}.fa-eye-slash:before{content: "\f070";}.fa-warning:before, .fa-exclamation-triangle:before{content: "\f071";}.fa-plane:before{content: "\f072";}.fa-calendar:before{content: "\f073";}.fa-random:before{content: "\f074";}.fa-comment:before{content: "\f075";}.fa-magnet:before{content: "\f076";}.fa-chevron-up:before{content: "\f077";}.fa-chevron-down:before{content: "\f078";}.fa-retweet:before{content: "\f079";}.fa-shopping-cart:before{content: "\f07a";}.fa-folder:before{content: "\f07b";}.fa-folder-open:before{content: "\f07c";}.fa-arrows-v:before{content: "\f07d";}.fa-arrows-h:before{content: "\f07e";}.fa-bar-chart-o:before, .fa-bar-chart:before{content: "\f080";}.fa-twitter-square:before{content: "\f081";}.fa-facebook-square:before{content: "\f082";}.fa-camera-retro:before{content: "\f083";}.fa-key:before{content: "\f084";}.fa-gears:before, .fa-cogs:before{content: "\f085";}.fa-comments:before{content: "\f086";}.fa-thumbs-o-up:before{content: "\f087";}.fa-thumbs-o-down:before{content: "\f088";}.fa-star-half:before{content: "\f089";}.fa-heart-o:before{content: "\f08a";}.fa-sign-out:before{content: "\f08b";}.fa-linkedin-square:before{content: "\f08c";}.fa-thumb-tack:before{content: "\f08d";}.fa-external-link:before{content: "\f08e";}.fa-sign-in:before{content: "\f090";}.fa-trophy:before{content: "\f091";}.fa-github-square:before{content: "\f092";}.fa-upload:before{content: "\f093";}.fa-lemon-o:before{content: "\f094";}.fa-phone:before{content: "\f095";}.fa-square-o:before{content: "\f096";}.fa-bookmark-o:before{content: "\f097";}.fa-phone-square:before{content: "\f098";}.fa-twitter:before{content: "\f099";}.fa-facebook-f:before, .fa-facebook:before{content: "\f09a";}.fa-github:before{content: "\f09b";}.fa-unlock:before{content: "\f09c";}.fa-credit-card:before{content: "\f09d";}.fa-feed:before, .fa-rss:before{content: "\f09e";}.fa-hdd-o:before{content: "\f0a0";}.fa-bullhorn:before{content: "\f0a1";}.fa-bell:before{content: "\f0f3";}.fa-certificate:before{content: "\f0a3";}.fa-hand-o-right:before{content: "\f0a4";}.fa-hand-o-left:before{content: "\f0a5";}.fa-hand-o-up:before{content: "\f0a6";}.fa-hand-o-down:before{content: "\f0a7";}.fa-arrow-circle-left:before{content: "\f0a8";}.fa-arrow-circle-right:before{content: "\f0a9";}.fa-arrow-circle-up:before{content: "\f0aa";}.fa-arrow-circle-down:before{content: "\f0ab";}.fa-globe:before{content: "\f0ac";}.fa-wrench:before{content: "\f0ad";}.fa-tasks:before{content: "\f0ae";}.fa-filter:before{content: "\f0b0";}.fa-briefcase:before{content: "\f0b1";}.fa-arrows-alt:before{content: "\f0b2";}.fa-group:before, .fa-users:before{content: "\f0c0";}.fa-chain:before, .fa-link:before{content: "\f0c1";}.fa-cloud:before{content: "\f0c2";}.fa-flask:before{content: "\f0c3";}.fa-cut:before, .fa-scissors:before{content: "\f0c4";}.fa-copy:before, .fa-files-o:before{content: "\f0c5";}.fa-paperclip:before{content: "\f0c6";}.fa-save:before, .fa-floppy-o:before{content: "\f0c7";}.fa-square:before{content: "\f0c8";}.fa-navicon:before, .fa-reorder:before, .fa-bars:before{content: "\f0c9";}.fa-list-ul:before{content: "\f0ca";}.fa-list-ol:before{content: "\f0cb";}.fa-strikethrough:before{content: "\f0cc";}.fa-underline:before{content: "\f0cd";}.fa-table:before{content: "\f0ce";}.fa-magic:before{content: "\f0d0";}.fa-truck:before{content: "\f0d1";}.fa-pinterest:before{content: "\f0d2";}.fa-pinterest-square:before{content: "\f0d3";}.fa-google-plus-square:before{content: "\f0d4";}.fa-google-plus:before{content: "\f0d5";}.fa-money:before{content: "\f0d6";}.fa-caret-down:before{content: "\f0d7";}.fa-caret-up:before{content: "\f0d8";}.fa-caret-left:before{content: "\f0d9";}.fa-caret-right:before{content: "\f0da";}.fa-columns:before{content: "\f0db";}.fa-unsorted:before, .fa-sort:before{content: "\f0dc";}.fa-sort-down:before, .fa-sort-desc:before{content: "\f0dd";}.fa-sort-up:before, .fa-sort-asc:before{content: "\f0de";}.fa-envelope:before{content: "\f0e0";}.fa-linkedin:before{content: "\f0e1";}.fa-rotate-left:before, .fa-undo:before{content: "\f0e2";}.fa-legal:before, .fa-gavel:before{content: "\f0e3";}.fa-dashboard:before, .fa-tachometer:before{content: "\f0e4";}.fa-comment-o:before{content: "\f0e5";}.fa-comments-o:before{content: "\f0e6";}.fa-flash:before, .fa-bolt:before{content: "\f0e7";}.fa-sitemap:before{content: "\f0e8";}.fa-umbrella:before{content: "\f0e9";}.fa-paste:before, .fa-clipboard:before{content: "\f0ea";}.fa-lightbulb-o:before{content: "\f0eb";}.fa-exchange:before{content: "\f0ec";}.fa-cloud-download:before{content: "\f0ed";}.fa-cloud-upload:before{content: "\f0ee";}.fa-user-md:before{content: "\f0f0";}.fa-stethoscope:before{content: "\f0f1";}.fa-suitcase:before{content: "\f0f2";}.fa-bell-o:before{content: "\f0a2";}.fa-coffee:before{content: "\f0f4";}.fa-cutlery:before{content: "\f0f5";}.fa-file-text-o:before{content: "\f0f6";}.fa-building-o:before{content: "\f0f7";}.fa-hospital-o:before{content: "\f0f8";}.fa-ambulance:before{content: "\f0f9";}.fa-medkit:before{content: "\f0fa";}.fa-fighter-jet:before{content: "\f0fb";}.fa-beer:before{content: "\f0fc";}.fa-h-square:before{content: "\f0fd";}.fa-plus-square:before{content: "\f0fe";}.fa-angle-double-left:before{content: "\f100";}.fa-angle-double-right:before{content: "\f101";}.fa-angle-double-up:before{content: "\f102";}.fa-angle-double-down:before{content: "\f103";}.fa-angle-left:before{content: "\f104";}.fa-angle-right:before{content: "\f105";}.fa-angle-up:before{content: "\f106";}.fa-angle-down:before{content: "\f107";}.fa-desktop:before{content: "\f108";}.fa-laptop:before{content: "\f109";}.fa-tablet:before{content: "\f10a";}.fa-mobile-phone:before, .fa-mobile:before{content: "\f10b";}.fa-circle-o:before{content: "\f10c";}.fa-quote-left:before{content: "\f10d";}.fa-quote-right:before{content: "\f10e";}.fa-spinner:before{content: "\f110";}.fa-circle:before{content: "\f111";}.fa-mail-reply:before, .fa-reply:before{content: "\f112";}.fa-github-alt:before{content: "\f113";}.fa-folder-o:before{content: "\f114";}.fa-folder-open-o:before{content: "\f115";}.fa-smile-o:before{content: "\f118";}.fa-frown-o:before{content: "\f119";}.fa-meh-o:before{content: "\f11a";}.fa-gamepad:before{content: "\f11b";}.fa-keyboard-o:before{content: "\f11c";}.fa-flag-o:before{content: "\f11d";}.fa-flag-checkered:before{content: "\f11e";}.fa-terminal:before{content: "\f120";}.fa-code:before{content: "\f121";}.fa-mail-reply-all:before, .fa-reply-all:before{content: "\f122";}.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before{content: "\f123";}.fa-location-arrow:before{content: "\f124";}.fa-crop:before{content: "\f125";}.fa-code-fork:before{content: "\f126";}.fa-unlink:before, .fa-chain-broken:before{content: "\f127";}.fa-question:before{content: "\f128";}.fa-info:before{content: "\f129";}.fa-exclamation:before{content: "\f12a";}.fa-superscript:before{content: "\f12b";}.fa-subscript:before{content: "\f12c";}.fa-eraser:before{content: "\f12d";}.fa-puzzle-piece:before{content: "\f12e";}.fa-microphone:before{content: "\f130";}.fa-microphone-slash:before{content: "\f131";}.fa-shield:before{content: "\f132";}.fa-calendar-o:before{content: "\f133";}.fa-fire-extinguisher:before{content: "\f134";}.fa-rocket:before{content: "\f135";}.fa-maxcdn:before{content: "\f136";}.fa-chevron-circle-left:before{content: "\f137";}.fa-chevron-circle-right:before{content: "\f138";}.fa-chevron-circle-up:before{content: "\f139";}.fa-chevron-circle-down:before{content: "\f13a";}.fa-html5:before{content: "\f13b";}.fa-css3:before{content: "\f13c";}.fa-anchor:before{content: "\f13d";}.fa-unlock-alt:before{content: "\f13e";}.fa-bullseye:before{content: "\f140";}.fa-ellipsis-h:before{content: "\f141";}.fa-ellipsis-v:before{content: "\f142";}.fa-rss-square:before{content: "\f143";}.fa-play-circle:before{content: "\f144";}.fa-ticket:before{content: "\f145";}.fa-minus-square:before{content: "\f146";}.fa-minus-square-o:before{content: "\f147";}.fa-level-up:before{content: "\f148";}.fa-level-down:before{content: "\f149";}.fa-check-square:before{content: "\f14a";}.fa-pencil-square:before{content: "\f14b";}.fa-external-link-square:before{content: "\f14c";}.fa-share-square:before{content: "\f14d";}.fa-compass:before{content: "\f14e";}.fa-toggle-down:before, .fa-caret-square-o-down:before{content: "\f150";}.fa-toggle-up:before, .fa-caret-square-o-up:before{content: "\f151";}.fa-toggle-right:before, .fa-caret-square-o-right:before{content: "\f152";}.fa-euro:before, .fa-eur:before{content: "\f153";}.fa-gbp:before{content: "\f154";}.fa-dollar:before, .fa-usd:before{content: "\f155";}.fa-rupee:before, .fa-inr:before{content: "\f156";}.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before{content: "\f157";}.fa-ruble:before, .fa-rouble:before, .fa-rub:before{content: "\f158";}.fa-won:before, .fa-krw:before{content: "\f159";}.fa-bitcoin:before, .fa-btc:before{content: "\f15a";}.fa-file:before{content: "\f15b";}.fa-file-text:before{content: "\f15c";}.fa-sort-alpha-asc:before{content: "\f15d";}.fa-sort-alpha-desc:before{content: "\f15e";}.fa-sort-amount-asc:before{content: "\f160";}.fa-sort-amount-desc:before{content: "\f161";}.fa-sort-numeric-asc:before{content: "\f162";}.fa-sort-numeric-desc:before{content: "\f163";}.fa-thumbs-up:before{content: "\f164";}.fa-thumbs-down:before{content: "\f165";}.fa-youtube-square:before{content: "\f166";}.fa-youtube:before{content: "\f167";}.fa-xing:before{content: "\f168";}.fa-xing-square:before{content: "\f169";}.fa-youtube-play:before{content: "\f16a";}.fa-dropbox:before{content: "\f16b";}.fa-stack-overflow:before{content: "\f16c";}.fa-instagram:before{content: "\f16d";}.fa-flickr:before{content: "\f16e";}.fa-adn:before{content: "\f170";}.fa-bitbucket:before{content: "\f171";}.fa-bitbucket-square:before{content: "\f172";}.fa-tumblr:before{content: "\f173";}.fa-tumblr-square:before{content: "\f174";}.fa-long-arrow-down:before{content: "\f175";}.fa-long-arrow-up:before{content: "\f176";}.fa-long-arrow-left:before{content: "\f177";}.fa-long-arrow-right:before{content: "\f178";}.fa-apple:before{content: "\f179";}.fa-windows:before{content: "\f17a";}.fa-android:before{content: "\f17b";}.fa-linux:before{content: "\f17c";}.fa-dribbble:before{content: "\f17d";}.fa-skype:before{content: "\f17e";}.fa-foursquare:before{content: "\f180";}.fa-trello:before{content: "\f181";}.fa-female:before{content: "\f182";}.fa-male:before{content: "\f183";}.fa-gittip:before, .fa-gratipay:before{content: "\f184";}.fa-sun-o:before{content: "\f185";}.fa-moon-o:before{content: "\f186";}.fa-archive:before{content: "\f187";}.fa-bug:before{content: "\f188";}.fa-vk:before{content: "\f189";}.fa-weibo:before{content: "\f18a";}.fa-renren:before{content: "\f18b";}.fa-pagelines:before{content: "\f18c";}.fa-stack-exchange:before{content: "\f18d";}.fa-arrow-circle-o-right:before{content: "\f18e";}.fa-arrow-circle-o-left:before{content: "\f190";}.fa-toggle-left:before, .fa-caret-square-o-left:before{content: "\f191";}.fa-dot-circle-o:before{content: "\f192";}.fa-wheelchair:before{content: "\f193";}.fa-vimeo-square:before{content: "\f194";}.fa-turkish-lira:before, .fa-try:before{content: "\f195";}.fa-plus-square-o:before{content: "\f196";}.fa-space-shuttle:before{content: "\f197";}.fa-slack:before{content: "\f198";}.fa-envelope-square:before{content: "\f199";}.fa-wordpress:before{content: "\f19a";}.fa-openid:before{content: "\f19b";}.fa-institution:before, .fa-bank:before, .fa-university:before{content: "\f19c";}.fa-mortar-board:before, .fa-graduation-cap:before{content: "\f19d";}.fa-yahoo:before{content: "\f19e";}.fa-google:before{content: "\f1a0";}.fa-reddit:before{content: "\f1a1";}.fa-reddit-square:before{content: "\f1a2";}.fa-stumbleupon-circle:before{content: "\f1a3";}.fa-stumbleupon:before{content: "\f1a4";}.fa-delicious:before{content: "\f1a5";}.fa-digg:before{content: "\f1a6";}.fa-pied-piper-pp:before{content: "\f1a7";}.fa-pied-piper-alt:before{content: "\f1a8";}.fa-drupal:before{content: "\f1a9";}.fa-joomla:before{content: "\f1aa";}.fa-language:before{content: "\f1ab";}.fa-fax:before{content: "\f1ac";}.fa-building:before{content: "\f1ad";}.fa-child:before{content: "\f1ae";}.fa-paw:before{content: "\f1b0";}.fa-spoon:before{content: "\f1b1";}.fa-cube:before{content: "\f1b2";}.fa-cubes:before{content: "\f1b3";}.fa-behance:before{content: "\f1b4";}.fa-behance-square:before{content: "\f1b5";}.fa-steam:before{content: "\f1b6";}.fa-steam-square:before{content: "\f1b7";}.fa-recycle:before{content: "\f1b8";}.fa-automobile:before, .fa-car:before{content: "\f1b9";}.fa-cab:before, .fa-taxi:before{content: "\f1ba";}.fa-tree:before{content: "\f1bb";}.fa-spotify:before{content: "\f1bc";}.fa-deviantart:before{content: "\f1bd";}.fa-soundcloud:before{content: "\f1be";}.fa-database:before{content: "\f1c0";}.fa-file-pdf-o:before{content: "\f1c1";}.fa-file-word-o:before{content: "\f1c2";}.fa-file-excel-o:before{content: "\f1c3";}.fa-file-powerpoint-o:before{content: "\f1c4";}.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before{content: "\f1c5";}.fa-file-zip-o:before, .fa-file-archive-o:before{content: "\f1c6";}.fa-file-sound-o:before, .fa-file-audio-o:before{content: "\f1c7";}.fa-file-movie-o:before, .fa-file-video-o:before{content: "\f1c8";}.fa-file-code-o:before{content: "\f1c9";}.fa-vine:before{content: "\f1ca";}.fa-codepen:before{content: "\f1cb";}.fa-jsfiddle:before{content: "\f1cc";}.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before{content: "\f1cd";}.fa-circle-o-notch:before{content: "\f1ce";}.fa-ra:before, .fa-resistance:before, .fa-rebel:before{content: "\f1d0";}.fa-ge:before, .fa-empire:before{content: "\f1d1";}.fa-git-square:before{content: "\f1d2";}.fa-git:before{content: "\f1d3";}.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before{content: "\f1d4";}.fa-tencent-weibo:before{content: "\f1d5";}.fa-qq:before{content: "\f1d6";}.fa-wechat:before, .fa-weixin:before{content: "\f1d7";}.fa-send:before, .fa-paper-plane:before{content: "\f1d8";}.fa-send-o:before, .fa-paper-plane-o:before{content: "\f1d9";}.fa-history:before{content: "\f1da";}.fa-circle-thin:before{content: "\f1db";}.fa-header:before{content: "\f1dc";}.fa-paragraph:before{content: "\f1dd";}.fa-sliders:before{content: "\f1de";}.fa-share-alt:before{content: "\f1e0";}.fa-share-alt-square:before{content: "\f1e1";}.fa-bomb:before{content: "\f1e2";}.fa-soccer-ball-o:before, .fa-futbol-o:before{content: "\f1e3";}.fa-tty:before{content: "\f1e4";}.fa-binoculars:before{content: "\f1e5";}.fa-plug:before{content: "\f1e6";}.fa-slideshare:before{content: "\f1e7";}.fa-twitch:before{content: "\f1e8";}.fa-yelp:before{content: "\f1e9";}.fa-newspaper-o:before{content: "\f1ea";}.fa-wifi:before{content: "\f1eb";}.fa-calculator:before{content: "\f1ec";}.fa-paypal:before{content: "\f1ed";}.fa-google-wallet:before{content: "\f1ee";}.fa-cc-visa:before{content: "\f1f0";}.fa-cc-mastercard:before{content: "\f1f1";}.fa-cc-discover:before{content: "\f1f2";}.fa-cc-amex:before{content: "\f1f3";}.fa-cc-paypal:before{content: "\f1f4";}.fa-cc-stripe:before{content: "\f1f5";}.fa-bell-slash:before{content: "\f1f6";}.fa-bell-slash-o:before{content: "\f1f7";}.fa-trash:before{content: "\f1f8";}.fa-copyright:before{content: "\f1f9";}.fa-at:before{content: "\f1fa";}.fa-eyedropper:before{content: "\f1fb";}.fa-paint-brush:before{content: "\f1fc";}.fa-birthday-cake:before{content: "\f1fd";}.fa-area-chart:before{content: "\f1fe";}.fa-pie-chart:before{content: "\f200";}.fa-line-chart:before{content: "\f201";}.fa-lastfm:before{content: "\f202";}.fa-lastfm-square:before{content: "\f203";}.fa-toggle-off:before{content: "\f204";}.fa-toggle-on:before{content: "\f205";}.fa-bicycle:before{content: "\f206";}.fa-bus:before{content: "\f207";}.fa-ioxhost:before{content: "\f208";}.fa-angellist:before{content: "\f209";}.fa-cc:before{content: "\f20a";}.fa-shekel:before, .fa-sheqel:before, .fa-ils:before{content: "\f20b";}.fa-meanpath:before{content: "\f20c";}.fa-buysellads:before{content: "\f20d";}.fa-connectdevelop:before{content: "\f20e";}.fa-dashcube:before{content: "\f210";}.fa-forumbee:before{content: "\f211";}.fa-leanpub:before{content: "\f212";}.fa-sellsy:before{content: "\f213";}.fa-shirtsinbulk:before{content: "\f214";}.fa-simplybuilt:before{content: "\f215";}.fa-skyatlas:before{content: "\f216";}.fa-cart-plus:before{content: "\f217";}.fa-cart-arrow-down:before{content: "\f218";}.fa-diamond:before{content: "\f219";}.fa-ship:before{content: "\f21a";}.fa-user-secret:before{content: "\f21b";}.fa-motorcycle:before{content: "\f21c";}.fa-street-view:before{content: "\f21d";}.fa-heartbeat:before{content: "\f21e";}.fa-venus:before{content: "\f221";}.fa-mars:before{content: "\f222";}.fa-mercury:before{content: "\f223";}.fa-intersex:before, .fa-transgender:before{content: "\f224";}.fa-transgender-alt:before{content: "\f225";}.fa-venus-double:before{content: "\f226";}.fa-mars-double:before{content: "\f227";}.fa-venus-mars:before{content: "\f228";}.fa-mars-stroke:before{content: "\f229";}.fa-mars-stroke-v:before{content: "\f22a";}.fa-mars-stroke-h:before{content: "\f22b";}.fa-neuter:before{content: "\f22c";}.fa-genderless:before{content: "\f22d";}.fa-facebook-official:before{content: "\f230";}.fa-pinterest-p:before{content: "\f231";}.fa-whatsapp:before{content: "\f232";}.fa-server:before{content: "\f233";}.fa-user-plus:before{content: "\f234";}.fa-user-times:before{content: "\f235";}.fa-hotel:before, .fa-bed:before{content: "\f236";}.fa-viacoin:before{content: "\f237";}.fa-train:before{content: "\f238";}.fa-subway:before{content: "\f239";}.fa-medium:before{content: "\f23a";}.fa-yc:before, .fa-y-combinator:before{content: "\f23b";}.fa-optin-monster:before{content: "\f23c";}.fa-opencart:before{content: "\f23d";}.fa-expeditedssl:before{content: "\f23e";}.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before{content: "\f240";}.fa-battery-3:before, .fa-battery-three-quarters:before{content: "\f241";}.fa-battery-2:before, .fa-battery-half:before{content: "\f242";}.fa-battery-1:before, .fa-battery-quarter:before{content: "\f243";}.fa-battery-0:before, .fa-battery-empty:before{content: "\f244";}.fa-mouse-pointer:before{content: "\f245";}.fa-i-cursor:before{content: "\f246";}.fa-object-group:before{content: "\f247";}.fa-object-ungroup:before{content: "\f248";}.fa-sticky-note:before{content: "\f249";}.fa-sticky-note-o:before{content: "\f24a";}.fa-cc-jcb:before{content: "\f24b";}.fa-cc-diners-club:before{content: "\f24c";}.fa-clone:before{content: "\f24d";}.fa-balance-scale:before{content: "\f24e";}.fa-hourglass-o:before{content: "\f250";}.fa-hourglass-1:before, .fa-hourglass-start:before{content: "\f251";}.fa-hourglass-2:before, .fa-hourglass-half:before{content: "\f252";}.fa-hourglass-3:before, .fa-hourglass-end:before{content: "\f253";}.fa-hourglass:before{content: "\f254";}.fa-hand-grab-o:before, .fa-hand-rock-o:before{content: "\f255";}.fa-hand-stop-o:before, .fa-hand-paper-o:before{content: "\f256";}.fa-hand-scissors-o:before{content: "\f257";}.fa-hand-lizard-o:before{content: "\f258";}.fa-hand-spock-o:before{content: "\f259";}.fa-hand-pointer-o:before{content: "\f25a";}.fa-hand-peace-o:before{content: "\f25b";}.fa-trademark:before{content: "\f25c";}.fa-registered:before{content: "\f25d";}.fa-creative-commons:before{content: "\f25e";}.fa-gg:before{content: "\f260";}.fa-gg-circle:before{content: "\f261";}.fa-tripadvisor:before{content: "\f262";}.fa-odnoklassniki:before{content: "\f263";}.fa-odnoklassniki-square:before{content: "\f264";}.fa-get-pocket:before{content: "\f265";}.fa-wikipedia-w:before{content: "\f266";}.fa-safari:before{content: "\f267";}.fa-chrome:before{content: "\f268";}.fa-firefox:before{content: "\f269";}.fa-opera:before{content: "\f26a";}.fa-internet-explorer:before{content: "\f26b";}.fa-tv:before, .fa-television:before{content: "\f26c";}.fa-contao:before{content: "\f26d";}.fa-500px:before{content: "\f26e";}.fa-amazon:before{content: "\f270";}.fa-calendar-plus-o:before{content: "\f271";}.fa-calendar-minus-o:before{content: "\f272";}.fa-calendar-times-o:before{content: "\f273";}.fa-calendar-check-o:before{content: "\f274";}.fa-industry:before{content: "\f275";}.fa-map-pin:before{content: "\f276";}.fa-map-signs:before{content: "\f277";}.fa-map-o:before{content: "\f278";}.fa-map:before{content: "\f279";}.fa-commenting:before{content: "\f27a";}.fa-commenting-o:before{content: "\f27b";}.fa-houzz:before{content: "\f27c";}.fa-vimeo:before{content: "\f27d";}.fa-black-tie:before{content: "\f27e";}.fa-fonticons:before{content: "\f280";}.fa-reddit-alien:before{content: "\f281";}.fa-edge:before{content: "\f282";}.fa-credit-card-alt:before{content: "\f283";}.fa-codiepie:before{content: "\f284";}.fa-modx:before{content: "\f285";}.fa-fort-awesome:before{content: "\f286";}.fa-usb:before{content: "\f287";}.fa-product-hunt:before{content: "\f288";}.fa-mixcloud:before{content: "\f289";}.fa-scribd:before{content: "\f28a";}.fa-pause-circle:before{content: "\f28b";}.fa-pause-circle-o:before{content: "\f28c";}.fa-stop-circle:before{content: "\f28d";}.fa-stop-circle-o:before{content: "\f28e";}.fa-shopping-bag:before{content: "\f290";}.fa-shopping-basket:before{content: "\f291";}.fa-hashtag:before{content: "\f292";}.fa-bluetooth:before{content: "\f293";}.fa-bluetooth-b:before{content: "\f294";}.fa-percent:before{content: "\f295";}.fa-gitlab:before{content: "\f296";}.fa-wpbeginner:before{content: "\f297";}.fa-wpforms:before{content: "\f298";}.fa-envira:before{content: "\f299";}.fa-universal-access:before{content: "\f29a";}.fa-wheelchair-alt:before{content: "\f29b";}.fa-question-circle-o:before{content: "\f29c";}.fa-blind:before{content: "\f29d";}.fa-audio-description:before{content: "\f29e";}.fa-volume-control-phone:before{content: "\f2a0";}.fa-braille:before{content: "\f2a1";}.fa-assistive-listening-systems:before{content: "\f2a2";}.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before{content: "\f2a3";}.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before{content: "\f2a4";}.fa-glide:before{content: "\f2a5";}.fa-glide-g:before{content: "\f2a6";}.fa-signing:before, .fa-sign-language:before{content: "\f2a7";}.fa-low-vision:before{content: "\f2a8";}.fa-viadeo:before{content: "\f2a9";}.fa-viadeo-square:before{content: "\f2aa";}.fa-snapchat:before{content: "\f2ab";}.fa-snapchat-ghost:before{content: "\f2ac";}.fa-snapchat-square:before{content: "\f2ad";}.fa-pied-piper:before{content: "\f2ae";}.fa-first-order:before{content: "\f2b0";}.fa-yoast:before{content: "\f2b1";}.fa-themeisle:before{content: "\f2b2";}.fa-google-plus-circle:before, .fa-google-plus-official:before{content: "\f2b3";}.fa-fa:before, .fa-font-awesome:before{content: "\f2b4";}.fa-handshake-o:before{content: "\f2b5";}.fa-envelope-open:before{content: "\f2b6";}.fa-envelope-open-o:before{content: "\f2b7";}.fa-linode:before{content: "\f2b8";}.fa-address-book:before{content: "\f2b9";}.fa-address-book-o:before{content: "\f2ba";}.fa-vcard:before, .fa-address-card:before{content: "\f2bb";}.fa-vcard-o:before, .fa-address-card-o:before{content: "\f2bc";}.fa-user-circle:before{content: "\f2bd";}.fa-user-circle-o:before{content: "\f2be";}.fa-user-o:before{content: "\f2c0";}.fa-id-badge:before{content: "\f2c1";}.fa-drivers-license:before, .fa-id-card:before{content: "\f2c2";}.fa-drivers-license-o:before, .fa-id-card-o:before{content: "\f2c3";}.fa-quora:before{content: "\f2c4";}.fa-free-code-camp:before{content: "\f2c5";}.fa-telegram:before{content: "\f2c6";}.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before{content: "\f2c7";}.fa-thermometer-3:before, .fa-thermometer-three-quarters:before{content: "\f2c8";}.fa-thermometer-2:before, .fa-thermometer-half:before{content: "\f2c9";}.fa-thermometer-1:before, .fa-thermometer-quarter:before{content: "\f2ca";}.fa-thermometer-0:before, .fa-thermometer-empty:before{content: "\f2cb";}.fa-shower:before{content: "\f2cc";}.fa-bathtub:before, .fa-s15:before, .fa-bath:before{content: "\f2cd";}.fa-podcast:before{content: "\f2ce";}.fa-window-maximize:before{content: "\f2d0";}.fa-window-minimize:before{content: "\f2d1";}.fa-window-restore:before{content: "\f2d2";}.fa-times-rectangle:before, .fa-window-close:before{content: "\f2d3";}.fa-times-rectangle-o:before, .fa-window-close-o:before{content: "\f2d4";}.fa-bandcamp:before{content: "\f2d5";}.fa-grav:before{content: "\f2d6";}.fa-etsy:before{content: "\f2d7";}.fa-imdb:before{content: "\f2d8";}.fa-ravelry:before{content: "\f2d9";}.fa-eercast:before{content: "\f2da";}.fa-microchip:before{content: "\f2db";}.fa-snowflake-o:before{content: "\f2dc";}.fa-superpowers:before{content: "\f2dd";}.fa-wpexplorer:before{content: "\f2de";}.fa-meetup:before{content: "\f2e0";}.visually-hidden{position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.visually-hidden-focusable:active, .visually-hidden-focusable:focus{position: static; width: auto; height: auto; margin: 0; overflow: visible; clip: auto;}

/* /web/static/src/scss/fontawesome_overridden.scss */
 @font-face{font-family: 'FontAwesome-tiktok-only'; src: url("/web/static/src/scss/../../fonts/tiktok_only.woff"); font-weight: normal; font-style: normal; font-display: block;}@font-face{font-family: 'FontAwesome-twitter-x-only'; src: url("/web/static/src/scss/../../fonts/twitter_x_only.woff"); font-weight: normal; font-style: normal; font-display: block;}.fa.fa-tiktok{font-family: 'FontAwesome-tiktok-only' !important;}.fa.fa-tiktok:before{content: '\e07b';}.fa-twitter.fa{font-family: 'FontAwesome-twitter-x-only' !important;}.fa-twitter.fa:before{content: '\e800';}.fa-twitter-square.fa{font-family: 'FontAwesome-twitter-x-only' !important;}.fa-twitter-square.fa:before{content: '\e803';}.o_rtl .fa.fa-align-right, .o_rtl .fa.fa-align-left, .o_rtl .fa.fa-chevron-right, .o_rtl .fa.fa-chevron-left, .o_rtl .fa.fa-arrow-right, .o_rtl .fa.fa-arrow-left, .o_rtl .fa.fa-hand-o-right, .o_rtl .fa.fa-hand-o-left, .o_rtl .fa.fa-arrow-circle-right, .o_rtl .fa.fa-arrow-circle-left, .o_rtl .fa.fa-caret-right, .o_rtl .fa.fa-caret-left, .o_rtl .fa.fa-rotate-right, .o_rtl .fa.fa-rotate-left, .o_rtl .fa.fa-angle-double-right, .o_rtl .fa.fa-angle-double-left, .o_rtl .fa.fa-angle-right, .o_rtl .fa.fa-angle-left, .o_rtl .fa.fa-quote-right, .o_rtl .fa.fa-quote-left, .o_rtl .fa.fa-chevron-circle-right, .o_rtl .fa.fa-chevron-circle-left, .o_rtl .fa.fa-long-arrow-right, .o_rtl .fa.fa-long-arrow-left, .o_rtl .fa.fa-toggle-right, .o_rtl .fa.fa-toggle-left, .o_rtl .fa.fa-caret-square-o-right, .o_rtl .fa.fa-arrow-circle-o-left, .o_rtl .fa.fa-arrow-circle-o-right, .o_rtl .fa.fa-caret-square-o-left{transform: rotate(180deg);}

/* /web/static/lib/odoo_ui_icons/style.css */
@font-face{font-family: 'odoo_ui_icons'; src: url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2') format('woff2'), url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.oi{display: inline-block; font-family: 'odoo_ui_icons'; speak: never; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.oi-view-pivot:before{content: '\e800';}.oi-text-break:before{content: '\e801';}.oi-text-inline:before{content: '\e802';}.oi-voip:before{content: '\e803';}.oi-odoo:before{content: '\e806';}.oi-search:before{content: '\e808';}.oi-group:before{content: '\e80a';}.oi-settings-adjust:before{content: '\e80c';}.oi-apps:before{content: '\e80d';}.oi-panel-right:before{content: '\e810';}.oi-launch:before{content: '\e812';}.oi-studio:before{content: '\e813';}.oi-view-kanban:before{content: '\e814';}.oi-text-wrap:before{content: '\e815';}.oi-view-cohort:before{content: '\e816';}.oi-view-list:before{content: '\e817';}.oi-gif-picker:before{content: '\e82e';}.oi-chevron-down:before{content: '\e839';}.oi-chevron-left:before{content: '\e83a';}.oi-chevron-right:before{content: '\e83b';}.oi-chevron-up:before{content: '\e83c';}.oi-arrows-h:before{content: '\e83d';}.oi-arrows-v:before{content: '\e83e';}.oi-arrow-down-left:before{content: '\e83f';}.oi-arrow-down-right:before{content: '\e840';}.oi-arrow-down:before{content: '\e841';}.oi-arrow-left:before{content: '\e842';}.oi-arrow-right:before{content: '\e843';}.oi-arrow-up-left:before{content: '\e844';}.oi-arrow-up-right:before{content: '\e845';}.oi-arrow-up:before{content: '\e846';}.oi-draggable:before{content: '\e847';}.oi-view:before{content: '\e861';}.oi-archive:before{content: '\e862';}.oi-unarchive:before{content: '\e863';}.oi-text-effect:before{content: '\e827';}.oi-smile-add:before{content: '\e84e';}.oi-close:before{content: '\e852';}.oi-food-delivery:before{content: '\e82a';}.o_rtl .oi-chevron-left, .o_rtl .oi-chevron-right, .o_rtl .oi-arrow-down-left, .o_rtl .oi-arrow-down-right, .o_rtl .oi-arrow-left, .o_rtl .oi-arrow-right, .o_rtl .oi-arrow-up-left, .o_rtl .oi-arrow-up-right{transform: rotate(180deg);}

/* /web/static/fonts/fonts.scss */
 @font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Hai-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.svg#Lato") format("svg"); font-weight: 100; font-style: normal;}@font-face{font-family: "Lato-Hai"; src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Hai-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.svg#Lato") format("svg"); font-weight: 100; font-style: italic;}@font-face{font-family: "Lato-HaiIta"; src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Lig-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.svg#Lato") format("svg"); font-weight: 300; font-style: normal;}@font-face{font-family: "Lato-Lig"; src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Lig-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.svg#Lato") format("svg"); font-weight: 300; font-style: italic;}@font-face{font-family: "Lato-LigIta"; src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Reg-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.svg#Lato") format("svg"); font-weight: 400; font-style: normal;}@font-face{font-family: "Lato-Reg"; src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Reg-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.svg#Lato") format("svg"); font-weight: 400; font-style: italic;}@font-face{font-family: "Lato-RegIta"; src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bol-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.svg#Lato") format("svg"); font-weight: 700; font-style: normal;}@font-face{font-family: "Lato-Bol"; src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bol-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.svg#Lato") format("svg"); font-weight: 700; font-style: italic;}@font-face{font-family: "Lato-BolIta"; src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bla-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.svg#Lato") format("svg"); font-weight: 900; font-style: normal;}@font-face{font-family: "Lato-Bla"; src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bla-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.svg#Lato") format("svg"); font-weight: 900; font-style: italic;}@font-face{font-family: "Lato-BlaIta"; src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: "Montserrat"; src: url("/web/static/fonts/./google/Montserrat/Montserrat-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Open_Sans"; src: url("/web/static/fonts/./google/Open_Sans/Open_Sans-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Oswald"; src: url("/web/static/fonts/./google/Oswald/Oswald-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Raleway"; src: url("/web/static/fonts/./google/Raleway/Raleway-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Roboto"; src: url("/web/static/fonts/./google/Roboto/Roboto-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Tajawal"; src: url("/web/static/fonts/./google/Tajawal/Tajawal-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Fira_Mono"; src: url("/web/static/fonts/./google/Fira_Mono/Fira_Mono-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}

/* /web/static/src/webclient/actions/reports/bootstrap_review_report.scss */
 .bg-primary{background-color: RGB(113, 75, 103) !important;}.bg-secondary{background-color: RGB(216, 218, 221) !important;}.bg-success{background-color: RGB(40, 167, 69) !important;}.bg-info{background-color: RGB(23, 162, 184) !important;}.bg-warning{background-color: RGB(233, 157, 0) !important;}.bg-danger{background-color: RGB(212, 76, 89) !important;}.bg-light{background-color: RGB(255, 255, 255) !important;}.bg-dark{background-color: RGB(17, 24, 39) !important;}.bg-black{background-color: RGB(0, 0, 0) !important;}.bg-white{background-color: RGB(255, 255, 255) !important;}.bg-body{background-color: RGB(255, 255, 255) !important;}.bg-100{background-color: #F9FAFB !important;}.bg-200{background-color: #e7e9ed !important;}.bg-300{background-color: #d8dadd !important;}.bg-400{background-color: #9a9ca5 !important;}.bg-500{background-color: #7c7f89 !important;}.bg-600{background-color: #5f636f !important;}.bg-700{background-color: #374151 !important;}.bg-800{background-color: #1F2937 !important;}.bg-900{background-color: #111827 !important;}.bg-white-85{background-color: rgba(255, 255, 255, 0.85) !important;}.bg-white-75{background-color: rgba(255, 255, 255, 0.75) !important;}.bg-white-50{background-color: rgba(255, 255, 255, 0.5) !important;}.bg-white-25{background-color: rgba(255, 255, 255, 0.25) !important;}.bg-black-75{background-color: rgba(0, 0, 0, 0.75) !important;}.bg-black-50{background-color: rgba(0, 0, 0, 0.5) !important;}.bg-black-25{background-color: rgba(0, 0, 0, 0.25) !important;}.bg-black-15{background-color: rgba(0, 0, 0, 0.15) !important;}.text-primary{color: #714B67 !important;}.text-secondary{color: #d8dadd !important;}.text-success{color: #008818 !important;}.text-info{color: #0180a5 !important;}.text-warning{color: #9a6b01 !important;}.text-danger{color: #d23f3a !important;}.text-light{color: #FFF !important;}.text-dark{color: #111827 !important;}.text-100{color: #F9FAFB !important;}.text-200{color: #e7e9ed !important;}.text-300{color: #d8dadd !important;}.text-400{color: #9a9ca5 !important;}.text-500{color: #7c7f89 !important;}.text-600{color: #5f636f !important;}.text-700{color: #374151 !important;}.text-800{color: #1F2937 !important;}.text-900{color: #111827 !important;}.text-white-85{color: rgba(255, 255, 255, 0.85) !important;}.text-white-75{color: rgba(255, 255, 255, 0.75) !important;}.text-white-50{color: rgba(255, 255, 255, 0.5) !important;}.text-white-25{color: rgba(255, 255, 255, 0.25) !important;}.text-black-75{color: rgba(0, 0, 0, 0.75) !important;}.text-black-50{color: rgba(0, 0, 0, 0.5) !important;}.text-black-25{color: rgba(0, 0, 0, 0.25) !important;}.text-black-15{color: rgba(0, 0, 0, 0.15) !important;}.text-body{color: #111827 !important;}.text-muted{color: #5f636f !important;}.jumbotron, .panel, .carousel, section{page-break-inside: avoid;}.d-print-none{display: none;}.col{-webkit-box-flex: 1; flex: 1 0 0%;}.report-wrapping-flexbox{display: block !important;}.report-wrapping-flexbox > .col{float: left;}.footer .row, .footer .col-lg-3, .footer .col-lg-4, .footer .col-lg-6{-webkit-box-flex: 1 !important;}.footer .col-lg-3, .footer .col-lg-4, .footer .col-lg-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.footer .col-lg-3{width: 25%;}.footer .col-lg-4{width: 33.33333333%;}.footer .col-lg-6{width: 50%;}.container, .o_container_small{padding-right: 16px; padding-left: 16px;}.table > :not(caption) > * > *{color: #111827; background-color: transparent;}.table-borderless tbody, .table-borderless thead, .table-borderless tfoot, .table-borderless tr, .table-borderless td, .table-borderless th{border: 0 none;}.table-borderless > :not(:first-child){border-top-style: none;}.flex-column{-webkit-box-orient: vertical;}.justify-content-center{-webkit-box-pack: center;}.justify-content-between{-webkit-box-pack: justify;}.align-items-start{-webkit-box-align: start;}.align-items-center{-webkit-box-align: center;}.align-items-between{-webkit-box-align: justify;}.align-items-end{-webkit-box-align: end;}.flex-grow-0{-webkit-box-flex: 0;}.flex-grow-1{-webkit-box-flex: 1;}.flex-shrink-0{-webkit-box-flex-group: 0;}.flex-shrink-1{-webkit-box-flex-group: 1;}

/* /web/static/src/webclient/actions/reports/report.scss */
 html, body{height: 100%; direction: ltr;}body{color: #111827; word-wrap: break-word; font-family: "Lato"; line-height: 1.5;}.o_body_html, .o_body_pdf.o_css_margins{padding: 0 11mm;}.o_body_html .header, .o_body_pdf.o_css_margins .header{padding-top: 11mm;}.o_body_html .footer > .o_footer_content, .o_body_pdf.o_css_margins .footer > .o_footer_content{padding-bottom: 11mm;}span.o_force_ltr{display: inline;}.o_force_ltr, .o_field_phone{unicode-bidi: embed; direction: ltr;}.zero_min_height{min-height: 0px !important;}.o_bold{font-weight: bolder;}.o_black_border tr th{border-bottom: 2px solid black !important;}ul.o_checklist > li{list-style: none; position: relative; margin-left: 20px;}ul.o_checklist > li:not(.oe-nested):before{content: ''; position: absolute; left: -20px; display: block; height: 14px; width: 14px; top: 1px; border: 1px solid; cursor: pointer;}ul.o_checklist > li.o_checked:after{content: "✓"; transition: opacity .5s; position: absolute; left: -18px; top: -1px; opacity: 1;}blockquote{padding: 8px 16px; border-left: 5px solid; border-color: #d8dadd; font-style: italic;}li.oe-nested{display: block;}.o_portal_address span[itemprop="telephone"]{white-space: nowrap; display: inline-block;}.o_report_layout_background{background-size: contains; background-position: center 300px; background-repeat: no-repeat;}.o_company_logo{max-height: 6rem; max-width: 12rem;}.o_company_logo_small{max-height: 4rem;}.o_company_logo_big{max-height: 8rem; max-width: 16rem;}.o_company_tagline p{margin-bottom: 0;}div[name=comment] p, div[name=address] p{margin-bottom: 0;}.o_snail_mail .address{padding-top: 42px;}.o_snail_mail .address div[name="address"] > address{margin-bottom: 16px !important;}.o_snail_mail .o_followup_address{padding-top: 42px;}.o_snail_mail .o_followup_address div[itemscope="itemscope"] > div:first-child{margin-bottom: 16px;}span[itemprop="streetAddress"]{white-space: normal;}.display-1-fs{font-size: 6rem;}.display-2-fs{font-size: 5.5rem;}.display-3-fs{font-size: 4.5rem;}.display-4-fs{font-size: 3.5rem;}.h1-fs{font-size: 2.5rem;}.h2-fs{font-size: 2rem;}.h3-fs{font-size: 1.75rem;}.h4-fs{font-size: 1.5rem;}.h5-fs{font-size: 1.25rem;}.h6-fs{font-size: 1rem;}.alert{padding: 16px 16px; margin-bottom: 1rem; color: inherit; background-color: transparent; border: 0 solid transparent; border-radius: 0.25rem;}.alert-heading{color: inherit;}.alert-link{font-weight: 700; color: inherit;}.alert-primary{color: #2d1e29; background-color: #e3dbe1; border-color: #c6b7c2;}.alert-secondary{color: #565758; background-color: #f7f8f8; border-color: #eff0f1;}.alert-success{color: #10431c; background-color: #d4edda; border-color: #a9dcb5;}.alert-info{color: #09414a; background-color: #d1ecf1; border-color: #a2dae3;}.alert-warning{color: #5d3f00; background-color: #fbebcc; border-color: #f6d899;}.alert-danger{color: #551e24; background-color: #f6dbde; border-color: #eeb7bd;}.alert-light{color: #374151; background-color: #fcfdfd; border-color: #e7e9ed;}.alert-dark{color: #374151; background-color: #9a9ca5; border-color: #7c7f89;}hr{border: 1px solid;}.btn{font-weight: 700; line-height: 1.5; color: var(--body-color); background-color: transparent; border: 1px solid transparent; padding: 0.625rem 0.3125rem; font-size: 1rem; border-radius: 0.25rem;}.btn-primary{background-color: #714B67; border: 1px solid #714B67; color: #FFF;}.btn-fill-primary{background-color: #714B67; border: 1px solid #714B67; color: #FFF;}.btn-secondary{background-color: #e7e9ed; border: 1px solid #e7e9ed; color: #374151;}.btn-fill-secondary{background-color: #e7e9ed; border: 1px solid #e7e9ed; color: #374151;}.btn-light{background-color: #FFF; border: 1px solid #FFF; color: #374151;}.btn-fill-light{background-color: #FFF; border: 1px solid #FFF; color: #374151;}.btn-outline-secondary{border: 1px solid #d8dadd; color: #374151;}.btn-outline-primary{border: 1px solid #714B67; color: #714B67;}.btn-success{background-color: #28a745; border: 1px solid #28a745; color: #28a745;}.btn-outline-success{border: 1px solid #28a745; color: #28a745;}.btn-info{background-color: #17a2b8; border: 1px solid #17a2b8; color: #17a2b8;}.btn-outline-info{border: 1px solid #17a2b8; color: #17a2b8;}.btn-warning{background-color: #e99d00; border: 1px solid #e99d00; color: #e99d00;}.btn-outline-warning{border: 1px solid #e99d00; color: #e99d00;}.btn-danger{background-color: #d44c59; border: 1px solid #d44c59; color: #d44c59;}.btn-outline-danger{border: 1px solid #d44c59; color: #d44c59;}.btn-outline-light{border: 1px solid #FFF; color: #FFF;}.btn-dark{background-color: #111827; border: 1px solid #111827; color: #111827;}.btn-outline-dark{border: 1px solid #111827; color: #111827;}

/* /web/static/src/webclient/actions/reports/report_tables.scss */
 table.table td{vertical-align: top;}table.table th{text-align: left; vertical-align: middle;}div#total{page-break-inside: avoid;}.o_table_standard table:not(.o_ignore_layout_styling) thead{border-bottom: 1px solid #111827;}.o_table_standard table:not(.o_ignore_layout_styling) th:first-child, .o_table_standard table:not(.o_ignore_layout_styling) td:first-child{padding-left: 0;}.o_table_standard table:not(.o_ignore_layout_styling) th:last-child, .o_table_standard table:not(.o_ignore_layout_styling) td:last-child{padding-right: 0;}.o_table_standard .o_total_table:not(.o_ignore_layout_styling), .o_table_standard .o_total_table:not(.o_ignore_layout_styling) .o_total{border-top: 1px solid #111827;}.o_table_bold table:not(.o_ignore_layout_styling) thead th{border-top: 3px solid #111827; text-transform: uppercase;}.o_table_bold table:not(.o_ignore_layout_styling) tbody tr:last-child td{border-bottom: 3px solid #111827;}.o_table_bold table:not(.o_ignore_layout_styling) tbody tr td{padding: 1rem 0.5rem;}.o_table_bold .o_total_table:not(.o_ignore_layout_styling) .o_total, .o_table_bold .o_total_table:not(.o_ignore_layout_styling) .o_price_total{border-top: 1px solid #e7e9ed;}.o_table_striped table:not(.o_ignore_layout_styling) tr:not(:first-child){border-top: 1px solid #e7e9ed;}.o_table_striped table:not(.o_ignore_layout_styling) tbody tr:first-child, .o_table_striped table:not(.o_ignore_layout_styling) tbody tr.o_line_section{border-top: 1px solid #9a9ca5;}.o_table_striped table:not(.o_ignore_layout_styling) tbody tr:last-child{border-bottom: 1px solid #e7e9ed;}.o_table_striped table:not(.o_ignore_layout_styling) tbody tr:nth-child(odd) td{background-color: rgba(231, 233, 237, 0.5);}.o_table_striped table:not(.o_ignore_layout_styling) tbody tr.is-subtotal{border-bottom: 1px solid #9a9ca5;}.o_table_striped .o_total_table:not(.o_ignore_layout_styling) tr:first-child{border-top: none;}.o_table_striped .o_total_table:not(.o_ignore_layout_styling) tr:nth-child(odd) td{background-color: rgba(231, 233, 237, 0.5);}.o_table_boxed table:not(.o_ignore_layout_styling), .o_table_boxed-rounded table:not(.o_ignore_layout_styling){position: relative;}.o_table_boxed table:not(.o_ignore_layout_styling)::before, .o_table_boxed-rounded table:not(.o_ignore_layout_styling)::before{content: ''; position: absolute; top: 0; left: 0; bottom: 0; right: 0; pointer-events: none; border: 1px solid #374151;}.o_table_boxed table:not(.o_ignore_layout_styling) thead, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) thead{text-transform: uppercase;}.o_table_boxed table:not(.o_ignore_layout_styling) thead th:not(:last-child), .o_table_boxed-rounded table:not(.o_ignore_layout_styling) thead th:not(:last-child){border-right: 1px solid #374151;}.o_table_boxed table:not(.o_ignore_layout_styling) thead th, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) thead th{border-bottom: 1px solid #111827;}.o_table_boxed table:not(.o_ignore_layout_styling) tbody tr:not(:last-child) td, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody tr:not(:last-child) td{border-bottom: 1px solid #9a9ca5;}.o_table_boxed table:not(.o_ignore_layout_styling) tbody td:not(:last-child), .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody td:not(:last-child){border-right: 1px solid #9a9ca5;}.o_table_boxed table:not(.o_ignore_layout_styling) tbody.o_line_section td, .o_table_boxed table:not(.o_ignore_layout_styling) tbody.o_line_note td, .o_table_boxed table:not(.o_ignore_layout_styling) tbody.is-subtotal td, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody.o_line_section td, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody.o_line_note td, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody.is-subtotal td{border-top: 1px solid #374151; border-bottom: 1px solid #374151;}.o_table_boxed table:not(.o_ignore_layout_styling) tbody.o_line_section td, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody.o_line_section td{background-color: rgba(231, 233, 237, 0.5);}.o_table_boxed table:not(.o_ignore_layout_styling) tbody td.o_price_total, .o_table_boxed table:not(.o_ignore_layout_styling) tbody .o_taxes td, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody td.o_price_total, .o_table_boxed-rounded table:not(.o_ignore_layout_styling) tbody .o_taxes td{background-color: rgba(231, 233, 237, 0.5);}.o_table_boxed .o_total_table:not(.o_ignore_layout_styling) td, .o_table_boxed-rounded .o_total_table:not(.o_ignore_layout_styling) td{border-right: none;}.o_table_boxed .o_total_table:not(.o_ignore_layout_styling)::before, .o_table_boxed-rounded .o_total_table:not(.o_ignore_layout_styling)::before{border-top: none;}.o_table_boxed-rounded table:not(.o_ignore_layout_styling)::before{border-radius: 0.75rem;}.o_table_boxed-rounded table:not(.o_ignore_layout_styling).o_has_total_table::before{border-radius: 0.75rem 0.75rem 0 0.75rem;}.o_table_boxed-rounded table:not(.o_ignore_layout_styling) th:first-child{border-radius: 0.75rem 0 0 0;}.o_table_boxed-rounded table:not(.o_ignore_layout_styling) th:last-child{border-radius: 0 0.75rem 0 0;}.o_table_boxed-rounded table:not(.o_ignore_layout_styling) th{border-color: #d8dadd !important;}.o_table_boxed-rounded .o_total_table:not(.o_ignore_layout_styling)::before{border-radius: 0 0 0.75rem 0.75rem;}.o_table_boxed-rounded .o_total_table:not(.o_ignore_layout_styling) tr:last-child td:first-child{border-radius: 0 0 0 0.75rem;}.o_table_boxed-rounded .o_total_table:not(.o_ignore_layout_styling) tr:last-child td:last-child{border-radius: 0 0 0.75rem 0;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_bubble.scss */
 .o_report_layout_bubble #informations{margin: 0; border: 2px solid; border-radius: 0.75rem; padding: 8px 0.5rem;}.o_report_layout_bubble #informations div:first-child{padding-left: 0;}.o_report_layout_bubble #informations div:last-child{padding-right: 0;}.o_shape_bubble_1{top: -870px; right: -450px;}.o_shape_bubble_2{left: -450px; top: 0px;}.o_shape_bubble_2-html{top: -50%;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_folder.scss */
 .o_folder_adaptative_shape{top: -1px; height: 3rem;}.o_folder_adaptative_shape .o_folder_angle_shape{left: -1px;}.o_folder_adaptative_shape .o_folder_title{margin-right: 11mm;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_wave.scss */
 .o_report_layout_wave #informations{margin: 0; border-radius: 0.75rem; padding: 8px 0.5rem;}.o_report_layout_wave #informations div:first-child{padding-left: 0;}.o_report_layout_wave #informations div:last-child{padding-right: 0;}

/* web/static/asset_styles_company_report.scss */
 .o_company_1_layout{font-family: Lato;}.o_company_1_layout h2, .o_company_1_layout .h2{color: #af5c21;}.o_company_1_layout #informations strong{color: #2c6536;}.o_company_1_layout .o_total strong{color: #af5c21;}.o_company_1_layout .o_company_tagline{color: #af5c21;}.o_company_1_layout.o_report_layout_wave #informations{border-color: #2c6536; background-color: #eef3ef;}

/* /product/static/src/scss/report_label_sheet.scss */
 .o_label_sheet{margin-left: -4mm; margin-right: -4mm; overflow: hidden; width: 210mm; height: 297mm; page-break-before: always;}.o_label_sheet.o_label_dymo{font-size: 90%; width: 57mm; height: 32mm;}.o_label_sheet div{padding: 2px 4px;}.o_label_sheet div.o_label_small_text{font-size: 60%; line-height: 130%;}.o_label_sheet div.o_label_name{background-color: ghostwhite; height: 3em; overflow: hidden;}.o_label_sheet div.o_label_full{overflow: hidden; padding: 0; margin: auto;}.o_label_sheet div.o_label_left_column{float: left; font-size: .6em; overflow: hidden; width: 40%;}.o_label_sheet div.o_label_left_column.o_label_full_with{width: 100%;}.o_label_sheet div.o_label_right_column{float: right;}.o_label_sheet div.o_label_small_barcode{font-size: .6em; padding: 0 4px; line-height: normal;}.o_label_sheet strong.o_label_price{font-size: 2em;}.o_label_sheet strong.o_label_price_medium{font-size: 1.3em; line-height: normal; padding: 0; padding-right: 2mm;}.o_label_sheet strong.o_label_price_small{font-size: 0.9em; padding: 0 4px; padding-right: 2mm;}.o_label_sheet div.o_label_extra_data{overflow: hidden; height: 2.5em; padding: 0;}.o_label_sheet div.o_label_clear{clear: both;}.o_label_sheet div.o_label_4x12{padding: 0; line-height: 1; font-size: 55%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}

/* /web_editor/static/src/js/editor/odoo-editor/src/base_style.scss */
 li.oe-nested{display: block;}.o_table tr{border-color: #d8dadd;}.o_table tr td{padding: 0.5rem;}.o_text_columns{max-width: 100% !important; padding: 0 !important;}@media screen{.o_text_columns > .row{margin: 0 !important;}.o_text_columns > .row > .col-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-12:last-of-type{padding-right: 0;}}.oe-tabs{display: inline-block; white-space: pre-wrap; max-width: 40px; width: 40px;}ol{list-style-type: decimal;}ul{list-style-type: disc;}ol ol{list-style-type: lower-alpha;}ul ul{list-style-type: circle;}ol ol ol{list-style-type: lower-roman;}ul ul ul{list-style-type: square;}ol ol ol ol{list-style-type: decimal;}ul ul ul ul{list-style-type: disc;}ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}

/* /web_editor/static/src/scss/web_editor.common.scss */
 :root{--100: #F9FAFB; --200: #e7e9ed; --300: #d8dadd; --400: #9a9ca5; --500: #7c7f89; --600: #5f636f; --700: #374151; --800: #1F2937; --900: #111827; --white-85: rgba(255, 255, 255, 0.85); --white-75: rgba(255, 255, 255, 0.75); --white-50: rgba(255, 255, 255, 0.5); --white-25: rgba(255, 255, 255, 0.25); --black-75: rgba(0, 0, 0, 0.75); --black-50: rgba(0, 0, 0, 0.5); --black-25: rgba(0, 0, 0, 0.25); --black-15: rgba(0, 0, 0, 0.15); --o-cc1-text: #000; --o-cc1-headings: #000; --o-cc1-h2: #000; --o-cc1-h3: #000; --o-cc1-h4: #000; --o-cc1-h5: #000; --o-cc1-h6: #000; --o-cc1-link: #65435c; --o-cc1-btn-primary: #714B67; --o-cc1-btn-primary-text: #FFF; --o-cc1-btn-primary-border: #714B67; --o-cc1-btn-secondary: #d8dadd; --o-cc1-btn-secondary-text: #000; --o-cc1-btn-secondary-border: #d8dadd; --o-cc2-text: #000; --o-cc2-h2: #111827; --o-cc2-h3: #111827; --o-cc2-h4: #111827; --o-cc2-h5: #111827; --o-cc2-h6: #111827; --o-cc2-link: #55394e; --o-cc2-btn-primary: #714B67; --o-cc2-btn-primary-text: #FFF; --o-cc2-btn-primary-border: #714B67; --o-cc2-btn-secondary: #d8dadd; --o-cc2-btn-secondary-text: #000; --o-cc2-btn-secondary-border: #d8dadd; --o-cc3-text: #FFF; --o-cc3-headings: #FFF; --o-cc3-h2: #FFF; --o-cc3-h3: #FFF; --o-cc3-h4: #FFF; --o-cc3-h5: #FFF; --o-cc3-h6: #FFF; --o-cc3-link: #b18aa7; --o-cc3-btn-primary: #714B67; --o-cc3-btn-primary-text: #FFF; --o-cc3-btn-primary-border: #714B67; --o-cc3-btn-secondary-text: #000; --o-cc3-btn-secondary-border: #F3F2F2; --o-cc4-text: #FFF; --o-cc4-headings: #FFF; --o-cc4-h2: #FFF; --o-cc4-h3: #FFF; --o-cc4-h4: #FFF; --o-cc4-h5: #FFF; --o-cc4-h6: #FFF; --o-cc4-link: black; --o-cc4-btn-primary-text: #FFF; --o-cc4-btn-primary-border: #111827; --o-cc4-btn-secondary-text: #000; --o-cc4-btn-secondary-border: #F3F2F2; --o-cc5-text: #FFF; --o-cc5-h2: #FFFFFF; --o-cc5-h3: #FFFFFF; --o-cc5-h4: #FFFFFF; --o-cc5-h5: #FFFFFF; --o-cc5-h6: #FFFFFF; --o-cc5-link: #b18aa7; --o-cc5-btn-primary: #714B67; --o-cc5-btn-primary-text: #FFF; --o-cc5-btn-primary-border: #714B67; --o-cc5-btn-secondary-text: #000; --o-cc5-btn-secondary-border: #F3F2F2; --o-grid-gutter-width: 32px; --o-md-container-max-width: 720px; --o-we-content-to-translate-color: rgba(255, 255, 90, 0.5); --o-we-translated-content-color: rgba(120, 215, 110, 0.5); --o-system-fonts: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; --display-1-font-size: 5rem; --display-2-font-size: 4.5rem; --display-3-font-size: 4rem; --display-4-font-size: 3.5rem; --h1-font-size: 2.5rem; --h2-font-size: 2rem; --h3-font-size: 1.75rem; --h4-font-size: 1.5rem; --h5-font-size: 1.25rem; --h6-font-size: 1rem; --font-size-base: 1rem; --small-font-size: 0.8125rem;}html, body{position: relative; width: 100%; height: 100%;}pre{padding: 8px 16px; border: 1px solid #111827; border-radius: 0.25rem; background-color: #F9FAFB; color: #111827;}*[contenteditable=true]{outline: none;}[contenteditable]{overflow-wrap: unset !important;}.css_non_editable_mode_hidden{display: none !important;}.editor_enable .css_editable_mode_hidden{display: none !important;}#wrapwrap table.table.table-bordered, .o_editable table.table.table-bordered{table-layout: fixed;}#wrapwrap table.table.table-bordered td, .o_editable table.table.table-bordered td{min-width: 20px;}@media (max-width: 767.98px){#wrapwrap .table-responsive > table.table, .o_editable .table-responsive > table.table{table-layout: auto;}}ul.o_checklist{list-style: none;}ul.o_checklist > li{list-style: none; position: relative; margin-left: 20px; margin-right: 20px;}ul.o_checklist > li:not(.oe-nested)::before{content: ''; position: absolute; left: -20px; display: block; height: 13px; width: 13px; top: 4px; border: 1px solid; text-align: center; cursor: pointer;}ul.o_checklist > li.o_checked{text-decoration: line-through;}ul.o_checklist > li.o_checked::before{content: "✓"; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; padding-left: 1px ; padding-top: 1px;}ul.o_checklist[dir="rtl"] li:not(.oe-nested)::before{left: auto; right: -20px; text-align: right;}ul.o_checklist[dir="ltr"] li:not(.oe-nested)::before{right: auto; left: -20px; text-align: left;}ol > li.o_indent, ul > li.o_indent{margin-left: 0; list-style: none;}ol > li.o_indent::before, ul > li.o_indent::before{content: none;}.o_stars .fa.fa-star{color: gold;}img.o_we_custom_image{display: inline-block;}img.shadow{box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);}img.padding-small, .img.padding-small, span.fa.padding-small, iframe.padding-small{padding: 4px;}img.padding-medium, .img.padding-medium, span.fa.padding-medium, iframe.padding-medium{padding: 8px;}img.padding-large, .img.padding-large, span.fa.padding-large, iframe.padding-large{padding: 16px;}img.padding-xl, .img.padding-xl, span.fa.padding-xl, iframe.padding-xl{padding: 32px;}img.ms-auto, img.mx-auto{display: block;}.fa-6x{font-size: 6em;}.fa-7x{font-size: 7em;}.fa-8x{font-size: 8em;}.fa-9x{font-size: 9em;}.fa-10x{font-size: 10em;}.fa.mx-auto{display: block; text-align: center;}.fa.card-img, .fa.card-img-top, .fa.card-img-bottom{width: auto;}.o_small{font-size: 0.875rem;}.display-1-fs{font-size: 5rem;}.display-2-fs{font-size: 4.5rem;}.display-3-fs{font-size: 4rem;}.display-4-fs{font-size: 3.5rem;}.h1-fs{font-size: 2.5rem;}.h2-fs{font-size: 2rem;}.h3-fs{font-size: 1.75rem;}.h4-fs{font-size: 1.5rem;}.h5-fs{font-size: 1.25rem;}.h6-fs{font-size: 1rem;}.base-fs{font-size: 1rem;}.o_small-fs{font-size: 0.8125rem;}div.media_iframe_video{margin: 0 auto; text-align: center; position: relative; overflow: hidden; min-width: 100px;}div.media_iframe_video iframe{width: 100%; height: 100%; position: absolute; top: 0; left: auto; bottom: auto; right: auto; margin: 0 auto; margin-left: -50%;}div.media_iframe_video.padding-small iframe{padding: 4px;}div.media_iframe_video.padding-medium iframe{padding: 8px;}div.media_iframe_video.padding-large iframe{padding: 16px;}div.media_iframe_video.padding-xl iframe{padding: 32px;}div.media_iframe_video .media_iframe_video_size{padding-bottom: 66.5%; position: relative; width: 100%; height: 0;}div.media_iframe_video .css_editable_mode_display{position: absolute; top: 0; left: 0; bottom: 0; right: 0; width: 100%; height: 100%; display: none; z-index: 2;}address .fa.fa-mobile-phone{margin: 0 3px 0 2px;}address .fa.fa-file-text-o{margin-right: 1px;}span[data-oe-type="monetary"]{white-space: nowrap;}ul.oe_menu_editor .oe_menu_placeholder{outline: 1px dashed #4183C4;}ul.oe_menu_editor ul{list-style: none;}ul.oe_menu_editor li div{cursor: url(/web/static/img/openhand.cur), grab;}ul.oe_menu_editor li div :active{cursor: grabbing;}.mt0{margin-top: 0px !important;}.mb0{margin-bottom: 0px !important;}.pt0{padding-top: 0px !important;}.pb0{padding-bottom: 0px !important;}.mt8{margin-top: 8px !important;}.mb8{margin-bottom: 8px !important;}.pt8{padding-top: 8px !important;}.pb8{padding-bottom: 8px !important;}.mt16{margin-top: 16px !important;}.mb16{margin-bottom: 16px !important;}.pt16{padding-top: 16px !important;}.pb16{padding-bottom: 16px !important;}.mt24{margin-top: 24px !important;}.mb24{margin-bottom: 24px !important;}.pt24{padding-top: 24px !important;}.pb24{padding-bottom: 24px !important;}.mt32{margin-top: 32px !important;}.mb32{margin-bottom: 32px !important;}.pt32{padding-top: 32px !important;}.pb32{padding-bottom: 32px !important;}.mt40{margin-top: 40px !important;}.mb40{margin-bottom: 40px !important;}.pt40{padding-top: 40px !important;}.pb40{padding-bottom: 40px !important;}.mt48{margin-top: 48px !important;}.mb48{margin-bottom: 48px !important;}.pt48{padding-top: 48px !important;}.pb48{padding-bottom: 48px !important;}.mt56{margin-top: 56px !important;}.mb56{margin-bottom: 56px !important;}.pt56{padding-top: 56px !important;}.pb56{padding-bottom: 56px !important;}.mt64{margin-top: 64px !important;}.mb64{margin-bottom: 64px !important;}.pt64{padding-top: 64px !important;}.pb64{padding-bottom: 64px !important;}.mt72{margin-top: 72px !important;}.mb72{margin-bottom: 72px !important;}.pt72{padding-top: 72px !important;}.pb72{padding-bottom: 72px !important;}.mt80{margin-top: 80px !important;}.mb80{margin-bottom: 80px !important;}.pt80{padding-top: 80px !important;}.pb80{padding-bottom: 80px !important;}.mt88{margin-top: 88px !important;}.mb88{margin-bottom: 88px !important;}.pt88{padding-top: 88px !important;}.pb88{padding-bottom: 88px !important;}.mt96{margin-top: 96px !important;}.mb96{margin-bottom: 96px !important;}.pt96{padding-top: 96px !important;}.pb96{padding-bottom: 96px !important;}.mt104{margin-top: 104px !important;}.mb104{margin-bottom: 104px !important;}.pt104{padding-top: 104px !important;}.pb104{padding-bottom: 104px !important;}.mt112{margin-top: 112px !important;}.mb112{margin-bottom: 112px !important;}.pt112{padding-top: 112px !important;}.pb112{padding-bottom: 112px !important;}.mt120{margin-top: 120px !important;}.mb120{margin-bottom: 120px !important;}.pt120{padding-top: 120px !important;}.pb120{padding-bottom: 120px !important;}.mt128{margin-top: 128px !important;}.mb128{margin-bottom: 128px !important;}.pt128{padding-top: 128px !important;}.pb128{padding-bottom: 128px !important;}.mt136{margin-top: 136px !important;}.mb136{margin-bottom: 136px !important;}.pt136{padding-top: 136px !important;}.pb136{padding-bottom: 136px !important;}.mt144{margin-top: 144px !important;}.mb144{margin-bottom: 144px !important;}.pt144{padding-top: 144px !important;}.pb144{padding-bottom: 144px !important;}.mt152{margin-top: 152px !important;}.mb152{margin-bottom: 152px !important;}.pt152{padding-top: 152px !important;}.pb152{padding-bottom: 152px !important;}.mt160{margin-top: 160px !important;}.mb160{margin-bottom: 160px !important;}.pt160{padding-top: 160px !important;}.pb160{padding-bottom: 160px !important;}.mt168{margin-top: 168px !important;}.mb168{margin-bottom: 168px !important;}.pt168{padding-top: 168px !important;}.pb168{padding-bottom: 168px !important;}.mt176{margin-top: 176px !important;}.mb176{margin-bottom: 176px !important;}.pt176{padding-top: 176px !important;}.pb176{padding-bottom: 176px !important;}.mt184{margin-top: 184px !important;}.mb184{margin-bottom: 184px !important;}.pt184{padding-top: 184px !important;}.pb184{padding-bottom: 184px !important;}.mt192{margin-top: 192px !important;}.mb192{margin-bottom: 192px !important;}.pt192{padding-top: 192px !important;}.pb192{padding-bottom: 192px !important;}.mt200{margin-top: 200px !important;}.mb200{margin-bottom: 200px !important;}.pt200{padding-top: 200px !important;}.pb200{padding-bottom: 200px !important;}.mt208{margin-top: 208px !important;}.mb208{margin-bottom: 208px !important;}.pt208{padding-top: 208px !important;}.pb208{padding-bottom: 208px !important;}.mt216{margin-top: 216px !important;}.mb216{margin-bottom: 216px !important;}.pt216{padding-top: 216px !important;}.pb216{padding-bottom: 216px !important;}.mt224{margin-top: 224px !important;}.mb224{margin-bottom: 224px !important;}.pt224{padding-top: 224px !important;}.pb224{padding-bottom: 224px !important;}.mt232{margin-top: 232px !important;}.mb232{margin-bottom: 232px !important;}.pt232{padding-top: 232px !important;}.pb232{padding-bottom: 232px !important;}.mt240{margin-top: 240px !important;}.mb240{margin-bottom: 240px !important;}.pt240{padding-top: 240px !important;}.pb240{padding-bottom: 240px !important;}.mt248{margin-top: 248px !important;}.mb248{margin-bottom: 248px !important;}.pt248{padding-top: 248px !important;}.pb248{padding-bottom: 248px !important;}.mt256{margin-top: 256px !important;}.mb256{margin-bottom: 256px !important;}.pt256{padding-top: 256px !important;}.pb256{padding-bottom: 256px !important;}.mt4{margin-top: 4px !important;}.mb4{margin-bottom: 4px !important;}.pt4{padding-top: 4px !important;}.pb4{padding-bottom: 4px !important;}.mt92{margin-top: 92px !important;}.mb92{margin-bottom: 92px !important;}.ml0{margin-left: 0px !important;}.mr0{margin-right: 0px !important;}.ml4{margin-left: 4px !important;}.mr4{margin-right: 4px !important;}.ml8{margin-left: 8px !important;}.mr8{margin-right: 8px !important;}.ml16{margin-left: 16px !important;}.mr16{margin-right: 16px !important;}.ml32{margin-left: 32px !important;}.mr32{margin-right: 32px !important;}.ml64{margin-left: 64px !important;}.mr64{margin-right: 64px !important;}a.o_underline{text-decoration: underline;}a.o_underline:hover{text-decoration: underline;}.o_nocontent_help{pointer-events: auto; max-width: 650px; margin: auto; padding: 15px; z-index: 1000; text-align: center; color: #111827; font-size: 115%;}.o_nocontent_help > p:first-of-type{margin-top: 0; color: #111827; font-weight: bold; font-size: 125%;}.o_nocontent_help a{cursor: pointer;}.o_we_search_prompt{position: relative; min-height: 250px; width: 100%; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; -webkit-box-pack: start; justify-content: flex-start;}.o_we_search_prompt > h2, .o_we_search_prompt > .h2{max-width: 500px; text-align: center; margin-left: 150px;}.o_we_search_prompt::before{transform: scale(-1, 1); content: ""; position: absolute; top: 0; left: 50px; bottom: auto; right: auto; width: 100px; height: 150px; opacity: .5; filter: var(--WebEditor__SearchPromptArrow-filter, invert(0)); background-image: url("/web_editor/static/src/img/curved_arrow.svg"); background-size: 100%; background-repeat: no-repeat;}@media (max-width: 767.98px){odoo-wysiwyg-container .btn-group{position: static;}.o_technical_modal.o_web_editor_dialog{z-index: 2001;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog{max-width: inherit !important; z-index: 2001;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-dialog, .o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .model-content{height: 100%;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .nav .nav-item.search{width: 100%;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .nav .nav-item.search .btn-group{display: -webkit-box; display: -webkit-flex; display: flex; justify-content: space-around; padding: 5px;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .font-icons-icons{text-align: center;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .form-control.o_we_search{height: inherit;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row > .o_existing_attachment_cell{flex: initial; max-width: 100%;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row > .o_existing_attachment_cell > .o_existing_attachment_remove{opacity: inherit; top: 10px;}}blockquote{padding: 8px 16px; border-left: 5px solid; border-color: #d8dadd; font-style: italic;}pre{white-space: pre-wrap;}.bg-o-color-1{background-color: #714B67 !important; color: #FFF;}.bg-o-color-1 .text-muted, .o_colored_level .bg-o-color-1 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-o-color-1:hover, a.bg-o-color-1:focus, button.bg-o-color-1:hover, button.bg-o-color-1:focus{background-color: #52374b !important; color: #FFF;}.text-o-color-1{--color: RGBA(113, 75, 103, var(--text-opacity, 1)); color: var(--color) !important;}a.text-o-color-1:hover, a.text-o-color-1:focus, a.text-o-color-1:active, a.text-o-color-1.active, button.text-o-color-1:hover, button.text-o-color-1:focus, button.text-o-color-1:active, button.text-o-color-1.active{--color: RGBA(79, 53, 72, var(--text-opacity, 1)); color: var(--color) !important;}.bg-o-color-2{background-color: #2D3142 !important; color: #FFF;}.bg-o-color-2 .text-muted, .o_colored_level .bg-o-color-2 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-o-color-2:hover, a.bg-o-color-2:focus, button.bg-o-color-2:hover, button.bg-o-color-2:focus{background-color: #181a24 !important; color: #FFF;}.text-o-color-2{--color: RGBA(45, 49, 66, var(--text-opacity, 1)); color: var(--color) !important;}a.text-o-color-2:hover, a.text-o-color-2:focus, a.text-o-color-2:active, a.text-o-color-2.active, button.text-o-color-2:hover, button.text-o-color-2:focus, button.text-o-color-2:active, button.text-o-color-2.active{--color: RGBA(32, 34, 46, var(--text-opacity, 1)); color: var(--color) !important;}.bg-o-color-3{background-color: #F3F2F2 !important; color: #000;}.bg-o-color-3 .text-muted, .o_colored_level .bg-o-color-3 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-o-color-3:hover, a.bg-o-color-3:focus, button.bg-o-color-3:hover, button.bg-o-color-3:focus{background-color: #dbd7d7 !important; color: #000;}.text-o-color-3{--color: RGBA(243, 242, 242, var(--text-opacity, 1)); color: var(--color) !important;}a.text-o-color-3:hover, a.text-o-color-3:focus, a.text-o-color-3:active, a.text-o-color-3.active, button.text-o-color-3:hover, button.text-o-color-3:focus, button.text-o-color-3:active, button.text-o-color-3.active{--color: RGBA(170, 169, 169, var(--text-opacity, 1)); color: var(--color) !important;}.bg-o-color-4{background-color: #FFFFFF !important; color: #000;}.bg-o-color-4 .text-muted, .o_colored_level .bg-o-color-4 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-o-color-4:hover, a.bg-o-color-4:focus, button.bg-o-color-4:hover, button.bg-o-color-4:focus{background-color: #e6e6e6 !important; color: #000;}.text-o-color-4{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}a.text-o-color-4:hover, a.text-o-color-4:focus, a.text-o-color-4:active, a.text-o-color-4.active, button.text-o-color-4:hover, button.text-o-color-4:focus, button.text-o-color-4:active, button.text-o-color-4.active{--color: RGBA(179, 179, 179, var(--text-opacity, 1)); color: var(--color) !important;}.bg-o-color-5{background-color: #111827 !important; color: #FFF;}.bg-o-color-5 .text-muted, .o_colored_level .bg-o-color-5 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-o-color-5:hover, a.bg-o-color-5:focus, button.bg-o-color-5:hover, button.bg-o-color-5:focus{background-color: #020203 !important; color: #FFF;}.text-o-color-5{--color: RGBA(17, 24, 39, var(--text-opacity, 1)); color: var(--color) !important;}a.text-o-color-5:hover, a.text-o-color-5:focus, a.text-o-color-5:active, a.text-o-color-5.active, button.text-o-color-5:hover, button.text-o-color-5:focus, button.text-o-color-5:active, button.text-o-color-5.active{--color: RGBA(12, 17, 27, var(--text-opacity, 1)); color: var(--color) !important;}.o_cc .dropdown-menu .dropdown-item, .o_cc .dropdown-menu .dropdown-item h6, .o_cc .dropdown-menu .dropdown-item .h6, .o_colored_level .o_cc .dropdown-menu .dropdown-item, .o_colored_level .o_cc .dropdown-menu .dropdown-item h6{color: #374151 !important;}.o_cc .dropdown-menu .dropdown-item:hover, .o_cc .dropdown-menu .dropdown-item:focus, .o_cc .dropdown-menu .dropdown-item h6:hover, .o_cc .dropdown-menu .dropdown-item .h6:hover, .o_cc .dropdown-menu .dropdown-item h6:focus, .o_cc .dropdown-menu .dropdown-item .h6:focus, .o_colored_level .o_cc .dropdown-menu .dropdown-item:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item:focus, .o_colored_level .o_cc .dropdown-menu .dropdown-item h6:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item h6:focus{color: #111827 !important;}.o_cc .dropdown-menu .dropdown-item.disabled, .o_cc .dropdown-menu .dropdown-item.disabled h6, .o_cc .dropdown-menu .dropdown-item.disabled .h6, .o_cc .dropdown-menu .dropdown-item:disabled, .o_cc .dropdown-menu .dropdown-item:disabled h6, .o_cc .dropdown-menu .dropdown-item:disabled .h6, .o_colored_level .o_cc .dropdown-menu .dropdown-item.disabled, .o_colored_level .o_cc .dropdown-menu .dropdown-item.disabled h6, .o_colored_level .o_cc .dropdown-menu .dropdown-item:disabled, .o_colored_level .o_cc .dropdown-menu .dropdown-item:disabled h6{color: rgba(55, 65, 81, 0.76) !important;}.o_cc .dropdown-menu .dropdown-item .btn-link, .o_colored_level .o_cc .dropdown-menu .dropdown-item .btn-link{color: var(--link-color);}.o_cc .dropdown-menu .dropdown-item .btn-link:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item .btn-link:hover{color: var(--link-hover-color);}.o_cc .dropdown-menu .dropdown-item .btn-link:disabled, .o_colored_level .o_cc .dropdown-menu .dropdown-item .btn-link:disabled{color: #5f636f;}.o_cc .dropdown-menu .dropdown-item-text .text-muted a, .o_colored_level .o_cc .dropdown-menu .dropdown-item-text .text-muted a{color: #017e84;}.o_cc .dropdown-menu .dropdown-item-text .text-muted a:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item-text .text-muted a:hover{color: #01585c;}.o_cc1{background-color: #FFFFFF; color: #000; --o-cc-bg: #FFFFFF;}.o_cc1 .text-muted, .o_colored_level .o_cc1 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_cc1 a:not(.btn), .o_cc1 .btn-link, .o_colored_level .o_cc1 a:not(.btn), .o_colored_level .o_cc1 .btn-link{color: #65435c;}.o_cc1 a:not(.btn):hover, .o_cc1 .btn-link:hover, .o_colored_level .o_cc1 a:not(.btn):hover, .o_colored_level .o_cc1 .btn-link:hover{color: #432c3d;}.o_cc1 .btn-fill-primary, .o_colored_level .o_cc1 .btn-fill-primary{--btn-color: #FFF; --btn-bg: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #604058; --btn-hover-border-color: #5a3c52; --btn-focus-shadow-rgb: 134, 102, 126; --btn-active-color: #FFF; --btn-active-bg: #5a3c52; --btn-active-border-color: #55384d; --btn-active-shadow: 0; --btn-disabled-color: #FFF; --btn-disabled-bg: #714B67; --btn-disabled-border-color: #714B67;}.o_cc1 .btn-outline-primary, .o_colored_level .o_cc1 .btn-outline-primary{--btn-color: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #714B67; --btn-hover-border-color: #714B67; --btn-focus-shadow-rgb: 113, 75, 103; --btn-active-color: #FFF; --btn-active-bg: #714B67; --btn-active-border-color: #714B67; --btn-active-shadow: 0; --btn-disabled-color: #714B67; --btn-disabled-bg: transparent; --btn-disabled-border-color: #714B67; --gradient: none;}.o_cc1 .btn-fill-secondary, .o_colored_level .o_cc1 .btn-fill-secondary{--btn-color: #000; --btn-bg: #d8dadd; --btn-border-color: #d8dadd; --btn-hover-color: #000; --btn-hover-bg: #dee0e2; --btn-hover-border-color: #dcdee0; --btn-focus-shadow-rgb: 184, 185, 188; --btn-active-color: #000; --btn-active-bg: #e0e1e4; --btn-active-border-color: #dcdee0; --btn-active-shadow: 0; --btn-disabled-color: #000; --btn-disabled-bg: #d8dadd; --btn-disabled-border-color: #d8dadd;}.o_cc1 .btn-outline-secondary, .o_colored_level .o_cc1 .btn-outline-secondary{--btn-color: #d8dadd; --btn-border-color: #d8dadd; --btn-hover-color: #000; --btn-hover-bg: #d8dadd; --btn-hover-border-color: #d8dadd; --btn-focus-shadow-rgb: 216, 218, 221; --btn-active-color: #000; --btn-active-bg: #d8dadd; --btn-active-border-color: #d8dadd; --btn-active-shadow: 0; --btn-disabled-color: #d8dadd; --btn-disabled-bg: transparent; --btn-disabled-border-color: #d8dadd; --gradient: none;}.o_cc1 .nav-pills .nav-link.active, .o_cc1 .nav-pills .show > .nav-link, .o_colored_level .o_cc1 .nav-pills .nav-link.active, .o_colored_level .o_cc1 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFF;}.o_cc1 .dropdown-menu .dropdown-item.active, .o_cc1 .dropdown-menu .dropdown-item.active h6, .o_cc1 .dropdown-menu .dropdown-item.active .h6, .o_cc1 .dropdown-menu .dropdown-item:active, .o_cc1 .dropdown-menu .dropdown-item:active h6, .o_cc1 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFF !important;}.o_cc1 .dropdown-menu .dropdown-item.active:hover, .o_cc1 .dropdown-menu .dropdown-item.active:focus, .o_cc1 .dropdown-menu .dropdown-item.active h6:hover, .o_cc1 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc1 .dropdown-menu .dropdown-item.active h6:focus, .o_cc1 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc1 .dropdown-menu .dropdown-item:active:hover, .o_cc1 .dropdown-menu .dropdown-item:active:focus, .o_cc1 .dropdown-menu .dropdown-item:active h6:hover, .o_cc1 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc1 .dropdown-menu .dropdown-item:active h6:focus, .o_cc1 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active h6:focus{color: #FFF !important;}.o_cc1 a.list-group-item, .o_colored_level .o_cc1 a.list-group-item{color: #714B67;}.o_cc1 a.list-group-item.active, .o_colored_level .o_cc1 a.list-group-item.active{background-color: #714B67; color: #FFF; border-color: #714B67;}.o_cc2{background-color: #F3F2F2; color: #000; --o-cc-bg: #F3F2F2;}.o_cc2 .text-muted, .o_colored_level .o_cc2 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_cc2 h1, .o_cc2 .h1, .o_cc2 h2, .o_cc2 .h2, .o_cc2 h3, .o_cc2 .h3, .o_cc2 h4, .o_cc2 .h4, .o_cc2 h5, .o_cc2 .h5, .o_cc2 h6, .o_cc2 .h6, .o_colored_level .o_cc2 h1, .o_colored_level .o_cc2 h2, .o_colored_level .o_cc2 h3, .o_colored_level .o_cc2 h4, .o_colored_level .o_cc2 h5, .o_colored_level .o_cc2 h6{color: #111827;}.o_cc2 a:not(.btn), .o_cc2 .btn-link, .o_colored_level .o_cc2 a:not(.btn), .o_colored_level .o_cc2 .btn-link{color: #55394e;}.o_cc2 a:not(.btn):hover, .o_cc2 .btn-link:hover, .o_colored_level .o_cc2 a:not(.btn):hover, .o_colored_level .o_cc2 .btn-link:hover{color: #432c3d;}.o_cc2 .btn-fill-primary, .o_colored_level .o_cc2 .btn-fill-primary{--btn-color: #FFF; --btn-bg: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #604058; --btn-hover-border-color: #5a3c52; --btn-focus-shadow-rgb: 134, 102, 126; --btn-active-color: #FFF; --btn-active-bg: #5a3c52; --btn-active-border-color: #55384d; --btn-active-shadow: 0; --btn-disabled-color: #FFF; --btn-disabled-bg: #714B67; --btn-disabled-border-color: #714B67;}.o_cc2 .btn-outline-primary, .o_colored_level .o_cc2 .btn-outline-primary{--btn-color: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #714B67; --btn-hover-border-color: #714B67; --btn-focus-shadow-rgb: 113, 75, 103; --btn-active-color: #FFF; --btn-active-bg: #714B67; --btn-active-border-color: #714B67; --btn-active-shadow: 0; --btn-disabled-color: #714B67; --btn-disabled-bg: transparent; --btn-disabled-border-color: #714B67; --gradient: none;}.o_cc2 .btn-fill-secondary, .o_colored_level .o_cc2 .btn-fill-secondary{--btn-color: #000; --btn-bg: #d8dadd; --btn-border-color: #d8dadd; --btn-hover-color: #000; --btn-hover-bg: #dee0e2; --btn-hover-border-color: #dcdee0; --btn-focus-shadow-rgb: 184, 185, 188; --btn-active-color: #000; --btn-active-bg: #e0e1e4; --btn-active-border-color: #dcdee0; --btn-active-shadow: 0; --btn-disabled-color: #000; --btn-disabled-bg: #d8dadd; --btn-disabled-border-color: #d8dadd;}.o_cc2 .btn-outline-secondary, .o_colored_level .o_cc2 .btn-outline-secondary{--btn-color: #d8dadd; --btn-border-color: #d8dadd; --btn-hover-color: #000; --btn-hover-bg: #d8dadd; --btn-hover-border-color: #d8dadd; --btn-focus-shadow-rgb: 216, 218, 221; --btn-active-color: #000; --btn-active-bg: #d8dadd; --btn-active-border-color: #d8dadd; --btn-active-shadow: 0; --btn-disabled-color: #d8dadd; --btn-disabled-bg: transparent; --btn-disabled-border-color: #d8dadd; --gradient: none;}.o_cc2 .nav-pills .nav-link.active, .o_cc2 .nav-pills .show > .nav-link, .o_colored_level .o_cc2 .nav-pills .nav-link.active, .o_colored_level .o_cc2 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFF;}.o_cc2 .dropdown-menu .dropdown-item.active, .o_cc2 .dropdown-menu .dropdown-item.active h6, .o_cc2 .dropdown-menu .dropdown-item.active .h6, .o_cc2 .dropdown-menu .dropdown-item:active, .o_cc2 .dropdown-menu .dropdown-item:active h6, .o_cc2 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFF !important;}.o_cc2 .dropdown-menu .dropdown-item.active:hover, .o_cc2 .dropdown-menu .dropdown-item.active:focus, .o_cc2 .dropdown-menu .dropdown-item.active h6:hover, .o_cc2 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc2 .dropdown-menu .dropdown-item.active h6:focus, .o_cc2 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc2 .dropdown-menu .dropdown-item:active:hover, .o_cc2 .dropdown-menu .dropdown-item:active:focus, .o_cc2 .dropdown-menu .dropdown-item:active h6:hover, .o_cc2 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc2 .dropdown-menu .dropdown-item:active h6:focus, .o_cc2 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active h6:focus{color: #FFF !important;}.o_cc2 a.list-group-item, .o_colored_level .o_cc2 a.list-group-item{color: #714B67;}.o_cc2 a.list-group-item.active, .o_colored_level .o_cc2 a.list-group-item.active{background-color: #714B67; color: #FFF; border-color: #714B67;}.o_cc3{background-color: #2D3142; color: #FFF; --o-cc-bg: #2D3142;}.o_cc3 .text-muted, .o_colored_level .o_cc3 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_cc3 a:not(.btn), .o_cc3 .btn-link, .o_colored_level .o_cc3 a:not(.btn), .o_colored_level .o_cc3 .btn-link{color: #b18aa7;}.o_cc3 a:not(.btn):hover, .o_cc3 .btn-link:hover, .o_colored_level .o_cc3 a:not(.btn):hover, .o_colored_level .o_cc3 .btn-link:hover{color: #905f83;}.o_cc3 .btn-fill-primary, .o_colored_level .o_cc3 .btn-fill-primary{--btn-color: #FFF; --btn-bg: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #604058; --btn-hover-border-color: #5a3c52; --btn-focus-shadow-rgb: 134, 102, 126; --btn-active-color: #FFF; --btn-active-bg: #5a3c52; --btn-active-border-color: #55384d; --btn-active-shadow: 0; --btn-disabled-color: #FFF; --btn-disabled-bg: #714B67; --btn-disabled-border-color: #714B67;}.o_cc3 .btn-outline-primary, .o_colored_level .o_cc3 .btn-outline-primary{--btn-color: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #714B67; --btn-hover-border-color: #714B67; --btn-focus-shadow-rgb: 113, 75, 103; --btn-active-color: #FFF; --btn-active-bg: #714B67; --btn-active-border-color: #714B67; --btn-active-shadow: 0; --btn-disabled-color: #714B67; --btn-disabled-bg: transparent; --btn-disabled-border-color: #714B67; --gradient: none;}.o_cc3 .btn-fill-secondary, .o_colored_level .o_cc3 .btn-fill-secondary{--btn-color: #000; --btn-bg: #F3F2F2; --btn-border-color: #F3F2F2; --btn-hover-color: #000; --btn-hover-bg: #f5f4f4; --btn-hover-border-color: #f4f3f3; --btn-focus-shadow-rgb: 207, 206, 206; --btn-active-color: #000; --btn-active-bg: whitesmoke; --btn-active-border-color: #f4f3f3; --btn-active-shadow: 0; --btn-disabled-color: #000; --btn-disabled-bg: #F3F2F2; --btn-disabled-border-color: #F3F2F2;}.o_cc3 .btn-outline-secondary, .o_colored_level .o_cc3 .btn-outline-secondary{--btn-color: #F3F2F2; --btn-border-color: #F3F2F2; --btn-hover-color: #000; --btn-hover-bg: #F3F2F2; --btn-hover-border-color: #F3F2F2; --btn-focus-shadow-rgb: 243, 242, 242; --btn-active-color: #000; --btn-active-bg: #F3F2F2; --btn-active-border-color: #F3F2F2; --btn-active-shadow: 0; --btn-disabled-color: #F3F2F2; --btn-disabled-bg: transparent; --btn-disabled-border-color: #F3F2F2; --gradient: none;}.o_cc3 .nav-pills .nav-link.active, .o_cc3 .nav-pills .show > .nav-link, .o_colored_level .o_cc3 .nav-pills .nav-link.active, .o_colored_level .o_cc3 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFF;}.o_cc3 .dropdown-menu .dropdown-item.active, .o_cc3 .dropdown-menu .dropdown-item.active h6, .o_cc3 .dropdown-menu .dropdown-item.active .h6, .o_cc3 .dropdown-menu .dropdown-item:active, .o_cc3 .dropdown-menu .dropdown-item:active h6, .o_cc3 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFF !important;}.o_cc3 .dropdown-menu .dropdown-item.active:hover, .o_cc3 .dropdown-menu .dropdown-item.active:focus, .o_cc3 .dropdown-menu .dropdown-item.active h6:hover, .o_cc3 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc3 .dropdown-menu .dropdown-item.active h6:focus, .o_cc3 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc3 .dropdown-menu .dropdown-item:active:hover, .o_cc3 .dropdown-menu .dropdown-item:active:focus, .o_cc3 .dropdown-menu .dropdown-item:active h6:hover, .o_cc3 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc3 .dropdown-menu .dropdown-item:active h6:focus, .o_cc3 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active h6:focus{color: #FFF !important;}.o_cc3 a.list-group-item, .o_colored_level .o_cc3 a.list-group-item{color: #714B67;}.o_cc3 a.list-group-item.active, .o_colored_level .o_cc3 a.list-group-item.active{background-color: #714B67; color: #FFF; border-color: #714B67;}.o_cc4{background-color: #714B67; color: #FFF; --o-cc-bg: #714B67;}.o_cc4 .text-muted, .o_colored_level .o_cc4 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_cc4 a:not(.btn), .o_cc4 .btn-link, .o_colored_level .o_cc4 a:not(.btn), .o_colored_level .o_cc4 .btn-link{color: black;}.o_cc4 a:not(.btn):hover, .o_cc4 .btn-link:hover, .o_colored_level .o_cc4 a:not(.btn):hover, .o_colored_level .o_cc4 .btn-link:hover{color: black;}.o_cc4 .btn-fill-primary, .o_colored_level .o_cc4 .btn-fill-primary{--btn-color: #FFF; --btn-bg: #111827; --btn-border-color: #111827; --btn-hover-color: #FFF; --btn-hover-bg: #0e1421; --btn-hover-border-color: #0e131f; --btn-focus-shadow-rgb: 53, 59, 71; --btn-active-color: #FFF; --btn-active-bg: #0e131f; --btn-active-border-color: #0d121d; --btn-active-shadow: 0; --btn-disabled-color: #FFF; --btn-disabled-bg: #111827; --btn-disabled-border-color: #111827;}.o_cc4 .btn-outline-primary, .o_colored_level .o_cc4 .btn-outline-primary{--btn-color: #111827; --btn-border-color: #111827; --btn-hover-color: #FFF; --btn-hover-bg: #111827; --btn-hover-border-color: #111827; --btn-focus-shadow-rgb: 17, 24, 39; --btn-active-color: #FFF; --btn-active-bg: #111827; --btn-active-border-color: #111827; --btn-active-shadow: 0; --btn-disabled-color: #111827; --btn-disabled-bg: transparent; --btn-disabled-border-color: #111827; --gradient: none;}.o_cc4 .btn-fill-secondary, .o_colored_level .o_cc4 .btn-fill-secondary{--btn-color: #000; --btn-bg: #F3F2F2; --btn-border-color: #F3F2F2; --btn-hover-color: #000; --btn-hover-bg: #f5f4f4; --btn-hover-border-color: #f4f3f3; --btn-focus-shadow-rgb: 207, 206, 206; --btn-active-color: #000; --btn-active-bg: whitesmoke; --btn-active-border-color: #f4f3f3; --btn-active-shadow: 0; --btn-disabled-color: #000; --btn-disabled-bg: #F3F2F2; --btn-disabled-border-color: #F3F2F2;}.o_cc4 .btn-outline-secondary, .o_colored_level .o_cc4 .btn-outline-secondary{--btn-color: #F3F2F2; --btn-border-color: #F3F2F2; --btn-hover-color: #000; --btn-hover-bg: #F3F2F2; --btn-hover-border-color: #F3F2F2; --btn-focus-shadow-rgb: 243, 242, 242; --btn-active-color: #000; --btn-active-bg: #F3F2F2; --btn-active-border-color: #F3F2F2; --btn-active-shadow: 0; --btn-disabled-color: #F3F2F2; --btn-disabled-bg: transparent; --btn-disabled-border-color: #F3F2F2; --gradient: none;}.o_cc4 .nav-pills .nav-link.active, .o_cc4 .nav-pills .show > .nav-link, .o_colored_level .o_cc4 .nav-pills .nav-link.active, .o_colored_level .o_cc4 .nav-pills .show > .nav-link{background-color: #111827; color: #FFF;}.o_cc4 .dropdown-menu .dropdown-item.active, .o_cc4 .dropdown-menu .dropdown-item.active h6, .o_cc4 .dropdown-menu .dropdown-item.active .h6, .o_cc4 .dropdown-menu .dropdown-item:active, .o_cc4 .dropdown-menu .dropdown-item:active h6, .o_cc4 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active h6{background-color: #111827; color: #FFF !important;}.o_cc4 .dropdown-menu .dropdown-item.active:hover, .o_cc4 .dropdown-menu .dropdown-item.active:focus, .o_cc4 .dropdown-menu .dropdown-item.active h6:hover, .o_cc4 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc4 .dropdown-menu .dropdown-item.active h6:focus, .o_cc4 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc4 .dropdown-menu .dropdown-item:active:hover, .o_cc4 .dropdown-menu .dropdown-item:active:focus, .o_cc4 .dropdown-menu .dropdown-item:active h6:hover, .o_cc4 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc4 .dropdown-menu .dropdown-item:active h6:focus, .o_cc4 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active h6:focus{color: #FFF !important;}.o_cc4 a.list-group-item, .o_colored_level .o_cc4 a.list-group-item{color: #111827;}.o_cc4 a.list-group-item.active, .o_colored_level .o_cc4 a.list-group-item.active{background-color: #111827; color: #FFF; border-color: #111827;}.o_cc5{background-color: #111827; color: #FFF; --o-cc-bg: #111827;}.o_cc5 .text-muted, .o_colored_level .o_cc5 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_cc5 h1, .o_cc5 .h1, .o_cc5 h2, .o_cc5 .h2, .o_cc5 h3, .o_cc5 .h3, .o_cc5 h4, .o_cc5 .h4, .o_cc5 h5, .o_cc5 .h5, .o_cc5 h6, .o_cc5 .h6, .o_colored_level .o_cc5 h1, .o_colored_level .o_cc5 h2, .o_colored_level .o_cc5 h3, .o_colored_level .o_cc5 h4, .o_colored_level .o_cc5 h5, .o_colored_level .o_cc5 h6{color: #FFFFFF;}.o_cc5 a:not(.btn), .o_cc5 .btn-link, .o_colored_level .o_cc5 a:not(.btn), .o_colored_level .o_cc5 .btn-link{color: #b18aa7;}.o_cc5 a:not(.btn):hover, .o_cc5 .btn-link:hover, .o_colored_level .o_cc5 a:not(.btn):hover, .o_colored_level .o_cc5 .btn-link:hover{color: #905f83;}.o_cc5 .btn-fill-primary, .o_colored_level .o_cc5 .btn-fill-primary{--btn-color: #FFF; --btn-bg: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #604058; --btn-hover-border-color: #5a3c52; --btn-focus-shadow-rgb: 134, 102, 126; --btn-active-color: #FFF; --btn-active-bg: #5a3c52; --btn-active-border-color: #55384d; --btn-active-shadow: 0; --btn-disabled-color: #FFF; --btn-disabled-bg: #714B67; --btn-disabled-border-color: #714B67;}.o_cc5 .btn-outline-primary, .o_colored_level .o_cc5 .btn-outline-primary{--btn-color: #714B67; --btn-border-color: #714B67; --btn-hover-color: #FFF; --btn-hover-bg: #714B67; --btn-hover-border-color: #714B67; --btn-focus-shadow-rgb: 113, 75, 103; --btn-active-color: #FFF; --btn-active-bg: #714B67; --btn-active-border-color: #714B67; --btn-active-shadow: 0; --btn-disabled-color: #714B67; --btn-disabled-bg: transparent; --btn-disabled-border-color: #714B67; --gradient: none;}.o_cc5 .btn-fill-secondary, .o_colored_level .o_cc5 .btn-fill-secondary{--btn-color: #000; --btn-bg: #F3F2F2; --btn-border-color: #F3F2F2; --btn-hover-color: #000; --btn-hover-bg: #f5f4f4; --btn-hover-border-color: #f4f3f3; --btn-focus-shadow-rgb: 207, 206, 206; --btn-active-color: #000; --btn-active-bg: whitesmoke; --btn-active-border-color: #f4f3f3; --btn-active-shadow: 0; --btn-disabled-color: #000; --btn-disabled-bg: #F3F2F2; --btn-disabled-border-color: #F3F2F2;}.o_cc5 .btn-outline-secondary, .o_colored_level .o_cc5 .btn-outline-secondary{--btn-color: #F3F2F2; --btn-border-color: #F3F2F2; --btn-hover-color: #000; --btn-hover-bg: #F3F2F2; --btn-hover-border-color: #F3F2F2; --btn-focus-shadow-rgb: 243, 242, 242; --btn-active-color: #000; --btn-active-bg: #F3F2F2; --btn-active-border-color: #F3F2F2; --btn-active-shadow: 0; --btn-disabled-color: #F3F2F2; --btn-disabled-bg: transparent; --btn-disabled-border-color: #F3F2F2; --gradient: none;}.o_cc5 .nav-pills .nav-link.active, .o_cc5 .nav-pills .show > .nav-link, .o_colored_level .o_cc5 .nav-pills .nav-link.active, .o_colored_level .o_cc5 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFF;}.o_cc5 .dropdown-menu .dropdown-item.active, .o_cc5 .dropdown-menu .dropdown-item.active h6, .o_cc5 .dropdown-menu .dropdown-item.active .h6, .o_cc5 .dropdown-menu .dropdown-item:active, .o_cc5 .dropdown-menu .dropdown-item:active h6, .o_cc5 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFF !important;}.o_cc5 .dropdown-menu .dropdown-item.active:hover, .o_cc5 .dropdown-menu .dropdown-item.active:focus, .o_cc5 .dropdown-menu .dropdown-item.active h6:hover, .o_cc5 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc5 .dropdown-menu .dropdown-item.active h6:focus, .o_cc5 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc5 .dropdown-menu .dropdown-item:active:hover, .o_cc5 .dropdown-menu .dropdown-item:active:focus, .o_cc5 .dropdown-menu .dropdown-item:active h6:hover, .o_cc5 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc5 .dropdown-menu .dropdown-item:active h6:focus, .o_cc5 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active h6:focus{color: #FFF !important;}.o_cc5 a.list-group-item, .o_colored_level .o_cc5 a.list-group-item{color: #714B67;}.o_cc5 a.list-group-item.active, .o_colored_level .o_cc5 a.list-group-item.active{background-color: #714B67; color: #FFF; border-color: #714B67;}.btn-custom:hover, .btn-fill-custom:hover{filter: invert(0.2);}.btn-outline-custom:not(:hover){background-color: transparent !important; background-image: none !important;}section, .oe_img_bg, [data-oe-shape-data], section > *, .oe_img_bg > *, [data-oe-shape-data] > *{position: relative;}.o_we_shape, .o_we_bg_filter{position: absolute; top: 0; left: 0; bottom: 0; right: 0; position: absolute !important; display: block; overflow: hidden; background-repeat: no-repeat; pointer-events: none;}.o_full_screen_height, .cover_full, .o_half_screen_height, .cover_mid{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: space-around; min-height: 100vh !important;}.o_half_screen_height, .cover_mid{min-height: 55vh !important;}@media (min-width: 992px){.o_container_small{max-width: 720px;}}.oe_img_bg{background-size: cover; background-repeat: no-repeat;}.oe_img_bg.o_bg_img_opt_repeat{background-size: auto; background-repeat: repeat;}.oe_img_bg.o_bg_img_center{background-position: center;}.text-gradient{-webkit-background-clip: text; -webkit-text-fill-color: transparent;}.text-gradient .o_animated_text, .text-gradient .o_animated_text *, .text-gradient.o_animated_text *, .text-gradient .o_text_highlight, .text-gradient .o_text_highlight *, .text-gradient.o_text_highlight *{background-image: inherit; -webkit-background-clip: inherit; -webkit-text-fill-color: inherit;}.text-gradient .fa{display: inherit;}.odoo-editor-editable.odoo-editor-qweb t, .odoo-editor-editable.odoo-editor-qweb [t-if], .odoo-editor-editable.odoo-editor-qweb [t-elif], .odoo-editor-editable.odoo-editor-qweb [t-else], .odoo-editor-editable.odoo-editor-qweb [t-foreach], .o_readonly t, .o_readonly [t-if], .o_readonly [t-elif], .o_readonly [t-else], .o_readonly [t-foreach]{background-color: rgba(0, 0, 102, 0.1) !important;}.odoo-editor-editable.odoo-editor-qweb t, .odoo-editor-editable.odoo-editor-qweb [t-esc], .odoo-editor-editable.odoo-editor-qweb [t-out], .odoo-editor-editable.odoo-editor-qweb [t-raw], .o_readonly t, .o_readonly [t-esc], .o_readonly [t-out], .o_readonly [t-raw]{border-radius: 2px;}.odoo-editor-editable.odoo-editor-qweb [t-esc], .odoo-editor-editable.odoo-editor-qweb [t-out], .odoo-editor-editable.odoo-editor-qweb [t-raw], .o_readonly [t-esc], .o_readonly [t-out], .o_readonly [t-raw]{background-color: rgba(36, 154, 255, 0.16) !important;}.odoo-editor-editable.odoo-editor-qweb [t-esc]:empty::before, .o_readonly [t-esc]:empty::before{content: attr(t-esc);}.odoo-editor-editable.odoo-editor-qweb [t-raw]:empty::before, .o_readonly [t-raw]:empty::before{content: attr(t-raw);}.odoo-editor-editable.odoo-editor-qweb [t-out]:empty::before, .o_readonly [t-out]:empty::before{content: attr(t-out);}.odoo-editor-editable.odoo-editor-qweb t[t-set], .o_readonly t[t-set]{display: none;}.odoo-editor-editable.odoo-editor-qweb t[data-oe-t-inline], .o_readonly t[data-oe-t-inline]{display: inline;}.odoo-editor-editable.odoo-editor-qweb t:not([data-oe-t-inline]), .o_readonly t:not([data-oe-t-inline]){display: block;}.odoo-editor-editable.odoo-editor-qweb t[data-oe-t-inline]:not([data-oe-t-group-active]), .o_readonly t[data-oe-t-inline]:not([data-oe-t-group-active]){display: unset;}.odoo-editor-editable.odoo-editor-qweb [data-oe-t-group]:not([data-oe-t-group-active]), .o_readonly [data-oe-t-group]:not([data-oe-t-group-active]){display: none !important;}.odoo-editor-editable.odoo-editor-qweb [data-oe-t-group][data-oe-t-selectable], .o_readonly [data-oe-t-group][data-oe-t-selectable]{outline: 1px dashed rgba(0, 0, 102, 0.4) !important;}.oe-qweb-select{position: absolute; z-index: 1056; background-color: white;}.o_we_shape.o_we_animated{will-change: transform;}.o_we_shape.o_web_editor_Airy_01{background-image: url("/web_editor/shape/web_editor/Airy/01.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_02{background-image: url("/web_editor/shape/web_editor/Airy/02.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_03{background-image: url("/web_editor/shape/web_editor/Airy/03.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_03_001{background-image: url("/web_editor/shape/web_editor/Airy/03_001.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_04{background-image: url("/web_editor/shape/web_editor/Airy/04.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_04_001{background-image: url("/web_editor/shape/web_editor/Airy/04_001.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_05{background-image: url("/web_editor/shape/web_editor/Airy/05.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_05_001{background-image: url("/web_editor/shape/web_editor/Airy/05_001.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_06{background-image: url("/web_editor/shape/web_editor/Airy/06.svg?c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_07{background-image: url("/web_editor/shape/web_editor/Airy/07.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_08{background-image: url("/web_editor/shape/web_editor/Airy/08.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_09{background-image: url("/web_editor/shape/web_editor/Airy/09.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_10{background-image: url("/web_editor/shape/web_editor/Airy/10.svg?c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_11{background-image: url("/web_editor/shape/web_editor/Airy/11.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_12{background-image: url("/web_editor/shape/web_editor/Airy/12.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_12_001{background-image: url("/web_editor/shape/web_editor/Airy/12_001.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_13{background-image: url("/web_editor/shape/web_editor/Airy/13.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_13_001{background-image: url("/web_editor/shape/web_editor/Airy/13_001.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_14{background-image: url("/web_editor/shape/web_editor/Airy/14.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_01{background-image: url("/web_editor/shape/web_editor/Blobs/01.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_01_001{background-image: url("/web_editor/shape/web_editor/Blobs/01_001.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_02{background-image: url("/web_editor/shape/web_editor/Blobs/02.svg?c1=%23714B67&c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_03{background-image: url("/web_editor/shape/web_editor/Blobs/03.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_04{background-image: url("/web_editor/shape/web_editor/Blobs/04.svg?c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_05{background-image: url("/web_editor/shape/web_editor/Blobs/05.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_06{background-image: url("/web_editor/shape/web_editor/Blobs/06.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_07{background-image: url("/web_editor/shape/web_editor/Blobs/07.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_08{background-image: url("/web_editor/shape/web_editor/Blobs/08.svg?c1=%23714B67"); background-position: right; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_09{background-image: url("/web_editor/shape/web_editor/Blobs/09.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_10{background-image: url("/web_editor/shape/web_editor/Blobs/10.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_10_001{background-image: url("/web_editor/shape/web_editor/Blobs/10_001.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_11{background-image: url("/web_editor/shape/web_editor/Blobs/11.svg?c1=%23714B67"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_12{background-image: url("/web_editor/shape/web_editor/Blobs/12.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_01{background-image: url("/web_editor/shape/web_editor/Blocks/01.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_01_001{background-image: url("/web_editor/shape/web_editor/Blocks/01_001.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_02{background-image: url("/web_editor/shape/web_editor/Blocks/02.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_02_001{background-image: url("/web_editor/shape/web_editor/Blocks/02_001.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_03{background-image: url("/web_editor/shape/web_editor/Blocks/03.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_04{background-image: url("/web_editor/shape/web_editor/Blocks/04.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_01{background-image: url("/web_editor/shape/web_editor/Bold/01.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_02{background-image: url("/web_editor/shape/web_editor/Bold/02.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_03{background-image: url("/web_editor/shape/web_editor/Bold/03.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_04{background-image: url("/web_editor/shape/web_editor/Bold/04.svg?c2=%232D3142&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_05{background-image: url("/web_editor/shape/web_editor/Bold/05.svg?c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_05_001{background-image: url("/web_editor/shape/web_editor/Bold/05_001.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_06{background-image: url("/web_editor/shape/web_editor/Bold/06.svg?c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_06_001{background-image: url("/web_editor/shape/web_editor/Bold/06_001.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_07{background-image: url("/web_editor/shape/web_editor/Bold/07.svg?c1=%23714B67&c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_07_001{background-image: url("/web_editor/shape/web_editor/Bold/07_001.svg?c1=%23714B67&c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_08{background-image: url("/web_editor/shape/web_editor/Bold/08.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_09{background-image: url("/web_editor/shape/web_editor/Bold/09.svg?c2=%232D3142&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_10{background-image: url("/web_editor/shape/web_editor/Bold/10.svg?c1=%23714B67&c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_10_001{background-image: url("/web_editor/shape/web_editor/Bold/10_001.svg?c1=%23714B67&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_11{background-image: url("/web_editor/shape/web_editor/Bold/11.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_11_001{background-image: url("/web_editor/shape/web_editor/Bold/11_001.svg?c1=%23714B67&c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_12{background-image: url("/web_editor/shape/web_editor/Bold/12.svg?c1=%23714B67&c2=%232D3142&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_12_001{background-image: url("/web_editor/shape/web_editor/Bold/12_001.svg?c1=%23714B67&c2=%232D3142&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_01{background-image: url("/web_editor/shape/web_editor/Floats/01.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: center right; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_02{background-image: url("/web_editor/shape/web_editor/Floats/02.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_03{background-image: url("/web_editor/shape/web_editor/Floats/03.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_04{background-image: url("/web_editor/shape/web_editor/Floats/04.svg?c1=%23714B67&c2=%232D3142&c4=%23FFFFFF&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_05{background-image: url("/web_editor/shape/web_editor/Floats/05.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_06{background-image: url("/web_editor/shape/web_editor/Floats/06.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_07{background-image: url("/web_editor/shape/web_editor/Floats/07.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: right bottom; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_08{background-image: url("/web_editor/shape/web_editor/Floats/08.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: top left; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_09{background-image: url("/web_editor/shape/web_editor/Floats/09.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2"); background-position: center right; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_10{background-image: url("/web_editor/shape/web_editor/Floats/10.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_11{background-image: url("/web_editor/shape/web_editor/Floats/11.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_12{background-image: url("/web_editor/shape/web_editor/Floats/12.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Floats_13{background-image: url("/web_editor/shape/web_editor/Floats/13.svg?c1=%23714B67&c2=%232D3142&c5=%23111827"); background-position: center; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_14{background-image: url("/web_editor/shape/web_editor/Floats/14.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Origins_01{background-image: url("/web_editor/shape/web_editor/Origins/01.svg?c2=%232D3142&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_02{background-image: url("/web_editor/shape/web_editor/Origins/02.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_02_001{background-image: url("/web_editor/shape/web_editor/Origins/02_001.svg?c4=%23FFFFFF&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_03{background-image: url("/web_editor/shape/web_editor/Origins/03.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_04{background-image: url("/web_editor/shape/web_editor/Origins/04.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_04_001{background-image: url("/web_editor/shape/web_editor/Origins/04_001.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_05{background-image: url("/web_editor/shape/web_editor/Origins/05.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_06{background-image: url("/web_editor/shape/web_editor/Origins/06.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_06_001{background-image: url("/web_editor/shape/web_editor/Origins/06_001.svg?c3=%23F3F2F2&c4=%23FFFFFF"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_07{background-image: url("/web_editor/shape/web_editor/Origins/07.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_07_001{background-image: url("/web_editor/shape/web_editor/Origins/07_001.svg?c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_07_002{background-image: url("/web_editor/shape/web_editor/Origins/07_002.svg?c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_08{background-image: url("/web_editor/shape/web_editor/Origins/08.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_09{background-image: url("/web_editor/shape/web_editor/Origins/09.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_09_001{background-image: url("/web_editor/shape/web_editor/Origins/09_001.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_10{background-image: url("/web_editor/shape/web_editor/Origins/10.svg?c2=%232D3142&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_11{background-image: url("/web_editor/shape/web_editor/Origins/11.svg?c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_11_001{background-image: url("/web_editor/shape/web_editor/Origins/11_001.svg?c3=%23F3F2F2&c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_12{background-image: url("/web_editor/shape/web_editor/Origins/12.svg?c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_13{background-image: url("/web_editor/shape/web_editor/Origins/13.svg?c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_14{background-image: url("/web_editor/shape/web_editor/Origins/14.svg?c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_14_001{background-image: url("/web_editor/shape/web_editor/Origins/14_001.svg?c3=%23F3F2F2&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_15{background-image: url("/web_editor/shape/web_editor/Origins/15.svg?c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_16{background-image: url("/web_editor/shape/web_editor/Origins/16.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_17{background-image: url("/web_editor/shape/web_editor/Origins/17.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_18{background-image: url("/web_editor/shape/web_editor/Origins/18.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_19{background-image: url("/web_editor/shape/web_editor/Origins/19.svg?c5=%23111827"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_01{background-image: url("/web_editor/shape/web_editor/Rainy/01.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_01_001{background-image: url("/web_editor/shape/web_editor/Rainy/01_001.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_02{background-image: url("/web_editor/shape/web_editor/Rainy/02.svg?c1=%23714B67&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_02_001{background-image: url("/web_editor/shape/web_editor/Rainy/02_001.svg?c1=%23714B67&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_03{background-image: url("/web_editor/shape/web_editor/Rainy/03.svg?c2=%232D3142&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Rainy_03_001{background-image: url("/web_editor/shape/web_editor/Rainy/03_001.svg?c2=%232D3142&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Rainy_04{background-image: url("/web_editor/shape/web_editor/Rainy/04.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_05{background-image: url("/web_editor/shape/web_editor/Rainy/05.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_05_001{background-image: url("/web_editor/shape/web_editor/Rainy/05_001.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_06{background-image: url("/web_editor/shape/web_editor/Rainy/06.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_07{background-image: url("/web_editor/shape/web_editor/Rainy/07.svg?c1=%23714B67&c2=%232D3142&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_08{background-image: url("/web_editor/shape/web_editor/Rainy/08.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_08_001{background-image: url("/web_editor/shape/web_editor/Rainy/08_001.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_09{background-image: url("/web_editor/shape/web_editor/Rainy/09.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_09_001{background-image: url("/web_editor/shape/web_editor/Rainy/09_001.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_10{background-image: url("/web_editor/shape/web_editor/Rainy/10.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_01{background-image: url("/web_editor/shape/web_editor/Wavy/01.svg?c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_01_001{background-image: url("/web_editor/shape/web_editor/Wavy/01_001.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_02{background-image: url("/web_editor/shape/web_editor/Wavy/02.svg?c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_02_001{background-image: url("/web_editor/shape/web_editor/Wavy/02_001.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_03{background-image: url("/web_editor/shape/web_editor/Wavy/03.svg?c1=%23714B67&c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_04{background-image: url("/web_editor/shape/web_editor/Wavy/04.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_05{background-image: url("/web_editor/shape/web_editor/Wavy/05.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_06{background-image: url("/web_editor/shape/web_editor/Wavy/06.svg?c1=%23714B67&c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_06_001{background-image: url("/web_editor/shape/web_editor/Wavy/06_001.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_07{background-image: url("/web_editor/shape/web_editor/Wavy/07.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_08{background-image: url("/web_editor/shape/web_editor/Wavy/08.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_09{background-image: url("/web_editor/shape/web_editor/Wavy/09.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_10{background-image: url("/web_editor/shape/web_editor/Wavy/10.svg?c1=%23714B67&c2=%232D3142"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_11{background-image: url("/web_editor/shape/web_editor/Wavy/11.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_12{background-image: url("/web_editor/shape/web_editor/Wavy/12.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_12_001{background-image: url("/web_editor/shape/web_editor/Wavy/12_001.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_13{background-image: url("/web_editor/shape/web_editor/Wavy/13.svg?c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_13_001{background-image: url("/web_editor/shape/web_editor/Wavy/13_001.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_14{background-image: url("/web_editor/shape/web_editor/Wavy/14.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_15{background-image: url("/web_editor/shape/web_editor/Wavy/15.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_16{background-image: url("/web_editor/shape/web_editor/Wavy/16.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_17{background-image: url("/web_editor/shape/web_editor/Wavy/17.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_18{background-image: url("/web_editor/shape/web_editor/Wavy/18.svg?c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_19{background-image: url("/web_editor/shape/web_editor/Wavy/19.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_20{background-image: url("/web_editor/shape/web_editor/Wavy/20.svg?c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_21{background-image: url("/web_editor/shape/web_editor/Wavy/21.svg?c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_22{background-image: url("/web_editor/shape/web_editor/Wavy/22.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_23{background-image: url("/web_editor/shape/web_editor/Wavy/23.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_24{background-image: url("/web_editor/shape/web_editor/Wavy/24.svg?c1=%23714B67&c2=%232D3142"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_25{background-image: url("/web_editor/shape/web_editor/Wavy/25.svg?c1=%23714B67&c2=%232D3142"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_26{background-image: url("/web_editor/shape/web_editor/Wavy/26.svg?c1=%23714B67&c2=%232D3142"); background-position: bottom right; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_27{background-image: url("/web_editor/shape/web_editor/Wavy/27.svg?c1=%23714B67&c2=%232D3142"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_28{background-image: url("/web_editor/shape/web_editor/Wavy/28.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_01{background-image: url("/web_editor/shape/web_editor/Zigs/01.svg?c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_01_001{background-image: url("/web_editor/shape/web_editor/Zigs/01_001.svg?c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_02{background-image: url("/web_editor/shape/web_editor/Zigs/02.svg?c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_02_001{background-image: url("/web_editor/shape/web_editor/Zigs/02_001.svg?c2=%232D3142"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_03{background-image: url("/web_editor/shape/web_editor/Zigs/03.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Zigs_04{background-image: url("/web_editor/shape/web_editor/Zigs/04.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_05{background-image: url("/web_editor/shape/web_editor/Zigs/05.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_06{background-image: url("/web_editor/shape/web_editor/Zigs/06.svg?c4=%23FFFFFF&c5=%23111827"); background-position: bottom; background-size: 30px 100%; background-repeat: repeat no-repeat;}.ui-autocomplete{max-height: 45vh; overflow-y: auto; overflow-x: hidden;}.ui-autocomplete .ui-menu-item{padding: 0;}.ui-autocomplete .ui-menu-item > .ui-state-active{border: none; font-weight: normal; margin: 0;}.ui-autocomplete .fw-bold{font-weight: 700 !important;}.o_editor_banner p, .o_editor_banner h1, .o_editor_banner .h1, .o_editor_banner h2, .o_editor_banner .h2, .o_editor_banner h3, .o_editor_banner .h3, .o_editor_banner ul, .o_editor_banner ol{margin-bottom: 1rem;}.o_editor_banner ol ol, .o_editor_banner ul ul, .o_editor_banner ol ul, .o_editor_banner ul ol{margin-bottom: 0;}.o_editor_banner ul.o_checklist > li:not(.oe-nested)::before{top: 0px !important;}code.o_inline_code{padding: .2em .4em; border-radius: 0.25rem; background-color: var(--WebEditor__inlineCode-bg, #e7e9ed); font-size: 85%; color: #111827;}

/* /account/static/src/css/report_invoice.css */
#payment_terms_note_id > p{margin-bottom: 0 !important;}.avoid-page-break-inside{page-break-inside: avoid;}.justify-text{text-align:justify; text-justify:inter-word;}#qrcode_odoo_logo{-webkit-transform:translate(-50%,-50%); height:18%; width:18%;}.tax_computation_company_currency{margin-bottom: 5px;}

/* /sale/static/src/scss/sale_report.scss */
 .sale_tbody .o_line_note{word-break: break-word;}

/* /stock/static/src/scss/report_stock_reception.scss */
 .o_report_reception{overflow-y: auto;}.o_report_reception .o_priority.o_priority_star{font-size: 1.35em;}.o_report_reception .o_priority.o_priority_star.fa-star{color: gold;}.o_report_reception .btn.btn-primary{height: 31px; background-color: #714B67; border-color: #714B67;}.o_report_reception .btn.btn-primary:hover:not([disabled]){background-color: #52374b;}.o_report_reception .badge{line-height: .75;}.o_report_reception .bg-primary-light{background-color: rgba(113, 75, 103, 0.5) !important; color: #000;}.o_report_reception .bg-primary-light .text-muted, .o_colored_level .o_report_reception .bg-primary-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-primary-light:hover, .o_report_reception a.bg-primary-light:focus, .o_report_reception button.bg-primary-light:hover, .o_report_reception button.bg-primary-light:focus{background-color: rgba(82, 55, 75, 0.5) !important; color: #000;}.o_report_reception .bg-secondary-light{background-color: rgba(216, 218, 221, 0.5) !important; color: #000;}.o_report_reception .bg-secondary-light .text-muted, .o_colored_level .o_report_reception .bg-secondary-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-secondary-light:hover, .o_report_reception a.bg-secondary-light:focus, .o_report_reception button.bg-secondary-light:hover, .o_report_reception button.bg-secondary-light:focus{background-color: rgba(189, 192, 197, 0.5) !important; color: #000;}.o_report_reception .bg-success-light{background-color: rgba(40, 167, 69, 0.5) !important; color: #000;}.o_report_reception .bg-success-light .text-muted, .o_colored_level .o_report_reception .bg-success-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-success-light:hover, .o_report_reception a.bg-success-light:focus, .o_report_reception button.bg-success-light:hover, .o_report_reception button.bg-success-light:focus{background-color: rgba(30, 126, 52, 0.5) !important; color: #000;}.o_report_reception .bg-info-light{background-color: rgba(23, 162, 184, 0.5) !important; color: #000;}.o_report_reception .bg-info-light .text-muted, .o_colored_level .o_report_reception .bg-info-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-info-light:hover, .o_report_reception a.bg-info-light:focus, .o_report_reception button.bg-info-light:hover, .o_report_reception button.bg-info-light:focus{background-color: rgba(17, 122, 139, 0.5) !important; color: #000;}.o_report_reception .bg-warning-light{background-color: rgba(233, 157, 0, 0.5) !important; color: #000;}.o_report_reception .bg-warning-light .text-muted, .o_colored_level .o_report_reception .bg-warning-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-warning-light:hover, .o_report_reception a.bg-warning-light:focus, .o_report_reception button.bg-warning-light:hover, .o_report_reception button.bg-warning-light:focus{background-color: rgba(182, 123, 0, 0.5) !important; color: #000;}.o_report_reception .bg-danger-light{background-color: rgba(212, 76, 89, 0.5) !important; color: #000;}.o_report_reception .bg-danger-light .text-muted, .o_colored_level .o_report_reception .bg-danger-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-danger-light:hover, .o_report_reception a.bg-danger-light:focus, .o_report_reception button.bg-danger-light:hover, .o_report_reception button.bg-danger-light:focus{background-color: rgba(191, 46, 60, 0.5) !important; color: #000;}.o_report_reception .bg-light-light{background-color: rgba(255, 255, 255, 0.5) !important; color: #000;}.o_report_reception .bg-light-light .text-muted, .o_colored_level .o_report_reception .bg-light-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-light-light:hover, .o_report_reception a.bg-light-light:focus, .o_report_reception button.bg-light-light:hover, .o_report_reception button.bg-light-light:focus{background-color: rgba(230, 230, 230, 0.5) !important; color: #000;}.o_report_reception .bg-dark-light{background-color: rgba(17, 24, 39, 0.5) !important; color: #FFF;}.o_report_reception .bg-dark-light .text-muted, .o_colored_level .o_report_reception .bg-dark-light .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_report_reception a.bg-dark-light:hover, .o_report_reception a.bg-dark-light:focus, .o_report_reception button.bg-dark-light:hover, .o_report_reception button.bg-dark-light:focus{background-color: rgba(2, 2, 3, 0.5) !important; color: #FFF;}.o_label_page{margin-left: -3mm; margin-right: -3mm; overflow: hidden; page-break-before: always; padding: 1mm 0mm 0mm;}.o_label_page.o_label_dymo{font-size: 80%; width: 57mm; height: 32mm;}.o_label_page.o_label_dymo span, .o_label_page.o_label_dymo div{line-height: 1; white-space: nowrap;}.o_label_page span[itemprop="name"]{font-weight: bold;}

/* /stock/static/src/scss/report_stock_rule.scss */
 .o_report_stock_rule .table > :not(:first-child){border-top: 2px solid currentColor;}.o_report_stock_rule .table{--table-border-color: #d8dadd;}.o_report_stock_rule .o_report_stock_rule_rule{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row nowrap; flex-flow: row nowrap;}.o_report_stock_rule .o_report_stock_rule_legend{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap; max-width: 1000px;}.o_report_stock_rule .o_report_stock_rule_legend_line{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row nowrap; flex-flow: row nowrap; width: 29%; margin-right: 20px; margin-left: 20px; margin-top: 15px; min-width: 200px;}.o_report_stock_rule .o_report_stock_rule_legend_line > .o_report_stock_rule_legend_label{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 30%; min-width: 100px;}.o_report_stock_rule .o_report_stock_rule_legend_line > .o_report_stock_rule_legend_symbol{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 70%;}.o_report_stock_rule .o_report_stock_rule_putaway > p{text-align: center; color: black; font-weight: normal; font-size: 12px;}.o_report_stock_rule .o_report_stock_rule_line{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; height: 20px;}.o_report_stock_rule .o_report_stock_rule_line > line{stroke: black; stroke-width: 1;}.o_report_stock_rule .o_report_stock_rule_arrow{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; height: 20px; width: 20px;}.o_report_stock_rule .o_report_stock_rule_arrow > svg > line{stroke: black; stroke-width: 1;}.o_report_stock_rule .o_report_stock_rule_arrow > svg > polygon{fill: black; fill-opacity: 0.5; stroke: black; stroke-width: 1;}.o_report_stock_rule .o_report_stock_rule_vertical_bar{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; height: 20px; width: 2px;}.o_report_stock_rule .o_report_stock_rule_vertical_bar > svg > line{stroke: black; stroke-width: 2;}.o_report_stock_rule .o_report_stock_rule_rule_name{text-align: center;}.o_report_stock_rule .o_report_stock_rule_symbol_cell{border: none !important;}.o_report_stock_rule .o_report_stock_rule_symbol_cell > div{max-width: 200px; height: 20px;}.o_report_stock_rule .o_report_stock_rule_rule_main{height: 100%; padding-top: 2px;}.o_report_stock_rule .o_report_stock_rule_location_header{text-align: center;}.o_report_stock_rule .o_report_stock_rule_location_header > a{display: block;}.o_report_stock_rule .o_report_stock_rule_location_header > a:hover{text-decoration: none; cursor: pointer; background-color: #efefef;}.o_report_stock_rule .o_report_stock_rule_location_header > a > div{color: black;}.o_report_stock_rule .o_report_stock_rule_rule_cell{padding: 0 !important;}.o_report_stock_rule .o_report_stock_rule_rule_cell > a{display: block;}.o_report_stock_rule .o_report_stock_rule_rule_cell > a:hover{text-decoration: none; cursor: pointer; background-color: #efefef;}.o_report_stock_rule .o_report_stock_rule_rtl{transform: scaleX(-1);}

/* /stock/static/src/scss/report_stockpicking_operations.scss */
 .o_report_stockpicking_operations table thead, .o_report_stockpicking_operations table tbody, .o_report_stockpicking_operations table td, .o_report_stockpicking_operations table th, .o_report_stockpicking_operations table tr{border: 0;}

/* /event/static/src/scss/event_badge_report.scss */
 .o_event_badge_report_page_break{page-break-after: always;}.o_event_badge_height{height: 148mm;}.o_event_badge_ticket_wrapper{background-color: white; background-repeat: no-repeat; background-position: center center; background-size: cover; border: solid 1px #939393; margin: 16px 8px; padding: 10px 0px 0px; font-size: 1.2rem; box-shadow: -3px 3px 9px 0px rgba(0, 0, 0, 0.51); height: 138mm;}.o_event_badge_ticket_wrapper .o_event_badge_font_faded{color: #939393;}.o_event_badge_ticket_wrapper .o_event_badge_font_small{font-size: 0.8rem;}.o_event_badge_ticket_wrapper .o_event_badge_logo{max-height: 84px; max-width: 174px;}.o_event_badge_ticket_center_vertically{top: 50%; -moz-transform: translateY(-50%); -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); transform: translateY(-50%);}.o_event_foldable_badge_container .o_event_foldable_badge_left_quarter{border-right: .75mm dotted lightgray;}.o_event_foldable_badge_container .o_event_foldable_badge_bottom_row{border-top: .75mm dotted black; margin-top: .25mm;}.o_event_foldable_badge_container .o_event_foldable_badge_bottom_row .o_event_foldable_badge_bottom_left .o_event_foldable_badge_answer{border-radius: 6px;}.o_event_foldable_badge_container .o_event_foldable_badge_instructions{background-color: white;}.o_event_foldable_badge_container .o_event_foldable_badge_instructions .o_event_foldable_badge_step{position: absolute; padding: 3px 5px; margin-top: 5mm; margin-left: 5mm; border-radius: 50%; background-color: black; color: white;}

/* /event/static/src/scss/event_full_page_ticket_report.scss */
 .o_event_full_page_ticket_footer .o_event_full_page_ticket_powered_by{font-size: 0.8rem; line-height: 1rem;}.o_event_full_page_ticket_container{padding-top: 40px;}.o_event_full_page_ticket_container .o_event_full_page_ticket_font_faded{color: #939393;}.o_event_full_page_ticket_container .o_event_full_page_ticket_details{background-color: white; border: solid 1px #939393; margin: 0px 8px; padding: 10px 3px; box-shadow: -3px 3px 9px 0px rgba(0, 0, 0, 0.51);}.o_event_full_page_ticket_container .o_event_full_page_ticket_barcode{min-height: 344px;}.o_event_full_page_ticket_container .o_event_full_page_ticket_barcode.o_event_full_page_ticket_qr_only{min-height: 168px;}.o_event_full_page_ticket_container .o_event_full_page_ticket_answer{font-size: .875rem;}.o_event_full_page_ticket .o_event_full_page_left_details{width: 78%;}.o_event_full_page_ticket .o_event_full_page_left_details .o_event_full_page_left_details_top{min-height: 152px;}.o_event_full_page_ticket .o_event_full_page_left_details .o_event_full_page_left_details_bottom_qr_only{width: 128%;}.o_event_full_page_ticket .o_event_full_page_ticket_barcode{width: 22%;}.o_event_full_page_ticket .o_event_full_page_ticket_column{width: 50%; padding-right: 45px;}.o_event_full_page_ticket .o_event_full_page_ticket_answer{border-radius: 6px;}.o_event_full_page_ticket .o_event_full_page_extra_instructions{width: 100%;}.o_event_full_page_ticket .o_event_full_page_extra_instructions p{margin: 0px;}.o_event_full_page_ticket_container .o_event_full_page_ticket_barcode .o_event_barcode{margin: 28px 0 0 -8px; -webkit-transform: rotate(90deg); transform: rotate(90deg);}

/* /event/static/src/scss/event_full_page_ticket_responsive_html_report.scss */
 .o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_details{max-width: 1000px; margin-left: auto; margin-right: auto;}@media (max-width: 576px){.o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_details .o_event_full_page_left_details{width: 100%;}.o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_details .o_event_full_page_ticket_barcode{width: 100%;}}.o_event_full_page_ticket_responsive_html .o_event_full_page_left_details{width: calc(100% - 168px);}.o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_barcode{width: 168px;}@media (max-width: 768px){.o_event_full_page_ticket_responsive_html .o_event_full_page_left_details{width: calc(100% - 152px); padding-left: 0.5rem !important;}.o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_barcode{width: 152px;}.o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_barcode .o_event_full_page_ticket_barcode_container{padding-left: 0px !important; padding-right: 0px !important;}.o_event_full_page_ticket_responsive_html .o_event_full_page_ticket_type{padding-right: 0rem !important;}}

/* /account_reports/static/src/scss/account_pdf_export_template.scss */
 .o_content{margin: 20px;}.o_content header{padding: 10px; background-color: #d8dce2; margin-bottom: 10px;}.o_content header .o_header_font{font-size: 13px;}.o_content .o_table{width: 100%; font-family: 'Verdana', sans-serif; font-size: 0.8rem; margin: 0 0 24px; padding: 24px; direction: ltr;}.o_content .o_table > :not(caption) > * > *{padding: 0.25rem 0.75rem;}.o_content .o_table thead, .o_content .o_table tbody, .o_content .o_table tfoot, .o_content .o_table tr, .o_content .o_table td, .o_content .o_table th{border-style: none;}.o_content .o_table.horizontal_split_page{width: 48%; margin-left: 1%; margin-right: 1%;}.o_content .o_table > thead{white-space: nowrap;}.o_content .o_table > thead tr > th:first-child{width: 100%;}.o_content .o_table > thead > tr:not(:last-child) th:not(:first-child){border: 1px solid #d8dadd; text-align: center;}.o_content .o_table > tbody{white-space: nowrap;}.o_content .o_table > tbody tr{height: 20px; padding: 2px; vertical-align: center;}.o_content .o_table > tbody td{word-wrap: break-word; max-width: 350px;}.o_content .o_table > tbody .o_column_percent_comparison{text-align: right;}.o_content .o_table > tbody .o_line_level_0{background-color: #e7e9ed; color: #374151; font-weight: bold;}.o_content .o_table > tbody .o_line_level_0 .o_line_name_level{padding-left: 8px;}.o_content .o_table > tbody .o_line_level_1{border-bottom: 0.5px solid #d8dadd;}.o_content .o_table > tbody .o_line_level_1 .o_line_name_level{padding-left: 16px;}.o_content .o_table > tbody .o_line_level_2{border-bottom: 0.5px solid #d8dadd;}.o_content .o_table > tbody .o_line_level_2 .o_line_name_level{padding-left: 24px;}.o_content .o_table > tbody .o_line_level_3{border-bottom: 0.5px solid #d8dadd;}.o_content .o_table > tbody .o_line_level_3 .o_line_name_level{padding-left: 32px;}.o_content .o_table > tbody .o_line_level_4{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_4 .o_line_name_level{padding-left: 40px;}.o_content .o_table > tbody .o_line_level_5{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_5 .o_line_name_level{padding-left: 48px;}.o_content .o_table > tbody .o_line_level_6{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_6 .o_line_name_level{padding-left: 56px;}.o_content .o_table > tbody .o_line_level_7{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_7 .o_line_name_level{padding-left: 64px;}.o_content .o_table > tbody .o_line_level_8{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_8 .o_line_name_level{padding-left: 72px;}.o_content .o_table > tbody .o_line_level_9{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_9 .o_line_name_level{padding-left: 80px;}.o_content .o_table > tbody .o_line_level_10{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_10 .o_line_name_level{padding-left: 88px;}.o_content .o_table > tbody .o_line_level_11{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_11 .o_line_name_level{padding-left: 96px;}.o_content .o_table > tbody .o_line_level_12{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_12 .o_line_name_level{padding-left: 104px;}.o_content .o_table > tbody .o_line_level_13{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_13 .o_line_name_level{padding-left: 112px;}.o_content .o_table > tbody .o_line_level_14{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_14 .o_line_name_level{padding-left: 120px;}.o_content .o_table > tbody .o_line_level_15{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_15 .o_line_name_level{padding-left: 128px;}.o_content .o_table > tbody .o_line_level_16{border-bottom: 0.5px solid #e7e9ed;}.o_content .o_table > tbody .o_line_level_16 .o_line_name_level{padding-left: 136px;}.o_content .o_table > tbody .o_cell_td > .o_line_cell_value_number{display: block; text-align: right;}.o_d_none{display: none;}.o_fw_bold{font-weight: bold;}.o_text_end{text-align: end;}.o_text_muted{color: #777;}.o_muted{color: #d8dadd;}.o_title{font-size: 20px; font-weight: bold; text-align: center; text-transform: uppercase;}.o_overflow_value{display: inline-block; max-width: 350px; white-space: normal !important; overflow-wrap: break-word;}.o_overflow_name{white-space: normal !important; overflow-wrap: break-word;}body[dir="rtl"] .o_line_name_level{white-space: normal !important; overflow-wrap: break-word;}.o_footer_font_size{font-size: 0.7rem;}.o_annotation{list-style: none;}

/* /account_batch_payment/static/src/scss/report_batch_payment.scss */
 .page_batch_payment{page-break-after: always;}.page_batch_payment .batch_details{margin: 0.2in 0; font-size: 1.5em;}

/* /l10n_us_check_printing/static/src/scss/report_check_commons.scss */
 @font-face{font-family: 'MICR'; src: url("/l10n_us_check_printing/static/src/fonts/E-13B.woff") format("woff");}.ckus_page{position: relative; font-size: 10pt; width: 8in !important; zoom: 1.25; margin-left: -15px; margin-right: -15px;}.ckus_page:after{display: block; content: "."; width: 8.25in; visibility: hidden;}.ckus_page .ckus_check, .ckus_page .ckus_stub_1, .ckus_page .ckus_stub_2, .ckus_page .ckus_stub_blank{position: absolute; height: 3.25in;}.ckus_page .ckus_check > *, .ckus_page .ckus_stub_blank > *{position: absolute; white-space: nowrap;}.ckus_page .sequence_number{text-align: right; font-size: 1.3em;}.ckus_page .ckus_stub, .ckus_page .ckus_stub_blank{width: 8in;}.ckus_page .ckus_stub .sequence_number{height: 0.3in;}.ckus_page .ckus_stub .summary_line{width: 100%; margin-bottom: 0.1in;}.ckus_page .ckus_stub .summary_line span{padding: 0 0.1in;}.ckus_page .ckus_stub .stub_lines td{padding: 0.01in 0.05in;}.ckus_page .ckus_stub .stub_total_amount{position: absolute; right: 0.1in; text-align: right; font-weight: bold;}.ckus_page p, .ckus_page span, .ckus_page strong, .ckus_page em{line-height: inherit;}.ckus_page .ckus_stub_blank .ckus_name{position: absolute; top: 0; left: 0.15in;}.ckus_page .ckus_hr_line{height: 1px; border: 0; clear: both; display: block; background-color: #000000;}.ckus_page .ckus_number{position: absolute; top: 0; right: 0;}.ckus_page .ckus_check_blank{font-size: 9pt;}.ckus_page .ckus_check_blank .ckus_company_logo{width: 0.55in; height: 0.55in; top: 0.08in; left: 0.3in;}.ckus_page .ckus_check_blank .ckus_company_block{top: 0.1in; left: 1in; text-align: center;}.ckus_page .ckus_check_blank .ckus_company_block .ckus_company_name{font-weight: bold; font-size: 10pt;}.ckus_page .ckus_check_blank .ckus_pay_order{top: 1.05in; left: 0;}.ckus_page .ckus_check_blank .ckus_payee_name{top: 1.05in; left: 1.3in;}.ckus_page .ckus_check_blank .ckus_amount_in_word{top: 1.425in; left: 0.4in;}.ckus_page .ckus_check_blank .ckus_amount_in_word_line{top: 1.45in; left: 0; width: 7.05in;}.ckus_page .ckus_check_blank .ckus_payee_addr{top: 1.758in; left: 0.4in;}.ckus_page .ckus_check_blank .ckus_bank_block{top: 0.1in; left: 4.4in; text-align: center;}.ckus_page .ckus_check_blank .ckus_bank_block .ckus_bank_routing{font-size: 8pt;}.ckus_page .ckus_check_blank .ckus_blank_date{top: 0.3in; left: 7.125in;}.ckus_page .ckus_check_blank .ckus_dollar_symbol{top: 1in; left: 6.45in; font-size: 15pt;}.ckus_page .ckus_check_blank .ckus_amount{top: 1in; left: 6.65in; font-size: 15pt;}.ckus_page .ckus_check_blank .ckus_dollar_text{top: 1.425in; left: 7.1in;}.ckus_page .ckus_check_blank .ckus_memo_blank{top: 2.4475in; left: 0.05in;}.ckus_page .ckus_check_blank .ckus_memo{top: 2.6in; left: 0.05in;}.ckus_page .ckus_check_blank .ckus_signature{top: 2.5in; left: 6.04in; font-size: 5.9pt;}.ckus_page .ckus_check_blank .ckus_signature_line{top: 2.3375in; left: 5.32in; width: 2.33in;}.ckus_page .ckus_check_blank .ckus_special_line{font-family: MICR, monospace; font-size: 13pt; top: 2.87in; left: 1.5in;}.o-watermark{width: 300px; top: 0.3in; font-size: 80pt; left: 250px; position: absolute; color: lightgray; z-index: -1; transform: rotate(-27deg);}

/* /l10n_us_check_printing/static/src/scss/report_check_bottom.scss */
 .ckus_bottom_page .ckus_stub_1{top: 0.3in;}.ckus_bottom_page .ckus_stub_2{top: 4.15in;}.ckus_bottom_page .ckus_check{top: 7.5in; height: 2.80in;}.ckus_bottom_page .ckus_stub_blank_1{top: 0.0325in;}.ckus_bottom_page .ckus_stub_blank_2{top: 3.8225in;}.ckus_bottom_page .ckus_check_blank{top: 7.45in;}.ckus_bottom_page .ckus_check_blank .ckus_company_logo{top: 0.33in; left: 0.3in;}.ckus_bottom_page .ckus_check_blank .ckus_company_block{top: 0.35in; left: 0.9375in;}.ckus_bottom_page .ckus_check_blank .ckus_number, .ckus_bottom_page .ckus_check_blank .sequence_number{top: 0.1875in !important; left: 7.4in !important;}.ckus_bottom_page .ckus_check_blank .ckus_blank_date{top: 0.4in; left: 7.4in;}.ckus_bottom_page .ckus_check_blank .ckus_pay_order{top: 1.3in;}.ckus_bottom_page .ckus_check_blank .ckus_amount_in_word{top: 1.675in;}.ckus_bottom_page .ckus_check_blank .ckus_amount_in_word_line{top: 1.7in;}.ckus_bottom_page .ckus_check_blank .ckus_payee_addr{top: 2in;}.ckus_bottom_page .ckus_check_blank .ckus_payee_name{top: 1.3in;}.ckus_bottom_page .ckus_check_blank .ckus_bank_block{top: 0.35in;}.ckus_bottom_page .ckus_check_blank .ckus_dollar_symbol{top: 1.3075in;}.ckus_bottom_page .ckus_check_blank .ckus_dollar_text{top: 1.7375in;}.ckus_bottom_page .ckus_check_blank .ckus_amount{top: 1.3075in;}.ckus_bottom_page .ckus_check_blank .ckus_memo_blank{top: 2.6375in;}.ckus_bottom_page .ckus_check_blank .ckus_memo{top: 2.7875in;}.ckus_bottom_page .ckus_check_blank .ckus_signature{top: 2.7475in;}.ckus_bottom_page .ckus_check_blank .ckus_signature_line{top: 2.5875in;}.ckus_bottom_page .ckus_check_blank .ckus_special_line{top: 3.065in;}.ckus_bottom_page .ckus_check .sequence_number{top: 0.2in; left: 6.9in; width: 0.7in;}.ckus_bottom_page .ckus_date{top: 0.6in; left: 6.51in; width: 1.50in; text-align: center;}.ckus_bottom_page .ckus_payee_name{top: 1.08in; left: 0.9in; width: 5.5in; text-align: left;}.ckus_bottom_page .ckus_amount{top: 1.08in; left: 6.77in; width: 1.08in; text-align: left;}.ckus_bottom_page .ckus_amount_in_word{top: 1.47in; left: 0.25in; width: 7in; text-align: left; font-weight: bold; overflow: hidden;}.ckus_bottom_page .ckus_payee_addr{top: 1.65in; left: 1in; line-height: 1.1em;}.ckus_bottom_page .ckus_memo{top: 2.45in; left: 0.6in; width: 2.9in; text-align: left; font-size: 0.9em; overflow: hidden;}

/* /l10n_us_check_printing/static/src/scss/report_check_middle.scss */
 .ckus_middle_page .ckus_stub_1{top: 0.24in;}.ckus_middle_page .ckus_check{top: 3.98in; left: 0.22in;}.ckus_middle_page .ckus_stub_2{top: 7.1in;}.ckus_middle_page .ckus_stub_blank_1{top: -0.15in;}.ckus_middle_page .ckus_check_blank{top: 3.4in;}.ckus_middle_page .ckus_stub_blank_2{top: 6.975in;}.ckus_middle_page .ckus_check_blank .ckus_company_logo{top: 0.1in; left: 0.05in;}.ckus_middle_page .ckus_check_blank .ckus_number{top: 0.1in !important;}.ckus_middle_page .ckus_check_blank .ckus_pay_order{top: 1.175in;}.ckus_middle_page .ckus_check_blank .ckus_payee_name{top: 1.175in;}.ckus_middle_page .ckus_check_blank .ckus_amount_in_word{top: 1.675in;}.ckus_middle_page .ckus_check_blank .ckus_amount_in_word_line{top: 1.7in;}.ckus_middle_page .ckus_check_blank .ckus_payee_addr{top: 2in;}.ckus_middle_page .ckus_check_blank .ckus_dollar_symbol{top: 1.25in;}.ckus_middle_page .ckus_check_blank .ckus_amount{top: 1.25in;}.ckus_middle_page .ckus_check_blank .ckus_dollar_text{top: 1.675in;}.ckus_middle_page .ckus_check_blank .ckus_memo_blank{top: 2.575in;}.ckus_middle_page .ckus_check_blank .ckus_memo{top: 2.725in;}.ckus_middle_page .ckus_check_blank .ckus_signature{top: 2.685in;}.ckus_middle_page .ckus_check_blank .ckus_signature_line{top: 2.525in;}.ckus_middle_page .ckus_check_blank .ckus_special_line{top: 3.0575in;}.ckus_middle_page .ckus_check .sequence_number{top: 0; left: 7.19in; width: 0.76in; text-align: center;}.ckus_middle_page .ckus_payee_name{top: 0.6in; left: 0.63in; width: 4.5in; text-align: left;}.ckus_middle_page .ckus_date{top: 0.1in; left: 6.75in; width: 0.98in; text-align: center;}.ckus_middle_page .ckus_amount{top: 0.6in; left: 6.38in; width: 1.07in; text-align: center;}.ckus_middle_page .ckus_amount_in_word{top: 1.0in; left: 0.05in; width: 7in; text-align: left; font-weight: bold; overflow: hidden;}.ckus_middle_page .ckus_payee_addr{top: 1.15in; left: 0.63in; line-height: 1.1em;}.ckus_middle_page .ckus_memo{top: 1.98in; left: 0.43in; width: 4.3in; text-align: left; font-size: 0.9em; overflow: hidden;}

/* /l10n_us_check_printing/static/src/scss/report_check_top.scss */
 .ckus_top_page .ckus_check{top: -0.02in;}.ckus_top_page .ckus_stub_1{top: 3.45in;}.ckus_top_page .ckus_stub_2{top: 7in;}.ckus_top_page .ckus_stub_blank_1{top: 3.27in;}.ckus_top_page .ckus_stub_blank_2{top: 6.9375in;}.ckus_top_page .ckus_check .sequence_number{top: 0.1in; left: 7.1in; width: 0.7in;}.ckus_top_page .ckus_date{top: 0.55in; left: 6.51in; width: 1.47in; text-align: center;}.ckus_top_page .ckus_payee_name{top: 1.09in; left: 0.85in; width: 4.0in; text-align: left;}.ckus_top_page .ckus_amount{top: 1.07in; left: 6.77in; width: 1.08in; text-align: left;}.ckus_top_page .ckus_amount_in_word{top: 1.45in; left: 0.25in; width: 7in; text-align: left; font-weight: bold; overflow: hidden;}.ckus_top_page .ckus_payee_addr{top: 1.65in; left: 0.85in; line-height: 1.1em; font-size: 0.12in;}.ckus_top_page .ckus_memo{top: 2.41in; left: 0.6in; width: 2.9in; text-align: left; font-size: 0.9em; overflow: hidden;}

/* /mrp_account_enterprise/static/src/scss/cost_structure_report.scss */
 .o_mrp_header{font-weight: 400;}.o_mrp_header.o_mrp_header1{font-size: 2rem; margin-bottom: 15px; margin-top: 15px;}.o_mrp_report_page{color: #212529; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}.o_mrp_report_table{font-size: 0.88333333rem; color: #666666;}.o_mrp_report_info{display: inline; font-weight: 300;}.o_mrp_table_header{font-size: 1.5rem; margin-top: 35px;}.o_mrp_report_header{border-top-style: groove; border-width: 2px; border-bottom: 2px solid #dee2e6;}.o_mrp_reports_web_action{color: #008784; cursor: pointer;}