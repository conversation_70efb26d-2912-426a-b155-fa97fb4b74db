{"version": 21, "sheets": [{"id": "df33ef16-d138-4c86-a257-a2d529a1cdfa", "name": "Dashboard", "colNumber": 11, "rowNumber": 115, "rows": {"6": {"size": 39}, "21": {"size": 42}, "22": {"size": 39}, "37": {"size": 33}, "38": {"size": 40}, "54": {"size": 39}, "70": {"size": 37}, "87": {"size": 40}}, "cols": {"1": {"size": 145}, "5": {"size": 19}, "7": {"size": 142}}, "merges": ["B30:D30", "G8:J8", "G29:J29"], "cells": {"A7": {"content": "='By product overview fill rate - On time'!B1"}, "A23": {"content": "=_t(\"Moves lines count by operation\")"}, "A39": {"content": "=_t(\"Internal lead time\")"}, "A55": {"content": "=_t(\"Weekly Stock Moves Lines by operation\")"}, "A71": {"content": "=_t(\"Transfers count by responsible and operation\")"}, "A88": {"content": "=_t(\"Quantity of stock adjustments by category\")"}, "G7": {"content": "='By product overview fill rate - On time'!B19"}, "G88": {"content": "=_t(\"Qty scrapped product by category\")"}}, "styles": {"A7": 1, "A23": 1, "A39": 1, "A55": 1, "A71": 1, "A88": 1, "G7": 1, "G88": 1}, "formats": {}, "borders": {"A7:E7": 1, "A88:E88": 1, "G7:K7": 1, "A23:K23": 1, "A39:K39": 1, "A55:K55": 1, "A71:K71": 1, "G88:K88": 1, "A8:E8": 2, "A89:E89": 2, "G8:K8": 2, "A24:K24": 2, "A40:K40": 2, "A56:K56": 2, "A72:K72": 2, "G89:K89": 2}, "conditionalFormats": [], "figures": [{"id": "00d06fd4-5cb9-443d-80b1-e8b6bf6c05a1", "x": 227, "y": 10, "width": 218, "height": 106, "tag": "chart", "data": {"baselineColorDown": "#E06666", "baselineColorUp": "#6aa84f", "baselineMode": "text", "title": {"text": "Avg Cycle Time", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FEF2F2", "baseline": "Data!C2", "baselineDescr": "last period", "keyValue": "Data!B2", "humanize": false}}, {"id": "bc286f72-8794-4655-b78e-b3811c2a15c5", "x": 0, "y": 10, "width": 218, "height": 106, "tag": "chart", "data": {"baselineColorDown": "#E06666", "baselineColorUp": "#6aa84f", "baselineMode": "difference", "title": {"text": "Avg  <PERSON>", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FEF2F2", "baseline": "Data!C3", "baselineDescr": "last period", "keyValue": "Data!B3", "humanize": false}}, {"id": "d993b670-2faa-49b2-ba06-5dc136d05e88", "x": 455, "y": 10, "width": 218, "height": 106, "tag": "chart", "data": {"baselineColorDown": "#E06666", "baselineColorUp": "#6aa84f", "baselineMode": "difference", "title": {"text": "Fill rate", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C6", "baselineDescr": "last period", "keyValue": "Data!B6", "humanize": false}}, {"id": "b6792369-556a-4568-aac3-89524cde3ed0", "x": 681, "y": 10, "width": 218, "height": 106, "tag": "chart", "data": {"baselineColorDown": "#E06666", "baselineColorUp": "#6aa84f", "baselineMode": "difference", "title": {"text": "On Time delivery", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#ECFDF5", "baseline": "Data!C9", "baselineDescr": "last period", "keyValue": "Data!B9", "humanize": false}}, {"id": "b537b287-018f-4e0c-904a-05ac413bd774", "x": 0, "y": 1741, "width": 1076, "height": 369, "tag": "chart", "data": {"title": {"text": ""}, "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["user_id", "picking_type_id"], "measure": "__count", "order": null, "resModel": "stock.picking", "mode": "bar"}, "searchParams": {"comparison": null, "context": {"contact_display": "partner_address", "default_company_id": 1, "restricted_picking_type_code": "incoming"}, "domain": [["state", "=", "done"]], "groupBy": ["user_id", "picking_type_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true, "fieldMatching": {"4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime", "offset": 0}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "picking_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}}, {"id": "f75d27b8-401c-4b23-be72-1e8c5f48c997", "x": 0, "y": 1359, "width": 1072, "height": 344, "tag": "chart", "data": {"title": {"text": ""}, "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["date:week", "picking_type_id"], "measure": "__count", "order": null, "resModel": "stock.move", "mode": "bar"}, "searchParams": {"comparison": null, "context": {"pivot_measures": ["product_uom_qty", "__count__"]}, "domain": ["&", ["state", "=", "done"], ["picking_type_id", "!=", false]], "groupBy": ["date:week", "picking_type_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true, "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": 0}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}}, {"id": "92ad86c5-b988-44b1-b5b3-17b6580743a6", "x": 549, "y": 177, "width": 525, "height": 364, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "'By product overview fill rate - On time'!E20:E30", "yAxisId": "y", "trend": {"type": "polynomial", "order": 1, "display": false}, "label": "On time rate"}, {"dataRange": "'By product overview fill rate - On time'!D20:D30", "yAxisId": "y1", "label": "Count"}], "legendPosition": "top", "labelRange": "'By product overview fill rate - On time'!B21:B30", "title": {"text": ""}, "aggregated": false, "axesDesign": {"y1": {"title": {"text": "Orders count"}}}, "showValues": false}}, {"id": "3832422c-37b7-4b17-92bc-e69219ebb918", "x": 0, "y": 580, "width": 1074, "height": 356, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "'WH Operations'!L2:L", "label": "Current period"}, {"dataRange": "'WH Operations'!M2:M", "label": "Previous period"}], "legendPosition": "top", "labelRange": "'WH Operations'!K2:K", "title": {"text": ""}, "aggregated": false, "showValues": false}}, {"id": "757ff23d-2733-4dd7-98c2-3df3dc5ff6ea", "x": 0, "y": 975, "width": 1074, "height": 344, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "'Cycle time'!O:O", "label": "Current period"}, {"dataRange": "'Cycle time'!P:P", "label": "Previous period"}], "legendPosition": "top", "labelRange": "'Cycle time'!N:N", "title": {"text": ""}, "aggregated": false}}, {"id": "87ab87e6-2bdd-4e75-a59f-a0c34f6742fb", "x": 0, "y": 2149, "width": 529, "height": 346, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "'Inventory adjustments by category'!I:I", "label": "Current period"}, {"dataRange": "'Inventory adjustments by category'!J:J", "label": "Previous period"}], "legendPosition": "top", "labelRange": "'Inventory adjustments by category'!H:H", "title": {"text": ""}, "aggregated": false}}, {"id": "f854691a-ed0a-4908-bd7b-3215c44f670a", "x": 549, "y": 2149, "width": 527, "height": 347, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "Scrap!H:H", "label": "Current period"}, {"dataRange": "Scrap!I:I", "label": "Previous period"}], "legendPosition": "top", "labelRange": "Scrap!G:G", "title": {"text": ""}, "aggregated": false}}, {"id": "106df83f-9731-4842-87d9-a8c7b848399e", "x": 0, "y": 177, "width": 527, "height": 364, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "'By product overview fill rate - On time'!F3:F13", "yAxisId": "y", "trend": {"type": "polynomial", "order": 1, "display": false}, "label": "Fill rate"}, {"dataRange": "'By product overview fill rate - On time'!D3:D13", "yAxisId": "y1", "label": "Demand"}], "legendPosition": "top", "labelRange": "'By product overview fill rate - On time'!B4:B13", "title": {"text": ""}, "aggregated": false, "axesDesign": {"y1": {"title": {"text": "Demand"}}}, "showValues": false}}], "tables": [], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "0a8b7ec6-6ab1-4f62-a7c6-49025a1e23a8", "name": "Data", "colNumber": 24, "rowNumber": 99, "rows": {}, "cols": {"0": {"size": 139}, "1": {"size": 115}, "7": {"size": 370}}, "merges": [], "cells": {"A1": {"content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Cycle Time (Days)\")"}, "A3": {"content": "=_t(\"Delay (Days)\")"}, "A4": {"content": "=_t(\"Demand\")"}, "A5": {"content": "=_t(\"Quantity\")"}, "A6": {"content": "=_t(\"Fill rate\")"}, "A7": {"content": "=_t(\"Count orders on time\")"}, "A8": {"content": "=_t(\"Total orders\")"}, "A9": {"content": "=_t(\"On time deliveries\")"}, "B1": {"content": "=_t(\"Current\")"}, "B2": {"content": "=PIVOT.VALUE(1,\"cycle_time\")"}, "B3": {"content": "=PIVOT.VALUE(1,\"delay\")"}, "B4": {"content": "=PIVOT.VALUE(2,\"product_uom_qty\")"}, "B5": {"content": "=PIVOT.VALUE(2,\"quantity\")"}, "B6": {"content": "=iferror(B5/B4,\"NA\")"}, "B7": {"content": "=PIVOT.VALUE(1,\"__count:sum\",\"is_late\",\"false\")"}, "B8": {"content": "=PIVOT.VALUE(1,\"__count:sum\")"}, "B9": {"content": "=iferror(B7/B8,\"NA\")"}, "C1": {"content": "=_t(\"Previous\")"}, "C2": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\", PIVOT.VALUE(14,\"cycle_time\"),\"N/A\")"}, "C3": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",PIVOT.VALUE(14,\"delay\"),\"N/A\")"}, "C4": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",PIVOT.VALUE(15,\"product_uom_qty\"),\"N/A\")"}, "C5": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",PIVOT.VALUE(15,\"quantity\"),\"N/A\")"}, "C6": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",iferror(C5/C4,\"NA\"),\"N/A\")"}, "C7": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",PIVOT.VALUE(14,\"__count:sum\",\"is_late\",\"false\"),\"N/A\")"}, "C8": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",PIVOT.VALUE(14,\"__count:sum\"),\"N/A\")"}, "C9": {"content": "=if(ODOO.FILTER.VALUE(\"Period\")<>\"\",iferror(C7/C8,\"NA\"),\"N/A\")"}}, "styles": {"A1:C1": 2, "A2:A9": 3}, "formats": {"B9": 1}, "borders": {}, "conditionalFormats": [], "figures": [], "tables": [{"range": "A1:C9", "type": "static"}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "840a0b8c-6a06-4db4-942b-5b0f3af77426", "name": "By product overview fill rate - On time", "colNumber": 22, "rowNumber": 101, "rows": {}, "cols": {"0": {"size": 33}, "1": {"size": 382}, "7": {"size": 99}}, "merges": ["G4:J4", "G20:J20", "I12:J12", "K4:N4", "G14:G15"], "cells": {"A20": {"content": "=_t(\"Row\")"}, "A21": {"content": "1"}, "A22": {"content": "2"}, "A23": {"content": "3"}, "A24": {"content": "4"}, "A25": {"content": "5"}, "A26": {"content": "6"}, "A27": {"content": "7"}, "A28": {"content": "8"}, "A29": {"content": "9"}, "A30": {"content": "10"}, "B1": {"content": "=_t(\"Fill rate sort by \")&H15& \" \"&H14"}, "B2": {"content": "=pivot(I14,10,false)"}, "B19": {"content": "=_t(\"On time rate sort by \")&H15& \" \"&H14"}, "B20": {"content": "=_t(\"Product\")"}, "B21": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A21)"}, "B22": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A22)"}, "B23": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A23)"}, "B24": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A24)"}, "B25": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A25)"}, "B26": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A26)"}, "B27": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A27)"}, "B28": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A28)"}, "B29": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A29)"}, "B30": {"content": "=PIVOT.HEADER($J$14,\"#product_id\",$A30)"}, "C20": {"content": "=_t(\"On time\")"}, "C21": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A21,\"is_late\",false)"}, "C22": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A22,\"is_late\",false)"}, "C23": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A23,\"is_late\",false)"}, "C24": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A24,\"is_late\",false)"}, "C25": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A25,\"is_late\",false)"}, "C26": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A26,\"is_late\",false)"}, "C27": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A27,\"is_late\",false)"}, "C28": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A28,\"is_late\",false)"}, "C29": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A29,\"is_late\",false)"}, "C30": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A30,\"is_late\",false)"}, "D20": {"content": "=_t(\"Total count\")"}, "D21": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A21)"}, "D22": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A22)"}, "D23": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A23)"}, "D24": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A24)"}, "D25": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A25)"}, "D26": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A26)"}, "D27": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A27)"}, "D28": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A28)"}, "D29": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A29)"}, "D30": {"content": "=PIVOT.VALUE($J$14,\"__count\",\"#product_id\",$A30)"}, "E20": {"content": "=_t(\"On time rate\")"}, "E21": {"content": "=if(C21<>\"\",C21/D21,\"\")"}, "E22": {"content": "=if(C22<>\"\",C22/D22,\"\")"}, "E23": {"content": "=if(C23<>\"\",C23/D23,\"\")"}, "E24": {"content": "=if(C24<>\"\",C24/D24,\"\")"}, "E25": {"content": "=if(C25<>\"\",C25/D25,\"\")"}, "E26": {"content": "=if(C26<>\"\",C26/D26,\"\")"}, "E27": {"content": "=if(C27<>\"\",C27/D27,\"\")"}, "E28": {"content": "=if(C28<>\"\",C28/D28,\"\")"}, "E29": {"content": "=if(C29<>\"\",C29/D29,\"\")"}, "E30": {"content": "=if(C30<>\"\",C30/D30,\"\")"}, "F3": {"content": "=_t(\"Fill rate\")"}, "F4": {"content": "=if(C4<>\"\",C4/D4,\"\")"}, "F5": {"content": "=if(C5<>\"\",C5/D5,\"\")"}, "F6": {"content": "=if(C6<>\"\",C6/D6,\"\")"}, "F7": {"content": "=if(C7<>\"\",C7/D7,\"\")"}, "F8": {"content": "=if(C8<>\"\",C8/D8,\"\")"}, "F9": {"content": "=if(C9<>\"\",C9/D9,\"\")"}, "F10": {"content": "=if(C10<>\"\",C10/D10,\"\")"}, "F11": {"content": "=if(C11<>\"\",C11/D11,\"\")"}, "F12": {"content": "=if(C12<>\"\",C12/D12,\"\")"}, "F13": {"content": "=if(C13<>\"\",C13/D13,\"\")"}, "G4": {"content": "=_t(\"Dynamic fields for filters fill rate\")"}, "G14": {"content": "=_t(\"Selected filter\")"}, "H5": {"content": "=_t(\"Filter\")"}, "H6": {"content": "=_t(\"Demand\")"}, "H7": {"content": "=_t(\"Count\")"}, "H14": {"content": "=ODOO.FILTER.VALUE(\"Sort graph by\")"}, "H15": {"content": "=ODOO.FILTER.VALUE(\"Top/Bottoms values\")"}, "I5": {"content": "=_t(\"Top\")"}, "I6": {"content": "23"}, "I7": {"content": "24"}, "I12": {"content": "=_t(\"Pivot to choose\")"}, "I13": {"content": "=_t(\"Fill rate\")"}, "I14": {"content": "=index(I6:J7,match(H14,H6:H7,0),match(H15,I5:J5,0))"}, "J5": {"content": "=_t(\"Bottom\")"}, "J6": {"content": "25"}, "J7": {"content": "26"}, "J13": {"content": "=_t(\"On Time\")"}, "J14": {"content": "=index(M6:N7,match(H14,L6:L7,0),match(H15,M5:N5,0))"}, "K4": {"content": "=_t(\"Dynamic fields for on time\")"}, "L5": {"content": "=_t(\"Filter\")"}, "L6": {"content": "=_t(\"Demand\")"}, "L7": {"content": "=_t(\"Count\")"}, "M5": {"content": "=_t(\"Top\")"}, "M6": {"content": "28"}, "M7": {"content": "30"}, "N5": {"content": "=_t(\"Bottom\")"}, "N6": {"content": "29"}, "N7": {"content": "31"}}, "styles": {"B21:B30": 2, "G4": 4, "K4": 4, "G14": 5, "I12": 6}, "formats": {"D21:D30": 2, "E21:E30": 3, "F4:F13": 3}, "borders": {"A4:A13": 3, "A21:A30": 3, "B1": 4, "C2:D2": 4, "B19:D19": 4, "B2": 5, "B20": 5, "B3": 6, "B4:B13": 7, "B21:B30": 7, "B14": 8, "B31": 8, "C3:C13": 9, "C20:C30": 9, "D4:D13": 9, "D21:D30": 9, "C14:D14": 10, "C31:D31": 10, "D3": 11, "D20": 11, "E3": 12, "E20": 13, "F3": 13}, "conditionalFormats": [], "figures": [], "tables": [{"range": "B2:F13", "type": "static"}, {"range": "B20:E30", "type": "static"}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "895d5943-4f00-41e4-960f-94fc0a852f25", "name": "WH Operations", "colNumber": 22, "rowNumber": 269, "rows": {}, "cols": {"0": {"size": 379}, "1": {"size": 56}, "4": {"size": 47}, "5": {"size": 232}, "6": {"size": 58}, "9": {"size": 61}, "10": {"size": 383}, "11": {"size": 83}}, "merges": [], "cells": {"A1": {"content": "=PIVOT(39)"}, "F1": {"content": "=PIVOT(40)"}, "K1": {"content": "=_t(\"Operations\")"}, "K2": {"content": "=iferror(UNIQUE(VSTACK(FILTER(A2:A270,A2:A270<>\"Total\",A2:A270<>\"\"),FILTER(F2:F270,F2:F270<>\"Total\",F2:F270<>\"\"))),\"\")"}, "L1": {"content": "=_t(\"Current period\")"}, "L2": {"content": "=iferror(if(K2<>\"\",VLOOKUP(K2,A:D,3,0),\"\"),\"\")"}, "L3": {"content": "=iferror(if(K3<>\"\",VLOOKUP(K3,A:D,3,0),\"\"),\"\")"}, "L4": {"content": "=iferror(if(K4<>\"\",VLOOKUP(K4,A:D,3,0),\"\"),\"\")"}, "L5": {"content": "=iferror(if(K5<>\"\",VLOOKUP(K5,A:D,3,0),\"\"),\"\")"}, "L6": {"content": "=iferror(if(K6<>\"\",VLOOKUP(K6,A:D,3,0),\"\"),\"\")"}, "L7": {"content": "=iferror(if(K7<>\"\",VLOOKUP(K7,A:D,3,0),\"\"),\"\")"}, "L8": {"content": "=iferror(if(K8<>\"\",VLOOKUP(K8,A:D,3,0),\"\"),\"\")"}, "L9": {"content": "=iferror(if(K9<>\"\",VLOOKUP(K9,A:D,3,0),\"\"),\"\")"}, "L10": {"content": "=iferror(if(K10<>\"\",VLOOKUP(K10,A:D,3,0),\"\"),\"\")"}, "L11": {"content": "=iferror(if(K11<>\"\",VLOOKUP(K11,A:D,3,0),\"\"),\"\")"}, "L12": {"content": "=iferror(if(K12<>\"\",VLOOKUP(K12,A:D,3,0),\"\"),\"\")"}, "L13": {"content": "=iferror(if(K13<>\"\",VLOOKUP(K13,A:D,3,0),\"\"),\"\")"}, "L14": {"content": "=iferror(if(K14<>\"\",VLOOKUP(K14,A:D,3,0),\"\"),\"\")"}, "L15": {"content": "=iferror(if(K15<>\"\",VLOOKUP(K15,A:D,3,0),\"\"),\"\")"}, "L16": {"content": "=iferror(if(K16<>\"\",VLOOKUP(K16,A:D,3,0),\"\"),\"\")"}, "L17": {"content": "=iferror(if(K17<>\"\",VLOOKUP(K17,A:D,3,0),\"\"),\"\")"}, "L18": {"content": "=iferror(if(K18<>\"\",VLOOKUP(K18,A:D,3,0),\"\"),\"\")"}, "L19": {"content": "=iferror(if(K19<>\"\",VLOOKUP(K19,A:D,3,0),\"\"),\"\")"}, "L20": {"content": "=iferror(if(K20<>\"\",VLOOKUP(K20,A:D,3,0),\"\"),\"\")"}, "L21": {"content": "=iferror(if(K21<>\"\",VLOOKUP(K21,A:D,3,0),\"\"),\"\")"}, "L22": {"content": "=iferror(if(K22<>\"\",VLOOKUP(K22,A:D,3,0),\"\"),\"\")"}, "L23": {"content": "=iferror(if(K23<>\"\",VLOOKUP(K23,A:D,3,0),\"\"),\"\")"}, "L24": {"content": "=iferror(if(K24<>\"\",VLOOKUP(K24,A:D,3,0),\"\"),\"\")"}, "L25": {"content": "=iferror(if(K25<>\"\",VLOOKUP(K25,A:D,3,0),\"\"),\"\")"}, "L26": {"content": "=iferror(if(K26<>\"\",VLOOKUP(K26,A:D,3,0),\"\"),\"\")"}, "L27": {"content": "=iferror(if(K27<>\"\",VLOOKUP(K27,A:D,3,0),\"\"),\"\")"}, "L28": {"content": "=iferror(if(K28<>\"\",VLOOKUP(K28,A:D,3,0),\"\"),\"\")"}, "L29": {"content": "=iferror(if(K29<>\"\",VLOOKUP(K29,A:D,3,0),\"\"),\"\")"}, "L30": {"content": "=iferror(if(K30<>\"\",VLOOKUP(K30,A:D,3,0),\"\"),\"\")"}, "L31": {"content": "=iferror(if(K31<>\"\",VLOOKUP(K31,A:D,3,0),\"\"),\"\")"}, "L32": {"content": "=iferror(if(K32<>\"\",VLOOKUP(K32,A:D,3,0),\"\"),\"\")"}, "L33": {"content": "=iferror(if(K33<>\"\",VLOOKUP(K33,A:D,3,0),\"\"),\"\")"}, "L34": {"content": "=iferror(if(K34<>\"\",VLOOKUP(K34,A:D,3,0),\"\"),\"\")"}, "L35": {"content": "=iferror(if(K35<>\"\",VLOOKUP(K35,A:D,3,0),\"\"),\"\")"}, "L36": {"content": "=iferror(if(K36<>\"\",VLOOKUP(K36,A:D,3,0),\"\"),\"\")"}, "L37": {"content": "=iferror(if(K37<>\"\",VLOOKUP(K37,A:D,3,0),\"\"),\"\")"}, "L38": {"content": "=iferror(if(K38<>\"\",VLOOKUP(K38,A:D,3,0),\"\"),\"\")"}, "L39": {"content": "=iferror(if(K39<>\"\",VLOOKUP(K39,A:D,3,0),\"\"),\"\")"}, "L40": {"content": "=iferror(if(K40<>\"\",VLOOKUP(K40,A:D,3,0),\"\"),\"\")"}, "L41": {"content": "=iferror(if(K41<>\"\",VLOOKUP(K41,A:D,3,0),\"\"),\"\")"}, "L42": {"content": "=iferror(if(K42<>\"\",VLOOKUP(K42,A:D,3,0),\"\"),\"\")"}, "L43": {"content": "=iferror(if(K43<>\"\",VLOOKUP(K43,A:D,3,0),\"\"),\"\")"}, "L44": {"content": "=iferror(if(K44<>\"\",VLOOKUP(K44,A:D,3,0),\"\"),\"\")"}, "L45": {"content": "=iferror(if(K45<>\"\",VLOOKUP(K45,A:D,3,0),\"\"),\"\")"}, "L46": {"content": "=iferror(if(K46<>\"\",VLOOKUP(K46,A:D,3,0),\"\"),\"\")"}, "L47": {"content": "=iferror(if(K47<>\"\",VLOOKUP(K47,A:D,3,0),\"\"),\"\")"}, "L48": {"content": "=iferror(if(K48<>\"\",VLOOKUP(K48,A:D,3,0),\"\"),\"\")"}, "L49": {"content": "=iferror(if(K49<>\"\",VLOOKUP(K49,A:D,3,0),\"\"),\"\")"}, "L50": {"content": "=iferror(if(K50<>\"\",VLOOKUP(K50,A:D,3,0),\"\"),\"\")"}, "L51": {"content": "=iferror(if(K51<>\"\",VLOOKUP(K51,A:D,3,0),\"\"),\"\")"}, "L52": {"content": "=iferror(if(K52<>\"\",VLOOKUP(K52,A:D,3,0),\"\"),\"\")"}, "L53": {"content": "=iferror(if(K53<>\"\",VLOOKUP(K53,A:D,3,0),\"\"),\"\")"}, "L54": {"content": "=iferror(if(K54<>\"\",VLOOKUP(K54,A:D,3,0),\"\"),\"\")"}, "L55": {"content": "=iferror(if(K55<>\"\",VLOOKUP(K55,A:D,3,0),\"\"),\"\")"}, "L56": {"content": "=iferror(if(K56<>\"\",VLOOKUP(K56,A:D,3,0),\"\"),\"\")"}, "L57": {"content": "=iferror(if(K57<>\"\",VLOOKUP(K57,A:D,3,0),\"\"),\"\")"}, "L58": {"content": "=iferror(if(K58<>\"\",VLOOKUP(K58,A:D,3,0),\"\"),\"\")"}, "L59": {"content": "=iferror(if(K59<>\"\",VLOOKUP(K59,A:D,3,0),\"\"),\"\")"}, "L60": {"content": "=iferror(if(K60<>\"\",VLOOKUP(K60,A:D,3,0),\"\"),\"\")"}, "L61": {"content": "=iferror(if(K61<>\"\",VLOOKUP(K61,A:D,3,0),\"\"),\"\")"}, "L62": {"content": "=iferror(if(K62<>\"\",VLOOKUP(K62,A:D,3,0),\"\"),\"\")"}, "L63": {"content": "=iferror(if(K63<>\"\",VLOOKUP(K63,A:D,3,0),\"\"),\"\")"}, "L64": {"content": "=iferror(if(K64<>\"\",VLOOKUP(K64,A:D,3,0),\"\"),\"\")"}, "L65": {"content": "=iferror(if(K65<>\"\",VLOOKUP(K65,A:D,3,0),\"\"),\"\")"}, "L66": {"content": "=iferror(if(K66<>\"\",VLOOKUP(K66,A:D,3,0),\"\"),\"\")"}, "L67": {"content": "=iferror(if(K67<>\"\",VLOOKUP(K67,A:D,3,0),\"\"),\"\")"}, "L68": {"content": "=iferror(if(K68<>\"\",VLOOKUP(K68,A:D,3,0),\"\"),\"\")"}, "L69": {"content": "=iferror(if(K69<>\"\",VLOOKUP(K69,A:D,3,0),\"\"),\"\")"}, "L70": {"content": "=iferror(if(K70<>\"\",VLOOKUP(K70,A:D,3,0),\"\"),\"\")"}, "L71": {"content": "=iferror(if(K71<>\"\",VLOOKUP(K71,A:D,3,0),\"\"),\"\")"}, "L72": {"content": "=iferror(if(K72<>\"\",VLOOKUP(K72,A:D,3,0),\"\"),\"\")"}, "L73": {"content": "=iferror(if(K73<>\"\",VLOOKUP(K73,A:D,3,0),\"\"),\"\")"}, "L74": {"content": "=iferror(if(K74<>\"\",VLOOKUP(K74,A:D,3,0),\"\"),\"\")"}, "L75": {"content": "=iferror(if(K75<>\"\",VLOOKUP(K75,A:D,3,0),\"\"),\"\")"}, "L76": {"content": "=iferror(if(K76<>\"\",VLOOKUP(K76,A:D,3,0),\"\"),\"\")"}, "L77": {"content": "=iferror(if(K77<>\"\",VLOOKUP(K77,A:D,3,0),\"\"),\"\")"}, "L78": {"content": "=iferror(if(K78<>\"\",VLOOKUP(K78,A:D,3,0),\"\"),\"\")"}, "L79": {"content": "=iferror(if(K79<>\"\",VLOOKUP(K79,A:D,3,0),\"\"),\"\")"}, "L80": {"content": "=iferror(if(K80<>\"\",VLOOKUP(K80,A:D,3,0),\"\"),\"\")"}, "L81": {"content": "=iferror(if(K81<>\"\",VLOOKUP(K81,A:D,3,0),\"\"),\"\")"}, "L82": {"content": "=iferror(if(K82<>\"\",VLOOKUP(K82,A:D,3,0),\"\"),\"\")"}, "L83": {"content": "=iferror(if(K83<>\"\",VLOOKUP(K83,A:D,3,0),\"\"),\"\")"}, "L84": {"content": "=iferror(if(K84<>\"\",VLOOKUP(K84,A:D,3,0),\"\"),\"\")"}, "L85": {"content": "=iferror(if(K85<>\"\",VLOOKUP(K85,A:D,3,0),\"\"),\"\")"}, "L86": {"content": "=iferror(if(K86<>\"\",VLOOKUP(K86,A:D,3,0),\"\"),\"\")"}, "L87": {"content": "=iferror(if(K87<>\"\",VLOOKUP(K87,A:D,3,0),\"\"),\"\")"}, "L88": {"content": "=iferror(if(K88<>\"\",VLOOKUP(K88,A:D,3,0),\"\"),\"\")"}, "L89": {"content": "=iferror(if(K89<>\"\",VLOOKUP(K89,A:D,3,0),\"\"),\"\")"}, "L90": {"content": "=iferror(if(K90<>\"\",VLOOKUP(K90,A:D,3,0),\"\"),\"\")"}, "L91": {"content": "=iferror(if(K91<>\"\",VLOOKUP(K91,A:D,3,0),\"\"),\"\")"}, "L92": {"content": "=iferror(if(K92<>\"\",VLOOKUP(K92,A:D,3,0),\"\"),\"\")"}, "L93": {"content": "=iferror(if(K93<>\"\",VLOOKUP(K93,A:D,3,0),\"\"),\"\")"}, "L94": {"content": "=iferror(if(K94<>\"\",VLOOKUP(K94,A:D,3,0),\"\"),\"\")"}, "L95": {"content": "=iferror(if(K95<>\"\",VLOOKUP(K95,A:D,3,0),\"\"),\"\")"}, "L96": {"content": "=iferror(if(K96<>\"\",VLOOKUP(K96,A:D,3,0),\"\"),\"\")"}, "L97": {"content": "=iferror(if(K97<>\"\",VLOOKUP(K97,A:D,3,0),\"\"),\"\")"}, "L98": {"content": "=iferror(if(K98<>\"\",VLOOKUP(K98,A:D,3,0),\"\"),\"\")"}, "L99": {"content": "=iferror(if(K99<>\"\",VLOOKUP(K99,A:D,3,0),\"\"),\"\")"}, "L100": {"content": "=iferror(if(K100<>\"\",VLOOKUP(K100,A:D,3,0),\"\"),\"\")"}, "L101": {"content": "=iferror(if(K101<>\"\",VLOOKUP(K101,A:D,3,0),\"\"),\"\")"}, "L102": {"content": "=iferror(if(K102<>\"\",VLOOKUP(K102,A:D,3,0),\"\"),\"\")"}, "L103": {"content": "=iferror(if(K103<>\"\",VLOOKUP(K103,A:D,3,0),\"\"),\"\")"}, "L104": {"content": "=iferror(if(K104<>\"\",VLOOKUP(K104,A:D,3,0),\"\"),\"\")"}, "L105": {"content": "=iferror(if(K105<>\"\",VLOOKUP(K105,A:D,3,0),\"\"),\"\")"}, "L106": {"content": "=iferror(if(K106<>\"\",VLOOKUP(K106,A:D,3,0),\"\"),\"\")"}, "L107": {"content": "=iferror(if(K107<>\"\",VLOOKUP(K107,A:D,3,0),\"\"),\"\")"}, "L108": {"content": "=iferror(if(K108<>\"\",VLOOKUP(K108,A:D,3,0),\"\"),\"\")"}, "L109": {"content": "=iferror(if(K109<>\"\",VLOOKUP(K109,A:D,3,0),\"\"),\"\")"}, "L110": {"content": "=iferror(if(K110<>\"\",VLOOKUP(K110,A:D,3,0),\"\"),\"\")"}, "L111": {"content": "=iferror(if(K111<>\"\",VLOOKUP(K111,A:D,3,0),\"\"),\"\")"}, "L112": {"content": "=iferror(if(K112<>\"\",VLOOKUP(K112,A:D,3,0),\"\"),\"\")"}, "L113": {"content": "=iferror(if(K113<>\"\",VLOOKUP(K113,A:D,3,0),\"\"),\"\")"}, "L114": {"content": "=iferror(if(K114<>\"\",VLOOKUP(K114,A:D,3,0),\"\"),\"\")"}, "L115": {"content": "=iferror(if(K115<>\"\",VLOOKUP(K115,A:D,3,0),\"\"),\"\")"}, "L116": {"content": "=iferror(if(K116<>\"\",VLOOKUP(K116,A:D,3,0),\"\"),\"\")"}, "L117": {"content": "=iferror(if(K117<>\"\",VLOOKUP(K117,A:D,3,0),\"\"),\"\")"}, "L118": {"content": "=iferror(if(K118<>\"\",VLOOKUP(K118,A:D,3,0),\"\"),\"\")"}, "L119": {"content": "=iferror(if(K119<>\"\",VLOOKUP(K119,A:D,3,0),\"\"),\"\")"}, "L120": {"content": "=iferror(if(K120<>\"\",VLOOKUP(K120,A:D,3,0),\"\"),\"\")"}, "L121": {"content": "=iferror(if(K121<>\"\",VLOOKUP(K121,A:D,3,0),\"\"),\"\")"}, "L122": {"content": "=iferror(if(K122<>\"\",VLOOKUP(K122,A:D,3,0),\"\"),\"\")"}, "L123": {"content": "=iferror(if(K123<>\"\",VLOOKUP(K123,A:D,3,0),\"\"),\"\")"}, "L124": {"content": "=iferror(if(K124<>\"\",VLOOKUP(K124,A:D,3,0),\"\"),\"\")"}, "L125": {"content": "=iferror(if(K125<>\"\",VLOOKUP(K125,A:D,3,0),\"\"),\"\")"}, "L126": {"content": "=iferror(if(K126<>\"\",VLOOKUP(K126,A:D,3,0),\"\"),\"\")"}, "L127": {"content": "=iferror(if(K127<>\"\",VLOOKUP(K127,A:D,3,0),\"\"),\"\")"}, "L128": {"content": "=iferror(if(K128<>\"\",VLOOKUP(K128,A:D,3,0),\"\"),\"\")"}, "L129": {"content": "=iferror(if(K129<>\"\",VLOOKUP(K129,A:D,3,0),\"\"),\"\")"}, "L130": {"content": "=iferror(if(K130<>\"\",VLOOKUP(K130,A:D,3,0),\"\"),\"\")"}, "L131": {"content": "=iferror(if(K131<>\"\",VLOOKUP(K131,A:D,3,0),\"\"),\"\")"}, "L132": {"content": "=iferror(if(K132<>\"\",VLOOKUP(K132,A:D,3,0),\"\"),\"\")"}, "L133": {"content": "=iferror(if(K133<>\"\",VLOOKUP(K133,A:D,3,0),\"\"),\"\")"}, "L134": {"content": "=iferror(if(K134<>\"\",VLOOKUP(K134,A:D,3,0),\"\"),\"\")"}, "L135": {"content": "=iferror(if(K135<>\"\",VLOOKUP(K135,A:D,3,0),\"\"),\"\")"}, "L136": {"content": "=iferror(if(K136<>\"\",VLOOKUP(K136,A:D,3,0),\"\"),\"\")"}, "L137": {"content": "=iferror(if(K137<>\"\",VLOOKUP(K137,A:D,3,0),\"\"),\"\")"}, "L138": {"content": "=iferror(if(K138<>\"\",VLOOKUP(K138,A:D,3,0),\"\"),\"\")"}, "L139": {"content": "=iferror(if(K139<>\"\",VLOOKUP(K139,A:D,3,0),\"\"),\"\")"}, "L140": {"content": "=iferror(if(K140<>\"\",VLOOKUP(K140,A:D,3,0),\"\"),\"\")"}, "L141": {"content": "=iferror(if(K141<>\"\",VLOOKUP(K141,A:D,3,0),\"\"),\"\")"}, "L142": {"content": "=iferror(if(K142<>\"\",VLOOKUP(K142,A:D,3,0),\"\"),\"\")"}, "L143": {"content": "=iferror(if(K143<>\"\",VLOOKUP(K143,A:D,3,0),\"\"),\"\")"}, "L144": {"content": "=iferror(if(K144<>\"\",VLOOKUP(K144,A:D,3,0),\"\"),\"\")"}, "L145": {"content": "=iferror(if(K145<>\"\",VLOOKUP(K145,A:D,3,0),\"\"),\"\")"}, "L146": {"content": "=iferror(if(K146<>\"\",VLOOKUP(K146,A:D,3,0),\"\"),\"\")"}, "L147": {"content": "=iferror(if(K147<>\"\",VLOOKUP(K147,A:D,3,0),\"\"),\"\")"}, "L148": {"content": "=iferror(if(K148<>\"\",VLOOKUP(K148,A:D,3,0),\"\"),\"\")"}, "L149": {"content": "=iferror(if(K149<>\"\",VLOOKUP(K149,A:D,3,0),\"\"),\"\")"}, "L150": {"content": "=iferror(if(K150<>\"\",VLOOKUP(K150,A:D,3,0),\"\"),\"\")"}, "L151": {"content": "=iferror(if(K151<>\"\",VLOOKUP(K151,A:D,3,0),\"\"),\"\")"}, "L152": {"content": "=iferror(if(K152<>\"\",VLOOKUP(K152,A:D,3,0),\"\"),\"\")"}, "L153": {"content": "=iferror(if(K153<>\"\",VLOOKUP(K153,A:D,3,0),\"\"),\"\")"}, "L154": {"content": "=iferror(if(K154<>\"\",VLOOKUP(K154,A:D,3,0),\"\"),\"\")"}, "L155": {"content": "=iferror(if(K155<>\"\",VLOOKUP(K155,A:D,3,0),\"\"),\"\")"}, "L156": {"content": "=iferror(if(K156<>\"\",VLOOKUP(K156,A:D,3,0),\"\"),\"\")"}, "L157": {"content": "=iferror(if(K157<>\"\",VLOOKUP(K157,A:D,3,0),\"\"),\"\")"}, "L158": {"content": "=iferror(if(K158<>\"\",VLOOKUP(K158,A:D,3,0),\"\"),\"\")"}, "L159": {"content": "=iferror(if(K159<>\"\",VLOOKUP(K159,A:D,3,0),\"\"),\"\")"}, "L160": {"content": "=iferror(if(K160<>\"\",VLOOKUP(K160,A:D,3,0),\"\"),\"\")"}, "L161": {"content": "=iferror(if(K161<>\"\",VLOOKUP(K161,A:D,3,0),\"\"),\"\")"}, "L162": {"content": "=iferror(if(K162<>\"\",VLOOKUP(K162,A:D,3,0),\"\"),\"\")"}, "L163": {"content": "=iferror(if(K163<>\"\",VLOOKUP(K163,A:D,3,0),\"\"),\"\")"}, "L164": {"content": "=iferror(if(K164<>\"\",VLOOKUP(K164,A:D,3,0),\"\"),\"\")"}, "L165": {"content": "=iferror(if(K165<>\"\",VLOOKUP(K165,A:D,3,0),\"\"),\"\")"}, "L166": {"content": "=iferror(if(K166<>\"\",VLOOKUP(K166,A:D,3,0),\"\"),\"\")"}, "L167": {"content": "=iferror(if(K167<>\"\",VLOOKUP(K167,A:D,3,0),\"\"),\"\")"}, "L168": {"content": "=iferror(if(K168<>\"\",VLOOKUP(K168,A:D,3,0),\"\"),\"\")"}, "L169": {"content": "=iferror(if(K169<>\"\",VLOOKUP(K169,A:D,3,0),\"\"),\"\")"}, "L170": {"content": "=iferror(if(K170<>\"\",VLOOKUP(K170,A:D,3,0),\"\"),\"\")"}, "L171": {"content": "=iferror(if(K171<>\"\",VLOOKUP(K171,A:D,3,0),\"\"),\"\")"}, "L172": {"content": "=iferror(if(K172<>\"\",VLOOKUP(K172,A:D,3,0),\"\"),\"\")"}, "L173": {"content": "=iferror(if(K173<>\"\",VLOOKUP(K173,A:D,3,0),\"\"),\"\")"}, "L174": {"content": "=iferror(if(K174<>\"\",VLOOKUP(K174,A:D,3,0),\"\"),\"\")"}, "L175": {"content": "=iferror(if(K175<>\"\",VLOOKUP(K175,A:D,3,0),\"\"),\"\")"}, "L176": {"content": "=iferror(if(K176<>\"\",VLOOKUP(K176,A:D,3,0),\"\"),\"\")"}, "L177": {"content": "=iferror(if(K177<>\"\",VLOOKUP(K177,A:D,3,0),\"\"),\"\")"}, "L178": {"content": "=iferror(if(K178<>\"\",VLOOKUP(K178,A:D,3,0),\"\"),\"\")"}, "L179": {"content": "=iferror(if(K179<>\"\",VLOOKUP(K179,A:D,3,0),\"\"),\"\")"}, "L180": {"content": "=iferror(if(K180<>\"\",VLOOKUP(K180,A:D,3,0),\"\"),\"\")"}, "L181": {"content": "=iferror(if(K181<>\"\",VLOOKUP(K181,A:D,3,0),\"\"),\"\")"}, "L182": {"content": "=iferror(if(K182<>\"\",VLOOKUP(K182,A:D,3,0),\"\"),\"\")"}, "L183": {"content": "=iferror(if(K183<>\"\",VLOOKUP(K183,A:D,3,0),\"\"),\"\")"}, "L184": {"content": "=iferror(if(K184<>\"\",VLOOKUP(K184,A:D,3,0),\"\"),\"\")"}, "L185": {"content": "=iferror(if(K185<>\"\",VLOOKUP(K185,A:D,3,0),\"\"),\"\")"}, "L186": {"content": "=iferror(if(K186<>\"\",VLOOKUP(K186,A:D,3,0),\"\"),\"\")"}, "L187": {"content": "=iferror(if(K187<>\"\",VLOOKUP(K187,A:D,3,0),\"\"),\"\")"}, "L188": {"content": "=iferror(if(K188<>\"\",VLOOKUP(K188,A:D,3,0),\"\"),\"\")"}, "L189": {"content": "=iferror(if(K189<>\"\",VLOOKUP(K189,A:D,3,0),\"\"),\"\")"}, "L190": {"content": "=iferror(if(K190<>\"\",VLOOKUP(K190,A:D,3,0),\"\"),\"\")"}, "L191": {"content": "=iferror(if(K191<>\"\",VLOOKUP(K191,A:D,3,0),\"\"),\"\")"}, "L192": {"content": "=iferror(if(K192<>\"\",VLOOKUP(K192,A:D,3,0),\"\"),\"\")"}, "L193": {"content": "=iferror(if(K193<>\"\",VLOOKUP(K193,A:D,3,0),\"\"),\"\")"}, "L194": {"content": "=iferror(if(K194<>\"\",VLOOKUP(K194,A:D,3,0),\"\"),\"\")"}, "L195": {"content": "=iferror(if(K195<>\"\",VLOOKUP(K195,A:D,3,0),\"\"),\"\")"}, "L196": {"content": "=iferror(if(K196<>\"\",VLOOKUP(K196,A:D,3,0),\"\"),\"\")"}, "L197": {"content": "=iferror(if(K197<>\"\",VLOOKUP(K197,A:D,3,0),\"\"),\"\")"}, "L198": {"content": "=iferror(if(K198<>\"\",VLOOKUP(K198,A:D,3,0),\"\"),\"\")"}, "L199": {"content": "=iferror(if(K199<>\"\",VLOOKUP(K199,A:D,3,0),\"\"),\"\")"}, "L200": {"content": "=iferror(if(K200<>\"\",VLOOKUP(K200,A:D,3,0),\"\"),\"\")"}, "L201": {"content": "=iferror(if(K201<>\"\",VLOOKUP(K201,A:D,3,0),\"\"),\"\")"}, "L202": {"content": "=iferror(if(K202<>\"\",VLOOKUP(K202,A:D,3,0),\"\"),\"\")"}, "L203": {"content": "=iferror(if(K203<>\"\",VLOOKUP(K203,A:D,3,0),\"\"),\"\")"}, "L204": {"content": "=iferror(if(K204<>\"\",VLOOKUP(K204,A:D,3,0),\"\"),\"\")"}, "L205": {"content": "=iferror(if(K205<>\"\",VLOOKUP(K205,A:D,3,0),\"\"),\"\")"}, "L206": {"content": "=iferror(if(K206<>\"\",VLOOKUP(K206,A:D,3,0),\"\"),\"\")"}, "L207": {"content": "=iferror(if(K207<>\"\",VLOOKUP(K207,A:D,3,0),\"\"),\"\")"}, "L208": {"content": "=iferror(if(K208<>\"\",VLOOKUP(K208,A:D,3,0),\"\"),\"\")"}, "L209": {"content": "=iferror(if(K209<>\"\",VLOOKUP(K209,A:D,3,0),\"\"),\"\")"}, "L210": {"content": "=iferror(if(K210<>\"\",VLOOKUP(K210,A:D,3,0),\"\"),\"\")"}, "L211": {"content": "=iferror(if(K211<>\"\",VLOOKUP(K211,A:D,3,0),\"\"),\"\")"}, "L212": {"content": "=iferror(if(K212<>\"\",VLOOKUP(K212,A:D,3,0),\"\"),\"\")"}, "L213": {"content": "=iferror(if(K213<>\"\",VLOOKUP(K213,A:D,3,0),\"\"),\"\")"}, "L214": {"content": "=iferror(if(K214<>\"\",VLOOKUP(K214,A:D,3,0),\"\"),\"\")"}, "L215": {"content": "=iferror(if(K215<>\"\",VLOOKUP(K215,A:D,3,0),\"\"),\"\")"}, "L216": {"content": "=iferror(if(K216<>\"\",VLOOKUP(K216,A:D,3,0),\"\"),\"\")"}, "L217": {"content": "=iferror(if(K217<>\"\",VLOOKUP(K217,A:D,3,0),\"\"),\"\")"}, "L218": {"content": "=iferror(if(K218<>\"\",VLOOKUP(K218,A:D,3,0),\"\"),\"\")"}, "L219": {"content": "=iferror(if(K219<>\"\",VLOOKUP(K219,A:D,3,0),\"\"),\"\")"}, "L220": {"content": "=iferror(if(K220<>\"\",VLOOKUP(K220,A:D,3,0),\"\"),\"\")"}, "L221": {"content": "=iferror(if(K221<>\"\",VLOOKUP(K221,A:D,3,0),\"\"),\"\")"}, "L222": {"content": "=iferror(if(K222<>\"\",VLOOKUP(K222,A:D,3,0),\"\"),\"\")"}, "L223": {"content": "=iferror(if(K223<>\"\",VLOOKUP(K223,A:D,3,0),\"\"),\"\")"}, "L224": {"content": "=iferror(if(K224<>\"\",VLOOKUP(K224,A:D,3,0),\"\"),\"\")"}, "L225": {"content": "=iferror(if(K225<>\"\",VLOOKUP(K225,A:D,3,0),\"\"),\"\")"}, "L226": {"content": "=iferror(if(K226<>\"\",VLOOKUP(K226,A:D,3,0),\"\"),\"\")"}, "L227": {"content": "=iferror(if(K227<>\"\",VLOOKUP(K227,A:D,3,0),\"\"),\"\")"}, "L228": {"content": "=iferror(if(K228<>\"\",VLOOKUP(K228,A:D,3,0),\"\"),\"\")"}, "L229": {"content": "=iferror(if(K229<>\"\",VLOOKUP(K229,A:D,3,0),\"\"),\"\")"}, "L230": {"content": "=iferror(if(K230<>\"\",VLOOKUP(K230,A:D,3,0),\"\"),\"\")"}, "L231": {"content": "=iferror(if(K231<>\"\",VLOOKUP(K231,A:D,3,0),\"\"),\"\")"}, "L232": {"content": "=iferror(if(K232<>\"\",VLOOKUP(K232,A:D,3,0),\"\"),\"\")"}, "L233": {"content": "=iferror(if(K233<>\"\",VLOOKUP(K233,A:D,3,0),\"\"),\"\")"}, "L234": {"content": "=iferror(if(K234<>\"\",VLOOKUP(K234,A:D,3,0),\"\"),\"\")"}, "L235": {"content": "=iferror(if(K235<>\"\",VLOOKUP(K235,A:D,3,0),\"\"),\"\")"}, "L236": {"content": "=iferror(if(K236<>\"\",VLOOKUP(K236,A:D,3,0),\"\"),\"\")"}, "L237": {"content": "=iferror(if(K237<>\"\",VLOOKUP(K237,A:D,3,0),\"\"),\"\")"}, "L238": {"content": "=iferror(if(K238<>\"\",VLOOKUP(K238,A:D,3,0),\"\"),\"\")"}, "L239": {"content": "=iferror(if(K239<>\"\",VLOOKUP(K239,A:D,3,0),\"\"),\"\")"}, "L240": {"content": "=iferror(if(K240<>\"\",VLOOKUP(K240,A:D,3,0),\"\"),\"\")"}, "L241": {"content": "=iferror(if(K241<>\"\",VLOOKUP(K241,A:D,3,0),\"\"),\"\")"}, "L242": {"content": "=iferror(if(K242<>\"\",VLOOKUP(K242,A:D,3,0),\"\"),\"\")"}, "L243": {"content": "=iferror(if(K243<>\"\",VLOOKUP(K243,A:D,3,0),\"\"),\"\")"}, "L244": {"content": "=iferror(if(K244<>\"\",VLOOKUP(K244,A:D,3,0),\"\"),\"\")"}, "L245": {"content": "=iferror(if(K245<>\"\",VLOOKUP(K245,A:D,3,0),\"\"),\"\")"}, "L246": {"content": "=iferror(if(K246<>\"\",VLOOKUP(K246,A:D,3,0),\"\"),\"\")"}, "L247": {"content": "=iferror(if(K247<>\"\",VLOOKUP(K247,A:D,3,0),\"\"),\"\")"}, "L248": {"content": "=iferror(if(K248<>\"\",VLOOKUP(K248,A:D,3,0),\"\"),\"\")"}, "L249": {"content": "=iferror(if(K249<>\"\",VLOOKUP(K249,A:D,3,0),\"\"),\"\")"}, "L250": {"content": "=iferror(if(K250<>\"\",VLOOKUP(K250,A:D,3,0),\"\"),\"\")"}, "L251": {"content": "=iferror(if(K251<>\"\",VLOOKUP(K251,A:D,3,0),\"\"),\"\")"}, "L252": {"content": "=iferror(if(K252<>\"\",VLOOKUP(K252,A:D,3,0),\"\"),\"\")"}, "L253": {"content": "=iferror(if(K253<>\"\",VLOOKUP(K253,A:D,3,0),\"\"),\"\")"}, "L254": {"content": "=iferror(if(K254<>\"\",VLOOKUP(K254,A:D,3,0),\"\"),\"\")"}, "L255": {"content": "=iferror(if(K255<>\"\",VLOOKUP(K255,A:D,3,0),\"\"),\"\")"}, "L256": {"content": "=iferror(if(K256<>\"\",VLOOKUP(K256,A:D,3,0),\"\"),\"\")"}, "L257": {"content": "=iferror(if(K257<>\"\",VLOOKUP(K257,A:D,3,0),\"\"),\"\")"}, "L258": {"content": "=iferror(if(K258<>\"\",VLOOKUP(K258,A:D,3,0),\"\"),\"\")"}, "L259": {"content": "=iferror(if(K259<>\"\",VLOOKUP(K259,A:D,3,0),\"\"),\"\")"}, "L260": {"content": "=iferror(if(K260<>\"\",VLOOKUP(K260,A:D,3,0),\"\"),\"\")"}, "L261": {"content": "=iferror(if(K261<>\"\",VLOOKUP(K261,A:D,3,0),\"\"),\"\")"}, "L262": {"content": "=iferror(if(K262<>\"\",VLOOKUP(K262,A:D,3,0),\"\"),\"\")"}, "L263": {"content": "=iferror(if(K263<>\"\",VLOOKUP(K263,A:D,3,0),\"\"),\"\")"}, "L264": {"content": "=iferror(if(K264<>\"\",VLOOKUP(K264,A:D,3,0),\"\"),\"\")"}, "L265": {"content": "=iferror(if(K265<>\"\",VLOOKUP(K265,A:D,3,0),\"\"),\"\")"}, "L266": {"content": "=iferror(if(K266<>\"\",VLOOKUP(K266,A:D,3,0),\"\"),\"\")"}, "L267": {"content": "=iferror(if(K267<>\"\",VLOOKUP(K267,A:D,3,0),\"\"),\"\")"}, "L268": {"content": "=iferror(if(K268<>\"\",VLOOKUP(K268,A:D,3,0),\"\"),\"\")"}, "L269": {"content": "=iferror(if(K269<>\"\",VLOOKUP(K269,A:D,3,0),\"\"),\"\")"}, "M1": {"content": "=_t(\"Previous period\")"}, "M2": {"content": "=iferror(if(and(K2<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K2,F:I,3,0),\"\"),\"\")"}, "M3": {"content": "=iferror(if(and(K3<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K3,F:I,3,0),\"\"),\"\")"}, "M4": {"content": "=iferror(if(and(K4<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K4,F:I,3,0),\"\"),\"\")"}, "M5": {"content": "=iferror(if(and(K5<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K5,F:I,3,0),\"\"),\"\")"}, "M6": {"content": "=iferror(if(and(K6<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K6,F:I,3,0),\"\"),\"\")"}, "M7": {"content": "=iferror(if(and(K7<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K7,F:I,3,0),\"\"),\"\")"}, "M8": {"content": "=iferror(if(and(K8<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K8,F:I,3,0),\"\"),\"\")"}, "M9": {"content": "=iferror(if(and(K9<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K9,F:I,3,0),\"\"),\"\")"}, "M10": {"content": "=iferror(if(and(K10<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K10,F:I,3,0),\"\"),\"\")"}, "M11": {"content": "=iferror(if(and(K11<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K11,F:I,3,0),\"\"),\"\")"}, "M12": {"content": "=iferror(if(and(K12<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K12,F:I,3,0),\"\"),\"\")"}, "M13": {"content": "=iferror(if(and(K13<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K13,F:I,3,0),\"\"),\"\")"}, "M14": {"content": "=iferror(if(and(K14<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K14,F:I,3,0),\"\"),\"\")"}, "M15": {"content": "=iferror(if(and(K15<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K15,F:I,3,0),\"\"),\"\")"}, "M16": {"content": "=iferror(if(and(K16<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K16,F:I,3,0),\"\"),\"\")"}, "M17": {"content": "=iferror(if(and(K17<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K17,F:I,3,0),\"\"),\"\")"}, "M18": {"content": "=iferror(if(and(K18<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K18,F:I,3,0),\"\"),\"\")"}, "M19": {"content": "=iferror(if(and(K19<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K19,F:I,3,0),\"\"),\"\")"}, "M20": {"content": "=iferror(if(and(K20<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K20,F:I,3,0),\"\"),\"\")"}, "M21": {"content": "=iferror(if(and(K21<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K21,F:I,3,0),\"\"),\"\")"}, "M22": {"content": "=iferror(if(and(K22<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K22,F:I,3,0),\"\"),\"\")"}, "M23": {"content": "=iferror(if(and(K23<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K23,F:I,3,0),\"\"),\"\")"}, "M24": {"content": "=iferror(if(and(K24<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K24,F:I,3,0),\"\"),\"\")"}, "M25": {"content": "=iferror(if(and(K25<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K25,F:I,3,0),\"\"),\"\")"}, "M26": {"content": "=iferror(if(and(K26<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K26,F:I,3,0),\"\"),\"\")"}, "M27": {"content": "=iferror(if(and(K27<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K27,F:I,3,0),\"\"),\"\")"}, "M28": {"content": "=iferror(if(and(K28<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K28,F:I,3,0),\"\"),\"\")"}, "M29": {"content": "=iferror(if(and(K29<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K29,F:I,3,0),\"\"),\"\")"}, "M30": {"content": "=iferror(if(and(K30<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K30,F:I,3,0),\"\"),\"\")"}, "M31": {"content": "=iferror(if(and(K31<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K31,F:I,3,0),\"\"),\"\")"}, "M32": {"content": "=iferror(if(and(K32<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K32,F:I,3,0),\"\"),\"\")"}, "M33": {"content": "=iferror(if(and(K33<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K33,F:I,3,0),\"\"),\"\")"}, "M34": {"content": "=iferror(if(and(K34<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K34,F:I,3,0),\"\"),\"\")"}, "M35": {"content": "=iferror(if(and(K35<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K35,F:I,3,0),\"\"),\"\")"}, "M36": {"content": "=iferror(if(and(K36<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K36,F:I,3,0),\"\"),\"\")"}, "M37": {"content": "=iferror(if(and(K37<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K37,F:I,3,0),\"\"),\"\")"}, "M38": {"content": "=iferror(if(and(K38<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K38,F:I,3,0),\"\"),\"\")"}, "M39": {"content": "=iferror(if(and(K39<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K39,F:I,3,0),\"\"),\"\")"}, "M40": {"content": "=iferror(if(and(K40<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K40,F:I,3,0),\"\"),\"\")"}, "M41": {"content": "=iferror(if(and(K41<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K41,F:I,3,0),\"\"),\"\")"}, "M42": {"content": "=iferror(if(and(K42<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K42,F:I,3,0),\"\"),\"\")"}, "M43": {"content": "=iferror(if(and(K43<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K43,F:I,3,0),\"\"),\"\")"}, "M44": {"content": "=iferror(if(and(K44<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K44,F:I,3,0),\"\"),\"\")"}, "M45": {"content": "=iferror(if(and(K45<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K45,F:I,3,0),\"\"),\"\")"}, "M46": {"content": "=iferror(if(and(K46<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K46,F:I,3,0),\"\"),\"\")"}, "M47": {"content": "=iferror(if(and(K47<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K47,F:I,3,0),\"\"),\"\")"}, "M48": {"content": "=iferror(if(and(K48<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K48,F:I,3,0),\"\"),\"\")"}, "M49": {"content": "=iferror(if(and(K49<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K49,F:I,3,0),\"\"),\"\")"}, "M50": {"content": "=iferror(if(and(K50<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K50,F:I,3,0),\"\"),\"\")"}, "M51": {"content": "=iferror(if(and(K51<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K51,F:I,3,0),\"\"),\"\")"}, "M52": {"content": "=iferror(if(and(K52<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K52,F:I,3,0),\"\"),\"\")"}, "M53": {"content": "=iferror(if(and(K53<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K53,F:I,3,0),\"\"),\"\")"}, "M54": {"content": "=iferror(if(and(K54<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K54,F:I,3,0),\"\"),\"\")"}, "M55": {"content": "=iferror(if(and(K55<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K55,F:I,3,0),\"\"),\"\")"}, "M56": {"content": "=iferror(if(and(K56<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K56,F:I,3,0),\"\"),\"\")"}, "M57": {"content": "=iferror(if(and(K57<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K57,F:I,3,0),\"\"),\"\")"}, "M58": {"content": "=iferror(if(and(K58<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K58,F:I,3,0),\"\"),\"\")"}, "M59": {"content": "=iferror(if(and(K59<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K59,F:I,3,0),\"\"),\"\")"}, "M60": {"content": "=iferror(if(and(K60<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K60,F:I,3,0),\"\"),\"\")"}, "M61": {"content": "=iferror(if(and(K61<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K61,F:I,3,0),\"\"),\"\")"}, "M62": {"content": "=iferror(if(and(K62<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K62,F:I,3,0),\"\"),\"\")"}, "M63": {"content": "=iferror(if(and(K63<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K63,F:I,3,0),\"\"),\"\")"}, "M64": {"content": "=iferror(if(and(K64<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K64,F:I,3,0),\"\"),\"\")"}, "M65": {"content": "=iferror(if(and(K65<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K65,F:I,3,0),\"\"),\"\")"}, "M66": {"content": "=iferror(if(and(K66<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K66,F:I,3,0),\"\"),\"\")"}, "M67": {"content": "=iferror(if(and(K67<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K67,F:I,3,0),\"\"),\"\")"}, "M68": {"content": "=iferror(if(and(K68<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K68,F:I,3,0),\"\"),\"\")"}, "M69": {"content": "=iferror(if(and(K69<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K69,F:I,3,0),\"\"),\"\")"}, "M70": {"content": "=iferror(if(and(K70<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K70,F:I,3,0),\"\"),\"\")"}, "M71": {"content": "=iferror(if(and(K71<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K71,F:I,3,0),\"\"),\"\")"}, "M72": {"content": "=iferror(if(and(K72<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K72,F:I,3,0),\"\"),\"\")"}, "M73": {"content": "=iferror(if(and(K73<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K73,F:I,3,0),\"\"),\"\")"}, "M74": {"content": "=iferror(if(and(K74<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K74,F:I,3,0),\"\"),\"\")"}, "M75": {"content": "=iferror(if(and(K75<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K75,F:I,3,0),\"\"),\"\")"}, "M76": {"content": "=iferror(if(and(K76<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K76,F:I,3,0),\"\"),\"\")"}, "M77": {"content": "=iferror(if(and(K77<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K77,F:I,3,0),\"\"),\"\")"}, "M78": {"content": "=iferror(if(and(K78<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K78,F:I,3,0),\"\"),\"\")"}, "M79": {"content": "=iferror(if(and(K79<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K79,F:I,3,0),\"\"),\"\")"}, "M80": {"content": "=iferror(if(and(K80<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K80,F:I,3,0),\"\"),\"\")"}, "M81": {"content": "=iferror(if(and(K81<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K81,F:I,3,0),\"\"),\"\")"}, "M82": {"content": "=iferror(if(and(K82<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K82,F:I,3,0),\"\"),\"\")"}, "M83": {"content": "=iferror(if(and(K83<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K83,F:I,3,0),\"\"),\"\")"}, "M84": {"content": "=iferror(if(and(K84<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K84,F:I,3,0),\"\"),\"\")"}, "M85": {"content": "=iferror(if(and(K85<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K85,F:I,3,0),\"\"),\"\")"}, "M86": {"content": "=iferror(if(and(K86<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K86,F:I,3,0),\"\"),\"\")"}, "M87": {"content": "=iferror(if(and(K87<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K87,F:I,3,0),\"\"),\"\")"}, "M88": {"content": "=iferror(if(and(K88<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K88,F:I,3,0),\"\"),\"\")"}, "M89": {"content": "=iferror(if(and(K89<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K89,F:I,3,0),\"\"),\"\")"}, "M90": {"content": "=iferror(if(and(K90<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K90,F:I,3,0),\"\"),\"\")"}, "M91": {"content": "=iferror(if(and(K91<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K91,F:I,3,0),\"\"),\"\")"}, "M92": {"content": "=iferror(if(and(K92<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K92,F:I,3,0),\"\"),\"\")"}, "M93": {"content": "=iferror(if(and(K93<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K93,F:I,3,0),\"\"),\"\")"}, "M94": {"content": "=iferror(if(and(K94<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K94,F:I,3,0),\"\"),\"\")"}, "M95": {"content": "=iferror(if(and(K95<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K95,F:I,3,0),\"\"),\"\")"}, "M96": {"content": "=iferror(if(and(K96<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K96,F:I,3,0),\"\"),\"\")"}, "M97": {"content": "=iferror(if(and(K97<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K97,F:I,3,0),\"\"),\"\")"}, "M98": {"content": "=iferror(if(and(K98<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K98,F:I,3,0),\"\"),\"\")"}, "M99": {"content": "=iferror(if(and(K99<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K99,F:I,3,0),\"\"),\"\")"}, "M100": {"content": "=iferror(if(and(K100<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K100,F:I,3,0),\"\"),\"\")"}, "M101": {"content": "=iferror(if(and(K101<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K101,F:I,3,0),\"\"),\"\")"}, "M102": {"content": "=iferror(if(and(K102<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K102,F:I,3,0),\"\"),\"\")"}, "M103": {"content": "=iferror(if(and(K103<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K103,F:I,3,0),\"\"),\"\")"}, "M104": {"content": "=iferror(if(and(K104<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K104,F:I,3,0),\"\"),\"\")"}, "M105": {"content": "=iferror(if(and(K105<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K105,F:I,3,0),\"\"),\"\")"}, "M106": {"content": "=iferror(if(and(K106<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K106,F:I,3,0),\"\"),\"\")"}, "M107": {"content": "=iferror(if(and(K107<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K107,F:I,3,0),\"\"),\"\")"}, "M108": {"content": "=iferror(if(and(K108<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K108,F:I,3,0),\"\"),\"\")"}, "M109": {"content": "=iferror(if(and(K109<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K109,F:I,3,0),\"\"),\"\")"}, "M110": {"content": "=iferror(if(and(K110<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K110,F:I,3,0),\"\"),\"\")"}, "M111": {"content": "=iferror(if(and(K111<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K111,F:I,3,0),\"\"),\"\")"}, "M112": {"content": "=iferror(if(and(K112<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K112,F:I,3,0),\"\"),\"\")"}, "M113": {"content": "=iferror(if(and(K113<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K113,F:I,3,0),\"\"),\"\")"}, "M114": {"content": "=iferror(if(and(K114<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K114,F:I,3,0),\"\"),\"\")"}, "M115": {"content": "=iferror(if(and(K115<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K115,F:I,3,0),\"\"),\"\")"}, "M116": {"content": "=iferror(if(and(K116<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K116,F:I,3,0),\"\"),\"\")"}, "M117": {"content": "=iferror(if(and(K117<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K117,F:I,3,0),\"\"),\"\")"}, "M118": {"content": "=iferror(if(and(K118<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K118,F:I,3,0),\"\"),\"\")"}, "M119": {"content": "=iferror(if(and(K119<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K119,F:I,3,0),\"\"),\"\")"}, "M120": {"content": "=iferror(if(and(K120<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K120,F:I,3,0),\"\"),\"\")"}, "M121": {"content": "=iferror(if(and(K121<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K121,F:I,3,0),\"\"),\"\")"}, "M122": {"content": "=iferror(if(and(K122<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K122,F:I,3,0),\"\"),\"\")"}, "M123": {"content": "=iferror(if(and(K123<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K123,F:I,3,0),\"\"),\"\")"}, "M124": {"content": "=iferror(if(and(K124<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K124,F:I,3,0),\"\"),\"\")"}, "M125": {"content": "=iferror(if(and(K125<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K125,F:I,3,0),\"\"),\"\")"}, "M126": {"content": "=iferror(if(and(K126<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K126,F:I,3,0),\"\"),\"\")"}, "M127": {"content": "=iferror(if(and(K127<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K127,F:I,3,0),\"\"),\"\")"}, "M128": {"content": "=iferror(if(and(K128<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K128,F:I,3,0),\"\"),\"\")"}, "M129": {"content": "=iferror(if(and(K129<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K129,F:I,3,0),\"\"),\"\")"}, "M130": {"content": "=iferror(if(and(K130<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K130,F:I,3,0),\"\"),\"\")"}, "M131": {"content": "=iferror(if(and(K131<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K131,F:I,3,0),\"\"),\"\")"}, "M132": {"content": "=iferror(if(and(K132<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K132,F:I,3,0),\"\"),\"\")"}, "M133": {"content": "=iferror(if(and(K133<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K133,F:I,3,0),\"\"),\"\")"}, "M134": {"content": "=iferror(if(and(K134<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K134,F:I,3,0),\"\"),\"\")"}, "M135": {"content": "=iferror(if(and(K135<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K135,F:I,3,0),\"\"),\"\")"}, "M136": {"content": "=iferror(if(and(K136<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K136,F:I,3,0),\"\"),\"\")"}, "M137": {"content": "=iferror(if(and(K137<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K137,F:I,3,0),\"\"),\"\")"}, "M138": {"content": "=iferror(if(and(K138<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K138,F:I,3,0),\"\"),\"\")"}, "M139": {"content": "=iferror(if(and(K139<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K139,F:I,3,0),\"\"),\"\")"}, "M140": {"content": "=iferror(if(and(K140<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K140,F:I,3,0),\"\"),\"\")"}, "M141": {"content": "=iferror(if(and(K141<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K141,F:I,3,0),\"\"),\"\")"}, "M142": {"content": "=iferror(if(and(K142<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K142,F:I,3,0),\"\"),\"\")"}, "M143": {"content": "=iferror(if(and(K143<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K143,F:I,3,0),\"\"),\"\")"}, "M144": {"content": "=iferror(if(and(K144<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K144,F:I,3,0),\"\"),\"\")"}, "M145": {"content": "=iferror(if(and(K145<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K145,F:I,3,0),\"\"),\"\")"}, "M146": {"content": "=iferror(if(and(K146<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K146,F:I,3,0),\"\"),\"\")"}, "M147": {"content": "=iferror(if(and(K147<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K147,F:I,3,0),\"\"),\"\")"}, "M148": {"content": "=iferror(if(and(K148<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K148,F:I,3,0),\"\"),\"\")"}, "M149": {"content": "=iferror(if(and(K149<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K149,F:I,3,0),\"\"),\"\")"}, "M150": {"content": "=iferror(if(and(K150<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K150,F:I,3,0),\"\"),\"\")"}, "M151": {"content": "=iferror(if(and(K151<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K151,F:I,3,0),\"\"),\"\")"}, "M152": {"content": "=iferror(if(and(K152<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K152,F:I,3,0),\"\"),\"\")"}, "M153": {"content": "=iferror(if(and(K153<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K153,F:I,3,0),\"\"),\"\")"}, "M154": {"content": "=iferror(if(and(K154<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K154,F:I,3,0),\"\"),\"\")"}, "M155": {"content": "=iferror(if(and(K155<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K155,F:I,3,0),\"\"),\"\")"}, "M156": {"content": "=iferror(if(and(K156<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K156,F:I,3,0),\"\"),\"\")"}, "M157": {"content": "=iferror(if(and(K157<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K157,F:I,3,0),\"\"),\"\")"}, "M158": {"content": "=iferror(if(and(K158<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K158,F:I,3,0),\"\"),\"\")"}, "M159": {"content": "=iferror(if(and(K159<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K159,F:I,3,0),\"\"),\"\")"}, "M160": {"content": "=iferror(if(and(K160<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K160,F:I,3,0),\"\"),\"\")"}, "M161": {"content": "=iferror(if(and(K161<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K161,F:I,3,0),\"\"),\"\")"}, "M162": {"content": "=iferror(if(and(K162<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K162,F:I,3,0),\"\"),\"\")"}, "M163": {"content": "=iferror(if(and(K163<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K163,F:I,3,0),\"\"),\"\")"}, "M164": {"content": "=iferror(if(and(K164<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K164,F:I,3,0),\"\"),\"\")"}, "M165": {"content": "=iferror(if(and(K165<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K165,F:I,3,0),\"\"),\"\")"}, "M166": {"content": "=iferror(if(and(K166<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K166,F:I,3,0),\"\"),\"\")"}, "M167": {"content": "=iferror(if(and(K167<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K167,F:I,3,0),\"\"),\"\")"}, "M168": {"content": "=iferror(if(and(K168<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K168,F:I,3,0),\"\"),\"\")"}, "M169": {"content": "=iferror(if(and(K169<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K169,F:I,3,0),\"\"),\"\")"}, "M170": {"content": "=iferror(if(and(K170<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K170,F:I,3,0),\"\"),\"\")"}, "M171": {"content": "=iferror(if(and(K171<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K171,F:I,3,0),\"\"),\"\")"}, "M172": {"content": "=iferror(if(and(K172<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K172,F:I,3,0),\"\"),\"\")"}, "M173": {"content": "=iferror(if(and(K173<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K173,F:I,3,0),\"\"),\"\")"}, "M174": {"content": "=iferror(if(and(K174<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K174,F:I,3,0),\"\"),\"\")"}, "M175": {"content": "=iferror(if(and(K175<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K175,F:I,3,0),\"\"),\"\")"}, "M176": {"content": "=iferror(if(and(K176<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K176,F:I,3,0),\"\"),\"\")"}, "M177": {"content": "=iferror(if(and(K177<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K177,F:I,3,0),\"\"),\"\")"}, "M178": {"content": "=iferror(if(and(K178<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K178,F:I,3,0),\"\"),\"\")"}, "M179": {"content": "=iferror(if(and(K179<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K179,F:I,3,0),\"\"),\"\")"}, "M180": {"content": "=iferror(if(and(K180<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K180,F:I,3,0),\"\"),\"\")"}, "M181": {"content": "=iferror(if(and(K181<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K181,F:I,3,0),\"\"),\"\")"}, "M182": {"content": "=iferror(if(and(K182<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K182,F:I,3,0),\"\"),\"\")"}, "M183": {"content": "=iferror(if(and(K183<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K183,F:I,3,0),\"\"),\"\")"}, "M184": {"content": "=iferror(if(and(K184<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K184,F:I,3,0),\"\"),\"\")"}, "M185": {"content": "=iferror(if(and(K185<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K185,F:I,3,0),\"\"),\"\")"}, "M186": {"content": "=iferror(if(and(K186<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K186,F:I,3,0),\"\"),\"\")"}, "M187": {"content": "=iferror(if(and(K187<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K187,F:I,3,0),\"\"),\"\")"}, "M188": {"content": "=iferror(if(and(K188<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K188,F:I,3,0),\"\"),\"\")"}, "M189": {"content": "=iferror(if(and(K189<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K189,F:I,3,0),\"\"),\"\")"}, "M190": {"content": "=iferror(if(and(K190<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K190,F:I,3,0),\"\"),\"\")"}, "M191": {"content": "=iferror(if(and(K191<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K191,F:I,3,0),\"\"),\"\")"}, "M192": {"content": "=iferror(if(and(K192<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K192,F:I,3,0),\"\"),\"\")"}, "M193": {"content": "=iferror(if(and(K193<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K193,F:I,3,0),\"\"),\"\")"}, "M194": {"content": "=iferror(if(and(K194<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K194,F:I,3,0),\"\"),\"\")"}, "M195": {"content": "=iferror(if(and(K195<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K195,F:I,3,0),\"\"),\"\")"}, "M196": {"content": "=iferror(if(and(K196<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K196,F:I,3,0),\"\"),\"\")"}, "M197": {"content": "=iferror(if(and(K197<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K197,F:I,3,0),\"\"),\"\")"}, "M198": {"content": "=iferror(if(and(K198<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K198,F:I,3,0),\"\"),\"\")"}, "M199": {"content": "=iferror(if(and(K199<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K199,F:I,3,0),\"\"),\"\")"}, "M200": {"content": "=iferror(if(and(K200<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K200,F:I,3,0),\"\"),\"\")"}, "M201": {"content": "=iferror(if(and(K201<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K201,F:I,3,0),\"\"),\"\")"}, "M202": {"content": "=iferror(if(and(K202<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K202,F:I,3,0),\"\"),\"\")"}, "M203": {"content": "=iferror(if(and(K203<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K203,F:I,3,0),\"\"),\"\")"}, "M204": {"content": "=iferror(if(and(K204<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K204,F:I,3,0),\"\"),\"\")"}, "M205": {"content": "=iferror(if(and(K205<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K205,F:I,3,0),\"\"),\"\")"}, "M206": {"content": "=iferror(if(and(K206<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K206,F:I,3,0),\"\"),\"\")"}, "M207": {"content": "=iferror(if(and(K207<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K207,F:I,3,0),\"\"),\"\")"}, "M208": {"content": "=iferror(if(and(K208<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K208,F:I,3,0),\"\"),\"\")"}, "M209": {"content": "=iferror(if(and(K209<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K209,F:I,3,0),\"\"),\"\")"}, "M210": {"content": "=iferror(if(and(K210<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K210,F:I,3,0),\"\"),\"\")"}, "M211": {"content": "=iferror(if(and(K211<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K211,F:I,3,0),\"\"),\"\")"}, "M212": {"content": "=iferror(if(and(K212<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K212,F:I,3,0),\"\"),\"\")"}, "M213": {"content": "=iferror(if(and(K213<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K213,F:I,3,0),\"\"),\"\")"}, "M214": {"content": "=iferror(if(and(K214<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K214,F:I,3,0),\"\"),\"\")"}, "M215": {"content": "=iferror(if(and(K215<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K215,F:I,3,0),\"\"),\"\")"}, "M216": {"content": "=iferror(if(and(K216<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K216,F:I,3,0),\"\"),\"\")"}, "M217": {"content": "=iferror(if(and(K217<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K217,F:I,3,0),\"\"),\"\")"}, "M218": {"content": "=iferror(if(and(K218<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K218,F:I,3,0),\"\"),\"\")"}, "M219": {"content": "=iferror(if(and(K219<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K219,F:I,3,0),\"\"),\"\")"}, "M220": {"content": "=iferror(if(and(K220<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K220,F:I,3,0),\"\"),\"\")"}, "M221": {"content": "=iferror(if(and(K221<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K221,F:I,3,0),\"\"),\"\")"}, "M222": {"content": "=iferror(if(and(K222<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K222,F:I,3,0),\"\"),\"\")"}, "M223": {"content": "=iferror(if(and(K223<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K223,F:I,3,0),\"\"),\"\")"}, "M224": {"content": "=iferror(if(and(K224<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K224,F:I,3,0),\"\"),\"\")"}, "M225": {"content": "=iferror(if(and(K225<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K225,F:I,3,0),\"\"),\"\")"}, "M226": {"content": "=iferror(if(and(K226<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K226,F:I,3,0),\"\"),\"\")"}, "M227": {"content": "=iferror(if(and(K227<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K227,F:I,3,0),\"\"),\"\")"}, "M228": {"content": "=iferror(if(and(K228<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K228,F:I,3,0),\"\"),\"\")"}, "M229": {"content": "=iferror(if(and(K229<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K229,F:I,3,0),\"\"),\"\")"}, "M230": {"content": "=iferror(if(and(K230<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K230,F:I,3,0),\"\"),\"\")"}, "M231": {"content": "=iferror(if(and(K231<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K231,F:I,3,0),\"\"),\"\")"}, "M232": {"content": "=iferror(if(and(K232<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K232,F:I,3,0),\"\"),\"\")"}, "M233": {"content": "=iferror(if(and(K233<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K233,F:I,3,0),\"\"),\"\")"}, "M234": {"content": "=iferror(if(and(K234<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K234,F:I,3,0),\"\"),\"\")"}, "M235": {"content": "=iferror(if(and(K235<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K235,F:I,3,0),\"\"),\"\")"}, "M236": {"content": "=iferror(if(and(K236<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K236,F:I,3,0),\"\"),\"\")"}, "M237": {"content": "=iferror(if(and(K237<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K237,F:I,3,0),\"\"),\"\")"}, "M238": {"content": "=iferror(if(and(K238<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K238,F:I,3,0),\"\"),\"\")"}, "M239": {"content": "=iferror(if(and(K239<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K239,F:I,3,0),\"\"),\"\")"}, "M240": {"content": "=iferror(if(and(K240<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K240,F:I,3,0),\"\"),\"\")"}, "M241": {"content": "=iferror(if(and(K241<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K241,F:I,3,0),\"\"),\"\")"}, "M242": {"content": "=iferror(if(and(K242<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K242,F:I,3,0),\"\"),\"\")"}, "M243": {"content": "=iferror(if(and(K243<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K243,F:I,3,0),\"\"),\"\")"}, "M244": {"content": "=iferror(if(and(K244<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K244,F:I,3,0),\"\"),\"\")"}, "M245": {"content": "=iferror(if(and(K245<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K245,F:I,3,0),\"\"),\"\")"}, "M246": {"content": "=iferror(if(and(K246<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K246,F:I,3,0),\"\"),\"\")"}, "M247": {"content": "=iferror(if(and(K247<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K247,F:I,3,0),\"\"),\"\")"}, "M248": {"content": "=iferror(if(and(K248<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K248,F:I,3,0),\"\"),\"\")"}, "M249": {"content": "=iferror(if(and(K249<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K249,F:I,3,0),\"\"),\"\")"}, "M250": {"content": "=iferror(if(and(K250<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K250,F:I,3,0),\"\"),\"\")"}, "M251": {"content": "=iferror(if(and(K251<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K251,F:I,3,0),\"\"),\"\")"}, "M252": {"content": "=iferror(if(and(K252<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K252,F:I,3,0),\"\"),\"\")"}, "M253": {"content": "=iferror(if(and(K253<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K253,F:I,3,0),\"\"),\"\")"}, "M254": {"content": "=iferror(if(and(K254<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K254,F:I,3,0),\"\"),\"\")"}, "M255": {"content": "=iferror(if(and(K255<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K255,F:I,3,0),\"\"),\"\")"}, "M256": {"content": "=iferror(if(and(K256<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K256,F:I,3,0),\"\"),\"\")"}, "M257": {"content": "=iferror(if(and(K257<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K257,F:I,3,0),\"\"),\"\")"}, "M258": {"content": "=iferror(if(and(K258<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K258,F:I,3,0),\"\"),\"\")"}, "M259": {"content": "=iferror(if(and(K259<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K259,F:I,3,0),\"\"),\"\")"}, "M260": {"content": "=iferror(if(and(K260<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K260,F:I,3,0),\"\"),\"\")"}, "M261": {"content": "=iferror(if(and(K261<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K261,F:I,3,0),\"\"),\"\")"}, "M262": {"content": "=iferror(if(and(K262<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K262,F:I,3,0),\"\"),\"\")"}, "M263": {"content": "=iferror(if(and(K263<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K263,F:I,3,0),\"\"),\"\")"}, "M264": {"content": "=iferror(if(and(K264<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K264,F:I,3,0),\"\"),\"\")"}, "M265": {"content": "=iferror(if(and(K265<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K265,F:I,3,0),\"\"),\"\")"}, "M266": {"content": "=iferror(if(and(K266<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K266,F:I,3,0),\"\"),\"\")"}, "M267": {"content": "=iferror(if(and(K267<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K267,F:I,3,0),\"\"),\"\")"}, "M268": {"content": "=iferror(if(and(K268<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K268,F:I,3,0),\"\"),\"\")"}, "M269": {"content": "=iferror(if(and(K269<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(K269,F:I,3,0),\"\"),\"\")"}}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": true}}, {"range": "F1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": true}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "2afdc0eb-3021-43dc-9656-de7c83bbd8d0", "name": "Cycle time", "colNumber": 18, "rowNumber": 87, "rows": {}, "cols": {"0": {"size": 183}, "1": {"size": 154}, "2": {"size": 42}, "3": {"size": 183}, "4": {"size": 177}, "5": {"size": 112}, "6": {"size": 233}, "7": {"size": 154}, "9": {"size": 233}, "10": {"size": 154}, "13": {"size": 233}}, "merges": ["G1:H1", "J1:K1"], "cells": {"A1": {"content": "=_t(\"Operation\")"}, "A2": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",1)"}, "A3": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",2)"}, "A4": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",3)"}, "A5": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",4)"}, "A6": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",5)"}, "A7": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",6)"}, "A8": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",7)"}, "A9": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",8)"}, "A10": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",9)"}, "A11": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",10)"}, "A12": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",11)"}, "A13": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",12)"}, "A14": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",13)"}, "A15": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",14)"}, "A16": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",15)"}, "A17": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",16)"}, "A18": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",17)"}, "A19": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",18)"}, "A20": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",19)"}, "A21": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",20)"}, "A22": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",21)"}, "A23": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",22)"}, "A24": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",23)"}, "A25": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",24)"}, "A26": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",25)"}, "A27": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",26)"}, "A28": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",27)"}, "A29": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",28)"}, "A30": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",29)"}, "A31": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",30)"}, "A32": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",31)"}, "A33": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",32)"}, "A34": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",33)"}, "A35": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",34)"}, "A36": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",35)"}, "A37": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",36)"}, "A38": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",37)"}, "A39": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",38)"}, "A40": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",39)"}, "A41": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",40)"}, "A42": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",41)"}, "A43": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",42)"}, "A44": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",43)"}, "A45": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",44)"}, "A46": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",45)"}, "A47": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",46)"}, "A48": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",47)"}, "A49": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",48)"}, "A50": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",49)"}, "A51": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",50)"}, "A52": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",51)"}, "A53": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",52)"}, "A54": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",53)"}, "A55": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",54)"}, "A56": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",55)"}, "A57": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",56)"}, "A58": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",57)"}, "A59": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",58)"}, "A60": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",59)"}, "A61": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",60)"}, "A62": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",61)"}, "A63": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",62)"}, "A64": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",63)"}, "A65": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",64)"}, "A66": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",65)"}, "A67": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",66)"}, "A68": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",67)"}, "A69": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",68)"}, "A70": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",69)"}, "A71": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",70)"}, "A72": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",71)"}, "A73": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",72)"}, "A74": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",73)"}, "A75": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",74)"}, "A76": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",75)"}, "A77": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",76)"}, "A78": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",77)"}, "A79": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",78)"}, "A80": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",79)"}, "A81": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",80)"}, "A82": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",81)"}, "A83": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",82)"}, "A84": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",83)"}, "A85": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",84)"}, "A86": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",85)"}, "A87": {"content": "=PIVOT.HEADER(1,\"#operation_type_id\",86)"}, "B1": {"content": "=_t(\"Cycle time Current period\")"}, "B2": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",1)"}, "B3": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",2)"}, "B4": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",3)"}, "B5": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",4)"}, "B6": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",5)"}, "B7": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",6)"}, "B8": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",7)"}, "B9": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",8)"}, "B10": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",9)"}, "B11": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",10)"}, "B12": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",11)"}, "B13": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",12)"}, "B14": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",13)"}, "B15": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",14)"}, "B16": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",15)"}, "B17": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",16)"}, "B18": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",17)"}, "B19": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",18)"}, "B20": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",19)"}, "B21": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",20)"}, "B22": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",21)"}, "B23": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",22)"}, "B24": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",23)"}, "B25": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",24)"}, "B26": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",25)"}, "B27": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",26)"}, "B28": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",27)"}, "B29": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",28)"}, "B30": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",29)"}, "B31": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",30)"}, "B32": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",31)"}, "B33": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",32)"}, "B34": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",33)"}, "B35": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",34)"}, "B36": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",35)"}, "B37": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",36)"}, "B38": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",37)"}, "B39": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",38)"}, "B40": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",39)"}, "B41": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",40)"}, "B42": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",41)"}, "B43": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",42)"}, "B44": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",43)"}, "B45": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",44)"}, "B46": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",45)"}, "B47": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",46)"}, "B48": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",47)"}, "B49": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",48)"}, "B50": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",49)"}, "B51": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",50)"}, "B52": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",51)"}, "B53": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",52)"}, "B54": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",53)"}, "B55": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",54)"}, "B56": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",55)"}, "B57": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",56)"}, "B58": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",57)"}, "B59": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",58)"}, "B60": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",59)"}, "B61": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",60)"}, "B62": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",61)"}, "B63": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",62)"}, "B64": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",63)"}, "B65": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",64)"}, "B66": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",65)"}, "B67": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",66)"}, "B68": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",67)"}, "B69": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",68)"}, "B70": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",69)"}, "B71": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",70)"}, "B72": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",71)"}, "B73": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",72)"}, "B74": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",73)"}, "B75": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",74)"}, "B76": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",75)"}, "B77": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",76)"}, "B78": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",77)"}, "B79": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",78)"}, "B80": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",79)"}, "B81": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",80)"}, "B82": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",81)"}, "B83": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",82)"}, "B84": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",83)"}, "B85": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",84)"}, "B86": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",85)"}, "B87": {"content": "=PIVOT.VALUE(1,\"cycle_time\",\"#operation_type_id\",86)"}, "D1": {"content": "=_t(\"Operation\")"}, "D2": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",1)"}, "D3": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",2)"}, "D4": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",3)"}, "D5": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",4)"}, "D6": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",5)"}, "D7": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",6)"}, "D8": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",7)"}, "D9": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",8)"}, "D10": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",9)"}, "D11": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",10)"}, "D12": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",11)"}, "D13": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",12)"}, "D14": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",13)"}, "D15": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",14)"}, "D16": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",15)"}, "D17": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",16)"}, "D18": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",17)"}, "D19": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",18)"}, "D20": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",19)"}, "D21": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",20)"}, "D22": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",21)"}, "D23": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",22)"}, "D24": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",23)"}, "D25": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",24)"}, "D26": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",25)"}, "D27": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",26)"}, "D28": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",27)"}, "D29": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",28)"}, "D30": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",29)"}, "D31": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",30)"}, "D32": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",31)"}, "D33": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",32)"}, "D34": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",33)"}, "D35": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",34)"}, "D36": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",35)"}, "D37": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",36)"}, "D38": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",37)"}, "D39": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",38)"}, "D40": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",39)"}, "D41": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",40)"}, "D42": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",41)"}, "D43": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",42)"}, "D44": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",43)"}, "D45": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",44)"}, "D46": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",45)"}, "D47": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",46)"}, "D48": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",47)"}, "D49": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",48)"}, "D50": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",49)"}, "D51": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",50)"}, "D52": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",51)"}, "D53": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",52)"}, "D54": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",53)"}, "D55": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",54)"}, "D56": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",55)"}, "D57": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",56)"}, "D58": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",57)"}, "D59": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",58)"}, "D60": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",59)"}, "D61": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",60)"}, "D62": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",61)"}, "D63": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",62)"}, "D64": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",63)"}, "D65": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",64)"}, "D66": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",65)"}, "D67": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",66)"}, "D68": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",67)"}, "D69": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",68)"}, "D70": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",69)"}, "D71": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",70)"}, "D72": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",71)"}, "D73": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",72)"}, "D74": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",73)"}, "D75": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",74)"}, "D76": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",75)"}, "D77": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",76)"}, "D78": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",77)"}, "D79": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",78)"}, "D80": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",79)"}, "D81": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",80)"}, "D82": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",81)"}, "D83": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",82)"}, "D84": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",83)"}, "D85": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",84)"}, "D86": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",85)"}, "D87": {"content": "=PIVOT.HEADER(14,\"#operation_type_id\",86)"}, "E1": {"content": "=_t(\"Cycle time Current period\")"}, "E2": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",1)"}, "E3": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",2)"}, "E4": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",3)"}, "E5": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",4)"}, "E6": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",5)"}, "E7": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",6)"}, "E8": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",7)"}, "E9": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",8)"}, "E10": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",9)"}, "E11": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",10)"}, "E12": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",11)"}, "E13": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",12)"}, "E14": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",13)"}, "E15": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",14)"}, "E16": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",15)"}, "E17": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",16)"}, "E18": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",17)"}, "E19": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",18)"}, "E20": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",19)"}, "E21": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",20)"}, "E22": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",21)"}, "E23": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",22)"}, "E24": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",23)"}, "E25": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",24)"}, "E26": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",25)"}, "E27": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",26)"}, "E28": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",27)"}, "E29": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",28)"}, "E30": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",29)"}, "E31": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",30)"}, "E32": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",31)"}, "E33": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",32)"}, "E34": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",33)"}, "E35": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",34)"}, "E36": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",35)"}, "E37": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",36)"}, "E38": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",37)"}, "E39": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",38)"}, "E40": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",39)"}, "E41": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",40)"}, "E42": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",41)"}, "E43": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",42)"}, "E44": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",43)"}, "E45": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",44)"}, "E46": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",45)"}, "E47": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",46)"}, "E48": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",47)"}, "E49": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",48)"}, "E50": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",49)"}, "E51": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",50)"}, "E52": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",51)"}, "E53": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",52)"}, "E54": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",53)"}, "E55": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",54)"}, "E56": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",55)"}, "E57": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",56)"}, "E58": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",57)"}, "E59": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",58)"}, "E60": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",59)"}, "E61": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",60)"}, "E62": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",61)"}, "E63": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",62)"}, "E64": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",63)"}, "E65": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",64)"}, "E66": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",65)"}, "E67": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",66)"}, "E68": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",67)"}, "E69": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",68)"}, "E70": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",69)"}, "E71": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",70)"}, "E72": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",71)"}, "E73": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",72)"}, "E74": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",73)"}, "E75": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",74)"}, "E76": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",75)"}, "E77": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",76)"}, "E78": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",77)"}, "E79": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",78)"}, "E80": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",79)"}, "E81": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",80)"}, "E82": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",81)"}, "E83": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",82)"}, "E84": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",83)"}, "E85": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",84)"}, "E86": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",85)"}, "E87": {"content": "=PIVOT.VALUE(14,\"cycle_time\",\"#operation_type_id\",86)"}, "G1": {"content": "=_t(\"Current period reworked\")"}, "G2": {"content": "=sort(FILTER(A:B,B:B<>\"\"),2,false)"}, "J1": {"content": "=_t(\"Previous period reworked\")"}, "J2": {"content": "=sort(FILTER(D:E,E:E<>\"\"),2,false)"}, "N1": {"content": "=_t(\"Operation name\")"}, "N2": {"content": "=iferror(UNIQUE(VSTACK(G3:G100,J3:J100)),\"\")"}, "O1": {"content": "=_t(\"Current period\")"}, "O2": {"content": "=iferror(if(N2<>\"\",VLOOKUP(N2,G1:H86,2,0),\"\"),\"\")"}, "O3": {"content": "=iferror(if(N3<>\"\",VLOOKUP(N3,G2:H87,2,0),\"\"),\"\")"}, "O4": {"content": "=iferror(if(N4<>\"\",VLOOKUP(N4,G3:H88,2,0),\"\"),\"\")"}, "O5": {"content": "=iferror(if(N5<>\"\",VLOOKUP(N5,G4:H89,2,0),\"\"),\"\")"}, "O6": {"content": "=iferror(if(N6<>\"\",VLOOKUP(N6,G5:H90,2,0),\"\"),\"\")"}, "O7": {"content": "=iferror(if(N7<>\"\",VLOOKUP(N7,G6:H91,2,0),\"\"),\"\")"}, "O8": {"content": "=iferror(if(N8<>\"\",VLOOKUP(N8,G7:H92,2,0),\"\"),\"\")"}, "O9": {"content": "=iferror(if(N9<>\"\",VLOOKUP(N9,G8:H93,2,0),\"\"),\"\")"}, "O10": {"content": "=iferror(if(N10<>\"\",VLOOKUP(N10,G9:H94,2,0),\"\"),\"\")"}, "O11": {"content": "=iferror(if(N11<>\"\",VLOOKUP(N11,G10:H95,2,0),\"\"),\"\")"}, "O12": {"content": "=iferror(if(N12<>\"\",VLOOKUP(N12,G11:H96,2,0),\"\"),\"\")"}, "O13": {"content": "=iferror(if(N13<>\"\",VLOOKUP(N13,G12:H97,2,0),\"\"),\"\")"}, "O14": {"content": "=iferror(if(N14<>\"\",VLOOKUP(N14,G13:H98,2,0),\"\"),\"\")"}, "O15": {"content": "=iferror(if(N15<>\"\",VLOOKUP(N15,G14:H99,2,0),\"\"),\"\")"}, "O16": {"content": "=iferror(if(N16<>\"\",VLOOKUP(N16,G15:H100,2,0),\"\"),\"\")"}, "O17": {"content": "=iferror(if(N17<>\"\",VLOOKUP(N17,G16:H101,2,0),\"\"),\"\")"}, "O18": {"content": "=iferror(if(N18<>\"\",VLOOKUP(N18,G17:H102,2,0),\"\"),\"\")"}, "O19": {"content": "=iferror(if(N19<>\"\",VLOOKUP(N19,G18:H103,2,0),\"\"),\"\")"}, "O20": {"content": "=iferror(if(N20<>\"\",VLOOKUP(N20,G19:H104,2,0),\"\"),\"\")"}, "O21": {"content": "=iferror(if(N21<>\"\",VLOOKUP(N21,G20:H105,2,0),\"\"),\"\")"}, "O22": {"content": "=iferror(if(N22<>\"\",VLOOKUP(N22,G21:H106,2,0),\"\"),\"\")"}, "O23": {"content": "=iferror(if(N23<>\"\",VLOOKUP(N23,G22:H107,2,0),\"\"),\"\")"}, "O24": {"content": "=iferror(if(N24<>\"\",VLOOKUP(N24,G23:H108,2,0),\"\"),\"\")"}, "O25": {"content": "=iferror(if(N25<>\"\",VLOOKUP(N25,G24:H109,2,0),\"\"),\"\")"}, "O26": {"content": "=iferror(if(N26<>\"\",VLOOKUP(N26,G25:H110,2,0),\"\"),\"\")"}, "O27": {"content": "=iferror(if(N27<>\"\",VLOOKUP(N27,G26:H111,2,0),\"\"),\"\")"}, "O28": {"content": "=iferror(if(N28<>\"\",VLOOKUP(N28,G27:H112,2,0),\"\"),\"\")"}, "O29": {"content": "=iferror(if(N29<>\"\",VLOOKUP(N29,G28:H113,2,0),\"\"),\"\")"}, "O30": {"content": "=iferror(if(N30<>\"\",VLOOKUP(N30,G29:H114,2,0),\"\"),\"\")"}, "O31": {"content": "=iferror(if(N31<>\"\",VLOOKUP(N31,G30:H115,2,0),\"\"),\"\")"}, "O32": {"content": "=iferror(if(N32<>\"\",VLOOKUP(N32,G31:H116,2,0),\"\"),\"\")"}, "O33": {"content": "=iferror(if(N33<>\"\",VLOOKUP(N33,G32:H117,2,0),\"\"),\"\")"}, "O34": {"content": "=iferror(if(N34<>\"\",VLOOKUP(N34,G33:H118,2,0),\"\"),\"\")"}, "O35": {"content": "=iferror(if(N35<>\"\",VLOOKUP(N35,G34:H119,2,0),\"\"),\"\")"}, "O36": {"content": "=iferror(if(N36<>\"\",VLOOKUP(N36,G35:H120,2,0),\"\"),\"\")"}, "O37": {"content": "=iferror(if(N37<>\"\",VLOOKUP(N37,G36:H121,2,0),\"\"),\"\")"}, "O38": {"content": "=iferror(if(N38<>\"\",VLOOKUP(N38,G37:H122,2,0),\"\"),\"\")"}, "O39": {"content": "=iferror(if(N39<>\"\",VLOOKUP(N39,G38:H123,2,0),\"\"),\"\")"}, "O40": {"content": "=iferror(if(N40<>\"\",VLOOKUP(N40,G39:H124,2,0),\"\"),\"\")"}, "O41": {"content": "=iferror(if(N41<>\"\",VLOOKUP(N41,G40:H125,2,0),\"\"),\"\")"}, "O42": {"content": "=iferror(if(N42<>\"\",VLOOKUP(N42,G41:H126,2,0),\"\"),\"\")"}, "O43": {"content": "=iferror(if(N43<>\"\",VLOOKUP(N43,G42:H127,2,0),\"\"),\"\")"}, "O44": {"content": "=iferror(if(N44<>\"\",VLOOKUP(N44,G43:H128,2,0),\"\"),\"\")"}, "O45": {"content": "=iferror(if(N45<>\"\",VLOOKUP(N45,G44:H129,2,0),\"\"),\"\")"}, "O46": {"content": "=iferror(if(N46<>\"\",VLOOKUP(N46,G45:H130,2,0),\"\"),\"\")"}, "O47": {"content": "=iferror(if(N47<>\"\",VLOOKUP(N47,G46:H131,2,0),\"\"),\"\")"}, "O48": {"content": "=iferror(if(N48<>\"\",VLOOKUP(N48,G47:H132,2,0),\"\"),\"\")"}, "O49": {"content": "=iferror(if(N49<>\"\",VLOOKUP(N49,G48:H133,2,0),\"\"),\"\")"}, "O50": {"content": "=iferror(if(N50<>\"\",VLOOKUP(N50,G49:H134,2,0),\"\"),\"\")"}, "O51": {"content": "=iferror(if(N51<>\"\",VLOOKUP(N51,G50:H135,2,0),\"\"),\"\")"}, "O52": {"content": "=iferror(if(N52<>\"\",VLOOKUP(N52,G51:H136,2,0),\"\"),\"\")"}, "O53": {"content": "=iferror(if(N53<>\"\",VLOOKUP(N53,G52:H137,2,0),\"\"),\"\")"}, "O54": {"content": "=iferror(if(N54<>\"\",VLOOKUP(N54,G53:H138,2,0),\"\"),\"\")"}, "O55": {"content": "=iferror(if(N55<>\"\",VLOOKUP(N55,G54:H139,2,0),\"\"),\"\")"}, "O56": {"content": "=iferror(if(N56<>\"\",VLOOKUP(N56,G55:H140,2,0),\"\"),\"\")"}, "O57": {"content": "=iferror(if(N57<>\"\",VLOOKUP(N57,G56:H141,2,0),\"\"),\"\")"}, "O58": {"content": "=iferror(if(N58<>\"\",VLOOKUP(N58,G57:H142,2,0),\"\"),\"\")"}, "O59": {"content": "=iferror(if(N59<>\"\",VLOOKUP(N59,G58:H143,2,0),\"\"),\"\")"}, "O60": {"content": "=iferror(if(N60<>\"\",VLOOKUP(N60,G59:H144,2,0),\"\"),\"\")"}, "O61": {"content": "=iferror(if(N61<>\"\",VLOOKUP(N61,G60:H145,2,0),\"\"),\"\")"}, "O62": {"content": "=iferror(if(N62<>\"\",VLOOKUP(N62,G61:H146,2,0),\"\"),\"\")"}, "O63": {"content": "=iferror(if(N63<>\"\",VLOOKUP(N63,G62:H147,2,0),\"\"),\"\")"}, "O64": {"content": "=iferror(if(N64<>\"\",VLOOKUP(N64,G63:H148,2,0),\"\"),\"\")"}, "O65": {"content": "=iferror(if(N65<>\"\",VLOOKUP(N65,G64:H149,2,0),\"\"),\"\")"}, "O66": {"content": "=iferror(if(N66<>\"\",VLOOKUP(N66,G65:H150,2,0),\"\"),\"\")"}, "O67": {"content": "=iferror(if(N67<>\"\",VLOOKUP(N67,G66:H151,2,0),\"\"),\"\")"}, "O68": {"content": "=iferror(if(N68<>\"\",VLOOKUP(N68,G67:H152,2,0),\"\"),\"\")"}, "O69": {"content": "=iferror(if(N69<>\"\",VLOOKUP(N69,G68:H153,2,0),\"\"),\"\")"}, "O70": {"content": "=iferror(if(N70<>\"\",VLOOKUP(N70,G69:H154,2,0),\"\"),\"\")"}, "O71": {"content": "=iferror(if(N71<>\"\",VLOOKUP(N71,G70:H155,2,0),\"\"),\"\")"}, "O72": {"content": "=iferror(if(N72<>\"\",VLOOKUP(N72,G71:H156,2,0),\"\"),\"\")"}, "O73": {"content": "=iferror(if(N73<>\"\",VLOOKUP(N73,G72:H157,2,0),\"\"),\"\")"}, "O74": {"content": "=iferror(if(N74<>\"\",VLOOKUP(N74,G73:H158,2,0),\"\"),\"\")"}, "O75": {"content": "=iferror(if(N75<>\"\",VLOOKUP(N75,G74:H159,2,0),\"\"),\"\")"}, "O76": {"content": "=iferror(if(N76<>\"\",VLOOKUP(N76,G75:H160,2,0),\"\"),\"\")"}, "O77": {"content": "=iferror(if(N77<>\"\",VLOOKUP(N77,G76:H161,2,0),\"\"),\"\")"}, "O78": {"content": "=iferror(if(N78<>\"\",VLOOKUP(N78,G77:H162,2,0),\"\"),\"\")"}, "O79": {"content": "=iferror(if(N79<>\"\",VLOOKUP(N79,G78:H163,2,0),\"\"),\"\")"}, "O80": {"content": "=iferror(if(N80<>\"\",VLOOKUP(N80,G79:H164,2,0),\"\"),\"\")"}, "O81": {"content": "=iferror(if(N81<>\"\",VLOOKUP(N81,G80:H165,2,0),\"\"),\"\")"}, "O82": {"content": "=iferror(if(N82<>\"\",VLOOKUP(N82,G81:H166,2,0),\"\"),\"\")"}, "O83": {"content": "=iferror(if(N83<>\"\",VLOOKUP(N83,G82:H167,2,0),\"\"),\"\")"}, "O84": {"content": "=iferror(if(N84<>\"\",VLOOKUP(N84,G83:H168,2,0),\"\"),\"\")"}, "O85": {"content": "=iferror(if(N85<>\"\",VLOOKUP(N85,G84:H169,2,0),\"\"),\"\")"}, "O86": {"content": "=iferror(if(N86<>\"\",VLOOKUP(N86,G85:H170,2,0),\"\"),\"\")"}, "O87": {"content": "=iferror(if(N87<>\"\",VLOOKUP(N87,G86:H171,2,0),\"\"),\"\")"}, "P1": {"content": "=_t(\"Previous period\")"}, "P2": {"content": "=iferror(if(and(N2<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N2,J1:K86,2,0),\"\"),\"\")"}, "P3": {"content": "=iferror(if(and(N3<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N3,J2:K87,2,0),\"\"),\"\")"}, "P4": {"content": "=iferror(if(and(N4<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N4,J3:K88,2,0),\"\"),\"\")"}, "P5": {"content": "=iferror(if(and(N5<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N5,J4:K89,2,0),\"\"),\"\")"}, "P6": {"content": "=iferror(if(and(N6<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N6,J5:K90,2,0),\"\"),\"\")"}, "P7": {"content": "=iferror(if(and(N7<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N7,J6:K91,2,0),\"\"),\"\")"}, "P8": {"content": "=iferror(if(and(N8<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N8,J7:K92,2,0),\"\"),\"\")"}, "P9": {"content": "=iferror(if(and(N9<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N9,J8:K93,2,0),\"\"),\"\")"}, "P10": {"content": "=iferror(if(and(N10<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N10,J9:K94,2,0),\"\"),\"\")"}, "P11": {"content": "=iferror(if(and(N11<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N11,J10:K95,2,0),\"\"),\"\")"}, "P12": {"content": "=iferror(if(and(N12<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N12,J11:K96,2,0),\"\"),\"\")"}, "P13": {"content": "=iferror(if(and(N13<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N13,J12:K97,2,0),\"\"),\"\")"}, "P14": {"content": "=iferror(if(and(N14<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N14,J13:K98,2,0),\"\"),\"\")"}, "P15": {"content": "=iferror(if(and(N15<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N15,J14:K99,2,0),\"\"),\"\")"}, "P16": {"content": "=iferror(if(and(N16<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N16,J15:K100,2,0),\"\"),\"\")"}, "P17": {"content": "=iferror(if(and(N17<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N17,J16:K101,2,0),\"\"),\"\")"}, "P18": {"content": "=iferror(if(and(N18<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N18,J17:K102,2,0),\"\"),\"\")"}, "P19": {"content": "=iferror(if(and(N19<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N19,J18:K103,2,0),\"\"),\"\")"}, "P20": {"content": "=iferror(if(and(N20<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N20,J19:K104,2,0),\"\"),\"\")"}, "P21": {"content": "=iferror(if(and(N21<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N21,J20:K105,2,0),\"\"),\"\")"}, "P22": {"content": "=iferror(if(and(N22<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N22,J21:K106,2,0),\"\"),\"\")"}, "P23": {"content": "=iferror(if(and(N23<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N23,J22:K107,2,0),\"\"),\"\")"}, "P24": {"content": "=iferror(if(and(N24<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N24,J23:K108,2,0),\"\"),\"\")"}, "P25": {"content": "=iferror(if(and(N25<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N25,J24:K109,2,0),\"\"),\"\")"}, "P26": {"content": "=iferror(if(and(N26<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N26,J25:K110,2,0),\"\"),\"\")"}, "P27": {"content": "=iferror(if(and(N27<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N27,J26:K111,2,0),\"\"),\"\")"}, "P28": {"content": "=iferror(if(and(N28<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N28,J27:K112,2,0),\"\"),\"\")"}, "P29": {"content": "=iferror(if(and(N29<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N29,J28:K113,2,0),\"\"),\"\")"}, "P30": {"content": "=iferror(if(and(N30<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N30,J29:K114,2,0),\"\"),\"\")"}, "P31": {"content": "=iferror(if(and(N31<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N31,J30:K115,2,0),\"\"),\"\")"}, "P32": {"content": "=iferror(if(and(N32<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N32,J31:K116,2,0),\"\"),\"\")"}, "P33": {"content": "=iferror(if(and(N33<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N33,J32:K117,2,0),\"\"),\"\")"}, "P34": {"content": "=iferror(if(and(N34<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N34,J33:K118,2,0),\"\"),\"\")"}, "P35": {"content": "=iferror(if(and(N35<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N35,J34:K119,2,0),\"\"),\"\")"}, "P36": {"content": "=iferror(if(and(N36<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N36,J35:K120,2,0),\"\"),\"\")"}, "P37": {"content": "=iferror(if(and(N37<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N37,J36:K121,2,0),\"\"),\"\")"}, "P38": {"content": "=iferror(if(and(N38<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N38,J37:K122,2,0),\"\"),\"\")"}, "P39": {"content": "=iferror(if(and(N39<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N39,J38:K123,2,0),\"\"),\"\")"}, "P40": {"content": "=iferror(if(and(N40<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N40,J39:K124,2,0),\"\"),\"\")"}, "P41": {"content": "=iferror(if(and(N41<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N41,J40:K125,2,0),\"\"),\"\")"}, "P42": {"content": "=iferror(if(and(N42<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N42,J41:K126,2,0),\"\"),\"\")"}, "P43": {"content": "=iferror(if(and(N43<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N43,J42:K127,2,0),\"\"),\"\")"}, "P44": {"content": "=iferror(if(and(N44<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N44,J43:K128,2,0),\"\"),\"\")"}, "P45": {"content": "=iferror(if(and(N45<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N45,J44:K129,2,0),\"\"),\"\")"}, "P46": {"content": "=iferror(if(and(N46<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N46,J45:K130,2,0),\"\"),\"\")"}, "P47": {"content": "=iferror(if(and(N47<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N47,J46:K131,2,0),\"\"),\"\")"}, "P48": {"content": "=iferror(if(and(N48<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N48,J47:K132,2,0),\"\"),\"\")"}, "P49": {"content": "=iferror(if(and(N49<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N49,J48:K133,2,0),\"\"),\"\")"}, "P50": {"content": "=iferror(if(and(N50<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N50,J49:K134,2,0),\"\"),\"\")"}, "P51": {"content": "=iferror(if(and(N51<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N51,J50:K135,2,0),\"\"),\"\")"}, "P52": {"content": "=iferror(if(and(N52<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N52,J51:K136,2,0),\"\"),\"\")"}, "P53": {"content": "=iferror(if(and(N53<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N53,J52:K137,2,0),\"\"),\"\")"}, "P54": {"content": "=iferror(if(and(N54<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N54,J53:K138,2,0),\"\"),\"\")"}, "P55": {"content": "=iferror(if(and(N55<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N55,J54:K139,2,0),\"\"),\"\")"}, "P56": {"content": "=iferror(if(and(N56<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N56,J55:K140,2,0),\"\"),\"\")"}, "P57": {"content": "=iferror(if(and(N57<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N57,J56:K141,2,0),\"\"),\"\")"}, "P58": {"content": "=iferror(if(and(N58<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N58,J57:K142,2,0),\"\"),\"\")"}, "P59": {"content": "=iferror(if(and(N59<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N59,J58:K143,2,0),\"\"),\"\")"}, "P60": {"content": "=iferror(if(and(N60<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N60,J59:K144,2,0),\"\"),\"\")"}, "P61": {"content": "=iferror(if(and(N61<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N61,J60:K145,2,0),\"\"),\"\")"}, "P62": {"content": "=iferror(if(and(N62<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N62,J61:K146,2,0),\"\"),\"\")"}, "P63": {"content": "=iferror(if(and(N63<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N63,J62:K147,2,0),\"\"),\"\")"}, "P64": {"content": "=iferror(if(and(N64<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N64,J63:K148,2,0),\"\"),\"\")"}, "P65": {"content": "=iferror(if(and(N65<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N65,J64:K149,2,0),\"\"),\"\")"}, "P66": {"content": "=iferror(if(and(N66<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N66,J65:K150,2,0),\"\"),\"\")"}, "P67": {"content": "=iferror(if(and(N67<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N67,J66:K151,2,0),\"\"),\"\")"}, "P68": {"content": "=iferror(if(and(N68<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N68,J67:K152,2,0),\"\"),\"\")"}, "P69": {"content": "=iferror(if(and(N69<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N69,J68:K153,2,0),\"\"),\"\")"}, "P70": {"content": "=iferror(if(and(N70<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N70,J69:K154,2,0),\"\"),\"\")"}, "P71": {"content": "=iferror(if(and(N71<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N71,J70:K155,2,0),\"\"),\"\")"}, "P72": {"content": "=iferror(if(and(N72<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N72,J71:K156,2,0),\"\"),\"\")"}, "P73": {"content": "=iferror(if(and(N73<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N73,J72:K157,2,0),\"\"),\"\")"}, "P74": {"content": "=iferror(if(and(N74<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N74,J73:K158,2,0),\"\"),\"\")"}, "P75": {"content": "=iferror(if(and(N75<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N75,J74:K159,2,0),\"\"),\"\")"}, "P76": {"content": "=iferror(if(and(N76<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N76,J75:K160,2,0),\"\"),\"\")"}, "P77": {"content": "=iferror(if(and(N77<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N77,J76:K161,2,0),\"\"),\"\")"}, "P78": {"content": "=iferror(if(and(N78<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N78,J77:K162,2,0),\"\"),\"\")"}, "P79": {"content": "=iferror(if(and(N79<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N79,J78:K163,2,0),\"\"),\"\")"}, "P80": {"content": "=iferror(if(and(N80<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N80,J79:K164,2,0),\"\"),\"\")"}, "P81": {"content": "=iferror(if(and(N81<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N81,J80:K165,2,0),\"\"),\"\")"}, "P82": {"content": "=iferror(if(and(N82<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N82,J81:K166,2,0),\"\"),\"\")"}, "P83": {"content": "=iferror(if(and(N83<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N83,J82:K167,2,0),\"\"),\"\")"}, "P84": {"content": "=iferror(if(and(N84<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N84,J83:K168,2,0),\"\"),\"\")"}, "P85": {"content": "=iferror(if(and(N85<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N85,J84:K169,2,0),\"\"),\"\")"}, "P86": {"content": "=iferror(if(and(N86<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N86,J85:K170,2,0),\"\"),\"\")"}, "P87": {"content": "=iferror(if(and(N87<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(N87,J86:K171,2,0),\"\"),\"\")"}}, "styles": {"G1": 6, "J1": 6}, "formats": {}, "borders": {}, "conditionalFormats": [], "figures": [], "tables": [{"range": "A1:B87", "type": "static"}, {"range": "D1:E87", "type": "static"}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "801fbbd6-fa08-4dfe-b562-f36e23f1463f", "name": "Inventory adjustments by category", "colNumber": 22, "rowNumber": 270, "rows": {}, "cols": {"0": {"size": 250}, "1": {"size": 149}, "3": {"size": 311}, "7": {"size": 280}, "8": {"size": 149}}, "merges": [], "cells": {"A1": {"content": "=PIVOT(34)"}, "D1": {"content": "=PIVOT(35)"}, "H1": {"content": "=_t(\"Categories\")"}, "H2": {"content": "=iferror(UNIQUE(VSTACK(FILTER(A2:A,A2:A<>\"Total\",A2:A<>\"\"),FILTER(D2:D,D2:D<>\"Total\",D2:D<>\"\"))),\"\")"}, "I1": {"content": "=_t(\"Current period\")"}, "I2": {"content": "=iferror(if(H2<>\"\",VLOOKUP(H2,A:B,2,0),\"\"),\"\")"}, "I3": {"content": "=iferror(if(H3<>\"\",VLOOKUP(H3,A:B,2,0),\"\"),\"\")"}, "I4": {"content": "=iferror(if(H4<>\"\",VLOOKUP(H4,A:B,2,0),\"\"),\"\")"}, "I5": {"content": "=iferror(if(H5<>\"\",VLOOKUP(H5,A:B,2,0),\"\"),\"\")"}, "I6": {"content": "=iferror(if(H6<>\"\",VLOOKUP(H6,A:B,2,0),\"\"),\"\")"}, "I7": {"content": "=iferror(if(H7<>\"\",VLOOKUP(H7,A:B,2,0),\"\"),\"\")"}, "I8": {"content": "=iferror(if(H8<>\"\",VLOOKUP(H8,A:B,2,0),\"\"),\"\")"}, "I9": {"content": "=iferror(if(H9<>\"\",VLOOKUP(H9,A:B,2,0),\"\"),\"\")"}, "I10": {"content": "=iferror(if(H10<>\"\",VLOOKUP(H10,A:B,2,0),\"\"),\"\")"}, "I11": {"content": "=iferror(if(H11<>\"\",VLOOKUP(H11,A:B,2,0),\"\"),\"\")"}, "I12": {"content": "=iferror(if(H12<>\"\",VLOOKUP(H12,A:B,2,0),\"\"),\"\")"}, "I13": {"content": "=iferror(if(H13<>\"\",VLOOKUP(H13,A:B,2,0),\"\"),\"\")"}, "I14": {"content": "=iferror(if(H14<>\"\",VLOOKUP(H14,A:B,2,0),\"\"),\"\")"}, "I15": {"content": "=iferror(if(H15<>\"\",VLOOKUP(H15,A:B,2,0),\"\"),\"\")"}, "I16": {"content": "=iferror(if(H16<>\"\",VLOOKUP(H16,A:B,2,0),\"\"),\"\")"}, "I17": {"content": "=iferror(if(H17<>\"\",VLOOKUP(H17,A:B,2,0),\"\"),\"\")"}, "I18": {"content": "=iferror(if(H18<>\"\",VLOOKUP(H18,A:B,2,0),\"\"),\"\")"}, "I19": {"content": "=iferror(if(H19<>\"\",VLOOKUP(H19,A:B,2,0),\"\"),\"\")"}, "I20": {"content": "=iferror(if(H20<>\"\",VLOOKUP(H20,A:B,2,0),\"\"),\"\")"}, "I21": {"content": "=iferror(if(H21<>\"\",VLOOKUP(H21,A:B,2,0),\"\"),\"\")"}, "I22": {"content": "=iferror(if(H22<>\"\",VLOOKUP(H22,A:B,2,0),\"\"),\"\")"}, "I23": {"content": "=iferror(if(H23<>\"\",VLOOKUP(H23,A:B,2,0),\"\"),\"\")"}, "I24": {"content": "=iferror(if(H24<>\"\",VLOOKUP(H24,A:B,2,0),\"\"),\"\")"}, "I25": {"content": "=iferror(if(H25<>\"\",VLOOKUP(H25,A:B,2,0),\"\"),\"\")"}, "I26": {"content": "=iferror(if(H26<>\"\",VLOOKUP(H26,A:B,2,0),\"\"),\"\")"}, "I27": {"content": "=iferror(if(H27<>\"\",VLOOKUP(H27,A:B,2,0),\"\"),\"\")"}, "I28": {"content": "=iferror(if(H28<>\"\",VLOOKUP(H28,A:B,2,0),\"\"),\"\")"}, "I29": {"content": "=iferror(if(H29<>\"\",VLOOKUP(H29,A:B,2,0),\"\"),\"\")"}, "I30": {"content": "=iferror(if(H30<>\"\",VLOOKUP(H30,A:B,2,0),\"\"),\"\")"}, "I31": {"content": "=iferror(if(H31<>\"\",VLOOKUP(H31,A:B,2,0),\"\"),\"\")"}, "I32": {"content": "=iferror(if(H32<>\"\",VLOOKUP(H32,A:B,2,0),\"\"),\"\")"}, "I33": {"content": "=iferror(if(H33<>\"\",VLOOKUP(H33,A:B,2,0),\"\"),\"\")"}, "I34": {"content": "=iferror(if(H34<>\"\",VLOOKUP(H34,A:B,2,0),\"\"),\"\")"}, "I35": {"content": "=iferror(if(H35<>\"\",VLOOKUP(H35,A:B,2,0),\"\"),\"\")"}, "I36": {"content": "=iferror(if(H36<>\"\",VLOOKUP(H36,A:B,2,0),\"\"),\"\")"}, "I37": {"content": "=iferror(if(H37<>\"\",VLOOKUP(H37,A:B,2,0),\"\"),\"\")"}, "I38": {"content": "=iferror(if(H38<>\"\",VLOOKUP(H38,A:B,2,0),\"\"),\"\")"}, "I39": {"content": "=iferror(if(H39<>\"\",VLOOKUP(H39,A:B,2,0),\"\"),\"\")"}, "I40": {"content": "=iferror(if(H40<>\"\",VLOOKUP(H40,A:B,2,0),\"\"),\"\")"}, "I41": {"content": "=iferror(if(H41<>\"\",VLOOKUP(H41,A:B,2,0),\"\"),\"\")"}, "I42": {"content": "=iferror(if(H42<>\"\",VLOOKUP(H42,A:B,2,0),\"\"),\"\")"}, "I43": {"content": "=iferror(if(H43<>\"\",VLOOKUP(H43,A:B,2,0),\"\"),\"\")"}, "I44": {"content": "=iferror(if(H44<>\"\",VLOOKUP(H44,A:B,2,0),\"\"),\"\")"}, "I45": {"content": "=iferror(if(H45<>\"\",VLOOKUP(H45,A:B,2,0),\"\"),\"\")"}, "I46": {"content": "=iferror(if(H46<>\"\",VLOOKUP(H46,A:B,2,0),\"\"),\"\")"}, "I47": {"content": "=iferror(if(H47<>\"\",VLOOKUP(H47,A:B,2,0),\"\"),\"\")"}, "I48": {"content": "=iferror(if(H48<>\"\",VLOOKUP(H48,A:B,2,0),\"\"),\"\")"}, "I49": {"content": "=iferror(if(H49<>\"\",VLOOKUP(H49,A:B,2,0),\"\"),\"\")"}, "I50": {"content": "=iferror(if(H50<>\"\",VLOOKUP(H50,A:B,2,0),\"\"),\"\")"}, "I51": {"content": "=iferror(if(H51<>\"\",VLOOKUP(H51,A:B,2,0),\"\"),\"\")"}, "I52": {"content": "=iferror(if(H52<>\"\",VLOOKUP(H52,A:B,2,0),\"\"),\"\")"}, "I53": {"content": "=iferror(if(H53<>\"\",VLOOKUP(H53,A:B,2,0),\"\"),\"\")"}, "I54": {"content": "=iferror(if(H54<>\"\",VLOOKUP(H54,A:B,2,0),\"\"),\"\")"}, "I55": {"content": "=iferror(if(H55<>\"\",VLOOKUP(H55,A:B,2,0),\"\"),\"\")"}, "I56": {"content": "=iferror(if(H56<>\"\",VLOOKUP(H56,A:B,2,0),\"\"),\"\")"}, "I57": {"content": "=iferror(if(H57<>\"\",VLOOKUP(H57,A:B,2,0),\"\"),\"\")"}, "I58": {"content": "=iferror(if(H58<>\"\",VLOOKUP(H58,A:B,2,0),\"\"),\"\")"}, "I59": {"content": "=iferror(if(H59<>\"\",VLOOKUP(H59,A:B,2,0),\"\"),\"\")"}, "I60": {"content": "=iferror(if(H60<>\"\",VLOOKUP(H60,A:B,2,0),\"\"),\"\")"}, "I61": {"content": "=iferror(if(H61<>\"\",VLOOKUP(H61,A:B,2,0),\"\"),\"\")"}, "I62": {"content": "=iferror(if(H62<>\"\",VLOOKUP(H62,A:B,2,0),\"\"),\"\")"}, "I63": {"content": "=iferror(if(H63<>\"\",VLOOKUP(H63,A:B,2,0),\"\"),\"\")"}, "I64": {"content": "=iferror(if(H64<>\"\",VLOOKUP(H64,A:B,2,0),\"\"),\"\")"}, "I65": {"content": "=iferror(if(H65<>\"\",VLOOKUP(H65,A:B,2,0),\"\"),\"\")"}, "I66": {"content": "=iferror(if(H66<>\"\",VLOOKUP(H66,A:B,2,0),\"\"),\"\")"}, "I67": {"content": "=iferror(if(H67<>\"\",VLOOKUP(H67,A:B,2,0),\"\"),\"\")"}, "I68": {"content": "=iferror(if(H68<>\"\",VLOOKUP(H68,A:B,2,0),\"\"),\"\")"}, "I69": {"content": "=iferror(if(H69<>\"\",VLOOKUP(H69,A:B,2,0),\"\"),\"\")"}, "I70": {"content": "=iferror(if(H70<>\"\",VLOOKUP(H70,A:B,2,0),\"\"),\"\")"}, "I71": {"content": "=iferror(if(H71<>\"\",VLOOKUP(H71,A:B,2,0),\"\"),\"\")"}, "I72": {"content": "=iferror(if(H72<>\"\",VLOOKUP(H72,A:B,2,0),\"\"),\"\")"}, "I73": {"content": "=iferror(if(H73<>\"\",VLOOKUP(H73,A:B,2,0),\"\"),\"\")"}, "I74": {"content": "=iferror(if(H74<>\"\",VLOOKUP(H74,A:B,2,0),\"\"),\"\")"}, "I75": {"content": "=iferror(if(H75<>\"\",VLOOKUP(H75,A:B,2,0),\"\"),\"\")"}, "I76": {"content": "=iferror(if(H76<>\"\",VLOOKUP(H76,A:B,2,0),\"\"),\"\")"}, "I77": {"content": "=iferror(if(H77<>\"\",VLOOKUP(H77,A:B,2,0),\"\"),\"\")"}, "I78": {"content": "=iferror(if(H78<>\"\",VLOOKUP(H78,A:B,2,0),\"\"),\"\")"}, "I79": {"content": "=iferror(if(H79<>\"\",VLOOKUP(H79,A:B,2,0),\"\"),\"\")"}, "I80": {"content": "=iferror(if(H80<>\"\",VLOOKUP(H80,A:B,2,0),\"\"),\"\")"}, "I81": {"content": "=iferror(if(H81<>\"\",VLOOKUP(H81,A:B,2,0),\"\"),\"\")"}, "I82": {"content": "=iferror(if(H82<>\"\",VLOOKUP(H82,A:B,2,0),\"\"),\"\")"}, "I83": {"content": "=iferror(if(H83<>\"\",VLOOKUP(H83,A:B,2,0),\"\"),\"\")"}, "I84": {"content": "=iferror(if(H84<>\"\",VLOOKUP(H84,A:B,2,0),\"\"),\"\")"}, "I85": {"content": "=iferror(if(H85<>\"\",VLOOKUP(H85,A:B,2,0),\"\"),\"\")"}, "I86": {"content": "=iferror(if(H86<>\"\",VLOOKUP(H86,A:B,2,0),\"\"),\"\")"}, "I87": {"content": "=iferror(if(H87<>\"\",VLOOKUP(H87,A:B,2,0),\"\"),\"\")"}, "I88": {"content": "=iferror(if(H88<>\"\",VLOOKUP(H88,A:B,2,0),\"\"),\"\")"}, "I89": {"content": "=iferror(if(H89<>\"\",VLOOKUP(H89,A:B,2,0),\"\"),\"\")"}, "I90": {"content": "=iferror(if(H90<>\"\",VLOOKUP(H90,A:B,2,0),\"\"),\"\")"}, "I91": {"content": "=iferror(if(H91<>\"\",VLOOKUP(H91,A:B,2,0),\"\"),\"\")"}, "I92": {"content": "=iferror(if(H92<>\"\",VLOOKUP(H92,A:B,2,0),\"\"),\"\")"}, "I93": {"content": "=iferror(if(H93<>\"\",VLOOKUP(H93,A:B,2,0),\"\"),\"\")"}, "I94": {"content": "=iferror(if(H94<>\"\",VLOOKUP(H94,A:B,2,0),\"\"),\"\")"}, "I95": {"content": "=iferror(if(H95<>\"\",VLOOKUP(H95,A:B,2,0),\"\"),\"\")"}, "I96": {"content": "=iferror(if(H96<>\"\",VLOOKUP(H96,A:B,2,0),\"\"),\"\")"}, "I97": {"content": "=iferror(if(H97<>\"\",VLOOKUP(H97,A:B,2,0),\"\"),\"\")"}, "I98": {"content": "=iferror(if(H98<>\"\",VLOOKUP(H98,A:B,2,0),\"\"),\"\")"}, "I99": {"content": "=iferror(if(H99<>\"\",VLOOKUP(H99,A:B,2,0),\"\"),\"\")"}, "I100": {"content": "=iferror(if(H100<>\"\",VLOOKUP(H100,A:B,2,0),\"\"),\"\")"}, "I101": {"content": "=iferror(if(H101<>\"\",VLOOKUP(H101,A:B,2,0),\"\"),\"\")"}, "I102": {"content": "=iferror(if(H102<>\"\",VLOOKUP(H102,A:B,2,0),\"\"),\"\")"}, "I103": {"content": "=iferror(if(H103<>\"\",VLOOKUP(H103,A:B,2,0),\"\"),\"\")"}, "I104": {"content": "=iferror(if(H104<>\"\",VLOOKUP(H104,A:B,2,0),\"\"),\"\")"}, "I105": {"content": "=iferror(if(H105<>\"\",VLOOKUP(H105,A:B,2,0),\"\"),\"\")"}, "I106": {"content": "=iferror(if(H106<>\"\",VLOOKUP(H106,A:B,2,0),\"\"),\"\")"}, "I107": {"content": "=iferror(if(H107<>\"\",VLOOKUP(H107,A:B,2,0),\"\"),\"\")"}, "I108": {"content": "=iferror(if(H108<>\"\",VLOOKUP(H108,A:B,2,0),\"\"),\"\")"}, "I109": {"content": "=iferror(if(H109<>\"\",VLOOKUP(H109,A:B,2,0),\"\"),\"\")"}, "I110": {"content": "=iferror(if(H110<>\"\",VLOOKUP(H110,A:B,2,0),\"\"),\"\")"}, "I111": {"content": "=iferror(if(H111<>\"\",VLOOKUP(H111,A:B,2,0),\"\"),\"\")"}, "I112": {"content": "=iferror(if(H112<>\"\",VLOOKUP(H112,A:B,2,0),\"\"),\"\")"}, "I113": {"content": "=iferror(if(H113<>\"\",VLOOKUP(H113,A:B,2,0),\"\"),\"\")"}, "I114": {"content": "=iferror(if(H114<>\"\",VLOOKUP(H114,A:B,2,0),\"\"),\"\")"}, "I115": {"content": "=iferror(if(H115<>\"\",VLOOKUP(H115,A:B,2,0),\"\"),\"\")"}, "I116": {"content": "=iferror(if(H116<>\"\",VLOOKUP(H116,A:B,2,0),\"\"),\"\")"}, "I117": {"content": "=iferror(if(H117<>\"\",VLOOKUP(H117,A:B,2,0),\"\"),\"\")"}, "I118": {"content": "=iferror(if(H118<>\"\",VLOOKUP(H118,A:B,2,0),\"\"),\"\")"}, "I119": {"content": "=iferror(if(H119<>\"\",VLOOKUP(H119,A:B,2,0),\"\"),\"\")"}, "I120": {"content": "=iferror(if(H120<>\"\",VLOOKUP(H120,A:B,2,0),\"\"),\"\")"}, "I121": {"content": "=iferror(if(H121<>\"\",VLOOKUP(H121,A:B,2,0),\"\"),\"\")"}, "I122": {"content": "=iferror(if(H122<>\"\",VLOOKUP(H122,A:B,2,0),\"\"),\"\")"}, "I123": {"content": "=iferror(if(H123<>\"\",VLOOKUP(H123,A:B,2,0),\"\"),\"\")"}, "I124": {"content": "=iferror(if(H124<>\"\",VLOOKUP(H124,A:B,2,0),\"\"),\"\")"}, "I125": {"content": "=iferror(if(H125<>\"\",VLOOKUP(H125,A:B,2,0),\"\"),\"\")"}, "I126": {"content": "=iferror(if(H126<>\"\",VLOOKUP(H126,A:B,2,0),\"\"),\"\")"}, "I127": {"content": "=iferror(if(H127<>\"\",VLOOKUP(H127,A:B,2,0),\"\"),\"\")"}, "I128": {"content": "=iferror(if(H128<>\"\",VLOOKUP(H128,A:B,2,0),\"\"),\"\")"}, "I129": {"content": "=iferror(if(H129<>\"\",VLOOKUP(H129,A:B,2,0),\"\"),\"\")"}, "I130": {"content": "=iferror(if(H130<>\"\",VLOOKUP(H130,A:B,2,0),\"\"),\"\")"}, "I131": {"content": "=iferror(if(H131<>\"\",VLOOKUP(H131,A:B,2,0),\"\"),\"\")"}, "I132": {"content": "=iferror(if(H132<>\"\",VLOOKUP(H132,A:B,2,0),\"\"),\"\")"}, "I133": {"content": "=iferror(if(H133<>\"\",VLOOKUP(H133,A:B,2,0),\"\"),\"\")"}, "I134": {"content": "=iferror(if(H134<>\"\",VLOOKUP(H134,A:B,2,0),\"\"),\"\")"}, "I135": {"content": "=iferror(if(H135<>\"\",VLOOKUP(H135,A:B,2,0),\"\"),\"\")"}, "I136": {"content": "=iferror(if(H136<>\"\",VLOOKUP(H136,A:B,2,0),\"\"),\"\")"}, "I137": {"content": "=iferror(if(H137<>\"\",VLOOKUP(H137,A:B,2,0),\"\"),\"\")"}, "I138": {"content": "=iferror(if(H138<>\"\",VLOOKUP(H138,A:B,2,0),\"\"),\"\")"}, "I139": {"content": "=iferror(if(H139<>\"\",VLOOKUP(H139,A:B,2,0),\"\"),\"\")"}, "I140": {"content": "=iferror(if(H140<>\"\",VLOOKUP(H140,A:B,2,0),\"\"),\"\")"}, "I141": {"content": "=iferror(if(H141<>\"\",VLOOKUP(H141,A:B,2,0),\"\"),\"\")"}, "I142": {"content": "=iferror(if(H142<>\"\",VLOOKUP(H142,A:B,2,0),\"\"),\"\")"}, "I143": {"content": "=iferror(if(H143<>\"\",VLOOKUP(H143,A:B,2,0),\"\"),\"\")"}, "I144": {"content": "=iferror(if(H144<>\"\",VLOOKUP(H144,A:B,2,0),\"\"),\"\")"}, "I145": {"content": "=iferror(if(H145<>\"\",VLOOKUP(H145,A:B,2,0),\"\"),\"\")"}, "I146": {"content": "=iferror(if(H146<>\"\",VLOOKUP(H146,A:B,2,0),\"\"),\"\")"}, "I147": {"content": "=iferror(if(H147<>\"\",VLOOKUP(H147,A:B,2,0),\"\"),\"\")"}, "I148": {"content": "=iferror(if(H148<>\"\",VLOOKUP(H148,A:B,2,0),\"\"),\"\")"}, "I149": {"content": "=iferror(if(H149<>\"\",VLOOKUP(H149,A:B,2,0),\"\"),\"\")"}, "I150": {"content": "=iferror(if(H150<>\"\",VLOOKUP(H150,A:B,2,0),\"\"),\"\")"}, "I151": {"content": "=iferror(if(H151<>\"\",VLOOKUP(H151,A:B,2,0),\"\"),\"\")"}, "I152": {"content": "=iferror(if(H152<>\"\",VLOOKUP(H152,A:B,2,0),\"\"),\"\")"}, "I153": {"content": "=iferror(if(H153<>\"\",VLOOKUP(H153,A:B,2,0),\"\"),\"\")"}, "I154": {"content": "=iferror(if(H154<>\"\",VLOOKUP(H154,A:B,2,0),\"\"),\"\")"}, "I155": {"content": "=iferror(if(H155<>\"\",VLOOKUP(H155,A:B,2,0),\"\"),\"\")"}, "I156": {"content": "=iferror(if(H156<>\"\",VLOOKUP(H156,A:B,2,0),\"\"),\"\")"}, "I157": {"content": "=iferror(if(H157<>\"\",VLOOKUP(H157,A:B,2,0),\"\"),\"\")"}, "I158": {"content": "=iferror(if(H158<>\"\",VLOOKUP(H158,A:B,2,0),\"\"),\"\")"}, "I159": {"content": "=iferror(if(H159<>\"\",VLOOKUP(H159,A:B,2,0),\"\"),\"\")"}, "I160": {"content": "=iferror(if(H160<>\"\",VLOOKUP(H160,A:B,2,0),\"\"),\"\")"}, "I161": {"content": "=iferror(if(H161<>\"\",VLOOKUP(H161,A:B,2,0),\"\"),\"\")"}, "I162": {"content": "=iferror(if(H162<>\"\",VLOOKUP(H162,A:B,2,0),\"\"),\"\")"}, "I163": {"content": "=iferror(if(H163<>\"\",VLOOKUP(H163,A:B,2,0),\"\"),\"\")"}, "I164": {"content": "=iferror(if(H164<>\"\",VLOOKUP(H164,A:B,2,0),\"\"),\"\")"}, "I165": {"content": "=iferror(if(H165<>\"\",VLOOKUP(H165,A:B,2,0),\"\"),\"\")"}, "I166": {"content": "=iferror(if(H166<>\"\",VLOOKUP(H166,A:B,2,0),\"\"),\"\")"}, "I167": {"content": "=iferror(if(H167<>\"\",VLOOKUP(H167,A:B,2,0),\"\"),\"\")"}, "I168": {"content": "=iferror(if(H168<>\"\",VLOOKUP(H168,A:B,2,0),\"\"),\"\")"}, "I169": {"content": "=iferror(if(H169<>\"\",VLOOKUP(H169,A:B,2,0),\"\"),\"\")"}, "I170": {"content": "=iferror(if(H170<>\"\",VLOOKUP(H170,A:B,2,0),\"\"),\"\")"}, "I171": {"content": "=iferror(if(H171<>\"\",VLOOKUP(H171,A:B,2,0),\"\"),\"\")"}, "I172": {"content": "=iferror(if(H172<>\"\",VLOOKUP(H172,A:B,2,0),\"\"),\"\")"}, "I173": {"content": "=iferror(if(H173<>\"\",VLOOKUP(H173,A:B,2,0),\"\"),\"\")"}, "I174": {"content": "=iferror(if(H174<>\"\",VLOOKUP(H174,A:B,2,0),\"\"),\"\")"}, "I175": {"content": "=iferror(if(H175<>\"\",VLOOKUP(H175,A:B,2,0),\"\"),\"\")"}, "I176": {"content": "=iferror(if(H176<>\"\",VLOOKUP(H176,A:B,2,0),\"\"),\"\")"}, "I177": {"content": "=iferror(if(H177<>\"\",VLOOKUP(H177,A:B,2,0),\"\"),\"\")"}, "I178": {"content": "=iferror(if(H178<>\"\",VLOOKUP(H178,A:B,2,0),\"\"),\"\")"}, "I179": {"content": "=iferror(if(H179<>\"\",VLOOKUP(H179,A:B,2,0),\"\"),\"\")"}, "I180": {"content": "=iferror(if(H180<>\"\",VLOOKUP(H180,A:B,2,0),\"\"),\"\")"}, "I181": {"content": "=iferror(if(H181<>\"\",VLOOKUP(H181,A:B,2,0),\"\"),\"\")"}, "I182": {"content": "=iferror(if(H182<>\"\",VLOOKUP(H182,A:B,2,0),\"\"),\"\")"}, "I183": {"content": "=iferror(if(H183<>\"\",VLOOKUP(H183,A:B,2,0),\"\"),\"\")"}, "I184": {"content": "=iferror(if(H184<>\"\",VLOOKUP(H184,A:B,2,0),\"\"),\"\")"}, "I185": {"content": "=iferror(if(H185<>\"\",VLOOKUP(H185,A:B,2,0),\"\"),\"\")"}, "I186": {"content": "=iferror(if(H186<>\"\",VLOOKUP(H186,A:B,2,0),\"\"),\"\")"}, "I187": {"content": "=iferror(if(H187<>\"\",VLOOKUP(H187,A:B,2,0),\"\"),\"\")"}, "I188": {"content": "=iferror(if(H188<>\"\",VLOOKUP(H188,A:B,2,0),\"\"),\"\")"}, "I189": {"content": "=iferror(if(H189<>\"\",VLOOKUP(H189,A:B,2,0),\"\"),\"\")"}, "I190": {"content": "=iferror(if(H190<>\"\",VLOOKUP(H190,A:B,2,0),\"\"),\"\")"}, "I191": {"content": "=iferror(if(H191<>\"\",VLOOKUP(H191,A:B,2,0),\"\"),\"\")"}, "I192": {"content": "=iferror(if(H192<>\"\",VLOOKUP(H192,A:B,2,0),\"\"),\"\")"}, "I193": {"content": "=iferror(if(H193<>\"\",VLOOKUP(H193,A:B,2,0),\"\"),\"\")"}, "I194": {"content": "=iferror(if(H194<>\"\",VLOOKUP(H194,A:B,2,0),\"\"),\"\")"}, "I195": {"content": "=iferror(if(H195<>\"\",VLOOKUP(H195,A:B,2,0),\"\"),\"\")"}, "I196": {"content": "=iferror(if(H196<>\"\",VLOOKUP(H196,A:B,2,0),\"\"),\"\")"}, "I197": {"content": "=iferror(if(H197<>\"\",VLOOKUP(H197,A:B,2,0),\"\"),\"\")"}, "I198": {"content": "=iferror(if(H198<>\"\",VLOOKUP(H198,A:B,2,0),\"\"),\"\")"}, "I199": {"content": "=iferror(if(H199<>\"\",VLOOKUP(H199,A:B,2,0),\"\"),\"\")"}, "I200": {"content": "=iferror(if(H200<>\"\",VLOOKUP(H200,A:B,2,0),\"\"),\"\")"}, "I201": {"content": "=iferror(if(H201<>\"\",VLOOKUP(H201,A:B,2,0),\"\"),\"\")"}, "I202": {"content": "=iferror(if(H202<>\"\",VLOOKUP(H202,A:B,2,0),\"\"),\"\")"}, "I203": {"content": "=iferror(if(H203<>\"\",VLOOKUP(H203,A:B,2,0),\"\"),\"\")"}, "I204": {"content": "=iferror(if(H204<>\"\",VLOOKUP(H204,A:B,2,0),\"\"),\"\")"}, "I205": {"content": "=iferror(if(H205<>\"\",VLOOKUP(H205,A:B,2,0),\"\"),\"\")"}, "I206": {"content": "=iferror(if(H206<>\"\",VLOOKUP(H206,A:B,2,0),\"\"),\"\")"}, "I207": {"content": "=iferror(if(H207<>\"\",VLOOKUP(H207,A:B,2,0),\"\"),\"\")"}, "I208": {"content": "=iferror(if(H208<>\"\",VLOOKUP(H208,A:B,2,0),\"\"),\"\")"}, "I209": {"content": "=iferror(if(H209<>\"\",VLOOKUP(H209,A:B,2,0),\"\"),\"\")"}, "I210": {"content": "=iferror(if(H210<>\"\",VLOOKUP(H210,A:B,2,0),\"\"),\"\")"}, "I211": {"content": "=iferror(if(H211<>\"\",VLOOKUP(H211,A:B,2,0),\"\"),\"\")"}, "I212": {"content": "=iferror(if(H212<>\"\",VLOOKUP(H212,A:B,2,0),\"\"),\"\")"}, "I213": {"content": "=iferror(if(H213<>\"\",VLOOKUP(H213,A:B,2,0),\"\"),\"\")"}, "I214": {"content": "=iferror(if(H214<>\"\",VLOOKUP(H214,A:B,2,0),\"\"),\"\")"}, "I215": {"content": "=iferror(if(H215<>\"\",VLOOKUP(H215,A:B,2,0),\"\"),\"\")"}, "I216": {"content": "=iferror(if(H216<>\"\",VLOOKUP(H216,A:B,2,0),\"\"),\"\")"}, "I217": {"content": "=iferror(if(H217<>\"\",VLOOKUP(H217,A:B,2,0),\"\"),\"\")"}, "I218": {"content": "=iferror(if(H218<>\"\",VLOOKUP(H218,A:B,2,0),\"\"),\"\")"}, "I219": {"content": "=iferror(if(H219<>\"\",VLOOKUP(H219,A:B,2,0),\"\"),\"\")"}, "I220": {"content": "=iferror(if(H220<>\"\",VLOOKUP(H220,A:B,2,0),\"\"),\"\")"}, "I221": {"content": "=iferror(if(H221<>\"\",VLOOKUP(H221,A:B,2,0),\"\"),\"\")"}, "I222": {"content": "=iferror(if(H222<>\"\",VLOOKUP(H222,A:B,2,0),\"\"),\"\")"}, "I223": {"content": "=iferror(if(H223<>\"\",VLOOKUP(H223,A:B,2,0),\"\"),\"\")"}, "I224": {"content": "=iferror(if(H224<>\"\",VLOOKUP(H224,A:B,2,0),\"\"),\"\")"}, "I225": {"content": "=iferror(if(H225<>\"\",VLOOKUP(H225,A:B,2,0),\"\"),\"\")"}, "I226": {"content": "=iferror(if(H226<>\"\",VLOOKUP(H226,A:B,2,0),\"\"),\"\")"}, "I227": {"content": "=iferror(if(H227<>\"\",VLOOKUP(H227,A:B,2,0),\"\"),\"\")"}, "I228": {"content": "=iferror(if(H228<>\"\",VLOOKUP(H228,A:B,2,0),\"\"),\"\")"}, "I229": {"content": "=iferror(if(H229<>\"\",VLOOKUP(H229,A:B,2,0),\"\"),\"\")"}, "I230": {"content": "=iferror(if(H230<>\"\",VLOOKUP(H230,A:B,2,0),\"\"),\"\")"}, "I231": {"content": "=iferror(if(H231<>\"\",VLOOKUP(H231,A:B,2,0),\"\"),\"\")"}, "I232": {"content": "=iferror(if(H232<>\"\",VLOOKUP(H232,A:B,2,0),\"\"),\"\")"}, "I233": {"content": "=iferror(if(H233<>\"\",VLOOKUP(H233,A:B,2,0),\"\"),\"\")"}, "I234": {"content": "=iferror(if(H234<>\"\",VLOOKUP(H234,A:B,2,0),\"\"),\"\")"}, "I235": {"content": "=iferror(if(H235<>\"\",VLOOKUP(H235,A:B,2,0),\"\"),\"\")"}, "I236": {"content": "=iferror(if(H236<>\"\",VLOOKUP(H236,A:B,2,0),\"\"),\"\")"}, "I237": {"content": "=iferror(if(H237<>\"\",VLOOKUP(H237,A:B,2,0),\"\"),\"\")"}, "I238": {"content": "=iferror(if(H238<>\"\",VLOOKUP(H238,A:B,2,0),\"\"),\"\")"}, "I239": {"content": "=iferror(if(H239<>\"\",VLOOKUP(H239,A:B,2,0),\"\"),\"\")"}, "I240": {"content": "=iferror(if(H240<>\"\",VLOOKUP(H240,A:B,2,0),\"\"),\"\")"}, "I241": {"content": "=iferror(if(H241<>\"\",VLOOKUP(H241,A:B,2,0),\"\"),\"\")"}, "I242": {"content": "=iferror(if(H242<>\"\",VLOOKUP(H242,A:B,2,0),\"\"),\"\")"}, "I243": {"content": "=iferror(if(H243<>\"\",VLOOKUP(H243,A:B,2,0),\"\"),\"\")"}, "I244": {"content": "=iferror(if(H244<>\"\",VLOOKUP(H244,A:B,2,0),\"\"),\"\")"}, "I245": {"content": "=iferror(if(H245<>\"\",VLOOKUP(H245,A:B,2,0),\"\"),\"\")"}, "I246": {"content": "=iferror(if(H246<>\"\",VLOOKUP(H246,A:B,2,0),\"\"),\"\")"}, "I247": {"content": "=iferror(if(H247<>\"\",VLOOKUP(H247,A:B,2,0),\"\"),\"\")"}, "I248": {"content": "=iferror(if(H248<>\"\",VLOOKUP(H248,A:B,2,0),\"\"),\"\")"}, "I249": {"content": "=iferror(if(H249<>\"\",VLOOKUP(H249,A:B,2,0),\"\"),\"\")"}, "I250": {"content": "=iferror(if(H250<>\"\",VLOOKUP(H250,A:B,2,0),\"\"),\"\")"}, "I251": {"content": "=iferror(if(H251<>\"\",VLOOKUP(H251,A:B,2,0),\"\"),\"\")"}, "I252": {"content": "=iferror(if(H252<>\"\",VLOOKUP(H252,A:B,2,0),\"\"),\"\")"}, "I253": {"content": "=iferror(if(H253<>\"\",VLOOKUP(H253,A:B,2,0),\"\"),\"\")"}, "I254": {"content": "=iferror(if(H254<>\"\",VLOOKUP(H254,A:B,2,0),\"\"),\"\")"}, "I255": {"content": "=iferror(if(H255<>\"\",VLOOKUP(H255,A:B,2,0),\"\"),\"\")"}, "I256": {"content": "=iferror(if(H256<>\"\",VLOOKUP(H256,A:B,2,0),\"\"),\"\")"}, "I257": {"content": "=iferror(if(H257<>\"\",VLOOKUP(H257,A:B,2,0),\"\"),\"\")"}, "I258": {"content": "=iferror(if(H258<>\"\",VLOOKUP(H258,A:B,2,0),\"\"),\"\")"}, "I259": {"content": "=iferror(if(H259<>\"\",VLOOKUP(H259,A:B,2,0),\"\"),\"\")"}, "I260": {"content": "=iferror(if(H260<>\"\",VLOOKUP(H260,A:B,2,0),\"\"),\"\")"}, "I261": {"content": "=iferror(if(H261<>\"\",VLOOKUP(H261,A:B,2,0),\"\"),\"\")"}, "I262": {"content": "=iferror(if(H262<>\"\",VLOOKUP(H262,A:B,2,0),\"\"),\"\")"}, "I263": {"content": "=iferror(if(H263<>\"\",VLOOKUP(H263,A:B,2,0),\"\"),\"\")"}, "I264": {"content": "=iferror(if(H264<>\"\",VLOOKUP(H264,A:B,2,0),\"\"),\"\")"}, "I265": {"content": "=iferror(if(H265<>\"\",VLOOKUP(H265,A:B,2,0),\"\"),\"\")"}, "I266": {"content": "=iferror(if(H266<>\"\",VLOOKUP(H266,A:B,2,0),\"\"),\"\")"}, "I267": {"content": "=iferror(if(H267<>\"\",VLOOKUP(H267,A:B,2,0),\"\"),\"\")"}, "I268": {"content": "=iferror(if(H268<>\"\",VLOOKUP(H268,A:B,2,0),\"\"),\"\")"}, "I269": {"content": "=iferror(if(H269<>\"\",VLOOKUP(H269,A:B,2,0),\"\"),\"\")"}, "I270": {"content": "=iferror(if(H270<>\"\",VLOOKUP(H270,A:B,2,0),\"\"),\"\")"}, "J1": {"content": "=_t(\"Previous period\")"}, "J2": {"content": "=iferror(if(and(H2<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H2,D:E,2,0),\"\"),\"\")"}, "J3": {"content": "=iferror(if(and(H3<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H3,D:E,2,0),\"\"),\"\")"}, "J4": {"content": "=iferror(if(and(H4<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H4,D:E,2,0),\"\"),\"\")"}, "J5": {"content": "=iferror(if(and(H5<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H5,D:E,2,0),\"\"),\"\")"}, "J6": {"content": "=iferror(if(and(H6<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H6,D:E,2,0),\"\"),\"\")"}, "J7": {"content": "=iferror(if(and(H7<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H7,D:E,2,0),\"\"),\"\")"}, "J8": {"content": "=iferror(if(and(H8<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H8,D:E,2,0),\"\"),\"\")"}, "J9": {"content": "=iferror(if(and(H9<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H9,D:E,2,0),\"\"),\"\")"}, "J10": {"content": "=iferror(if(and(H10<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H10,D:E,2,0),\"\"),\"\")"}, "J11": {"content": "=iferror(if(and(H11<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H11,D:E,2,0),\"\"),\"\")"}, "J12": {"content": "=iferror(if(and(H12<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H12,D:E,2,0),\"\"),\"\")"}, "J13": {"content": "=iferror(if(and(H13<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H13,D:E,2,0),\"\"),\"\")"}, "J14": {"content": "=iferror(if(and(H14<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H14,D:E,2,0),\"\"),\"\")"}, "J15": {"content": "=iferror(if(and(H15<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H15,D:E,2,0),\"\"),\"\")"}, "J16": {"content": "=iferror(if(and(H16<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H16,D:E,2,0),\"\"),\"\")"}, "J17": {"content": "=iferror(if(and(H17<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H17,D:E,2,0),\"\"),\"\")"}, "J18": {"content": "=iferror(if(and(H18<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H18,D:E,2,0),\"\"),\"\")"}, "J19": {"content": "=iferror(if(and(H19<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H19,D:E,2,0),\"\"),\"\")"}, "J20": {"content": "=iferror(if(and(H20<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H20,D:E,2,0),\"\"),\"\")"}, "J21": {"content": "=iferror(if(and(H21<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H21,D:E,2,0),\"\"),\"\")"}, "J22": {"content": "=iferror(if(and(H22<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H22,D:E,2,0),\"\"),\"\")"}, "J23": {"content": "=iferror(if(and(H23<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H23,D:E,2,0),\"\"),\"\")"}, "J24": {"content": "=iferror(if(and(H24<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H24,D:E,2,0),\"\"),\"\")"}, "J25": {"content": "=iferror(if(and(H25<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H25,D:E,2,0),\"\"),\"\")"}, "J26": {"content": "=iferror(if(and(H26<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H26,D:E,2,0),\"\"),\"\")"}, "J27": {"content": "=iferror(if(and(H27<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H27,D:E,2,0),\"\"),\"\")"}, "J28": {"content": "=iferror(if(and(H28<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H28,D:E,2,0),\"\"),\"\")"}, "J29": {"content": "=iferror(if(and(H29<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H29,D:E,2,0),\"\"),\"\")"}, "J30": {"content": "=iferror(if(and(H30<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H30,D:E,2,0),\"\"),\"\")"}, "J31": {"content": "=iferror(if(and(H31<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H31,D:E,2,0),\"\"),\"\")"}, "J32": {"content": "=iferror(if(and(H32<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H32,D:E,2,0),\"\"),\"\")"}, "J33": {"content": "=iferror(if(and(H33<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H33,D:E,2,0),\"\"),\"\")"}, "J34": {"content": "=iferror(if(and(H34<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H34,D:E,2,0),\"\"),\"\")"}, "J35": {"content": "=iferror(if(and(H35<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H35,D:E,2,0),\"\"),\"\")"}, "J36": {"content": "=iferror(if(and(H36<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H36,D:E,2,0),\"\"),\"\")"}, "J37": {"content": "=iferror(if(and(H37<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H37,D:E,2,0),\"\"),\"\")"}, "J38": {"content": "=iferror(if(and(H38<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H38,D:E,2,0),\"\"),\"\")"}, "J39": {"content": "=iferror(if(and(H39<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H39,D:E,2,0),\"\"),\"\")"}, "J40": {"content": "=iferror(if(and(H40<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H40,D:E,2,0),\"\"),\"\")"}, "J41": {"content": "=iferror(if(and(H41<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H41,D:E,2,0),\"\"),\"\")"}, "J42": {"content": "=iferror(if(and(H42<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H42,D:E,2,0),\"\"),\"\")"}, "J43": {"content": "=iferror(if(and(H43<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H43,D:E,2,0),\"\"),\"\")"}, "J44": {"content": "=iferror(if(and(H44<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H44,D:E,2,0),\"\"),\"\")"}, "J45": {"content": "=iferror(if(and(H45<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H45,D:E,2,0),\"\"),\"\")"}, "J46": {"content": "=iferror(if(and(H46<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H46,D:E,2,0),\"\"),\"\")"}, "J47": {"content": "=iferror(if(and(H47<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H47,D:E,2,0),\"\"),\"\")"}, "J48": {"content": "=iferror(if(and(H48<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H48,D:E,2,0),\"\"),\"\")"}, "J49": {"content": "=iferror(if(and(H49<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H49,D:E,2,0),\"\"),\"\")"}, "J50": {"content": "=iferror(if(and(H50<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H50,D:E,2,0),\"\"),\"\")"}, "J51": {"content": "=iferror(if(and(H51<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H51,D:E,2,0),\"\"),\"\")"}, "J52": {"content": "=iferror(if(and(H52<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H52,D:E,2,0),\"\"),\"\")"}, "J53": {"content": "=iferror(if(and(H53<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H53,D:E,2,0),\"\"),\"\")"}, "J54": {"content": "=iferror(if(and(H54<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H54,D:E,2,0),\"\"),\"\")"}, "J55": {"content": "=iferror(if(and(H55<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H55,D:E,2,0),\"\"),\"\")"}, "J56": {"content": "=iferror(if(and(H56<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H56,D:E,2,0),\"\"),\"\")"}, "J57": {"content": "=iferror(if(and(H57<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H57,D:E,2,0),\"\"),\"\")"}, "J58": {"content": "=iferror(if(and(H58<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H58,D:E,2,0),\"\"),\"\")"}, "J59": {"content": "=iferror(if(and(H59<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H59,D:E,2,0),\"\"),\"\")"}, "J60": {"content": "=iferror(if(and(H60<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H60,D:E,2,0),\"\"),\"\")"}, "J61": {"content": "=iferror(if(and(H61<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H61,D:E,2,0),\"\"),\"\")"}, "J62": {"content": "=iferror(if(and(H62<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H62,D:E,2,0),\"\"),\"\")"}, "J63": {"content": "=iferror(if(and(H63<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H63,D:E,2,0),\"\"),\"\")"}, "J64": {"content": "=iferror(if(and(H64<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H64,D:E,2,0),\"\"),\"\")"}, "J65": {"content": "=iferror(if(and(H65<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H65,D:E,2,0),\"\"),\"\")"}, "J66": {"content": "=iferror(if(and(H66<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H66,D:E,2,0),\"\"),\"\")"}, "J67": {"content": "=iferror(if(and(H67<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H67,D:E,2,0),\"\"),\"\")"}, "J68": {"content": "=iferror(if(and(H68<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H68,D:E,2,0),\"\"),\"\")"}, "J69": {"content": "=iferror(if(and(H69<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H69,D:E,2,0),\"\"),\"\")"}, "J70": {"content": "=iferror(if(and(H70<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H70,D:E,2,0),\"\"),\"\")"}, "J71": {"content": "=iferror(if(and(H71<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H71,D:E,2,0),\"\"),\"\")"}, "J72": {"content": "=iferror(if(and(H72<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H72,D:E,2,0),\"\"),\"\")"}, "J73": {"content": "=iferror(if(and(H73<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H73,D:E,2,0),\"\"),\"\")"}, "J74": {"content": "=iferror(if(and(H74<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H74,D:E,2,0),\"\"),\"\")"}, "J75": {"content": "=iferror(if(and(H75<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H75,D:E,2,0),\"\"),\"\")"}, "J76": {"content": "=iferror(if(and(H76<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H76,D:E,2,0),\"\"),\"\")"}, "J77": {"content": "=iferror(if(and(H77<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H77,D:E,2,0),\"\"),\"\")"}, "J78": {"content": "=iferror(if(and(H78<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H78,D:E,2,0),\"\"),\"\")"}, "J79": {"content": "=iferror(if(and(H79<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H79,D:E,2,0),\"\"),\"\")"}, "J80": {"content": "=iferror(if(and(H80<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H80,D:E,2,0),\"\"),\"\")"}, "J81": {"content": "=iferror(if(and(H81<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H81,D:E,2,0),\"\"),\"\")"}, "J82": {"content": "=iferror(if(and(H82<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H82,D:E,2,0),\"\"),\"\")"}, "J83": {"content": "=iferror(if(and(H83<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H83,D:E,2,0),\"\"),\"\")"}, "J84": {"content": "=iferror(if(and(H84<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H84,D:E,2,0),\"\"),\"\")"}, "J85": {"content": "=iferror(if(and(H85<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H85,D:E,2,0),\"\"),\"\")"}, "J86": {"content": "=iferror(if(and(H86<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H86,D:E,2,0),\"\"),\"\")"}, "J87": {"content": "=iferror(if(and(H87<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H87,D:E,2,0),\"\"),\"\")"}, "J88": {"content": "=iferror(if(and(H88<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H88,D:E,2,0),\"\"),\"\")"}, "J89": {"content": "=iferror(if(and(H89<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H89,D:E,2,0),\"\"),\"\")"}, "J90": {"content": "=iferror(if(and(H90<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H90,D:E,2,0),\"\"),\"\")"}, "J91": {"content": "=iferror(if(and(H91<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H91,D:E,2,0),\"\"),\"\")"}, "J92": {"content": "=iferror(if(and(H92<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H92,D:E,2,0),\"\"),\"\")"}, "J93": {"content": "=iferror(if(and(H93<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H93,D:E,2,0),\"\"),\"\")"}, "J94": {"content": "=iferror(if(and(H94<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H94,D:E,2,0),\"\"),\"\")"}, "J95": {"content": "=iferror(if(and(H95<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H95,D:E,2,0),\"\"),\"\")"}, "J96": {"content": "=iferror(if(and(H96<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H96,D:E,2,0),\"\"),\"\")"}, "J97": {"content": "=iferror(if(and(H97<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H97,D:E,2,0),\"\"),\"\")"}, "J98": {"content": "=iferror(if(and(H98<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H98,D:E,2,0),\"\"),\"\")"}, "J99": {"content": "=iferror(if(and(H99<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H99,D:E,2,0),\"\"),\"\")"}, "J100": {"content": "=iferror(if(and(H100<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H100,D:E,2,0),\"\"),\"\")"}, "J101": {"content": "=iferror(if(and(H101<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H101,D:E,2,0),\"\"),\"\")"}, "J102": {"content": "=iferror(if(and(H102<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H102,D:E,2,0),\"\"),\"\")"}, "J103": {"content": "=iferror(if(and(H103<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H103,D:E,2,0),\"\"),\"\")"}, "J104": {"content": "=iferror(if(and(H104<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H104,D:E,2,0),\"\"),\"\")"}, "J105": {"content": "=iferror(if(and(H105<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H105,D:E,2,0),\"\"),\"\")"}, "J106": {"content": "=iferror(if(and(H106<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H106,D:E,2,0),\"\"),\"\")"}, "J107": {"content": "=iferror(if(and(H107<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H107,D:E,2,0),\"\"),\"\")"}, "J108": {"content": "=iferror(if(and(H108<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H108,D:E,2,0),\"\"),\"\")"}, "J109": {"content": "=iferror(if(and(H109<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H109,D:E,2,0),\"\"),\"\")"}, "J110": {"content": "=iferror(if(and(H110<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H110,D:E,2,0),\"\"),\"\")"}, "J111": {"content": "=iferror(if(and(H111<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H111,D:E,2,0),\"\"),\"\")"}, "J112": {"content": "=iferror(if(and(H112<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H112,D:E,2,0),\"\"),\"\")"}, "J113": {"content": "=iferror(if(and(H113<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H113,D:E,2,0),\"\"),\"\")"}, "J114": {"content": "=iferror(if(and(H114<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H114,D:E,2,0),\"\"),\"\")"}, "J115": {"content": "=iferror(if(and(H115<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H115,D:E,2,0),\"\"),\"\")"}, "J116": {"content": "=iferror(if(and(H116<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H116,D:E,2,0),\"\"),\"\")"}, "J117": {"content": "=iferror(if(and(H117<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H117,D:E,2,0),\"\"),\"\")"}, "J118": {"content": "=iferror(if(and(H118<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H118,D:E,2,0),\"\"),\"\")"}, "J119": {"content": "=iferror(if(and(H119<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H119,D:E,2,0),\"\"),\"\")"}, "J120": {"content": "=iferror(if(and(H120<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H120,D:E,2,0),\"\"),\"\")"}, "J121": {"content": "=iferror(if(and(H121<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H121,D:E,2,0),\"\"),\"\")"}, "J122": {"content": "=iferror(if(and(H122<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H122,D:E,2,0),\"\"),\"\")"}, "J123": {"content": "=iferror(if(and(H123<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H123,D:E,2,0),\"\"),\"\")"}, "J124": {"content": "=iferror(if(and(H124<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H124,D:E,2,0),\"\"),\"\")"}, "J125": {"content": "=iferror(if(and(H125<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H125,D:E,2,0),\"\"),\"\")"}, "J126": {"content": "=iferror(if(and(H126<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H126,D:E,2,0),\"\"),\"\")"}, "J127": {"content": "=iferror(if(and(H127<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H127,D:E,2,0),\"\"),\"\")"}, "J128": {"content": "=iferror(if(and(H128<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H128,D:E,2,0),\"\"),\"\")"}, "J129": {"content": "=iferror(if(and(H129<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H129,D:E,2,0),\"\"),\"\")"}, "J130": {"content": "=iferror(if(and(H130<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H130,D:E,2,0),\"\"),\"\")"}, "J131": {"content": "=iferror(if(and(H131<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H131,D:E,2,0),\"\"),\"\")"}, "J132": {"content": "=iferror(if(and(H132<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H132,D:E,2,0),\"\"),\"\")"}, "J133": {"content": "=iferror(if(and(H133<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H133,D:E,2,0),\"\"),\"\")"}, "J134": {"content": "=iferror(if(and(H134<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H134,D:E,2,0),\"\"),\"\")"}, "J135": {"content": "=iferror(if(and(H135<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H135,D:E,2,0),\"\"),\"\")"}, "J136": {"content": "=iferror(if(and(H136<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H136,D:E,2,0),\"\"),\"\")"}, "J137": {"content": "=iferror(if(and(H137<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H137,D:E,2,0),\"\"),\"\")"}, "J138": {"content": "=iferror(if(and(H138<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H138,D:E,2,0),\"\"),\"\")"}, "J139": {"content": "=iferror(if(and(H139<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H139,D:E,2,0),\"\"),\"\")"}, "J140": {"content": "=iferror(if(and(H140<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H140,D:E,2,0),\"\"),\"\")"}, "J141": {"content": "=iferror(if(and(H141<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H141,D:E,2,0),\"\"),\"\")"}, "J142": {"content": "=iferror(if(and(H142<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H142,D:E,2,0),\"\"),\"\")"}, "J143": {"content": "=iferror(if(and(H143<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H143,D:E,2,0),\"\"),\"\")"}, "J144": {"content": "=iferror(if(and(H144<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H144,D:E,2,0),\"\"),\"\")"}, "J145": {"content": "=iferror(if(and(H145<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H145,D:E,2,0),\"\"),\"\")"}, "J146": {"content": "=iferror(if(and(H146<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H146,D:E,2,0),\"\"),\"\")"}, "J147": {"content": "=iferror(if(and(H147<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H147,D:E,2,0),\"\"),\"\")"}, "J148": {"content": "=iferror(if(and(H148<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H148,D:E,2,0),\"\"),\"\")"}, "J149": {"content": "=iferror(if(and(H149<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H149,D:E,2,0),\"\"),\"\")"}, "J150": {"content": "=iferror(if(and(H150<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H150,D:E,2,0),\"\"),\"\")"}, "J151": {"content": "=iferror(if(and(H151<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H151,D:E,2,0),\"\"),\"\")"}, "J152": {"content": "=iferror(if(and(H152<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H152,D:E,2,0),\"\"),\"\")"}, "J153": {"content": "=iferror(if(and(H153<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H153,D:E,2,0),\"\"),\"\")"}, "J154": {"content": "=iferror(if(and(H154<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H154,D:E,2,0),\"\"),\"\")"}, "J155": {"content": "=iferror(if(and(H155<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H155,D:E,2,0),\"\"),\"\")"}, "J156": {"content": "=iferror(if(and(H156<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H156,D:E,2,0),\"\"),\"\")"}, "J157": {"content": "=iferror(if(and(H157<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H157,D:E,2,0),\"\"),\"\")"}, "J158": {"content": "=iferror(if(and(H158<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H158,D:E,2,0),\"\"),\"\")"}, "J159": {"content": "=iferror(if(and(H159<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H159,D:E,2,0),\"\"),\"\")"}, "J160": {"content": "=iferror(if(and(H160<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H160,D:E,2,0),\"\"),\"\")"}, "J161": {"content": "=iferror(if(and(H161<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H161,D:E,2,0),\"\"),\"\")"}, "J162": {"content": "=iferror(if(and(H162<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H162,D:E,2,0),\"\"),\"\")"}, "J163": {"content": "=iferror(if(and(H163<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H163,D:E,2,0),\"\"),\"\")"}, "J164": {"content": "=iferror(if(and(H164<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H164,D:E,2,0),\"\"),\"\")"}, "J165": {"content": "=iferror(if(and(H165<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H165,D:E,2,0),\"\"),\"\")"}, "J166": {"content": "=iferror(if(and(H166<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H166,D:E,2,0),\"\"),\"\")"}, "J167": {"content": "=iferror(if(and(H167<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H167,D:E,2,0),\"\"),\"\")"}, "J168": {"content": "=iferror(if(and(H168<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H168,D:E,2,0),\"\"),\"\")"}, "J169": {"content": "=iferror(if(and(H169<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H169,D:E,2,0),\"\"),\"\")"}, "J170": {"content": "=iferror(if(and(H170<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H170,D:E,2,0),\"\"),\"\")"}, "J171": {"content": "=iferror(if(and(H171<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H171,D:E,2,0),\"\"),\"\")"}, "J172": {"content": "=iferror(if(and(H172<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H172,D:E,2,0),\"\"),\"\")"}, "J173": {"content": "=iferror(if(and(H173<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H173,D:E,2,0),\"\"),\"\")"}, "J174": {"content": "=iferror(if(and(H174<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H174,D:E,2,0),\"\"),\"\")"}, "J175": {"content": "=iferror(if(and(H175<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H175,D:E,2,0),\"\"),\"\")"}, "J176": {"content": "=iferror(if(and(H176<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H176,D:E,2,0),\"\"),\"\")"}, "J177": {"content": "=iferror(if(and(H177<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H177,D:E,2,0),\"\"),\"\")"}, "J178": {"content": "=iferror(if(and(H178<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H178,D:E,2,0),\"\"),\"\")"}, "J179": {"content": "=iferror(if(and(H179<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H179,D:E,2,0),\"\"),\"\")"}, "J180": {"content": "=iferror(if(and(H180<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H180,D:E,2,0),\"\"),\"\")"}, "J181": {"content": "=iferror(if(and(H181<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H181,D:E,2,0),\"\"),\"\")"}, "J182": {"content": "=iferror(if(and(H182<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H182,D:E,2,0),\"\"),\"\")"}, "J183": {"content": "=iferror(if(and(H183<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H183,D:E,2,0),\"\"),\"\")"}, "J184": {"content": "=iferror(if(and(H184<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H184,D:E,2,0),\"\"),\"\")"}, "J185": {"content": "=iferror(if(and(H185<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H185,D:E,2,0),\"\"),\"\")"}, "J186": {"content": "=iferror(if(and(H186<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H186,D:E,2,0),\"\"),\"\")"}, "J187": {"content": "=iferror(if(and(H187<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H187,D:E,2,0),\"\"),\"\")"}, "J188": {"content": "=iferror(if(and(H188<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H188,D:E,2,0),\"\"),\"\")"}, "J189": {"content": "=iferror(if(and(H189<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H189,D:E,2,0),\"\"),\"\")"}, "J190": {"content": "=iferror(if(and(H190<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H190,D:E,2,0),\"\"),\"\")"}, "J191": {"content": "=iferror(if(and(H191<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H191,D:E,2,0),\"\"),\"\")"}, "J192": {"content": "=iferror(if(and(H192<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H192,D:E,2,0),\"\"),\"\")"}, "J193": {"content": "=iferror(if(and(H193<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H193,D:E,2,0),\"\"),\"\")"}, "J194": {"content": "=iferror(if(and(H194<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H194,D:E,2,0),\"\"),\"\")"}, "J195": {"content": "=iferror(if(and(H195<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H195,D:E,2,0),\"\"),\"\")"}, "J196": {"content": "=iferror(if(and(H196<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H196,D:E,2,0),\"\"),\"\")"}, "J197": {"content": "=iferror(if(and(H197<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H197,D:E,2,0),\"\"),\"\")"}, "J198": {"content": "=iferror(if(and(H198<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H198,D:E,2,0),\"\"),\"\")"}, "J199": {"content": "=iferror(if(and(H199<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H199,D:E,2,0),\"\"),\"\")"}, "J200": {"content": "=iferror(if(and(H200<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H200,D:E,2,0),\"\"),\"\")"}, "J201": {"content": "=iferror(if(and(H201<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H201,D:E,2,0),\"\"),\"\")"}, "J202": {"content": "=iferror(if(and(H202<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H202,D:E,2,0),\"\"),\"\")"}, "J203": {"content": "=iferror(if(and(H203<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H203,D:E,2,0),\"\"),\"\")"}, "J204": {"content": "=iferror(if(and(H204<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H204,D:E,2,0),\"\"),\"\")"}, "J205": {"content": "=iferror(if(and(H205<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H205,D:E,2,0),\"\"),\"\")"}, "J206": {"content": "=iferror(if(and(H206<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H206,D:E,2,0),\"\"),\"\")"}, "J207": {"content": "=iferror(if(and(H207<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H207,D:E,2,0),\"\"),\"\")"}, "J208": {"content": "=iferror(if(and(H208<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H208,D:E,2,0),\"\"),\"\")"}, "J209": {"content": "=iferror(if(and(H209<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H209,D:E,2,0),\"\"),\"\")"}, "J210": {"content": "=iferror(if(and(H210<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H210,D:E,2,0),\"\"),\"\")"}, "J211": {"content": "=iferror(if(and(H211<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H211,D:E,2,0),\"\"),\"\")"}, "J212": {"content": "=iferror(if(and(H212<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H212,D:E,2,0),\"\"),\"\")"}, "J213": {"content": "=iferror(if(and(H213<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H213,D:E,2,0),\"\"),\"\")"}, "J214": {"content": "=iferror(if(and(H214<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H214,D:E,2,0),\"\"),\"\")"}, "J215": {"content": "=iferror(if(and(H215<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H215,D:E,2,0),\"\"),\"\")"}, "J216": {"content": "=iferror(if(and(H216<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H216,D:E,2,0),\"\"),\"\")"}, "J217": {"content": "=iferror(if(and(H217<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H217,D:E,2,0),\"\"),\"\")"}, "J218": {"content": "=iferror(if(and(H218<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H218,D:E,2,0),\"\"),\"\")"}, "J219": {"content": "=iferror(if(and(H219<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H219,D:E,2,0),\"\"),\"\")"}, "J220": {"content": "=iferror(if(and(H220<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H220,D:E,2,0),\"\"),\"\")"}, "J221": {"content": "=iferror(if(and(H221<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H221,D:E,2,0),\"\"),\"\")"}, "J222": {"content": "=iferror(if(and(H222<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H222,D:E,2,0),\"\"),\"\")"}, "J223": {"content": "=iferror(if(and(H223<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H223,D:E,2,0),\"\"),\"\")"}, "J224": {"content": "=iferror(if(and(H224<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H224,D:E,2,0),\"\"),\"\")"}, "J225": {"content": "=iferror(if(and(H225<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H225,D:E,2,0),\"\"),\"\")"}, "J226": {"content": "=iferror(if(and(H226<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H226,D:E,2,0),\"\"),\"\")"}, "J227": {"content": "=iferror(if(and(H227<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H227,D:E,2,0),\"\"),\"\")"}, "J228": {"content": "=iferror(if(and(H228<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H228,D:E,2,0),\"\"),\"\")"}, "J229": {"content": "=iferror(if(and(H229<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H229,D:E,2,0),\"\"),\"\")"}, "J230": {"content": "=iferror(if(and(H230<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H230,D:E,2,0),\"\"),\"\")"}, "J231": {"content": "=iferror(if(and(H231<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H231,D:E,2,0),\"\"),\"\")"}, "J232": {"content": "=iferror(if(and(H232<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H232,D:E,2,0),\"\"),\"\")"}, "J233": {"content": "=iferror(if(and(H233<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H233,D:E,2,0),\"\"),\"\")"}, "J234": {"content": "=iferror(if(and(H234<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H234,D:E,2,0),\"\"),\"\")"}, "J235": {"content": "=iferror(if(and(H235<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H235,D:E,2,0),\"\"),\"\")"}, "J236": {"content": "=iferror(if(and(H236<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H236,D:E,2,0),\"\"),\"\")"}, "J237": {"content": "=iferror(if(and(H237<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H237,D:E,2,0),\"\"),\"\")"}, "J238": {"content": "=iferror(if(and(H238<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H238,D:E,2,0),\"\"),\"\")"}, "J239": {"content": "=iferror(if(and(H239<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H239,D:E,2,0),\"\"),\"\")"}, "J240": {"content": "=iferror(if(and(H240<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H240,D:E,2,0),\"\"),\"\")"}, "J241": {"content": "=iferror(if(and(H241<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H241,D:E,2,0),\"\"),\"\")"}, "J242": {"content": "=iferror(if(and(H242<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H242,D:E,2,0),\"\"),\"\")"}, "J243": {"content": "=iferror(if(and(H243<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H243,D:E,2,0),\"\"),\"\")"}, "J244": {"content": "=iferror(if(and(H244<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H244,D:E,2,0),\"\"),\"\")"}, "J245": {"content": "=iferror(if(and(H245<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H245,D:E,2,0),\"\"),\"\")"}, "J246": {"content": "=iferror(if(and(H246<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H246,D:E,2,0),\"\"),\"\")"}, "J247": {"content": "=iferror(if(and(H247<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H247,D:E,2,0),\"\"),\"\")"}, "J248": {"content": "=iferror(if(and(H248<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H248,D:E,2,0),\"\"),\"\")"}, "J249": {"content": "=iferror(if(and(H249<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H249,D:E,2,0),\"\"),\"\")"}, "J250": {"content": "=iferror(if(and(H250<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H250,D:E,2,0),\"\"),\"\")"}, "J251": {"content": "=iferror(if(and(H251<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H251,D:E,2,0),\"\"),\"\")"}, "J252": {"content": "=iferror(if(and(H252<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H252,D:E,2,0),\"\"),\"\")"}, "J253": {"content": "=iferror(if(and(H253<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H253,D:E,2,0),\"\"),\"\")"}, "J254": {"content": "=iferror(if(and(H254<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H254,D:E,2,0),\"\"),\"\")"}, "J255": {"content": "=iferror(if(and(H255<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H255,D:E,2,0),\"\"),\"\")"}, "J256": {"content": "=iferror(if(and(H256<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H256,D:E,2,0),\"\"),\"\")"}, "J257": {"content": "=iferror(if(and(H257<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H257,D:E,2,0),\"\"),\"\")"}, "J258": {"content": "=iferror(if(and(H258<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H258,D:E,2,0),\"\"),\"\")"}, "J259": {"content": "=iferror(if(and(H259<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H259,D:E,2,0),\"\"),\"\")"}, "J260": {"content": "=iferror(if(and(H260<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H260,D:E,2,0),\"\"),\"\")"}, "J261": {"content": "=iferror(if(and(H261<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H261,D:E,2,0),\"\"),\"\")"}, "J262": {"content": "=iferror(if(and(H262<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H262,D:E,2,0),\"\"),\"\")"}, "J263": {"content": "=iferror(if(and(H263<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H263,D:E,2,0),\"\"),\"\")"}, "J264": {"content": "=iferror(if(and(H264<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H264,D:E,2,0),\"\"),\"\")"}, "J265": {"content": "=iferror(if(and(H265<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H265,D:E,2,0),\"\"),\"\")"}, "J266": {"content": "=iferror(if(and(H266<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H266,D:E,2,0),\"\"),\"\")"}, "J267": {"content": "=iferror(if(and(H267<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H267,D:E,2,0),\"\"),\"\")"}, "J268": {"content": "=iferror(if(and(H268<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H268,D:E,2,0),\"\"),\"\")"}, "J269": {"content": "=iferror(if(and(H269<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H269,D:E,2,0),\"\"),\"\")"}, "J270": {"content": "=iferror(if(and(H270<>\"\",ODOO.FILTER.VALUE(\"Period\")<>\"\"),VLOOKUP(H270,D:E,2,0),\"\"),\"\")"}}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": true}}, {"range": "D1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": true}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "3664431f-492f-45c3-be3e-42a1f1c1163c", "name": "Scrap", "colNumber": 22, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 243}, "1": {"size": 149}, "2": {"size": 26}, "3": {"size": 425}, "4": {"size": 149}, "5": {"size": 83}, "6": {"size": 240}, "7": {"size": 48}}, "merges": [], "cells": {"A1": {"content": "=PIVOT(36)"}, "D1": {"content": "=PIVOT(37)"}, "G1": {"content": "=_t(\"Categories\")"}, "G2": {"content": "=iferror(UNIQUE(VSTACK(FILTER(A2:A,A2:A<>\"Total\",A2:A<>\"\"),FILTER(D2:D,D2:D<>\"Total\",D2:D<>\"\"))),\"\")"}, "H1": {"content": "=_t(\"Current period\")"}, "H2": {"content": "=iferror(if(G2<>\"\",VLOOKUP(G2,A:B,2,0),\"\"),\"\")"}, "H3": {"content": "=iferror(if(G3<>\"\",VLOOKUP(G3,A:B,2,0),\"\"),\"\")"}, "H4": {"content": "=iferror(if(G4<>\"\",VLOOKUP(G4,A:B,2,0),\"\"),\"\")"}, "H5": {"content": "=iferror(if(G5<>\"\",VLOOKUP(G5,A:B,2,0),\"\"),\"\")"}, "H6": {"content": "=iferror(if(G6<>\"\",VLOOKUP(G6,A:B,2,0),\"\"),\"\")"}, "H7": {"content": "=iferror(if(G7<>\"\",VLOOKUP(G7,A:B,2,0),\"\"),\"\")"}, "H8": {"content": "=iferror(if(G8<>\"\",VLOOKUP(G8,A:B,2,0),\"\"),\"\")"}, "H9": {"content": "=iferror(if(G9<>\"\",VLOOKUP(G9,A:B,2,0),\"\"),\"\")"}, "H10": {"content": "=iferror(if(G10<>\"\",VLOOKUP(G10,A:B,2,0),\"\"),\"\")"}, "H11": {"content": "=iferror(if(G11<>\"\",VLOOKUP(G11,A:B,2,0),\"\"),\"\")"}, "H12": {"content": "=iferror(if(G12<>\"\",VLOOKUP(G12,A:B,2,0),\"\"),\"\")"}, "H13": {"content": "=iferror(if(G13<>\"\",VLOOKUP(G13,A:B,2,0),\"\"),\"\")"}, "H14": {"content": "=iferror(if(G14<>\"\",VLOOKUP(G14,A:B,2,0),\"\"),\"\")"}, "H15": {"content": "=iferror(if(G15<>\"\",VLOOKUP(G15,A:B,2,0),\"\"),\"\")"}, "H16": {"content": "=iferror(if(G16<>\"\",VLOOKUP(G16,A:B,2,0),\"\"),\"\")"}, "H17": {"content": "=iferror(if(G17<>\"\",VLOOKUP(G17,A:B,2,0),\"\"),\"\")"}, "H18": {"content": "=iferror(if(G18<>\"\",VLOOKUP(G18,A:B,2,0),\"\"),\"\")"}, "H19": {"content": "=iferror(if(G19<>\"\",VLOOKUP(G19,A:B,2,0),\"\"),\"\")"}, "H20": {"content": "=iferror(if(G20<>\"\",VLOOKUP(G20,A:B,2,0),\"\"),\"\")"}, "H21": {"content": "=iferror(if(G21<>\"\",VLOOKUP(G21,A:B,2,0),\"\"),\"\")"}, "H22": {"content": "=iferror(if(G22<>\"\",VLOOKUP(G22,A:B,2,0),\"\"),\"\")"}, "H23": {"content": "=iferror(if(G23<>\"\",VLOOKUP(G23,A:B,2,0),\"\"),\"\")"}, "H24": {"content": "=iferror(if(G24<>\"\",VLOOKUP(G24,A:B,2,0),\"\"),\"\")"}, "H25": {"content": "=iferror(if(G25<>\"\",VLOOKUP(G25,A:B,2,0),\"\"),\"\")"}, "H26": {"content": "=iferror(if(G26<>\"\",VLOOKUP(G26,A:B,2,0),\"\"),\"\")"}, "H27": {"content": "=iferror(if(G27<>\"\",VLOOKUP(G27,A:B,2,0),\"\"),\"\")"}, "H28": {"content": "=iferror(if(G28<>\"\",VLOOKUP(G28,A:B,2,0),\"\"),\"\")"}, "H29": {"content": "=iferror(if(G29<>\"\",VLOOKUP(G29,A:B,2,0),\"\"),\"\")"}, "H30": {"content": "=iferror(if(G30<>\"\",VLOOKUP(G30,A:B,2,0),\"\"),\"\")"}, "H31": {"content": "=iferror(if(G31<>\"\",VLOOKUP(G31,A:B,2,0),\"\"),\"\")"}, "H32": {"content": "=iferror(if(G32<>\"\",VLOOKUP(G32,A:B,2,0),\"\"),\"\")"}, "H33": {"content": "=iferror(if(G33<>\"\",VLOOKUP(G33,A:B,2,0),\"\"),\"\")"}, "H34": {"content": "=iferror(if(G34<>\"\",VLOOKUP(G34,A:B,2,0),\"\"),\"\")"}, "H35": {"content": "=iferror(if(G35<>\"\",VLOOKUP(G35,A:B,2,0),\"\"),\"\")"}, "H36": {"content": "=iferror(if(G36<>\"\",VLOOKUP(G36,A:B,2,0),\"\"),\"\")"}, "H37": {"content": "=iferror(if(G37<>\"\",VLOOKUP(G37,A:B,2,0),\"\"),\"\")"}, "H38": {"content": "=iferror(if(G38<>\"\",VLOOKUP(G38,A:B,2,0),\"\"),\"\")"}, "H39": {"content": "=iferror(if(G39<>\"\",VLOOKUP(G39,A:B,2,0),\"\"),\"\")"}, "H40": {"content": "=iferror(if(G40<>\"\",VLOOKUP(G40,A:B,2,0),\"\"),\"\")"}, "H41": {"content": "=iferror(if(G41<>\"\",VLOOKUP(G41,A:B,2,0),\"\"),\"\")"}, "H42": {"content": "=iferror(if(G42<>\"\",VLOOKUP(G42,A:B,2,0),\"\"),\"\")"}, "H43": {"content": "=iferror(if(G43<>\"\",VLOOKUP(G43,A:B,2,0),\"\"),\"\")"}, "H44": {"content": "=iferror(if(G44<>\"\",VLOOKUP(G44,A:B,2,0),\"\"),\"\")"}, "H45": {"content": "=iferror(if(G45<>\"\",VLOOKUP(G45,A:B,2,0),\"\"),\"\")"}, "H46": {"content": "=iferror(if(G46<>\"\",VLOOKUP(G46,A:B,2,0),\"\"),\"\")"}, "H47": {"content": "=iferror(if(G47<>\"\",VLOOKUP(G47,A:B,2,0),\"\"),\"\")"}, "H48": {"content": "=iferror(if(G48<>\"\",VLOOKUP(G48,A:B,2,0),\"\"),\"\")"}, "H49": {"content": "=iferror(if(G49<>\"\",VLOOKUP(G49,A:B,2,0),\"\"),\"\")"}, "H50": {"content": "=iferror(if(G50<>\"\",VLOOKUP(G50,A:B,2,0),\"\"),\"\")"}, "H51": {"content": "=iferror(if(G51<>\"\",VLOOKUP(G51,A:B,2,0),\"\"),\"\")"}, "H52": {"content": "=iferror(if(G52<>\"\",VLOOKUP(G52,A:B,2,0),\"\"),\"\")"}, "H53": {"content": "=iferror(if(G53<>\"\",VLOOKUP(G53,A:B,2,0),\"\"),\"\")"}, "H54": {"content": "=iferror(if(G54<>\"\",VLOOKUP(G54,A:B,2,0),\"\"),\"\")"}, "H55": {"content": "=iferror(if(G55<>\"\",VLOOKUP(G55,A:B,2,0),\"\"),\"\")"}, "H56": {"content": "=iferror(if(G56<>\"\",VLOOKUP(G56,A:B,2,0),\"\"),\"\")"}, "H57": {"content": "=iferror(if(G57<>\"\",VLOOKUP(G57,A:B,2,0),\"\"),\"\")"}, "H58": {"content": "=iferror(if(G58<>\"\",VLOOKUP(G58,A:B,2,0),\"\"),\"\")"}, "H59": {"content": "=iferror(if(G59<>\"\",VLOOKUP(G59,A:B,2,0),\"\"),\"\")"}, "H60": {"content": "=iferror(if(G60<>\"\",VLOOKUP(G60,A:B,2,0),\"\"),\"\")"}, "H61": {"content": "=iferror(if(G61<>\"\",VLOOKUP(G61,A:B,2,0),\"\"),\"\")"}, "H62": {"content": "=iferror(if(G62<>\"\",VLOOKUP(G62,A:B,2,0),\"\"),\"\")"}, "H63": {"content": "=iferror(if(G63<>\"\",VLOOKUP(G63,A:B,2,0),\"\"),\"\")"}, "H64": {"content": "=iferror(if(G64<>\"\",VLOOKUP(G64,A:B,2,0),\"\"),\"\")"}, "H65": {"content": "=iferror(if(G65<>\"\",VLOOKUP(G65,A:B,2,0),\"\"),\"\")"}, "H66": {"content": "=iferror(if(G66<>\"\",VLOOKUP(G66,A:B,2,0),\"\"),\"\")"}, "H67": {"content": "=iferror(if(G67<>\"\",VLOOKUP(G67,A:B,2,0),\"\"),\"\")"}, "H68": {"content": "=iferror(if(G68<>\"\",VLOOKUP(G68,A:B,2,0),\"\"),\"\")"}, "H69": {"content": "=iferror(if(G69<>\"\",VLOOKUP(G69,A:B,2,0),\"\"),\"\")"}, "H70": {"content": "=iferror(if(G70<>\"\",VLOOKUP(G70,A:B,2,0),\"\"),\"\")"}, "H71": {"content": "=iferror(if(G71<>\"\",VLOOKUP(G71,A:B,2,0),\"\"),\"\")"}, "H72": {"content": "=iferror(if(G72<>\"\",VLOOKUP(G72,A:B,2,0),\"\"),\"\")"}, "H73": {"content": "=iferror(if(G73<>\"\",VLOOKUP(G73,A:B,2,0),\"\"),\"\")"}, "H74": {"content": "=iferror(if(G74<>\"\",VLOOKUP(G74,A:B,2,0),\"\"),\"\")"}, "H75": {"content": "=iferror(if(G75<>\"\",VLOOKUP(G75,A:B,2,0),\"\"),\"\")"}, "H76": {"content": "=iferror(if(G76<>\"\",VLOOKUP(G76,A:B,2,0),\"\"),\"\")"}, "H77": {"content": "=iferror(if(G77<>\"\",VLOOKUP(G77,A:B,2,0),\"\"),\"\")"}, "H78": {"content": "=iferror(if(G78<>\"\",VLOOKUP(G78,A:B,2,0),\"\"),\"\")"}, "H79": {"content": "=iferror(if(G79<>\"\",VLOOKUP(G79,A:B,2,0),\"\"),\"\")"}, "H80": {"content": "=iferror(if(G80<>\"\",VLOOKUP(G80,A:B,2,0),\"\"),\"\")"}, "H81": {"content": "=iferror(if(G81<>\"\",VLOOKUP(G81,A:B,2,0),\"\"),\"\")"}, "H82": {"content": "=iferror(if(G82<>\"\",VLOOKUP(G82,A:B,2,0),\"\"),\"\")"}, "H83": {"content": "=iferror(if(G83<>\"\",VLOOKUP(G83,A:B,2,0),\"\"),\"\")"}, "H84": {"content": "=iferror(if(G84<>\"\",VLOOKUP(G84,A:B,2,0),\"\"),\"\")"}, "H85": {"content": "=iferror(if(G85<>\"\",VLOOKUP(G85,A:B,2,0),\"\"),\"\")"}, "H86": {"content": "=iferror(if(G86<>\"\",VLOOKUP(G86,A:B,2,0),\"\"),\"\")"}, "H87": {"content": "=iferror(if(G87<>\"\",VLOOKUP(G87,A:B,2,0),\"\"),\"\")"}, "H88": {"content": "=iferror(if(G88<>\"\",VLOOKUP(G88,A:B,2,0),\"\"),\"\")"}, "H89": {"content": "=iferror(if(G89<>\"\",VLOOKUP(G89,A:B,2,0),\"\"),\"\")"}, "H90": {"content": "=iferror(if(G90<>\"\",VLOOKUP(G90,A:B,2,0),\"\"),\"\")"}, "H91": {"content": "=iferror(if(G91<>\"\",VLOOKUP(G91,A:B,2,0),\"\"),\"\")"}, "H92": {"content": "=iferror(if(G92<>\"\",VLOOKUP(G92,A:B,2,0),\"\"),\"\")"}, "H93": {"content": "=iferror(if(G93<>\"\",VLOOKUP(G93,A:B,2,0),\"\"),\"\")"}, "H94": {"content": "=iferror(if(G94<>\"\",VLOOKUP(G94,A:B,2,0),\"\"),\"\")"}, "H95": {"content": "=iferror(if(G95<>\"\",VLOOKUP(G95,A:B,2,0),\"\"),\"\")"}, "H96": {"content": "=iferror(if(G96<>\"\",VLOOKUP(G96,A:B,2,0),\"\"),\"\")"}, "H97": {"content": "=iferror(if(G97<>\"\",VLOOKUP(G97,A:B,2,0),\"\"),\"\")"}, "H98": {"content": "=iferror(if(G98<>\"\",VLOOKUP(G98,A:B,2,0),\"\"),\"\")"}, "H99": {"content": "=iferror(if(G99<>\"\",VLOOKUP(G99,A:B,2,0),\"\"),\"\")"}, "H100": {"content": "=iferror(if(G100<>\"\",VLOOKUP(G100,A:B,2,0),\"\"),\"\")"}, "I1": {"content": "=_t(\"Previous period\")"}, "I2": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G2<>\"\",VLOOKUP(G2,D:E,2,0),\"\"),\"\"))"}, "I3": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G3<>\"\",VLOOKUP(G3,D:E,2,0),\"\"),\"\"))"}, "I4": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G4<>\"\",VLOOKUP(G4,D:E,2,0),\"\"),\"\"))"}, "I5": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G5<>\"\",VLOOKUP(G5,D:E,2,0),\"\"),\"\"))"}, "I6": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G6<>\"\",VLOOKUP(G6,D:E,2,0),\"\"),\"\"))"}, "I7": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G7<>\"\",VLOOKUP(G7,D:E,2,0),\"\"),\"\"))"}, "I8": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G8<>\"\",VLOOKUP(G8,D:E,2,0),\"\"),\"\"))"}, "I9": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G9<>\"\",VLOOKUP(G9,D:E,2,0),\"\"),\"\"))"}, "I10": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G10<>\"\",VLOOKUP(G10,D:E,2,0),\"\"),\"\"))"}, "I11": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G11<>\"\",VLOOKUP(G11,D:E,2,0),\"\"),\"\"))"}, "I12": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G12<>\"\",VLOOKUP(G12,D:E,2,0),\"\"),\"\"))"}, "I13": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G13<>\"\",VLOOKUP(G13,D:E,2,0),\"\"),\"\"))"}, "I14": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G14<>\"\",VLOOKUP(G14,D:E,2,0),\"\"),\"\"))"}, "I15": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G15<>\"\",VLOOKUP(G15,D:E,2,0),\"\"),\"\"))"}, "I16": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G16<>\"\",VLOOKUP(G16,D:E,2,0),\"\"),\"\"))"}, "I17": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G17<>\"\",VLOOKUP(G17,D:E,2,0),\"\"),\"\"))"}, "I18": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G18<>\"\",VLOOKUP(G18,D:E,2,0),\"\"),\"\"))"}, "I19": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G19<>\"\",VLOOKUP(G19,D:E,2,0),\"\"),\"\"))"}, "I20": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G20<>\"\",VLOOKUP(G20,D:E,2,0),\"\"),\"\"))"}, "I21": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G21<>\"\",VLOOKUP(G21,D:E,2,0),\"\"),\"\"))"}, "I22": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G22<>\"\",VLOOKUP(G22,D:E,2,0),\"\"),\"\"))"}, "I23": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G23<>\"\",VLOOKUP(G23,D:E,2,0),\"\"),\"\"))"}, "I24": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G24<>\"\",VLOOKUP(G24,D:E,2,0),\"\"),\"\"))"}, "I25": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G25<>\"\",VLOOKUP(G25,D:E,2,0),\"\"),\"\"))"}, "I26": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G26<>\"\",VLOOKUP(G26,D:E,2,0),\"\"),\"\"))"}, "I27": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G27<>\"\",VLOOKUP(G27,D:E,2,0),\"\"),\"\"))"}, "I28": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G28<>\"\",VLOOKUP(G28,D:E,2,0),\"\"),\"\"))"}, "I29": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G29<>\"\",VLOOKUP(G29,D:E,2,0),\"\"),\"\"))"}, "I30": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G30<>\"\",VLOOKUP(G30,D:E,2,0),\"\"),\"\"))"}, "I31": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G31<>\"\",VLOOKUP(G31,D:E,2,0),\"\"),\"\"))"}, "I32": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G32<>\"\",VLOOKUP(G32,D:E,2,0),\"\"),\"\"))"}, "I33": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G33<>\"\",VLOOKUP(G33,D:E,2,0),\"\"),\"\"))"}, "I34": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G34<>\"\",VLOOKUP(G34,D:E,2,0),\"\"),\"\"))"}, "I35": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G35<>\"\",VLOOKUP(G35,D:E,2,0),\"\"),\"\"))"}, "I36": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G36<>\"\",VLOOKUP(G36,D:E,2,0),\"\"),\"\"))"}, "I37": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G37<>\"\",VLOOKUP(G37,D:E,2,0),\"\"),\"\"))"}, "I38": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G38<>\"\",VLOOKUP(G38,D:E,2,0),\"\"),\"\"))"}, "I39": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G39<>\"\",VLOOKUP(G39,D:E,2,0),\"\"),\"\"))"}, "I40": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G40<>\"\",VLOOKUP(G40,D:E,2,0),\"\"),\"\"))"}, "I41": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G41<>\"\",VLOOKUP(G41,D:E,2,0),\"\"),\"\"))"}, "I42": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G42<>\"\",VLOOKUP(G42,D:E,2,0),\"\"),\"\"))"}, "I43": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G43<>\"\",VLOOKUP(G43,D:E,2,0),\"\"),\"\"))"}, "I44": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G44<>\"\",VLOOKUP(G44,D:E,2,0),\"\"),\"\"))"}, "I45": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G45<>\"\",VLOOKUP(G45,D:E,2,0),\"\"),\"\"))"}, "I46": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G46<>\"\",VLOOKUP(G46,D:E,2,0),\"\"),\"\"))"}, "I47": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G47<>\"\",VLOOKUP(G47,D:E,2,0),\"\"),\"\"))"}, "I48": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G48<>\"\",VLOOKUP(G48,D:E,2,0),\"\"),\"\"))"}, "I49": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G49<>\"\",VLOOKUP(G49,D:E,2,0),\"\"),\"\"))"}, "I50": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G50<>\"\",VLOOKUP(G50,D:E,2,0),\"\"),\"\"))"}, "I51": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G51<>\"\",VLOOKUP(G51,D:E,2,0),\"\"),\"\"))"}, "I52": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G52<>\"\",VLOOKUP(G52,D:E,2,0),\"\"),\"\"))"}, "I53": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G53<>\"\",VLOOKUP(G53,D:E,2,0),\"\"),\"\"))"}, "I54": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G54<>\"\",VLOOKUP(G54,D:E,2,0),\"\"),\"\"))"}, "I55": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G55<>\"\",VLOOKUP(G55,D:E,2,0),\"\"),\"\"))"}, "I56": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G56<>\"\",VLOOKUP(G56,D:E,2,0),\"\"),\"\"))"}, "I57": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G57<>\"\",VLOOKUP(G57,D:E,2,0),\"\"),\"\"))"}, "I58": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G58<>\"\",VLOOKUP(G58,D:E,2,0),\"\"),\"\"))"}, "I59": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G59<>\"\",VLOOKUP(G59,D:E,2,0),\"\"),\"\"))"}, "I60": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G60<>\"\",VLOOKUP(G60,D:E,2,0),\"\"),\"\"))"}, "I61": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G61<>\"\",VLOOKUP(G61,D:E,2,0),\"\"),\"\"))"}, "I62": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G62<>\"\",VLOOKUP(G62,D:E,2,0),\"\"),\"\"))"}, "I63": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G63<>\"\",VLOOKUP(G63,D:E,2,0),\"\"),\"\"))"}, "I64": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G64<>\"\",VLOOKUP(G64,D:E,2,0),\"\"),\"\"))"}, "I65": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G65<>\"\",VLOOKUP(G65,D:E,2,0),\"\"),\"\"))"}, "I66": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G66<>\"\",VLOOKUP(G66,D:E,2,0),\"\"),\"\"))"}, "I67": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G67<>\"\",VLOOKUP(G67,D:E,2,0),\"\"),\"\"))"}, "I68": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G68<>\"\",VLOOKUP(G68,D:E,2,0),\"\"),\"\"))"}, "I69": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G69<>\"\",VLOOKUP(G69,D:E,2,0),\"\"),\"\"))"}, "I70": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G70<>\"\",VLOOKUP(G70,D:E,2,0),\"\"),\"\"))"}, "I71": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G71<>\"\",VLOOKUP(G71,D:E,2,0),\"\"),\"\"))"}, "I72": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G72<>\"\",VLOOKUP(G72,D:E,2,0),\"\"),\"\"))"}, "I73": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G73<>\"\",VLOOKUP(G73,D:E,2,0),\"\"),\"\"))"}, "I74": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G74<>\"\",VLOOKUP(G74,D:E,2,0),\"\"),\"\"))"}, "I75": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G75<>\"\",VLOOKUP(G75,D:E,2,0),\"\"),\"\"))"}, "I76": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G76<>\"\",VLOOKUP(G76,D:E,2,0),\"\"),\"\"))"}, "I77": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G77<>\"\",VLOOKUP(G77,D:E,2,0),\"\"),\"\"))"}, "I78": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G78<>\"\",VLOOKUP(G78,D:E,2,0),\"\"),\"\"))"}, "I79": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G79<>\"\",VLOOKUP(G79,D:E,2,0),\"\"),\"\"))"}, "I80": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G80<>\"\",VLOOKUP(G80,D:E,2,0),\"\"),\"\"))"}, "I81": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G81<>\"\",VLOOKUP(G81,D:E,2,0),\"\"),\"\"))"}, "I82": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G82<>\"\",VLOOKUP(G82,D:E,2,0),\"\"),\"\"))"}, "I83": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G83<>\"\",VLOOKUP(G83,D:E,2,0),\"\"),\"\"))"}, "I84": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G84<>\"\",VLOOKUP(G84,D:E,2,0),\"\"),\"\"))"}, "I85": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G85<>\"\",VLOOKUP(G85,D:E,2,0),\"\"),\"\"))"}, "I86": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G86<>\"\",VLOOKUP(G86,D:E,2,0),\"\"),\"\"))"}, "I87": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G87<>\"\",VLOOKUP(G87,D:E,2,0),\"\"),\"\"))"}, "I88": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G88<>\"\",VLOOKUP(G88,D:E,2,0),\"\"),\"\"))"}, "I89": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G89<>\"\",VLOOKUP(G89,D:E,2,0),\"\"),\"\"))"}, "I90": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G90<>\"\",VLOOKUP(G90,D:E,2,0),\"\"),\"\"))"}, "I91": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G91<>\"\",VLOOKUP(G91,D:E,2,0),\"\"),\"\"))"}, "I92": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G92<>\"\",VLOOKUP(G92,D:E,2,0),\"\"),\"\"))"}, "I93": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G93<>\"\",VLOOKUP(G93,D:E,2,0),\"\"),\"\"))"}, "I94": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G94<>\"\",VLOOKUP(G94,D:E,2,0),\"\"),\"\"))"}, "I95": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G95<>\"\",VLOOKUP(G95,D:E,2,0),\"\"),\"\"))"}, "I96": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G96<>\"\",VLOOKUP(G96,D:E,2,0),\"\"),\"\"))"}, "I97": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G97<>\"\",VLOOKUP(G97,D:E,2,0),\"\"),\"\"))"}, "I98": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G98<>\"\",VLOOKUP(G98,D:E,2,0),\"\"),\"\"))"}, "I99": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G99<>\"\",VLOOKUP(G99,D:E,2,0),\"\"),\"\"))"}, "I100": {"content": "=iferror(if(and(ODOO.FILTER.VALUE(\"Period\")<>\"\",G100<>\"\",VLOOKUP(G100,D:E,2,0),\"\"),\"\"))"}}, "styles": {}, "formats": {}, "borders": {}, "conditionalFormats": [], "figures": [], "tables": [{"range": "A1", "type": "dynamic", "config": {"hasFilters": false, "totalRow": false, "firstColumn": true, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "styleId": "TableStyleMedium5", "automaticAutofill": true}}, {"range": "D1", "type": "dynamic", "config": {"hasFilters": true, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "TableStyleMedium5"}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}], "styles": {"1": {"bold": true, "fontSize": 16, "textColor": "#01636B"}, "2": {"bold": true}, "3": {"italic": true}, "4": {"align": "center"}, "5": {"align": "center", "wrapping": "wrap"}, "6": {"bold": true, "align": "center"}}, "formats": {"1": "0.0%", "2": "#,##0.00", "3": "0.00%"}, "borders": {"1": {"bottom": {"style": "thin", "color": "#CCCCCC"}}, "2": {"top": {"style": "thin", "color": "#CCCCCC"}}, "3": {"right": {"style": "thin", "color": "#2D7E84"}}, "4": {"bottom": {"color": "#B498AE", "style": "thin"}}, "5": {"top": {"color": "#B498AE", "style": "thin"}, "bottom": {"style": "thin", "color": "#2D7E84"}}, "6": {"top": {"style": "thin", "color": "#2D7E84"}, "bottom": {"style": "thin", "color": "#2D7E84"}}, "7": {"top": {"style": "thin", "color": "#2D7E84"}, "bottom": {"style": "thin", "color": "#2D7E84"}, "left": {"style": "thin", "color": "#2D7E84"}}, "8": {"top": {"style": "thin", "color": "#2D7E84"}}, "9": {"top": {"color": "#B498AE", "style": "thin"}, "bottom": {"color": "#B498AE", "style": "thin"}}, "10": {"top": {"color": "#B498AE", "style": "thin"}}, "11": {"top": {"color": "#B498AE", "style": "thin"}, "bottom": {"color": "#B498AE", "style": "thin"}, "right": {"color": "#B498AE", "style": "thin"}}, "12": {"left": {"color": "#B498AE", "style": "thin"}, "right": {"color": "#B498AE", "style": "thin"}}, "13": {"left": {"color": "#B498AE", "style": "thin"}}}, "revisionId": "START_REVISION", "uniqueFigureIds": true, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {"9ecaf59e-b8b6-4849-8036-3949f5eb8ee4": {"type": "ODOO", "domain": [["state", "=", "done"]], "context": {}, "sortedColumn": null, "measures": [{"id": "cycle_time", "fieldName": "cycle_time", "aggregator": "avg"}, {"id": "delay", "fieldName": "delay", "aggregator": "avg"}, {"id": "product_qty:sum", "fieldName": "product_qty", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}], "model": "stock.report", "columns": [{"fieldName": "is_late"}], "rows": [{"fieldName": "operation_type_id"}], "name": "stock.report -  Cycles", "actionXmlId": "stock_enterprise.stock_report_action_performance", "formulaId": "1", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime", "offset": 0}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "operation_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "operation_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "categ_id", "type": "many2one"}}}, "ca610e50-1eab-4c4e-bec9-037188180b21": {"type": "ODOO", "domain": ["&", "&", ["state", "=", "done"], ["picking_code", "=", "outgoing"], ["location_dest_usage", "=", "customer"]], "context": {}, "sortedColumn": null, "measures": [{"id": "product_uom_qty", "fieldName": "product_uom_qty", "aggregator": "sum"}, {"id": "quantity", "fieldName": "quantity", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "picking_type_id"}], "name": "stock.move -  Fill rate", "actionXmlId": "stock.stock_move_action", "formulaId": "2", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": 0}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "31574ed5-1611-4785-8fa4-be3503cfe704": {"type": "ODOO", "domain": [["state", "=", "done"]], "context": {}, "sortedColumn": null, "measures": [{"id": "cycle_time", "fieldName": "cycle_time", "aggregator": "avg"}, {"id": "delay", "fieldName": "delay", "aggregator": "avg"}, {"id": "product_qty:sum", "fieldName": "product_qty", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}], "model": "stock.report", "columns": [{"fieldName": "is_late"}], "rows": [{"fieldName": "operation_type_id"}], "name": "stock.report - Cycles - Previous", "actionXmlId": "stock_enterprise.stock_report_action_performance", "formulaId": "14", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime", "offset": -1}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "operation_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "operation_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "categ_id", "type": "many2one"}}}, "cf3c336a-5275-4398-a9c3-69a38022e240": {"type": "ODOO", "domain": ["&", "&", ["state", "=", "done"], ["picking_code", "=", "outgoing"], ["location_dest_usage", "=", "customer"]], "context": {}, "sortedColumn": null, "measures": [{"id": "product_uom_qty", "fieldName": "product_uom_qty", "aggregator": "sum"}, {"id": "quantity", "fieldName": "quantity", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "picking_type_id"}], "name": "stock.move - Fill rate -  Previous", "actionXmlId": "stock.stock_move_action", "formulaId": "15", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": -1}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "d7b58456-0683-420d-bf41-72157db8d455": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], ["location_dest_usage", "=", "customer"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "product_uom_qty", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "quantity:sum", "fieldName": "quantity", "aggregator": "sum"}, {"id": "product_uom_qty:sum", "fieldName": "product_uom_qty", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.move -  by Product sort by Top Demand", "actionXmlId": "stock.stock_move_action", "formulaId": "23", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "f133549b-f406-4d69-b233-10b56c26e04e": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], ["location_dest_usage", "=", "customer"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "__count", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "quantity:sum", "fieldName": "quantity", "aggregator": "sum"}, {"id": "product_uom_qty:sum", "fieldName": "product_uom_qty", "aggregator": "sum"}, {"id": "__count", "fieldName": "__count", "format": "#,##0.00"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.move -  by Product sort by Top Count", "actionXmlId": "stock.stock_move_action", "formulaId": "24", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "e551f5e9-677d-4248-a2be-ed0556547774": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], ["location_dest_usage", "=", "customer"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "product_uom_qty", "order": "asc", "originIndexes": [0]}, "measures": [{"id": "quantity:sum", "fieldName": "quantity", "aggregator": "sum"}, {"id": "product_uom_qty:sum", "fieldName": "product_uom_qty", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.move - by Product sort by Bottom Demand", "actionXmlId": "stock.stock_move_action", "formulaId": "25", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "ac6b8779-6e3c-4400-a76e-359074313e62": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], ["location_dest_usage", "=", "customer"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "__count", "order": "asc", "originIndexes": [0]}, "measures": [{"id": "quantity:sum", "fieldName": "quantity", "aggregator": "sum"}, {"id": "__count", "fieldName": "__count"}, {"id": "product_uom_qty:sum", "fieldName": "product_uom_qty", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.move - by Product sort by Bottom Count", "actionXmlId": "stock.stock_move_action", "formulaId": "26", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "cf4aab22-9a75-4eb1-bba8-82ba91798037": {"type": "ODOO", "domain": ["&", ["picking_type_code", "!=", false], "&", ["state", "=", "done"], ["picking_type_code", "=", "outgoing"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "product_qty", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "product_qty:sum", "fieldName": "product_qty", "aggregator": "sum"}], "model": "stock.report", "columns": [{"fieldName": "is_late"}], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.report - by Is Late - sort top by quantity", "actionXmlId": "stock_enterprise.stock_report_action_performance", "formulaId": "28", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "operation_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "operation_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "categ_id", "type": "many2one"}}}, "9ed56837-b86b-472d-92ce-b39f84169364": {"type": "ODOO", "domain": ["&", ["picking_type_code", "!=", false], "&", ["state", "=", "done"], ["picking_type_code", "=", "outgoing"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "product_qty", "order": "asc", "originIndexes": [0]}, "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "product_qty:sum", "fieldName": "product_qty", "aggregator": "sum"}], "model": "stock.report", "columns": [{"fieldName": "is_late"}], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.report - by Is Late - sort bottom by quantity", "actionXmlId": "stock_enterprise.stock_report_action_performance", "formulaId": "29", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "operation_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "operation_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "categ_id", "type": "many2one"}}}, "ce5e29ee-e648-4d74-8ecd-5631ca5da59d": {"type": "ODOO", "domain": ["&", ["picking_type_code", "!=", false], "&", ["state", "=", "done"], ["picking_type_code", "=", "outgoing"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "__count", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "product_qty:sum", "fieldName": "product_qty", "aggregator": "sum"}], "model": "stock.report", "columns": [{"fieldName": "is_late"}], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE]  stock.report - by Is Late - sort top by count", "actionXmlId": "stock_enterprise.stock_report_action_performance", "formulaId": "30", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "operation_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "operation_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "categ_id", "type": "many2one"}}}, "e98925d5-eae6-42e5-822e-c933033b9594": {"type": "ODOO", "domain": ["&", ["picking_type_code", "!=", false], "&", ["state", "=", "done"], ["picking_type_code", "=", "outgoing"]], "context": {}, "sortedColumn": {"groupId": [[], []], "measure": "__count", "order": "asc", "originIndexes": [0]}, "measures": [{"id": "__count", "fieldName": "__count"}, {"id": "product_qty:sum", "fieldName": "product_qty", "aggregator": "sum"}], "model": "stock.report", "columns": [{"fieldName": "is_late"}], "rows": [{"fieldName": "product_id"}], "name": "[NO DELETE] stock.report - by Is Late - sort bottom by count", "actionXmlId": "stock_enterprise.stock_report_action_performance", "formulaId": "31", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date_done", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "operation_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "operation_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "categ_id", "type": "many2one"}}}, "811c6505-87f5-422a-b5b2-89cdf07b679a": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], "|", "|", "|", ["location_id.usage", "=", "inventory"], "&", ["location_id.scrap_location", "=", false], ["location_id.usage", "=", "inventory"], ["location_dest_id.usage", "=", "inventory"], "&", ["location_dest_id.scrap_location", "=", false], ["location_dest_id.usage", "=", "inventory"]], "context": {"create": 0}, "sortedColumn": {"groupId": [[], []], "measure": "quantity_product_uom", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "quantity_product_uom:sum", "fieldName": "quantity_product_uom", "aggregator": "sum"}], "model": "stock.move.line", "columns": [], "rows": [{"fieldName": "product_category_name"}], "name": "stock.move.line  -  by category", "actionXmlId": "stock.stock_move_line_action", "formulaId": "34", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "picking_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "07609008-3f3a-4c9d-b1f0-78dc6ffcd318": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], "|", "|", "|", ["location_id.usage", "=", "inventory"], "&", ["location_id.scrap_location", "=", false], ["location_id.usage", "=", "inventory"], ["location_dest_id.usage", "=", "inventory"], "&", ["location_dest_id.scrap_location", "=", false], ["location_dest_id.usage", "=", "inventory"]], "context": {"create": 0}, "sortedColumn": {"groupId": [[], []], "measure": "quantity_product_uom", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "quantity_product_uom:sum", "fieldName": "quantity_product_uom", "aggregator": "sum"}], "model": "stock.move.line", "columns": [], "rows": [{"fieldName": "product_category_name"}], "name": "stock.move.line  -   by category - Previous", "actionXmlId": "stock.stock_move_line_action", "formulaId": "35", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": -1}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "picking_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "5bd5b2f6-e8c3-47d3-b44e-81119332d5fc": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], "|", ["location_id.scrap_location", "=", true], ["location_dest_id.scrap_location", "=", true]], "context": {"create": 0}, "sortedColumn": {"groupId": [[], []], "measure": "quantity_product_uom", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "quantity_product_uom:sum", "fieldName": "quantity_product_uom", "aggregator": "sum"}], "model": "stock.move.line", "columns": [], "rows": [{"fieldName": "product_category_name"}], "name": "stock.move.line  -  Sent to scrap  by product category", "actionXmlId": "stock.stock_move_line_action", "formulaId": "36", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime"}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "picking_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "ffd15363-881e-42fe-b128-32143afbd873": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], "|", ["location_id.scrap_location", "=", true], ["location_dest_id.scrap_location", "=", true]], "context": {"create": 0}, "sortedColumn": {"groupId": [[], []], "measure": "quantity_product_uom", "order": "desc", "originIndexes": [0]}, "measures": [{"id": "quantity_product_uom:sum", "fieldName": "quantity_product_uom", "aggregator": "sum"}], "model": "stock.move.line", "columns": [], "rows": [{"fieldName": "product_category_name"}], "name": "stock.move.line  - Sent to scrap  by product category Previous", "actionXmlId": "stock.stock_move_line_action", "formulaId": "37", "fieldMatching": {"472f70c0-7336-48e5-a84c-9d03754250b7": {}, "ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": -1}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "picking_type_id.warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "d1b45602-1284-47ee-911a-a5bc4bfa59c0": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], ["picking_type_id", "!=", false]], "context": {}, "sortedColumn": null, "measures": [{"id": "product_uom_qty", "fieldName": "product_uom_qty", "aggregator": "sum"}, {"id": "quantity", "fieldName": "quantity", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "picking_type_id"}], "name": "stock.move -  DC Operations", "actionXmlId": "stock.stock_move_action", "formulaId": "39", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": 0}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}, "8ad5f862-4abe-4659-94ae-d51031e048e7": {"type": "ODOO", "domain": ["&", ["state", "=", "done"], ["picking_type_id", "!=", false]], "context": {}, "sortedColumn": null, "measures": [{"id": "product_uom_qty", "fieldName": "product_uom_qty", "aggregator": "sum"}, {"id": "quantity", "fieldName": "quantity", "aggregator": "sum"}, {"id": "__count:sum", "fieldName": "__count", "aggregator": "sum"}], "model": "stock.move", "columns": [], "rows": [{"fieldName": "picking_type_id"}], "name": "stock.move -  DC Operations (Comparison)", "actionXmlId": "stock.stock_move_action", "formulaId": "40", "fieldMatching": {"ad701208-d8cf-431f-97eb-dc72a229a727": {}, "4ab93c39-bd84-437b-b00f-9688b680937e": {"chain": "date", "type": "datetime", "offset": -1}, "049280a5-d8a4-4e65-a895-8bdfe313b7b2": {"chain": "product_id", "type": "many2one"}, "81e2a28d-25f4-4ebf-b550-ac5e53833b56": {"chain": "warehouse_id", "type": "many2one"}, "11efc48d-661b-4b4a-aa83-7bdee9f5f36c": {"chain": "picking_type_id", "type": "many2one"}, "472f70c0-7336-48e5-a84c-9d03754250b7": {}, "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98": {"chain": "product_id.categ_id", "type": "many2one"}}}}, "pivotNextId": 41, "customTableStyles": {}, "odooVersion": 12, "globalFilters": [{"id": "472f70c0-7336-48e5-a84c-9d03754250b7", "type": "text", "label": "Sort graph by", "defaultValue": "Demand", "rangeOfAllowedValues": "'By product overview fill rate - On time'!H6:H7"}, {"id": "ad701208-d8cf-431f-97eb-dc72a229a727", "type": "text", "label": "Top/Bottoms values", "defaultValue": "Top", "rangeOfAllowedValues": "'By product overview fill rate - On time'!I5:J5"}, {"id": "4ab93c39-bd84-437b-b00f-9688b680937e", "type": "date", "label": "Period", "defaultValue": "", "rangeType": "relative"}, {"id": "049280a5-d8a4-4e65-a895-8bdfe313b7b2", "type": "relation", "label": "Product", "defaultValue": [], "defaultValueDisplayNames": [], "modelName": "product.product", "includeChildren": false}, {"id": "f2ff9a3a-a142-4d6e-ab47-c1f4923f3c98", "type": "relation", "label": "Product Category", "defaultValue": [], "defaultValueDisplayNames": [], "modelName": "product.category", "includeChildren": true}, {"id": "81e2a28d-25f4-4ebf-b550-ac5e53833b56", "type": "relation", "label": "Warehouse", "defaultValue": [], "defaultValueDisplayNames": [], "modelName": "stock.warehouse", "includeChildren": false}, {"id": "11efc48d-661b-4b4a-aa83-7bdee9f5f36c", "type": "relation", "label": "Transfer", "defaultValue": [], "defaultValueDisplayNames": [], "modelName": "stock.picking.type", "includeChildren": false}], "lists": {}, "listNextId": 5, "chartOdooMenusReferences": {"92ad86c5-b988-44b1-b5b3-17b6580743a6": "stock.stock_move_menu", "106df83f-9731-4842-87d9-a8c7b848399e": "stock.stock_move_menu", "3832422c-37b7-4b17-92bc-e69219ebb918": "stock.stock_move_menu", "757ff23d-2733-4dd7-98c2-3df3dc5ff6ea": "stock_enterprise.stock_dashboard_menuitem", "f854691a-ed0a-4908-bd7b-3215c44f670a": "stock.menu_stock_scrap", "87ab87e6-2bdd-4e75-a59f-a0c34f6742fb": "stock.menu_action_inventory_tree", "bc286f72-8794-4655-b78e-b3811c2a15c5": "stock_enterprise.stock_dashboard_menuitem", "d993b670-2faa-49b2-ba06-5dc136d05e88": "stock.stock_move_menu", "b6792369-556a-4568-aac3-89524cde3ed0": "stock_enterprise.stock_dashboard_menuitem", "00d06fd4-5cb9-443d-80b1-e8b6bf6c05a1": "stock_enterprise.stock_dashboard_menuitem"}}