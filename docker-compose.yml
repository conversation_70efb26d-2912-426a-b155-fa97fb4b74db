version: '3.3'

services:
  web:
    image: odoo:18.0
    depends_on:
        - db

    restart: "no"

    ports:
      - 8069:8069

    volumes:
      - ./config:/etc/odoo
      - ./addons:/mnt/extra-addons
      - odoo:/var/lib/odoo

    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo

  db:
    image: postgres:14
    restart: "no"

    volumes:
      - postgresql:/var/lib/postgresql/data

    # defined in the web container environment variables.
    environment:
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_USER=odoo
      - POSTGRES_DB=postgres  # Leave this set to postgres

  nginx:
    image: nginx
    restart: "no"
    volumes:
        - ./nginx:/etc/nginx/conf.d
    ports:
        - 8000:80
    environment:
        - NGINX_PORT=80
    depends_on:
        - web
        - pgadmin

  pgadmin:
    image: dpage/pgadmin4
    restart: "no"
    depends_on:
        - db
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-admin}
    volumes:
      - pgadmin:/root/.pgadmin
    # ports:
    #   - "${PGADMIN_PORT:-5050}:80"

volumes:
    pgadmin:
    postgresql:
    odoo: