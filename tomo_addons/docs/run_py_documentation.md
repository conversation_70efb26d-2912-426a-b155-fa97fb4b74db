# TomoEngine run.py 代码文档

## 概述

`run.py` 是 TomoEngine 项目的核心执行脚本，专门用于**单投影器光固化3D打印系统**的优化和渲染。与 `perfi_basic_dual.py` 的双投影器系统不同，这个脚本专注于单投影器的高精度优化算法。

## 主要功能

- **STL/OBJ 模型处理**：自动转换和体素化
- **迭代优化算法**：实现 void_diff 和 gel_diff 优化
- **正向/反向投影**：支持多种渲染模式
- **实时可视化**：生成投影图像和体积重建结果

## 代码结构分析

### 1. 配置参数 (第5-16行)

```python
model_path = r"E:\Programming in progress\geometries"
obj_name = "_200L"                                    # 处理的模型名称
render_model = "orthographic_const_box_texArray"      # 渲染算法
projector_type = 'single_color'                      # 单色投影器
mode = "offline"                                      # 离线模式
```

**关键参数说明**：
- `render_model`: 使用纹理数组的正交投影算法
- `projector_type`: 单色投影器配置
- `optimize = True`: 启用优化算法
- `voxelize = True`: 启用体素化处理

### 2. 电机和投影参数 (第34-45行)

```python
fps = 30                           # 投影帧率 (比dual版本的60fps慢)
render_frames = 360                # 总帧数 (比dual版本的720帧少)
optim_frames = 90                  # 优化帧数
iterations = 50                    # 优化迭代次数 (比dual版本多)
rev_time = render_frames / fps     # 旋转时间 = 12秒
DeviceMaxVelocity = 360/rev_time   # 旋转速度 = 30°/秒
```

**性能对比**：
- **帧率**: 30fps (vs dual的60fps) - 更注重质量而非速度
- **总帧数**: 360帧 (vs dual的720帧) - 1°/帧的角度分辨率
- **优化迭代**: 50次 (vs dual的10次) - 更深度的优化

### 3. DMD投影器配置 (第48-70行)

```python
# 主投影器
dmd1 = DMD(
    focus_plane=170.85,
    offset_x=0,                    # 无偏移 (居中)
    pixelSize_fp=0.0463,
    aspect_ratio=16/10,            # 16:10宽屏比例
    image_width=1920,              # 1920×1200分辨率
    vfov=16.98
)

# 辅助投影器 (监控用)
dmd2 = DMD(
    focus_plane=170.85,
    vfov=14.86                     # 更小的视野角
)
```

**单投影器特点**：
- 无空间偏移，投影器居中对准
- 16:10宽屏比例，适合更大的打印区域
- 双DMD配置用于主投影和监控

### 4. 材料属性 (第65-70行)

```python
material = Material(
    mu=0.1,       # 吸收系数
    n1=1.0,       # 空气折射率
    n2=1.47,      # 容器折射率
    n3=1.47,      # 树脂折射率
    r1=38/2.,     # 外径 19mm (比dual版本的25mm小)
    r2=34/2.      # 内径 17mm (比dual版本的23.5mm小)
)
```

**容器规格**：更小的打印容器，适合精密小件制造。

### 5. 体素化处理 (第72-80行)

```python
if voxelize:
    mesh1 = MeshRenderer(
        dmd1, material, model_path, obj_name,
        voxel_factor=1,            # 1倍体素精度 (vs dual的3倍)
        method="solid"             # 实体体素化
    )
    mesh1.run()
```

**体素化策略**：使用1倍精度，平衡计算效率和质量。

## 核心优化算法 (第98-149行)

### 算法流程

这是该脚本的核心部分，实现了**迭代优化算法**：

```python
if render_model == "orthographic_const_box_texArray":
    optim_imgs = 150               # 优化图像数量
    
    # 初始前向投影
    b_tex = renderer1.forward2(volume_texture=renderer1.x_model, num_images=optim_imgs)
    b_tex = renderer1.normalize(b_tex)
    
    # 初始反向重建
    x = renderer1.backward2(b_tex, num_images=optim_imgs)
    x = renderer1.normalize(x, is_3d=True)
```

### 迭代优化循环 (30次迭代)

```python
for i in range(30):
    # 第一步：void_diff 优化 (避免空洞)
    xm = renderer1.void_diff(x, xm=renderer1.x_model)
    b_tex = renderer1.forward2(xm, num_images=optim_imgs)
    b_max, b_tex = renderer1.normalize(b_tex, save_max=True)
    x = renderer1.backward2(b_tex, num_images=optim_imgs)
    x_max, x = renderer1.normalize(x, is_3d=True, save_max=True)
    
    # 第二步：gel_diff 优化 (确保凝胶化)
    xm = renderer1.gel_diff(x, xm)
    b_tex = renderer1.forward2(xm, num_images=optim_imgs)
    b_max, b_tex = renderer1.normalize(b_tex, save_max=True)
    x = renderer1.backward2(b_tex, num_images=optim_imgs)
    x_max, x = renderer1.normalize(x, is_3d=True, save_max=True)
```

### 优化算法原理

1. **void_diff**: 减少空洞缺陷
   - 识别未充分固化的区域
   - 增加这些区域的曝光剂量

2. **gel_diff**: 确保凝胶化
   - 确保目标区域达到凝胶化阈值
   - 避免过度曝光导致的精度损失

3. **forward2/backward2**: 
   - 使用纹理数组的高效GPU计算
   - 支持大批量图像的并行处理

## 可视化输出 (第162-234行)

### 投影图像可视化

```python
# 显示优化后的投影图像
b[b<0] = 0
image = b[0]/b_max
im = ax1.imshow(image.astype(float), cmap='gray')
```

### 体积重建可视化

```python
# 显示重建的3D体积切片
x_vol[x_vol<0] = 0
image = x_vol[:, 100]/x_max
im = ax1.imshow(image.astype(float), cmap='viridis')
```

**输出内容**：
- 投影图像的灰度显示和直方图
- 3D体积的切片显示和强度分布
- 真实世界坐标系标注

## 与 perfi_basic_dual.py 的对比

| 特性 | run.py (单投影器) | perfi_basic_dual.py (双投影器) |
|------|------------------|-------------------------------|
| **投影器数量** | 1个主投影器 + 1个监控 | 2个主投影器 + 1个监控 |
| **帧率** | 30 fps | 60 fps |
| **总帧数** | 360帧 (1°/帧) | 720帧 (0.5°/帧) |
| **优化迭代** | 30次 | 通过OSMO算法 |
| **容器尺寸** | 19mm/17mm | 25mm/23.5mm |
| **应用场景** | 精密小件，高质量 | 大件快速打印 |
| **算法复杂度** | 高 (深度优化) | 中 (双投影器协调) |

## 适用场景

1. **精密医疗器械**：如牙科修复体、助听器外壳
2. **珠宝首饰**：需要极高表面质量的小件
3. **微机械零件**：精密仪器的微小组件
4. **原型验证**：需要高质量验证的设计原型

## 技术优势

1. **深度优化**：30次迭代确保最优质量
2. **纹理数组加速**：GPU并行处理提高效率
3. **双重优化策略**：void_diff + gel_diff 组合
4. **实时可视化**：即时查看优化效果

这个脚本代表了TomoEngine在**高精度单投影器系统**方面的技术实现，特别适合对质量要求极高的小批量精密制造。

