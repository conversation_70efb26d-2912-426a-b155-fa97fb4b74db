<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Module Category -->
        <record id="module_category_tomo_print" model="ir.module.category">
            <field name="name">TomoPrint</field>
            <field name="description">Manage TomoPrint CAD file processing and 3D printing workflows.</field>
            <field name="sequence">20</field>
        </record>

        <!-- TomoPrint User Group -->
        <record id="group_tomo_user" model="res.groups">
            <field name="name">TomoPrint User</field>
            <field name="category_id" ref="tomo_print.module_category_tomo_print"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Users can create and manage their own TomoPrint jobs, upload CAD files, and track processing status.</field>
        </record>

        <!-- TomoPrint Manager Group -->
        <record id="group_tomo_manager" model="res.groups">
            <field name="name">TomoPrint Manager</field>
            <field name="category_id" ref="module_category_tomo_print"/>
            <field name="implied_ids" eval="[(4, ref('group_tomo_user'))]"/>
            <field name="comment">Managers have full access to all TomoPrint features including configuration, user management, and system administration.</field>
        </record>

    </data>

    <data noupdate="1">
        
        <!-- Record Rules -->
        
        <!-- TomoPrint Job Rules -->
        <record id="tomo_print_rule_user" model="ir.rule">
            <field name="name">TomoPrint Job: User Access</field>
            <field name="model_id" ref="model_tomo_print"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_tomo_user'))]"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <record id="tomo_print_rule_manager" model="ir.rule">
            <field name="name">TomoPrint Job: Manager Access</field>
            <field name="model_id" ref="model_tomo_print"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_tomo_manager')), (4, ref('base.group_system'))]"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <!-- TomoPrint Config Rules -->
        <record id="tomo_print_config_rule_manager" model="ir.rule">
            <field name="name">TomoPrint Config: Manager Access</field>
            <field name="model_id" ref="model_tomo_print_config"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_tomo_manager')), (4, ref('base.group_system'))]"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <!-- Multi-company Rules -->
        <record id="tomo_print_company_rule" model="ir.rule">
            <field name="name">TomoPrint Job: Multi-company</field>
            <field name="model_id" ref="model_tomo_print"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <record id="tomo_print_config_company_rule" model="ir.rule">
            <field name="name">TomoPrint Config: Multi-company</field>
            <field name="model_id" ref="model_tomo_print_config"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

    </data>
</odoo>
