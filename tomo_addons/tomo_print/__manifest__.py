# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'TomoPrint - PERFI Cloud Specs',
    'version': '1.0',
    'category': 'Manufacturing',
    'sequence': 100,
    'summary': 'Web-based minimalistic GUI for CAD file processing with advanced options',
    'description': """
TomoPrint - PERFI Cloud Specs
=============================

This module provides a web-based interface for CAD file processing with the following features:

Frontend:
- Web-based minimalistic GUI
- CAD file drag-and-drop upload
- Email address input
- Advanced options configuration:
  * Reorientation methods (Vertical longest axis, Building volume fit, Cross-section area)
  * Resolution of voxelating (1-100%)
  * Number of projections (default: 365)
  * Brightness/Gamma correction (default: 0.8)

Backend Computation:
- File upload and temporary storage
- Preprocessing of geometry
- Principal component analysis
- Re-orientation according to selected method
- Confirmation email with summary and download link

Access Rights:
- TomoPrint Manager: Full access to all features and configurations
- TomoPrint User: Access to create and manage own print jobs
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'web',
        'mail',
        'base_setup',
    ],
    'data': [
        # Security
        'security/security.xml',
        'security/ir.model.access.csv',
        
        # Data
        'data/tomo_print_data.xml',
        
        # Views
        'views/tomo_print_views.xml',
        'views/tomo_print_new_views.xml',
        'views/tomo_print_config_views.xml',
        'views/res_config_settings_views.xml',
        'views/templates.xml',
        'views/menu.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'tomo_print/static/src/css/tomo_print.css',
            'tomo_print/static/src/css/tomo_print_new.css',
        ],
        'web.assets_frontend': [
            'tomo_print/static/src/css/tomo_print.css',
            'tomo_print/static/src/css/tomo_print_new.css',
            'tomo_print/static/src/js/tomo_print_frontend.js',
        ],
    },
    'demo': [],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
    'post_init_hook': 'post_init_hook',
}
