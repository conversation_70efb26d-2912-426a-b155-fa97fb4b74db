# TomoPrint - PERFI Cloud Specs

TomoPrint 是一个基于 Odoo 18 的 CAD 文件处理模块，提供 Web 界面用于上传和处理 3D 打印文件。

## 功能特性

### 前端界面
- 🖱️ Web 界面拖拽上传 CAD 文件
- 📧 邮件地址输入用于通知
- ⚙️ 高级选项配置面板
- 📊 实时处理进度显示
- 📱 响应式设计，支持移动设备

### 高级选项
- 🔄 **重新定向方法**：垂直最长轴、建筑体积适配、最大横截面积
- 🎯 **体素化分辨率**：1-100% 可调
- 📐 **投影数量**：默认 365，可自定义
- 🌟 **亮度/Gamma 校正**：默认 0.8
- ✨ **亮度补偿**：可选启用

### 后端处理
- 📁 文件上传和临时存储
- 🔍 几何预处理和分析
- 📊 主成分分析
- 🔄 智能重新定向算法
- 📧 自动邮件通知
- 💾 结果文件生成和下载

### 权限管理
- 👤 **TomoPrint 用户**：创建和管理自己的作业
- 👨‍💼 **TomoPrint 管理员**：完整系统访问和配置管理
- 🏢 多公司支持，数据隔离

## 支持的文件格式

- STL (Stereolithography)
- OBJ (Wavefront OBJ)
- PLY (Polygon File Format)
- 3MF (3D Manufacturing Format)
- AMF (Additive Manufacturing File)

## 快速开始

### 安装要求
- Odoo 18.0+
- Python 3.8+
- PostgreSQL 12+

### 安装步骤

1. **复制模块**
   ```bash
   cp -r tomo_print /path/to/odoo/addons/
   ```

2. **重启 Odoo**
   ```bash
   sudo systemctl restart odoo
   ```

3. **安装模块**
   - 在 Odoo 界面中：Apps → Update Apps List
   - 搜索 "TomoPrint" 并点击安装

4. **配置权限**
   - 设置用户组：Settings → Users & Companies → Groups
   - 分配用户到相应的 TomoPrint 组

### 基本使用

1. **上传文件**
   - 访问 TomoPrint 菜单
   - 拖拽或点击上传 CAD 文件
   - 输入通知邮箱地址

2. **配置参数**（可选）
   - 展开高级选项
   - 调整重新定向方法
   - 设置体素化分辨率
   - 配置投影数量和 Gamma 值

3. **开始处理**
   - 点击"上传并处理"按钮
   - 系统将自动开始处理
   - 可在作业列表中跟踪进度

4. **下载结果**
   - 处理完成后会收到邮件通知
   - 在作业详情页面下载结果文件

## 配置选项

### 系统参数

| 参数 | 默认值 | 描述 |
|------|--------|------|
| 默认体素化分辨率 | 50% | 新作业的默认分辨率 |
| 默认投影数量 | 365 | 默认投影数量 |
| 默认 Gamma 值 | 0.8 | 默认 Gamma 校正值 |
| 最大文件大小 | 100MB | 文件上传大小限制 |
| 处理超时时间 | 24小时 | 最大处理时间 |

### 管理员配置
- 访问：Settings → TomoPrint
- 或者：TomoPrint → Configuration → Parameters

## 开发文档

详细的开发文档请参考：[tomo_dev_sop.md](./tomo_dev_sop.md)

## 故障排除

### 常见问题

**Q: 文件上传失败**
- 检查文件格式是否支持
- 确认文件大小未超过限制
- 验证网络连接稳定

**Q: 处理进度不更新**
- 刷新页面重新加载
- 检查后台任务是否正常运行
- 查看系统日志获取详细信息

**Q: 未收到邮件通知**
- 确认邮件地址正确
- 检查垃圾邮件文件夹
- 验证系统邮件服务器配置

### 获取帮助

如需技术支持，请：
1. 查看开发文档
2. 检查系统日志
3. 联系技术支持团队

## 许可证

本模块基于 LGPL-3 许可证发布。

## 贡献

欢迎提交问题报告和功能请求。

---

**TomoPrint** - 让 3D 打印更智能 🚀
