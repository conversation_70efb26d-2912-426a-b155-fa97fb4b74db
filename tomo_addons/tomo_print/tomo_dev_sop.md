# TomoPrint 开发文档 (Development SOP)

## 项目概述 (Project Overview)

TomoPrint 是一个基于 Odoo 18 的 CAD 文件处理模块，提供 Web 界面用于上传和处理 3D 打印文件。该模块实现了 PERFI Cloud Specs 的需求，包括前端用户界面、后端处理逻辑和配置管理。

## 模块结构 (Module Structure)

```
tomo_print/
├── __manifest__.py                 # 模块清单文件
├── models/                         # 数据模型
│   ├── __init__.py
│   ├── tomo_print.py              # 主要业务模型
│   ├── tomo_print_config.py       # 配置管理模型
│   └── res_config_settings.py     # 系统设置扩展
├── views/                          # 视图定义
│   ├── tomo_print_views.xml       # 主要视图
│   ├── tomo_print_config_views.xml # 配置视图
│   ├── res_config_settings_views.xml # 系统设置视图
│   └── menu.xml                   # 菜单定义
├── security/                       # 安全配置
│   ├── security.xml               # 用户组和规则
│   └── ir.model.access.csv        # 访问权限
├── data/                          # 初始数据
│   └── tomo_print_data.xml        # 序列、模板等
├── static/                        # 静态资源
│   ├── src/
│   │   ├── js/
│   │   │   └── tomo_print.js      # JavaScript 组件
│   │   └── css/
│   │       └── tomo_print.css     # 样式文件
│   └── description/
│       └── index.html             # 模块描述页面
└── tomo_dev_sop.md               # 开发文档
```

## 核心功能 (Core Features)

### 1. 文件上传和处理 (File Upload & Processing)

**模型字段 (Model Fields):**
- `cad_file`: Binary 字段存储 CAD 文件
- `cad_filename`: 文件名
- `email`: 通知邮箱
- `state`: 处理状态 (draft, uploaded, processing, completed, error)
- `progress`: 处理进度 (0-100%)

**处理流程 (Processing Workflow):**
1. 用户上传 CAD 文件
2. 系统验证文件格式和大小
3. 文件存储到数据库
4. 开始后端处理
5. 实时更新处理进度
6. 完成后发送邮件通知

### 2. 高级选项配置 (Advanced Options)

**重新定向方法 (Reorientation Methods):**
- `vertical`: 垂直最长轴
- `volume`: 建筑体积适配
- `area`: 最大横截面积

**处理参数 (Processing Parameters):**
- `voxel_resolution`: 体素化分辨率 (1-100%)
- `projection_count`: 投影数量 (默认 365)
- `gamma_value`: Gamma 值 (默认 0.8)
- `brightness_compensation`: 亮度补偿 (布尔值)

### 3. 权限管理 (Access Control)

**用户组 (User Groups):**
- `group_tomo_user`: TomoPrint 用户
  - 可以创建和管理自己的作业
  - 上传文件和跟踪进度
- `group_tomo_manager`: TomoPrint 管理员
  - 完整系统访问权限
  - 配置管理和用户监督

**访问规则 (Access Rules):**
- 用户只能访问自己创建的作业
- 管理员可以访问所有作业
- 多公司支持，数据隔离

## 技术实现 (Technical Implementation)

### 1. 数据模型 (Data Models)

#### TomoPrint 主模型 (tomo.print)
```python
class TomoPrint(models.Model):
    _name = 'tomo.print'
    _description = 'TomoPrint Job'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # 核心字段
    name = fields.Char(required=True, default='New')
    cad_file = fields.Binary(required=True)
    email = fields.Char(required=True)
    state = fields.Selection([...])
    progress = fields.Float(default=0.0)
    
    # 高级选项
    reorientation_method = fields.Selection([...])
    voxel_resolution = fields.Integer(default=50)
    # ... 其他字段
```

#### 配置模型 (tomo.print.config)
```python
class TomoPrintConfig(models.Model):
    _name = 'tomo.print.config'
    _description = 'TomoPrint Configuration'
    
    name = fields.Char(required=True)
    value = fields.Char(required=True)
    parameter_type = fields.Selection([...])
    description = fields.Text()
```

### 2. 前端组件 (Frontend Components)

#### 文件上传组件
- 拖拽上传支持
- 文件类型验证
- 进度显示
- 错误处理

#### 进度跟踪组件
- 实时进度更新
- 状态可视化
- 自动轮询

### 3. 视图定义 (View Definitions)

**Odoo 18 语法注意事项:**
- 使用 `<list>` 替代 `<tree>`
- 移除 `attrs` 属性，使用新语法
- 状态栏使用 `statusbar_visible`

## 配置管理 (Configuration Management)

### 系统参数 (System Parameters)

| 参数名 | 默认值 | 描述 |
|--------|--------|------|
| default_voxel_resolution | 50 | 默认体素化分辨率 |
| default_projection_count | 365 | 默认投影数量 |
| default_gamma_value | 0.8 | 默认 Gamma 值 |
| max_file_size_mb | 100 | 最大文件大小 (MB) |
| processing_timeout_hours | 24 | 处理超时时间 (小时) |
| enable_email_notifications | true | 启用邮件通知 |

### 配置界面
- 系统设置集成
- 参数类型验证
- 实时配置更新

## 安装和部署 (Installation & Deployment)

### 1. 环境要求
- Odoo 18.0+
- Python 3.8+
- PostgreSQL 12+

### 2. 安装步骤
```bash
# 1. 复制模块到 addons 目录
cp -r tomo_print /path/to/odoo/addons/

# 2. 重启 Odoo 服务
sudo systemctl restart odoo

# 3. 更新应用列表
# 在 Odoo 界面中：Apps -> Update Apps List

# 4. 安装模块
# 搜索 "TomoPrint" 并点击安装
```

### 3. 初始配置
1. 创建用户组和权限
2. 配置邮件服务器
3. 设置系统参数
4. 测试文件上传功能

## 开发指南 (Development Guidelines)

### 1. 代码规范
- 遵循 Odoo 编码标准
- 使用英文注释和文档字符串
- 模型方法使用动词命名
- 视图 ID 使用描述性名称

### 2. 测试策略
- 单元测试覆盖核心业务逻辑
- 集成测试验证文件上传流程
- 用户界面测试确保交互正常

### 3. 性能优化
- 大文件处理使用异步任务
- 数据库查询优化
- 前端资源压缩和缓存

## 故障排除 (Troubleshooting)

### 常见问题

1. **文件上传失败**
   - 检查文件大小限制
   - 验证文件格式支持
   - 确认磁盘空间充足

2. **处理进度不更新**
   - 检查后台任务状态
   - 验证数据库连接
   - 查看系统日志

3. **邮件通知未发送**
   - 确认邮件服务器配置
   - 检查邮件模板设置
   - 验证收件人地址

### 日志调试
```python
import logging
_logger = logging.getLogger(__name__)

# 在代码中添加日志
_logger.info('Processing started for job %s', self.name)
_logger.error('Processing failed: %s', str(e))
```

## 扩展开发 (Extension Development)

### 1. 添加新的处理算法
```python
def custom_processing_method(self):
    """自定义处理方法"""
    # 实现新的处理逻辑
    pass
```

### 2. 集成外部 API
```python
def call_external_service(self, data):
    """调用外部处理服务"""
    # API 集成代码
    pass
```

### 3. 自定义报告
- 创建新的报告模板
- 添加统计分析功能
- 导出处理结果

## 维护和更新 (Maintenance & Updates)

### 1. 定期维护任务
- 清理临时文件
- 数据库性能优化
- 日志文件轮转

### 2. 版本更新
- 备份数据库
- 测试新功能
- 逐步部署更新

### 3. 监控指标
- 处理成功率
- 平均处理时间
- 系统资源使用率

## API 接口 (API Endpoints)

### Web 控制器接口

| 路径 | 方法 | 认证 | 描述 |
|------|------|------|------|
| `/tomo_print` | GET | user | 主界面 |
| `/tomo_print/upload` | POST | user | 文件上传 |
| `/tomo_print/progress/<job_id>` | JSON | user | 获取进度 |
| `/tomo_print/download/<job_id>` | GET | user | 下载结果 |
| `/tomo_print/stats` | JSON | user | 统计信息 |
| `/tomo_print/config` | JSON | user | 配置参数 |

### 模型方法

#### TomoPrint 模型方法
```python
# 文件处理
action_upload_file()           # 上传文件
action_start_processing()      # 开始处理
action_download_result()       # 下载结果
action_reset_to_draft()        # 重置为草稿

# 工具方法
get_processing_stats()         # 获取统计信息
_simulate_processing()         # 模拟处理过程
_send_completion_email()       # 发送完成邮件
```

#### 配置模型方法
```python
# 参数管理
get_param(param_name, default) # 获取参数
set_param(name, value, desc)   # 设置参数
get_typed_value()              # 获取类型化值
init_default_configs()         # 初始化默认配置
```

## 数据库结构 (Database Schema)

### tomo_print 表
```sql
CREATE TABLE tomo_print (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL,
    cad_file BYTEA,
    cad_filename VARCHAR,
    email VARCHAR NOT NULL,
    state VARCHAR DEFAULT 'draft',
    progress FLOAT DEFAULT 0.0,
    reorientation_method VARCHAR DEFAULT 'vertical',
    voxel_resolution INTEGER DEFAULT 50,
    projection_count INTEGER DEFAULT 365,
    gamma_value FLOAT DEFAULT 0.8,
    brightness_compensation BOOLEAN DEFAULT FALSE,
    show_advanced BOOLEAN DEFAULT FALSE,
    result_file BYTEA,
    result_filename VARCHAR,
    processing_log TEXT,
    estimated_delivery_time TIMESTAMP,
    user_id INTEGER REFERENCES res_users(id),
    company_id INTEGER REFERENCES res_company(id),
    create_date TIMESTAMP DEFAULT NOW(),
    write_date TIMESTAMP DEFAULT NOW()
);
```

### tomo_print_config 表
```sql
CREATE TABLE tomo_print_config (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL,
    value VARCHAR NOT NULL,
    description TEXT,
    parameter_type VARCHAR DEFAULT 'string',
    selection_options TEXT,
    sequence INTEGER DEFAULT 10,
    active BOOLEAN DEFAULT TRUE,
    is_system BOOLEAN DEFAULT FALSE,
    company_id INTEGER REFERENCES res_company(id),
    create_date TIMESTAMP DEFAULT NOW(),
    write_date TIMESTAMP DEFAULT NOW()
);
```

## 前端架构 (Frontend Architecture)

### JavaScript 组件结构
```
static/src/js/
├── tomo_print.js              # 后台组件
│   ├── TomoPrintFileUpload    # 文件上传组件
│   └── TomoPrintProgress      # 进度跟踪组件
└── tomo_print_frontend.js     # 前端脚本
    ├── initFileUpload()       # 初始化文件上传
    ├── initAdvancedOptions()  # 初始化高级选项
    ├── initFormValidation()   # 初始化表单验证
    └── initProgressTracking() # 初始化进度跟踪
```

### CSS 样式结构
```
static/src/css/tomo_print.css
├── .tomo-upload-zone          # 上传区域样式
├── .tomo-progress-*           # 进度条样式
├── .tomo-advanced-*           # 高级选项样式
├── .tomo-status-badge         # 状态徽章样式
├── .tomo-job-card             # 作业卡片样式
└── .tomo-notification         # 通知样式
```

## 部署清单 (Deployment Checklist)

### 安装前检查
- [ ] Odoo 18.0+ 环境
- [ ] Python 3.8+ 运行时
- [ ] PostgreSQL 12+ 数据库
- [ ] 足够的磁盘空间（文件存储）
- [ ] 邮件服务器配置

### 安装步骤
1. [ ] 复制模块到 addons 目录
2. [ ] 重启 Odoo 服务
3. [ ] 更新应用列表
4. [ ] 安装 TomoPrint 模块
5. [ ] 配置用户权限
6. [ ] 设置系统参数
7. [ ] 测试文件上传功能
8. [ ] 验证邮件通知

### 配置验证
- [ ] 用户组权限正确
- [ ] 文件大小限制合理
- [ ] 邮件模板正常
- [ ] 默认参数设置
- [ ] 多公司支持（如需要）

## 性能优化建议 (Performance Optimization)

### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_tomo_print_user_state ON tomo_print(user_id, state);
CREATE INDEX idx_tomo_print_create_date ON tomo_print(create_date DESC);
CREATE INDEX idx_tomo_config_name ON tomo_print_config(name);
```

### 文件存储优化
- 考虑使用外部文件存储（如 S3）
- 实现文件压缩和去重
- 定期清理临时文件

### 缓存策略
- 配置参数缓存
- 用户权限缓存
- 静态资源 CDN

## 监控和日志 (Monitoring & Logging)

### 关键指标
- 文件上传成功率
- 处理完成时间
- 错误率和类型
- 系统资源使用

### 日志配置
```python
# 在 odoo.conf 中添加
log_level = info
log_handler = :INFO,werkzeug:WARNING,odoo.addons.tomo_print:DEBUG
```

### 监控脚本示例
```bash
#!/bin/bash
# 监控处理中的作业
psql -d odoo -c "SELECT COUNT(*) FROM tomo_print WHERE state='processing';"

# 检查错误作业
psql -d odoo -c "SELECT name, processing_log FROM tomo_print WHERE state='error' AND create_date > NOW() - INTERVAL '1 day';"
```

## 联系信息 (Contact Information)

- 开发团队：TomoPrint Development Team
- 技术支持：<EMAIL>
- 项目仓库：https://github.com/company/tomo_print
- 文档更新：2024年1月

---

**注意：** 本文档会随着模块功能的更新而持续维护，请定期查看最新版本。

## 版本历史 (Version History)

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础文件上传和处理功能
- 用户权限管理
- 配置参数系统
- Web 界面和 API

### 计划功能 (Planned Features)
- 批量文件处理
- 处理队列管理
- 更多文件格式支持
- 高级统计报告
- 移动应用支持
