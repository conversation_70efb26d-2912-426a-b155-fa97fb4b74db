<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Sequence for TomoPrint Jobs -->
        <record id="seq_tomo_print" model="ir.sequence">
            <field name="name">TomoPrint Job</field>
            <field name="code">tomo.print</field>
            <field name="prefix">TP</field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
        </record>

        <!-- Email Template for Job Completion -->
        <record id="email_template_completion" model="mail.template">
            <field name="name">TomoPrint Job Completion</field>
            <field name="model_id" ref="model_tomo_print"/>
            <field name="subject">TomoPrint Job {{ object.name }} - Processing Complete</field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{ object.email }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p>Dear Customer,</p>
                    
                    <p>Your TomoPrint job <strong>{{ object.name }}</strong> has been completed successfully!</p>
                    
                    <h3>Job Summary:</h3>
                    <ul>
                        <li><strong>Job Name:</strong> {{ object.name }}</li>
                        <li><strong>Original File:</strong> {{ object.cad_filename }}</li>
                        <li><strong>Processing Started:</strong> {{ object.create_date }}</li>
                        <li><strong>Processing Completed:</strong> {{ object.write_date }}</li>
                    </ul>
                    
                    <h3>Processing Parameters:</h3>
                    <ul>
                        <li><strong>Reorientation Method:</strong> {{ dict(object._fields['reorientation_method'].selection)[object.reorientation_method] }}</li>
                        <li><strong>Voxel Resolution:</strong> {{ object.voxel_resolution }}%</li>
                        <li><strong>Number of Projections:</strong> {{ object.projection_count }}</li>
                        <li><strong>Gamma Value:</strong> {{ object.gamma_value }}</li>
                        <li><strong>Brightness Compensation:</strong> {{ 'Yes' if object.brightness_compensation else 'No' }}</li>
                    </ul>
                    
                    <p>You can download your processed file by logging into the TomoPrint system and accessing your job details.</p>
                    
                    <p>Thank you for using TomoPrint!</p>
                    
                    <p>Best regards,<br/>
                    The TomoPrint Team</p>
                </div>
            </field>
        </record>

        <!-- Default Configuration Parameters -->
        <function model="tomo.print.config" name="init_default_configs"/>

    </data>
</odoo>
