# -*- coding: utf-8 -*-

from . import models
from . import controllers


def post_init_hook(env):
    """
    Post-installation hook to set up default permissions
    """
    # Get the admin user (usually ID 1)
    admin_user = env['res.users'].browse(1)

    # Get TomoPrint Manager group
    try:
        tomo_manager_group = env.ref('tomo_print.group_tomo_manager')

        # Add admin user to TomoPrint Manager group if not already added
        if tomo_manager_group not in admin_user.groups_id:
            admin_user.groups_id = [(4, tomo_manager_group.id)]

        print(f"✅ Admin user '{admin_user.name}' added to TomoPrint Manager group")

    except Exception as e:
        print(f"⚠️ Could not add admin to TomoPrint Manager group: {e}")

    # Get all users with Administration/Settings access and add them to TomoPrint Manager
    try:
        settings_group = env.ref('base.group_system')
        admin_users = env['res.users'].search([('groups_id', 'in', [settings_group.id])])

        for user in admin_users:
            if tomo_manager_group not in user.groups_id:
                user.groups_id = [(4, tomo_manager_group.id)]
                print(f"✅ Admin user '{user.name}' added to TomoPrint Manager group")

    except Exception as e:
        print(f"⚠️ Could not add admin users to TomoPrint Manager group: {e}")

    # Initialize default configuration
    try:
        config_model = env['tomo.print.config']
        config_model.init_default_configs()
        print("✅ Default TomoPrint configuration initialized")
    except Exception as e:
        print(f"⚠️ Could not initialize default configuration: {e}")
