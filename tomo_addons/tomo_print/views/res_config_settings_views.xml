<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Extend General Settings with TomoPrint Configuration -->
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.tomo.print</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                    <app data-string="TomoPrint" string="TomoPrint" name="tomo_print" groups="tomo_print.group_tomo_manager">
                        <block title="Default Processing Parameters" name="tomo_processing_defaults">
                            <setting string="Default Voxel Resolution" help="Default voxel resolution percentage for new jobs">
                                <field name="tomo_default_voxel_resolution" class="o_light_label"/>
                                <span class="o_form_label">%</span>
                            </setting>
                            <setting string="Default Projection Count" help="Default number of projections for processing">
                                <field name="tomo_default_projection_count" class="o_light_label"/>
                            </setting>
                            <setting string="Default Gamma Value" help="Default gamma correction value">
                                <field name="tomo_default_gamma_value" class="o_light_label"/>
                            </setting>
                            <setting string="Default Reorientation Method" help="Default method for reorienting 3D models">
                                <field name="tomo_default_reorientation_method" class="o_light_label"/>
                            </setting>
                        </block>
                        
                        <block title="File Upload Settings" name="tomo_file_settings">
                            <setting string="Maximum File Size" help="Maximum file size in MB for CAD file uploads">
                                <field name="tomo_max_file_size_mb" class="o_light_label"/>
                                <span class="o_form_label">MB</span>
                            </setting>
                            <setting string="Processing Timeout" help="Maximum processing time before timeout">
                                <field name="tomo_processing_timeout_hours" class="o_light_label"/>
                                <span class="o_form_label">hours</span>
                            </setting>
                            <setting string="Temporary File Cleanup" help="Number of days to keep temporary files">
                                <field name="tomo_temp_file_cleanup_days" class="o_light_label"/>
                                <span class="o_form_label">days</span>
                            </setting>
                        </block>
                        
                        <block title="User Interface Settings" name="tomo_ui_settings">
                            <setting string="Enable Email Notifications" help="Send email notifications when processing is completed">
                                <field name="tomo_enable_email_notifications"/>
                            </setting>
                            <setting string="Enable Advanced Options by Default" help="Show advanced options by default in the interface">
                                <field name="tomo_enable_advanced_options"/>
                            </setting>
                            <setting string="Auto Start Processing" help="Automatically start processing after file upload">
                                <field name="tomo_auto_start_processing"/>
                            </setting>
                        </block>
                        
                        <block title="Advanced Configuration" name="tomo_advanced_config">
                            <setting string="Advanced Configuration" help="Configure detailed system parameters">
                                <div class="text-info">
                                    <i class="fa fa-info-circle"/>
                                    Use the TomoPrint → Configuration → Parameters menu to configure detailed system parameters.
                                </div>
                            </setting>
                        </block>
                    </app>
                </xpath>
            </field>
        </record>

        <!-- TomoPrint Settings Action -->
        <record id="action_tomo_print_settings" model="ir.actions.act_window">
            <field name="name">TomoPrint Settings</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module': 'tomo_print'}</field>
        </record>

    </data>
</odoo>
