<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- TomoPrint Config List View -->
        <record id="view_tomo_print_config_list" model="ir.ui.view">
            <field name="name">tomo.print.config.list</field>
            <field name="model">tomo.print.config</field>
            <field name="arch" type="xml">
                <list string="TomoPrint Configuration" default_order="sequence, name" editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="parameter_type"/>
                    <field name="value"/>
                    <field name="description"/>
                    <field name="active"/>
                    <field name="is_system" readonly="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

        <!-- TomoPrint Config Form View -->
        <record id="view_tomo_print_config_form" model="ir.ui.view">
            <field name="name">tomo.print.config.form</field>
            <field name="model">tomo.print.config</field>
            <field name="arch" type="xml">
                <form string="TomoPrint Configuration">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Parameter Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="parameter_info">
                                <field name="parameter_type"/>
                                <field name="value"/>
                                <field name="selection_options" invisible="parameter_type != 'selection'" 
                                       placeholder="option1,option2,option3"/>
                            </group>
                            <group name="system_info">
                                <field name="sequence"/>
                                <field name="is_system" readonly="1"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        
                        <group name="description">
                            <field name="description" placeholder="Description of what this parameter controls"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- TomoPrint Config Search View -->
        <record id="view_tomo_print_config_search" model="ir.ui.view">
            <field name="name">tomo.print.config.search</field>
            <field name="model">tomo.print.config</field>
            <field name="arch" type="xml">
                <search string="TomoPrint Configuration">
                    <field name="name" string="Parameter Name"/>
                    <field name="value" string="Value"/>
                    <field name="description" string="Description"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <filter string="System Parameters" name="system" domain="[('is_system', '=', True)]"/>
                    <filter string="Custom Parameters" name="custom" domain="[('is_system', '=', False)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Parameter Type" name="group_type" context="{'group_by': 'parameter_type'}"/>
                        <filter string="System/Custom" name="group_system" context="{'group_by': 'is_system'}"/>
                        <filter string="Company" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- TomoPrint Config Action -->
        <record id="action_tomo_print_config" model="ir.actions.act_window">
            <field name="name">TomoPrint Configuration</field>
            <field name="res_model">tomo.print.config</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Configure TomoPrint parameters!
                </p>
                <p>
                    Set up default values and system parameters for TomoPrint processing.
                    These settings will be used as defaults for new jobs.
                </p>
            </field>
        </record>

    </data>
</odoo>
