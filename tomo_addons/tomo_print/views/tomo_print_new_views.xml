<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- TomoPrint New Job Form View -->
        <record id="view_tomo_print_new_form" model="ir.ui.view">
            <field name="name">tomo.print.new.form</field>
            <field name="model">tomo.print</field>
            <field name="arch" type="xml">
                <form string="TomoPrint - New Job" class="tomo_print_new_form">
                    <header>
                        <button name="action_upload_file" string="Confirm" type="object" 
                                invisible="state != 'draft'" class="btn-primary"/>
                        <button name="action_start_processing" string="Start Processing" type="object" 
                                invisible="state != 'uploaded'" class="btn-primary"/>
                        <field name="state" invisible="1"/>
                    </header>
                    
                    <sheet>
                        <!-- TomoPrint Header -->
                        <div class="tomo_header text-center mb-4">
                            <h1 class="tomo_title">TomoPrint</h1>
                            <p class="tomo_subtitle">A PERFI Product</p>
                        </div>

                        <!-- Main Content Area -->
                        <div class="tomo_main_content">
                            <div class="row">
                                <!-- Left Column - File Upload -->
                                <div class="col-md-6">
                                    <div class="tomo_upload_section">
                                        <!-- File Upload Area -->
                                        <div class="tomo_file_upload_area mb-3">
                                            <field name="cad_file" filename="cad_filename" 
                                                   readonly="state != 'draft'" 
                                                   class="tomo_file_input"/>
                                            <field name="cad_filename" invisible="1"/>
                                        </div>
                                        
                                        <!-- Advanced Options Toggle -->
                                        <div class="tomo_advanced_section mb-3">
                                            <div class="form-check">
                                                <field name="show_advanced" widget="boolean_toggle"
                                                       readonly="state not in ['draft', 'uploaded']"/>
                                                <div class="form-check-label">
                                                    <i class="fa fa-cog"/> Advanced
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Email Input -->
                                        <div class="tomo_email_section">
                                            <field name="email" placeholder="<EMAIL>" 
                                                   readonly="state not in ['draft', 'uploaded']"
                                                   class="tomo_email_input"/>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Right Column - Advanced Options -->
                                <div class="col-md-6">
                                    <div class="tomo_options_section" invisible="not show_advanced">
                                        <!-- Reorientate Option -->
                                        <div class="tomo_option_group mb-3">
                                            <div class="form-check">
                                                <field name="reorientation_method" invisible="1"/>
                                                <div class="form-check-input-wrapper">
                                                    <i class="fa fa-refresh"/>
                                                    <span class="form-check-label">Reorientate</span>
                                                    <i class="fa fa-check text-success ml-2"/>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Resolution -->
                                        <div class="tomo_option_group mb-3">
                                            <div class="tomo_option_row">
                                                <div class="o_form_label tomo_option_label">Resolution</div>
                                                <div class="tomo_input_group">
                                                    <field name="voxel_resolution"
                                                           readonly="state not in ['draft', 'uploaded']"
                                                           class="tomo_number_input"/>
                                                    <span class="tomo_input_suffix">%</span>
                                                </div>
                                            </div>
                                            <div class="tomo_range_info">1 — 100 %</div>
                                        </div>

                                        <!-- Number of Projections -->
                                        <div class="tomo_option_group mb-3">
                                            <div class="tomo_option_row">
                                                <div class="o_form_label tomo_option_label"># of Projections</div>
                                                <field name="projection_count"
                                                       placeholder="e.g., 1800"
                                                       readonly="state not in ['draft', 'uploaded']"
                                                       class="tomo_number_input"/>
                                            </div>
                                        </div>

                                        <!-- Brightness -->
                                        <div class="tomo_option_group mb-3">
                                            <div class="tomo_option_row">
                                                <div class="o_form_label tomo_option_label">Brightness</div>
                                                <field name="gamma_value"
                                                       placeholder="gamma"
                                                       readonly="state not in ['draft', 'uploaded']"
                                                       class="tomo_number_input"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Processing Status -->
                            <div class="tomo_status_section mt-4" invisible="state == 'draft'">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="tomo_status_message text-center" invisible="state != 'completed'">
                                            <div class="alert alert-success" role="alert">
                                                <i class="fa fa-check-circle"/> We have sent you an email
                                            </div>
                                        </div>
                                        
                                        <div class="tomo_progress_section" invisible="state not in ['processing', 'uploaded']">
                                            <field name="progress" widget="progressbar"/>
                                            <div class="text-center mt-2">
                                                <small class="text-muted">Processing your file...</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hidden Fields -->
                        <div style="display: none;">
                            <field name="name"/>
                            <field name="user_id"/>
                            <field name="company_id"/>
                            <field name="reorientation_method"/>
                            <field name="brightness_compensation"/>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- TomoPrint New Job Action -->
        <record id="action_tomo_print_new" model="ir.actions.act_window">
            <field name="name">New TomoPrint Job</field>
            <field name="res_model">tomo.print</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_tomo_print_new_form"/>
            <field name="target">current</field>
            <field name="context">{
                'default_show_advanced': False,
                'default_voxel_resolution': 50,
                'default_projection_count': 365,
                'default_gamma_value': 0.8,
                'default_reorientation_method': 'vertical'
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new TomoPrint job!
                </p>
                <p>
                    Upload your CAD file and configure processing parameters.
                    TomoPrint will optimize your file for 3D printing.
                </p>
            </field>
        </record>

    </data>
</odoo>
