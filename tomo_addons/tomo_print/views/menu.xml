<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Main TomoPrint Menu -->
        <menuitem id="menu_tomo_print_root"
                  name="TomoPrint"
                  sequence="100"
                  groups="tomo_print.group_tomo_user"/>

        <!-- Jobs Menu -->
        <menuitem id="menu_tomo_print_jobs" 
                  name="Jobs" 
                  parent="menu_tomo_print_root" 
                  sequence="10" 
                  groups="tomo_print.group_tomo_user"/>

        <menuitem id="menu_tomo_print_new_job"
                  name="New Job"
                  parent="menu_tomo_print_jobs"
                  action="action_tomo_print_new"
                  sequence="5"
                  groups="tomo_print.group_tomo_user"/>

        <menuitem id="menu_tomo_print_my_jobs"
                  name="My Jobs"
                  parent="menu_tomo_print_jobs"
                  action="action_tomo_print"
                  sequence="10"
                  groups="tomo_print.group_tomo_user"/>

        <menuitem id="menu_tomo_print_all_jobs"
                  name="All Jobs"
                  parent="menu_tomo_print_jobs"
                  action="action_tomo_print_all"
                  sequence="20"
                  groups="tomo_print.group_tomo_manager"/>

        <!-- Configuration Menu -->
        <menuitem id="menu_tomo_print_config" 
                  name="Configuration" 
                  parent="menu_tomo_print_root" 
                  sequence="90" 
                  groups="tomo_print.group_tomo_manager"/>

        <menuitem id="menu_tomo_print_parameters" 
                  name="Parameters" 
                  parent="menu_tomo_print_config" 
                  action="action_tomo_print_config" 
                  sequence="10" 
                  groups="tomo_print.group_tomo_manager"/>

        <menuitem id="menu_tomo_print_settings"
                  name="Settings"
                  parent="menu_tomo_print_config"
                  action="tomo_print.action_tomo_print_settings"
                  sequence="20"
                  groups="tomo_print.group_tomo_manager"/>

    </data>
</odoo>
