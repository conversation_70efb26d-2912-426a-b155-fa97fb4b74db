<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- TomoPrint Job List View -->
        <record id="view_tomo_print_list" model="ir.ui.view">
            <field name="name">tomo.print.list</field>
            <field name="model">tomo.print</field>
            <field name="arch" type="xml">
                <list string="TomoPrint Jobs" default_order="create_date desc" sample="1">
                    <field name="name"/>
                    <field name="email"/>
                    <field name="cad_filename"/>
                    <field name="state" decoration-success="state == 'completed'" decoration-info="state == 'processing'" decoration-warning="state == 'uploaded'" decoration-danger="state == 'error'"/>
                    <field name="progress" widget="progressbar"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="create_date"/>
                    <field name="estimated_delivery_time"/>
                </list>
            </field>
        </record>

        <!-- Tomo<PERSON>rint Job Form View -->
        <record id="view_tomo_print_form" model="ir.ui.view">
            <field name="name">tomo.print.form</field>
            <field name="model">tomo.print</field>
            <field name="arch" type="xml">
                <form string="TomoPrint Job">
                    <header>
                        <button name="action_upload_file" string="Upload File" type="object" 
                                invisible="state != 'draft'" class="btn-primary"/>
                        <button name="action_start_processing" string="Start Processing" type="object" 
                                invisible="state != 'uploaded'" class="btn-primary"/>
                        <button name="action_download_result" string="Download Result" type="object" 
                                invisible="state != 'completed'" class="btn-success"/>
                        <button name="action_reset_to_draft" string="Reset to Draft" type="object" 
                                invisible="state == 'draft'" groups="tomo_print.group_tomo_manager"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,uploaded,processing,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="state != 'draft'"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="file_info">
                                <field name="cad_file" filename="cad_filename" readonly="state != 'draft'"/>
                                <field name="cad_filename" invisible="1"/>
                                <field name="email" readonly="state not in ['draft', 'uploaded']"/>
                            </group>
                            <group name="status_info">
                                <field name="progress" widget="progressbar" invisible="state == 'draft'"/>
                                <field name="estimated_delivery_time" invisible="state in ['draft', 'uploaded']"/>
                                <field name="user_id" readonly="1"/>
                                <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Advanced Options" name="advanced_options">
                                <group>
                                    <field name="show_advanced"/>
                                </group>
                                <group invisible="not show_advanced">
                                    <group name="reorientation">
                                        <field name="reorientation_method" readonly="state not in ['draft', 'uploaded']"/>
                                    </group>
                                    <group name="processing_params">
                                        <field name="voxel_resolution" readonly="state not in ['draft', 'uploaded']"/>
                                        <field name="projection_count" readonly="state not in ['draft', 'uploaded']"/>
                                        <field name="gamma_value" readonly="state not in ['draft', 'uploaded']"/>
                                        <field name="brightness_compensation" readonly="state not in ['draft', 'uploaded']"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Processing Log" name="processing_log" invisible="not processing_log">
                                <field name="processing_log" readonly="1"/>
                            </page>
                            
                            <page string="Result" name="result" invisible="state != 'completed'">
                                <group>
                                    <field name="result_file" filename="result_filename" readonly="1"/>
                                    <field name="result_filename" invisible="1"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter>
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </chatter>
                </form>
            </field>
        </record>

        <!-- TomoPrint Job Kanban View -->
        <record id="view_tomo_print_kanban" model="ir.ui.view">
            <field name="name">tomo.print.kanban</field>
            <field name="model">tomo.print</field>
            <field name="arch" type="xml">
                <kanban default_group_by="state" class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="email"/>
                    <field name="state"/>
                    <field name="progress"/>
                    <field name="user_id"/>
                    <field name="cad_filename"/>
                    <field name="create_date"/>
                    <templates>
                        <t t-name="card">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#" title="Manage">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <field name="cad_filename"/>
                                            <br/>
                                            <field name="email"/>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <field name="user_id" widget="many2one_avatar_user"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_bottom" t-if="record.state.raw_value == 'processing'">
                                        <div class="oe_kanban_bottom_left">
                                            <field name="progress" widget="progressbar"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- TomoPrint Job Search View -->
        <record id="view_tomo_print_search" model="ir.ui.view">
            <field name="name">tomo.print.search</field>
            <field name="model">tomo.print</field>
            <field name="arch" type="xml">
                <search string="TomoPrint Jobs">
                    <field name="name" string="Job Name"/>
                    <field name="email" string="Email"/>
                    <field name="cad_filename" string="Filename"/>
                    <field name="user_id" string="User"/>
                    
                    <filter string="My Jobs" name="my_jobs" domain="[('user_id', '=', uid)]"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Processing" name="processing" domain="[('state', '=', 'processing')]"/>
                    <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                    <filter string="Error" name="error" domain="[('state', '=', 'error')]"/>
                    
                    <separator/>
                    <filter string="Today" name="today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" domain="[('create_date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="User" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Creation Date" name="group_create_date" context="{'group_by': 'create_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- TomoPrint Job Action -->
        <record id="action_tomo_print" model="ir.actions.act_window">
            <field name="name">TomoPrint Jobs</field>
            <field name="res_model">tomo.print</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="context">{'search_default_my_jobs': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first TomoPrint job!
                </p>
                <p>
                    Upload your CAD files and let TomoPrint process them with advanced 3D printing optimization.
                    Configure reorientation, voxelation, and other parameters to get the best results.
                </p>
            </field>
        </record>

        <!-- TomoPrint All Jobs Action (for managers) -->
        <record id="action_tomo_print_all" model="ir.actions.act_window">
            <field name="name">All TomoPrint Jobs</field>
            <field name="res_model">tomo.print</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No TomoPrint jobs found!
                </p>
                <p>
                    This view shows all TomoPrint jobs from all users.
                    Monitor processing status and manage the system.
                </p>
            </field>
        </record>

    </data>
</odoo>
