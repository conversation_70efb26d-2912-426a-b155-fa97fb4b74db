<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Main TomoPrint Interface Template -->
        <template id="main_interface" name="TomoPrint Interface">
            <t t-call="web.layout">
                <t t-set="title">TomoPrint - PERFI Cloud Specs</t>
                
                <div class="container mt-4">
                    <div class="row">
                        <div class="col-12">
                            <div class="text-center mb-4">
                                <h1 class="display-4">TomoPrint</h1>
                                <p class="lead">A PERFI Product</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Upload Form -->
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-body">
                                    <form id="tomo-print-form" action="/tomo_print/upload" method="post" enctype="multipart/form-data">
                                        
                                        <!-- File Upload Zone -->
                                        <div class="tomo-upload-zone mb-4">
                                            <div class="tomo-upload-icon">
                                                <i class="fa fa-cloud-upload"></i>
                                            </div>
                                            <div class="tomo-upload-text">
                                                Drop your CAD file here
                                            </div>
                                            <div class="tomo-upload-subtext">
                                                or click to browse files<br/>
                                                Supported formats: STL, OBJ, PLY, 3MF, AMF<br/>
                                                Maximum size: <t t-esc="default_values.get('max_file_size_mb', 100)"/>MB
                                            </div>
                                            <input type="file" 
                                                   id="cad_file_input" 
                                                   name="cad_file" 
                                                   accept=".stl,.obj,.ply,.3mf,.amf" 
                                                   style="display: none;" 
                                                   required="required"/>
                                        </div>
                                        
                                        <div class="file-info mb-3"></div>
                                        
                                        <!-- Email Input -->
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">Notification Email</label>
                                            <input type="email" 
                                                   class="form-control" 
                                                   id="email" 
                                                   name="email" 
                                                   placeholder="<EMAIL>" 
                                                   required="required"/>
                                        </div>
                                        
                                        <!-- Advanced Options Toggle -->
                                        <div class="tomo-advanced-toggle mb-3">
                                            <i class="fa fa-chevron-right"></i>
                                            <span>Advanced Options</span>
                                            <input type="checkbox" name="show_advanced" style="display: none;"/>
                                        </div>
                                        
                                        <!-- Advanced Options Panel -->
                                        <div class="tomo-advanced-options" style="display: none;">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="reorientation_method" class="form-label">Reorientation Method</label>
                                                        <select class="form-control" id="reorientation_method" name="reorientation_method">
                                                            <option value="vertical" t-att-selected="default_values.get('reorientation_method') == 'vertical'">Vertical - longest axis</option>
                                                            <option value="volume" t-att-selected="default_values.get('reorientation_method') == 'volume'">Building volume fit</option>
                                                            <option value="area" t-att-selected="default_values.get('reorientation_method') == 'area'">Cross-section area</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="voxel_resolution" class="form-label">Resolution (%)</label>
                                                        <input type="range" 
                                                               class="form-range" 
                                                               id="voxel_resolution" 
                                                               name="voxel_resolution" 
                                                               min="1" 
                                                               max="100" 
                                                               t-att-value="default_values.get('voxel_resolution', 50)"
                                                               oninput="document.getElementById('voxel_value').textContent = this.value + '%'"/>
                                                        <div class="text-center">
                                                            <span id="voxel_value"><t t-esc="default_values.get('voxel_resolution', 50)"/>%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="projection_count" class="form-label"># of Projections</label>
                                                        <input type="number" 
                                                               class="form-control" 
                                                               id="projection_count" 
                                                               name="projection_count" 
                                                               t-att-value="default_values.get('projection_count', 365)"
                                                               min="1"/>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="gamma_value" class="form-label">Gamma</label>
                                                        <input type="number" 
                                                               class="form-control" 
                                                               id="gamma_value" 
                                                               name="gamma_value" 
                                                               step="0.1" 
                                                               min="0.1" 
                                                               max="3.0" 
                                                               t-att-value="default_values.get('gamma_value', 0.8)"/>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input type="checkbox" 
                                                       class="form-check-input" 
                                                       id="brightness_compensation" 
                                                       name="brightness_compensation"/>
                                                <label class="form-check-label" for="brightness_compensation">
                                                    Brightness Compensation
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <!-- Submit Button -->
                                        <div class="text-center">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fa fa-upload"></i>
                                                Upload and Process
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Jobs -->
                    <div class="row mt-5" t-if="jobs">
                        <div class="col-12">
                            <h3>Recent Jobs</h3>
                            <div class="row">
                                <t t-foreach="jobs" t-as="job">
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="tomo-job-card" t-att-data-job-id="job.id">
                                            <div class="tomo-job-header">
                                                <h5 class="tomo-job-title" t-esc="job.name"/>
                                                <span class="tomo-status-badge" t-att-class="job.state" t-esc="job.state.upper()"/>
                                            </div>
                                            <div class="tomo-job-meta">
                                                <div><strong>File:</strong> <t t-esc="job.cad_filename"/></div>
                                                <div><strong>Created:</strong> <t t-esc="job.create_date.strftime('%Y-%m-%d %H:%M')"/></div>
                                            </div>
                                            <div class="tomo-progress-container" t-if="job.state == 'processing'">
                                                <div class="tomo-progress-bar">
                                                    <div class="tomo-progress-fill" t-att-style="'width: ' + str(job.progress) + '%'"/>
                                                </div>
                                                <div class="tomo-progress-text">
                                                    <t t-esc="int(job.progress)"/>% - <t t-esc="job.state"/>
                                                </div>
                                            </div>
                                            <div class="tomo-job-actions">
                                                <a t-attf-href="/web#id=#{job.id}&amp;model=tomo.print&amp;view_type=form" 
                                                   class="tomo-job-action">View Details</a>
                                                <a t-if="job.state == 'completed' and job.result_file" 
                                                   t-attf-href="/tomo_print/download/#{job.id}" 
                                                   class="tomo-job-action success">Download</a>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Notification Container -->
                <div id="notification-container"></div>
            </t>
        </template>

    </data>
</odoo>
