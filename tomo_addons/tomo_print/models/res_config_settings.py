# -*- coding: utf-8 -*-

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # TomoPrint Configuration Settings
    tomo_default_voxel_resolution = fields.Integer(
        string='Default Voxel Resolution (%)',
        default=50,
        config_parameter='tomo_print.default_voxel_resolution',
        help='Default voxel resolution percentage (1-100)'
    )
    
    tomo_default_projection_count = fields.Integer(
        string='Default Projection Count',
        default=365,
        config_parameter='tomo_print.default_projection_count',
        help='Default number of projections for processing'
    )
    
    tomo_default_gamma_value = fields.Float(
        string='Default Gamma Value',
        default=0.8,
        config_parameter='tomo_print.default_gamma_value',
        help='Default gamma correction value'
    )
    
    tomo_default_reorientation_method = fields.Selection([
        ('vertical', 'Vertical - longest axis'),
        ('volume', 'Building volume fit'),
        ('area', 'Cross-section area')
    ], string='Default Reorientation Method',
       default='vertical',
       config_parameter='tomo_print.default_reorientation_method',
       help='Default method for reorienting 3D models')
    
    tomo_max_file_size_mb = fields.Integer(
        string='Maximum File Size (MB)',
        default=100,
        config_parameter='tomo_print.max_file_size_mb',
        help='Maximum file size in MB for CAD file uploads'
    )
    
    tomo_processing_timeout_hours = fields.Integer(
        string='Processing Timeout (Hours)',
        default=24,
        config_parameter='tomo_print.processing_timeout_hours',
        help='Maximum processing time in hours before timeout'
    )
    
    tomo_enable_email_notifications = fields.Boolean(
        string='Enable Email Notifications',
        default=True,
        config_parameter='tomo_print.enable_email_notifications',
        help='Send email notifications when processing is completed'
    )
    
    tomo_temp_file_cleanup_days = fields.Integer(
        string='Temporary File Cleanup (Days)',
        default=7,
        config_parameter='tomo_print.temp_file_cleanup_days',
        help='Number of days to keep temporary files before cleanup'
    )
    
    tomo_enable_advanced_options = fields.Boolean(
        string='Enable Advanced Options by Default',
        default=False,
        config_parameter='tomo_print.enable_advanced_options',
        help='Show advanced options by default in the interface'
    )
    
    tomo_auto_start_processing = fields.Boolean(
        string='Auto Start Processing',
        default=False,
        config_parameter='tomo_print.auto_start_processing',
        help='Automatically start processing after file upload'
    )

    @api.model
    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        
        # Get TomoPrint configuration values
        config_model = self.env['tomo.print.config']
        
        res.update({
            'tomo_default_voxel_resolution': config_model.get_param('default_voxel_resolution', 50),
            'tomo_default_projection_count': config_model.get_param('default_projection_count', 365),
            'tomo_default_gamma_value': config_model.get_param('default_gamma_value', 0.8),
            'tomo_default_reorientation_method': config_model.get_param('default_reorientation_method', 'vertical'),
            'tomo_max_file_size_mb': config_model.get_param('max_file_size_mb', 100),
            'tomo_processing_timeout_hours': config_model.get_param('processing_timeout_hours', 24),
            'tomo_enable_email_notifications': config_model.get_param('enable_email_notifications', True),
            'tomo_temp_file_cleanup_days': config_model.get_param('temp_file_cleanup_days', 7),
        })
        
        return res

    def set_values(self):
        super(ResConfigSettings, self).set_values()
        
        # Set TomoPrint configuration values
        config_model = self.env['tomo.print.config']
        
        config_model.set_param('default_voxel_resolution', self.tomo_default_voxel_resolution, 
                              'Default voxel resolution percentage', 'integer')
        config_model.set_param('default_projection_count', self.tomo_default_projection_count,
                              'Default number of projections', 'integer')
        config_model.set_param('default_gamma_value', self.tomo_default_gamma_value,
                              'Default gamma correction value', 'float')
        config_model.set_param('default_reorientation_method', self.tomo_default_reorientation_method,
                              'Default reorientation method', 'selection')
        config_model.set_param('max_file_size_mb', self.tomo_max_file_size_mb,
                              'Maximum file size in MB', 'integer')
        config_model.set_param('processing_timeout_hours', self.tomo_processing_timeout_hours,
                              'Processing timeout in hours', 'integer')
        config_model.set_param('enable_email_notifications', self.tomo_enable_email_notifications,
                              'Enable email notifications', 'boolean')
        config_model.set_param('temp_file_cleanup_days', self.tomo_temp_file_cleanup_days,
                              'Temporary file cleanup days', 'integer')
