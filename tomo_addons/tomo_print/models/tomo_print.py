# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
import base64
import logging
import os
import tempfile

_logger = logging.getLogger(__name__)


class TomoPrint(models.Model):
    _name = 'tomo.print'
    _description = 'TomoPrint Job'
    _order = 'create_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(
        string='Job Name',
        required=True,
        default=lambda self: _('New'),
        tracking=True
    )
    
    # File upload fields
    cad_file = fields.Binary(
        string='CAD File',
        required=True,
        help='Upload your CAD file for processing'
    )
    cad_filename = fields.Char(
        string='Filename',
        help='Name of the uploaded CAD file'
    )
    
    # Contact information
    email = fields.Char(
        string='Notification Email',
        required=True,
        help='Email address to receive processing notifications'
    )
    
    # Advanced options toggle
    show_advanced = fields.Boolean(
        string='Show Advanced Options',
        default=False,
        help='Show advanced processing options'
    )
    
    # Reorientation options
    reorientation_method = fields.Selection([
        ('vertical', 'Vertical - longest axis'),
        ('volume', 'Building volume fit'),
        ('area', 'Cross-section area')
    ], string='Reorientation Method', default='vertical',
       help='Method for reorienting the 3D model')
    
    # Processing parameters
    voxel_resolution = fields.Integer(
        string='Resolution (%)',
        default=50,
        help='Voxelating resolution from 1 to 100%'
    )
    
    projection_count = fields.Integer(
        string='# of Projections',
        default=365,
        help='Number of projections for processing'
    )
    
    gamma_value = fields.Float(
        string='Gamma',
        default=0.8,
        help='Gamma correction value'
    )
    
    brightness_compensation = fields.Boolean(
        string='Brightness Compensation',
        default=False,
        help='Apply brightness compensation'
    )
    
    # Processing status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('uploaded', 'File Uploaded'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('error', 'Error')
    ], string='Status', default='draft', tracking=True)
    
    progress = fields.Float(
        string='Progress (%)',
        default=0.0,
        help='Processing progress from 0 to 100%'
    )
    
    # Results
    result_file = fields.Binary(
        string='Result File',
        help='Processed result file'
    )
    result_filename = fields.Char(
        string='Result Filename',
        help='Name of the result file'
    )
    
    # Processing details
    processing_log = fields.Text(
        string='Processing Log',
        help='Detailed processing log'
    )
    
    estimated_delivery_time = fields.Datetime(
        string='Estimated Delivery Time',
        help='Estimated completion time'
    )
    
    # User tracking
    user_id = fields.Many2one(
        'res.users',
        string='Created by',
        default=lambda self: self.env.user,
        required=True
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.model
    def create(self, vals):
        # Generate sequence number if name is not provided or is default
        if not vals.get('name') or vals.get('name') in [_('New'), 'New Job', 'New']:
            sequence = self.env['ir.sequence'].next_by_code('tomo.print')
            if sequence:
                vals['name'] = sequence
            else:
                # Fallback if sequence is not found
                vals['name'] = _('New Job %s') % fields.Datetime.now().strftime('%Y%m%d_%H%M%S')
        return super(TomoPrint, self).create(vals)

    @api.constrains('voxel_resolution')
    def _check_voxel_resolution(self):
        for record in self:
            if record.voxel_resolution < 1 or record.voxel_resolution > 100:
                raise ValidationError(_('Voxel resolution must be between 1 and 100%.'))

    @api.constrains('projection_count')
    def _check_projection_count(self):
        for record in self:
            if record.projection_count < 1:
                raise ValidationError(_('Number of projections must be greater than 0.'))

    @api.constrains('gamma_value')
    def _check_gamma_value(self):
        for record in self:
            if record.gamma_value <= 0:
                raise ValidationError(_('Gamma value must be greater than 0.'))

    def action_upload_file(self):
        """Action to upload and validate the CAD file"""
        self.ensure_one()
        if not self.cad_file:
            raise UserError(_('Please select a CAD file to upload.'))
        
        self.state = 'uploaded'
        self.message_post(body=_('CAD file uploaded successfully.'))
        return True

    def action_start_processing(self):
        """Start the processing of the uploaded CAD file"""
        self.ensure_one()
        if self.state != 'uploaded':
            raise UserError(_('File must be uploaded before processing can start.'))
        
        self.state = 'processing'
        self.progress = 0.0
        
        # Estimate delivery time (example: 2 hours from now)
        from datetime import datetime, timedelta
        self.estimated_delivery_time = datetime.now() + timedelta(hours=2)
        
        self.message_post(body=_('Processing started.'))
        
        # Here you would typically call your processing backend
        # For now, we'll simulate the process
        self._simulate_processing()
        
        return True

    def _simulate_processing(self):
        """Simulate the processing workflow"""
        try:
            # Simulate processing steps
            self._update_progress(10, 'File validation completed')
            self._update_progress(30, 'Principal component analysis completed')
            self._update_progress(50, 'Geometry preprocessing completed')
            self._update_progress(70, 'Reorientation completed')
            self._update_progress(90, 'Final processing completed')
            
            # Mark as completed
            self.state = 'completed'
            self.progress = 100.0
            self.message_post(body=_('Processing completed successfully.'))
            
            # Send notification email
            self._send_completion_email()
            
        except Exception as e:
            self.state = 'error'
            self.processing_log = str(e)
            self.message_post(body=_('Processing failed: %s') % str(e))
            _logger.error('TomoPrint processing failed: %s', str(e))

    def _update_progress(self, progress, message):
        """Update processing progress"""
        self.progress = progress
        if self.processing_log:
            self.processing_log += '\n' + message
        else:
            self.processing_log = message
        self.env.cr.commit()  # Commit to show progress in real-time

    def _send_completion_email(self):
        """Send completion notification email"""
        if not self.email:
            return
        
        template = self.env.ref('tomo_print.email_template_completion', raise_if_not_found=False)
        if template:
            template.send_mail(self.id, force_send=True)

    def action_download_result(self):
        """Download the processed result file"""
        self.ensure_one()
        if not self.result_file:
            raise UserError(_('No result file available for download.'))
        
        return {
            'type': 'ir.actions.act_url',
            'url': '/web/content/?model=tomo.print&id=%s&field=result_file&download=true&filename=%s' % (
                self.id, self.result_filename or 'result.mov'
            ),
            'target': 'self',
        }

    def action_reset_to_draft(self):
        """Reset job to draft state"""
        self.ensure_one()
        self.state = 'draft'
        self.progress = 0.0
        self.processing_log = ''
        self.result_file = False
        self.result_filename = ''
        self.message_post(body=_('Job reset to draft.'))

    @api.model
    def get_processing_stats(self):
        """Get processing statistics for dashboard"""
        return {
            'total_jobs': self.search_count([]),
            'completed_jobs': self.search_count([('state', '=', 'completed')]),
            'processing_jobs': self.search_count([('state', '=', 'processing')]),
            'error_jobs': self.search_count([('state', '=', 'error')]),
        }
