# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class TomoPrintConfig(models.Model):
    _name = 'tomo.print.config'
    _description = 'TomoPrint Configuration'
    _order = 'sequence, name'

    name = fields.Char(
        string='Parameter Name',
        required=True,
        help='Name of the configuration parameter'
    )
    
    value = fields.Char(
        string='Value',
        required=True,
        help='Value of the configuration parameter'
    )
    
    description = fields.Text(
        string='Description',
        help='Description of what this parameter controls'
    )
    
    parameter_type = fields.Selection([
        ('string', 'String'),
        ('integer', 'Integer'),
        ('float', 'Float'),
        ('boolean', 'Boolean'),
        ('selection', 'Selection')
    ], string='Parameter Type', default='string', required=True)
    
    selection_options = fields.Text(
        string='Selection Options',
        help='For selection type, enter options separated by commas (e.g., option1,option2,option3)'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Sequence for ordering parameters'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this configuration parameter is active'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )
    
    # System parameters
    is_system = fields.Boolean(
        string='System Parameter',
        default=False,
        help='System parameters cannot be deleted by users'
    )

    @api.constrains('parameter_type', 'value')
    def _check_value_type(self):
        """Validate that the value matches the parameter type"""
        for record in self:
            if record.parameter_type == 'integer':
                try:
                    int(record.value)
                except ValueError:
                    raise ValidationError(_('Value must be an integer for parameter "%s"') % record.name)
            
            elif record.parameter_type == 'float':
                try:
                    float(record.value)
                except ValueError:
                    raise ValidationError(_('Value must be a float for parameter "%s"') % record.name)
            
            elif record.parameter_type == 'boolean':
                if record.value.lower() not in ['true', 'false', '1', '0', 'yes', 'no']:
                    raise ValidationError(_('Value must be true/false for boolean parameter "%s"') % record.name)
            
            elif record.parameter_type == 'selection':
                if record.selection_options:
                    options = [opt.strip() for opt in record.selection_options.split(',')]
                    if record.value not in options:
                        raise ValidationError(_('Value must be one of: %s for parameter "%s"') % (
                            ', '.join(options), record.name))

    @api.constrains('selection_options', 'parameter_type')
    def _check_selection_options(self):
        """Validate selection options when parameter type is selection"""
        for record in self:
            if record.parameter_type == 'selection' and not record.selection_options:
                raise ValidationError(_('Selection options are required for selection type parameter "%s"') % record.name)

    def get_typed_value(self):
        """Return the value converted to the appropriate Python type"""
        self.ensure_one()
        
        if self.parameter_type == 'integer':
            return int(self.value)
        elif self.parameter_type == 'float':
            return float(self.value)
        elif self.parameter_type == 'boolean':
            return self.value.lower() in ['true', '1', 'yes']
        else:
            return self.value

    @api.model
    def get_param(self, param_name, default=None):
        """Get a configuration parameter value"""
        param = self.search([('name', '=', param_name), ('active', '=', True)], limit=1)
        if param:
            return param.get_typed_value()
        return default

    @api.model
    def set_param(self, param_name, value, description=None, param_type='string'):
        """Set a configuration parameter value"""
        param = self.search([('name', '=', param_name)], limit=1)
        
        vals = {
            'name': param_name,
            'value': str(value),
            'parameter_type': param_type,
        }
        
        if description:
            vals['description'] = description
        
        if param:
            param.write(vals)
        else:
            self.create(vals)
        
        return True

    @api.model
    def get_default_configs(self):
        """Return default configuration parameters"""
        return [
            {
                'name': 'default_voxel_resolution',
                'value': '50',
                'description': 'Default voxel resolution percentage (1-100)',
                'parameter_type': 'integer',
                'sequence': 10,
                'is_system': True,
            },
            {
                'name': 'default_projection_count',
                'value': '365',
                'description': 'Default number of projections',
                'parameter_type': 'integer',
                'sequence': 20,
                'is_system': True,
            },
            {
                'name': 'default_gamma_value',
                'value': '0.8',
                'description': 'Default gamma correction value',
                'parameter_type': 'float',
                'sequence': 30,
                'is_system': True,
            },
            {
                'name': 'default_reorientation_method',
                'value': 'vertical',
                'description': 'Default reorientation method',
                'parameter_type': 'selection',
                'selection_options': 'vertical,volume,area',
                'sequence': 40,
                'is_system': True,
            },
            {
                'name': 'max_file_size_mb',
                'value': '100',
                'description': 'Maximum file size in MB for uploads',
                'parameter_type': 'integer',
                'sequence': 50,
                'is_system': True,
            },
            {
                'name': 'processing_timeout_hours',
                'value': '24',
                'description': 'Maximum processing time in hours before timeout',
                'parameter_type': 'integer',
                'sequence': 60,
                'is_system': True,
            },
            {
                'name': 'enable_email_notifications',
                'value': 'true',
                'description': 'Enable email notifications for job completion',
                'parameter_type': 'boolean',
                'sequence': 70,
                'is_system': True,
            },
            {
                'name': 'temp_file_cleanup_days',
                'value': '7',
                'description': 'Number of days to keep temporary files',
                'parameter_type': 'integer',
                'sequence': 80,
                'is_system': True,
            },
        ]

    @api.model
    def init_default_configs(self):
        """Initialize default configuration parameters"""
        default_configs = self.get_default_configs()
        
        for config_data in default_configs:
            existing = self.search([('name', '=', config_data['name'])], limit=1)
            if not existing:
                self.create(config_data)

    def unlink(self):
        """Prevent deletion of system parameters"""
        for record in self:
            if record.is_system:
                raise ValidationError(_('System parameter "%s" cannot be deleted.') % record.name)
        return super(TomoPrintConfig, self).unlink()
