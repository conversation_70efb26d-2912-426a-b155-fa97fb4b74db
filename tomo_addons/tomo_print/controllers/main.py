# -*- coding: utf-8 -*-

import json
import base64
from odoo import http, _
from odoo.http import request
from odoo.exceptions import AccessError, UserError


class TomoPrintController(http.Controller):

    @http.route('/tomo_print', type='http', auth='user', website=True)
    def tomo_print_index(self, **kwargs):
        """Main TomoPrint interface"""
        # Get user's jobs
        jobs = request.env['tomo.print'].search([
            ('user_id', '=', request.env.user.id)
        ], order='create_date desc', limit=10)
        
        # Get configuration
        config = request.env['tomo.print.config']
        default_values = {
            'voxel_resolution': config.get_param('default_voxel_resolution', 50),
            'projection_count': config.get_param('default_projection_count', 365),
            'gamma_value': config.get_param('default_gamma_value', 0.8),
            'reorientation_method': config.get_param('default_reorientation_method', 'vertical'),
            'max_file_size_mb': config.get_param('max_file_size_mb', 100),
        }
        
        return request.render('tomo_print.main_interface', {
            'jobs': jobs,
            'default_values': default_values,
        })

    @http.route('/tomo_print/upload', type='http', auth='user', methods=['POST'], csrf=False)
    def upload_file(self, **kwargs):
        """Handle file upload"""
        try:
            # Get uploaded file
            uploaded_file = request.httprequest.files.get('cad_file')
            if not uploaded_file:
                return json.dumps({'error': 'No file uploaded'})
            
            # Validate file
            allowed_extensions = ['.stl', '.obj', '.ply', '.3mf', '.amf']
            file_ext = '.' + uploaded_file.filename.split('.')[-1].lower()
            
            if file_ext not in allowed_extensions:
                return json.dumps({'error': 'Unsupported file type'})
            
            # Check file size
            max_size = request.env['tomo.print.config'].get_param('max_file_size_mb', 100)
            if len(uploaded_file.read()) > max_size * 1024 * 1024:
                return json.dumps({'error': f'File size exceeds {max_size}MB limit'})
            
            # Reset file pointer
            uploaded_file.seek(0)
            
            # Create TomoPrint job
            job_data = {
                'name': f'Job for {uploaded_file.filename}',
                'cad_file': base64.b64encode(uploaded_file.read()).decode('utf-8'),
                'cad_filename': uploaded_file.filename,
                'email': kwargs.get('email', ''),
                'reorientation_method': kwargs.get('reorientation_method', 'vertical'),
                'voxel_resolution': int(kwargs.get('voxel_resolution', 50)),
                'projection_count': int(kwargs.get('projection_count', 365)),
                'gamma_value': float(kwargs.get('gamma_value', 0.8)),
                'brightness_compensation': kwargs.get('brightness_compensation') == 'on',
                'show_advanced': kwargs.get('show_advanced') == 'on',
            }
            
            job = request.env['tomo.print'].create(job_data)
            
            # Auto-start processing if enabled
            auto_start = request.env['tomo.print.config'].get_param('auto_start_processing', False)
            if auto_start:
                job.action_upload_file()
                job.action_start_processing()
            else:
                job.action_upload_file()
            
            return json.dumps({
                'success': True,
                'job_id': job.id,
                'redirect_url': f'/web#id={job.id}&model=tomo.print&view_type=form'
            })
            
        except Exception as e:
            return json.dumps({'error': str(e)})

    @http.route('/tomo_print/progress/<int:job_id>', type='json', auth='user')
    def get_progress(self, job_id, **kwargs):
        """Get job progress"""
        try:
            job = request.env['tomo.print'].browse(job_id)
            
            # Check access rights
            if job.user_id != request.env.user and not request.env.user.has_group('tomo_print.group_tomo_manager'):
                raise AccessError(_('Access denied'))
            
            return {
                'progress': job.progress,
                'state': job.state,
                'processing_log': job.processing_log,
                'estimated_delivery_time': job.estimated_delivery_time.isoformat() if job.estimated_delivery_time else None,
            }
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/tomo_print/download/<int:job_id>', type='http', auth='user')
    def download_result(self, job_id, **kwargs):
        """Download processed result file"""
        try:
            job = request.env['tomo.print'].browse(job_id)
            
            # Check access rights
            if job.user_id != request.env.user and not request.env.user.has_group('tomo_print.group_tomo_manager'):
                raise AccessError(_('Access denied'))
            
            if not job.result_file:
                raise UserError(_('No result file available'))
            
            # Prepare file response
            file_data = base64.b64decode(job.result_file)
            filename = job.result_filename or f'{job.name}_result.mov'
            
            return request.make_response(
                file_data,
                headers=[
                    ('Content-Type', 'application/octet-stream'),
                    ('Content-Disposition', f'attachment; filename="{filename}"'),
                    ('Content-Length', len(file_data))
                ]
            )
            
        except Exception as e:
            return request.render('web.http_error', {
                'status_code': 500,
                'status_message': 'Internal Server Error',
                'error_message': str(e)
            })

    @http.route('/tomo_print/stats', type='json', auth='user')
    def get_stats(self, **kwargs):
        """Get processing statistics"""
        try:
            # Check if user is manager
            if not request.env.user.has_group('tomo_print.group_tomo_manager'):
                # Return user's stats only
                domain = [('user_id', '=', request.env.user.id)]
            else:
                # Return all stats for managers
                domain = []
            
            stats = {
                'total_jobs': request.env['tomo.print'].search_count(domain),
                'completed_jobs': request.env['tomo.print'].search_count(domain + [('state', '=', 'completed')]),
                'processing_jobs': request.env['tomo.print'].search_count(domain + [('state', '=', 'processing')]),
                'error_jobs': request.env['tomo.print'].search_count(domain + [('state', '=', 'error')]),
            }
            
            return stats
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/tomo_print/config', type='json', auth='user')
    def get_config(self, **kwargs):
        """Get configuration parameters"""
        try:
            config = request.env['tomo.print.config']
            
            return {
                'max_file_size_mb': config.get_param('max_file_size_mb', 100),
                'default_voxel_resolution': config.get_param('default_voxel_resolution', 50),
                'default_projection_count': config.get_param('default_projection_count', 365),
                'default_gamma_value': config.get_param('default_gamma_value', 0.8),
                'default_reorientation_method': config.get_param('default_reorientation_method', 'vertical'),
                'enable_email_notifications': config.get_param('enable_email_notifications', True),
            }
            
        except Exception as e:
            return {'error': str(e)}
