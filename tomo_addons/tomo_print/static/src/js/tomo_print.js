/** @odoo-module **/

import { registry } from "@web/core/registry";

/**
 * TomoPrint JavaScript utilities
 */
const TomoPrintUtils = {
    /**
     * Initialize file upload functionality
     */
    initFileUpload() {
        const uploadZones = document.querySelectorAll('.tomo-upload-zone');
        uploadZones.forEach(zone => {
            this.setupFileUpload(zone);
        });
    },

    /**
     * Setup file upload for a specific zone
     */
    setupFileUpload(uploadZone) {
        const fileInput = uploadZone.querySelector('input[type="file"]');
        if (!fileInput) return;

        // Drag and drop events
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('drag-over');
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelection(files[0], uploadZone);
            }
        });

        // Click to upload
        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelection(e.target.files[0], uploadZone);
            }
        });
    },

    /**
     * Handle file selection and validation
     */
    handleFileSelection(file, uploadZone) {
        const allowedTypes = ['stl', 'obj', 'ply', '3mf', 'amf'];
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const maxSizeMB = 100; // This should come from configuration

        // Validate file type
        if (!allowedTypes.includes(fileExtension)) {
            this.showNotification('Unsupported file type. Please upload: ' + allowedTypes.join(', '), 'warning');
            return;
        }

        // Validate file size
        if (file.size > maxSizeMB * 1024 * 1024) {
            this.showNotification(`File size exceeds maximum limit of ${maxSizeMB}MB`, 'warning');
            return;
        }

        // Update UI
        this.updateFileInfo(file, uploadZone);
        this.showNotification('File selected successfully!', 'success');
    },

    /**
     * Update file information display
     */
    updateFileInfo(file, uploadZone) {
        const fileInfo = uploadZone.querySelector('.file-info');
        if (fileInfo) {
            fileInfo.innerHTML = `
                <div class="selected-file">
                    <i class="fa fa-file"></i>
                    <span class="filename">${file.name}</span>
                    <span class="filesize">(${this.formatFileSize(file.size)})</span>
                </div>
            `;
        }

        // Update upload zone appearance
        uploadZone.classList.add('file-selected');
    },

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `tomo-notification tomo-notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fa fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    },

    /**
     * Get icon for notification type
     */
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    /**
     * Initialize progress tracking for processing jobs
     */
    initProgressTracking() {
        const progressElements = document.querySelectorAll('[data-job-id]');

        progressElements.forEach(element => {
            const jobId = element.dataset.jobId;
            if (jobId) {
                this.startProgressPolling(jobId, element);
            }
        });
    },

    /**
     * Start polling for job progress
     */
    startProgressPolling(jobId, element) {
        const interval = setInterval(async () => {
            try {
                const response = await fetch(`/tomo_print/progress/${jobId}`);
                const data = await response.json();

                this.updateProgressDisplay(element, data);

                // Stop polling if job is complete
                if (data.state === 'completed' || data.state === 'error') {
                    clearInterval(interval);
                }
            } catch (error) {
                console.error('Progress polling error:', error);
                clearInterval(interval);
            }
        }, 2000); // Poll every 2 seconds
    },

    /**
     * Update progress display
     */
    updateProgressDisplay(element, data) {
        const progressBar = element.querySelector('.tomo-progress-fill');
        const progressText = element.querySelector('.tomo-progress-text');
        const statusBadge = element.querySelector('.tomo-status-badge');

        if (progressBar) {
            progressBar.style.width = data.progress + '%';
            progressBar.className = `tomo-progress-fill ${data.state}`;
        }

        if (progressText) {
            progressText.textContent = `${Math.round(data.progress)}% - ${data.state}`;
        }

        if (statusBadge) {
            statusBadge.className = `tomo-status-badge ${data.state}`;
            statusBadge.textContent = data.state.toUpperCase();
        }
    },

    /**
     * Initialize advanced options toggle
     */
    initAdvancedOptions() {
        const toggleButtons = document.querySelectorAll('.tomo-advanced-toggle');

        toggleButtons.forEach(toggleButton => {
            const optionsPanel = toggleButton.nextElementSibling;
            if (!optionsPanel) return;

            toggleButton.addEventListener('click', function() {
                const isExpanded = toggleButton.classList.contains('expanded');

                if (isExpanded) {
                    toggleButton.classList.remove('expanded');
                    optionsPanel.style.display = 'none';
                } else {
                    toggleButton.classList.add('expanded');
                    optionsPanel.style.display = 'block';
                }
            });
        });
    },

    /**
     * Initialize all TomoPrint functionality
     */
    init() {
        this.initFileUpload();
        this.initProgressTracking();
        this.initAdvancedOptions();
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    TomoPrintUtils.init();
});

// Export for use in other modules
registry.category("services").add("tomo_print_utils", TomoPrintUtils);
