/** @odoo-module **/

/**
 * Frontend JavaScript for TomoPrint public interface
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize file upload functionality
    initFileUpload();
    
    // Initialize advanced options toggle
    initAdvancedOptions();
    
    // Initialize form validation
    initFormValidation();
});

/**
 * Initialize file upload with drag and drop
 */
function initFileUpload() {
    const uploadZone = document.querySelector('.tomo-upload-zone');
    const fileInput = document.querySelector('#cad_file_input');
    
    if (!uploadZone || !fileInput) return;
    
    // Drag and drop events
    uploadZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadZone.classList.add('drag-over');
    });
    
    uploadZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('drag-over');
    });
    
    uploadZone.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
    
    // Click to upload
    uploadZone.addEventListener('click', function() {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });
}

/**
 * Handle file selection and validation
 */
function handleFileSelection(file) {
    const allowedTypes = ['stl', 'obj', 'ply', '3mf', 'amf'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    const maxSizeMB = 100; // This should come from configuration
    
    // Validate file type
    if (!allowedTypes.includes(fileExtension)) {
        showNotification('Unsupported file type. Please upload: ' + allowedTypes.join(', '), 'warning');
        return;
    }
    
    // Validate file size
    if (file.size > maxSizeMB * 1024 * 1024) {
        showNotification(`File size exceeds maximum limit of ${maxSizeMB}MB`, 'warning');
        return;
    }
    
    // Update UI
    updateFileInfo(file);
    showNotification('File selected successfully!', 'success');
}

/**
 * Update file information display
 */
function updateFileInfo(file) {
    const fileInfo = document.querySelector('.file-info');
    if (fileInfo) {
        fileInfo.innerHTML = `
            <div class="selected-file">
                <i class="fa fa-file"></i>
                <span class="filename">${file.name}</span>
                <span class="filesize">(${formatFileSize(file.size)})</span>
            </div>
        `;
    }
    
    // Update upload zone appearance
    const uploadZone = document.querySelector('.tomo-upload-zone');
    if (uploadZone) {
        uploadZone.classList.add('file-selected');
    }
}

/**
 * Initialize advanced options toggle
 */
function initAdvancedOptions() {
    const toggleButton = document.querySelector('.tomo-advanced-toggle');
    const optionsPanel = document.querySelector('.tomo-advanced-options');
    
    if (!toggleButton || !optionsPanel) return;
    
    toggleButton.addEventListener('click', function() {
        const isExpanded = toggleButton.classList.contains('expanded');
        
        if (isExpanded) {
            toggleButton.classList.remove('expanded');
            optionsPanel.style.display = 'none';
        } else {
            toggleButton.classList.add('expanded');
            optionsPanel.style.display = 'block';
        }
    });
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    const form = document.querySelector('#tomo-print-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
    });
}

/**
 * Validate form before submission
 */
function validateForm() {
    const email = document.querySelector('#email').value;
    const fileInput = document.querySelector('#cad_file_input');
    
    // Validate email
    if (!email || !isValidEmail(email)) {
        showNotification('Please enter a valid email address', 'error');
        return false;
    }
    
    // Validate file
    if (!fileInput.files || fileInput.files.length === 0) {
        showNotification('Please select a CAD file to upload', 'error');
        return false;
    }
    
    return true;
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Show notification to user
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `tomo-notification tomo-notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fa fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fa fa-times"></i>
            </button>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Get icon for notification type
 */
function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Initialize progress tracking for processing jobs
 */
function initProgressTracking() {
    const progressElements = document.querySelectorAll('[data-job-id]');
    
    progressElements.forEach(element => {
        const jobId = element.dataset.jobId;
        if (jobId) {
            startProgressPolling(jobId, element);
        }
    });
}

/**
 * Start polling for job progress
 */
function startProgressPolling(jobId, element) {
    const interval = setInterval(async () => {
        try {
            const response = await fetch(`/tomo_print/progress/${jobId}`);
            const data = await response.json();
            
            updateProgressDisplay(element, data);
            
            // Stop polling if job is complete
            if (data.state === 'completed' || data.state === 'error') {
                clearInterval(interval);
            }
        } catch (error) {
            console.error('Progress polling error:', error);
            clearInterval(interval);
        }
    }, 2000); // Poll every 2 seconds
}

/**
 * Update progress display
 */
function updateProgressDisplay(element, data) {
    const progressBar = element.querySelector('.tomo-progress-fill');
    const progressText = element.querySelector('.tomo-progress-text');
    const statusBadge = element.querySelector('.tomo-status-badge');
    
    if (progressBar) {
        progressBar.style.width = data.progress + '%';
        progressBar.className = `tomo-progress-fill ${data.state}`;
    }
    
    if (progressText) {
        progressText.textContent = `${Math.round(data.progress)}% - ${data.state}`;
    }
    
    if (statusBadge) {
        statusBadge.className = `tomo-status-badge ${data.state}`;
        statusBadge.textContent = data.state.toUpperCase();
    }
}

// Initialize progress tracking when page loads
document.addEventListener('DOMContentLoaded', initProgressTracking);
