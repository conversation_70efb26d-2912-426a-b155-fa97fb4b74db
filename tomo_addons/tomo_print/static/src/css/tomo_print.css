/* TomoPrint Styles */

/* File Upload Drop Zone */
.tomo-upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.tomo-upload-zone:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.tomo-upload-zone.drag-over {
    border-color: #28a745;
    background-color: #d4edda;
    border-style: solid;
}

.tomo-upload-zone.uploading {
    border-color: #ffc107;
    background-color: #fff3cd;
    pointer-events: none;
}

.tomo-upload-icon {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 16px;
}

.tomo-upload-zone.drag-over .tomo-upload-icon {
    color: #28a745;
}

.tomo-upload-zone.uploading .tomo-upload-icon {
    color: #ffc107;
    animation: pulse 1.5s infinite;
}

.tomo-upload-text {
    font-size: 18px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.tomo-upload-subtext {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 16px;
}

.tomo-upload-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.tomo-upload-button:hover {
    background-color: #0056b3;
}

.tomo-upload-button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Progress Bar Styles */
.tomo-progress-container {
    margin: 16px 0;
}

.tomo-progress-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.tomo-progress-fill {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.tomo-progress-fill.success {
    background-color: #28a745;
}

.tomo-progress-fill.danger {
    background-color: #dc3545;
}

.tomo-progress-fill.warning {
    background-color: #ffc107;
}

.tomo-progress-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    text-align: center;
}

/* Advanced Options Toggle */
.tomo-advanced-toggle {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px 16px;
    margin: 16px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tomo-advanced-toggle:hover {
    background-color: #e9ecef;
}

.tomo-advanced-toggle .fa {
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.tomo-advanced-toggle.expanded .fa {
    transform: rotate(90deg);
}

.tomo-advanced-options {
    margin-top: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

/* Status Badges */
.tomo-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.tomo-status-badge.draft {
    background-color: #e9ecef;
    color: #495057;
}

.tomo-status-badge.uploaded {
    background-color: #cce5ff;
    color: #0056b3;
}

.tomo-status-badge.processing {
    background-color: #fff3cd;
    color: #856404;
    animation: pulse 2s infinite;
}

.tomo-status-badge.completed {
    background-color: #d4edda;
    color: #155724;
}

.tomo-status-badge.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Job Cards */
.tomo-job-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background-color: white;
    transition: box-shadow 0.3s ease;
}

.tomo-job-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tomo-job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.tomo-job-title {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin: 0;
}

.tomo-job-meta {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.tomo-job-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.tomo-job-action {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
    color: #495057;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.3s ease;
}

.tomo-job-action:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.tomo-job-action.primary {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.tomo-job-action.primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.tomo-job-action.success {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

.tomo-job-action.success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tomo-upload-zone {
        padding: 20px 10px;
        min-height: 150px;
    }
    
    .tomo-upload-icon {
        font-size: 36px;
    }
    
    .tomo-upload-text {
        font-size: 16px;
    }
    
    .tomo-job-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .tomo-job-actions {
        flex-wrap: wrap;
    }
}

/* Animations */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.tomo-spinner {
    animation: spin 1s linear infinite;
}

/* Form Enhancements */
.tomo-form-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.tomo-form-section h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.tomo-parameter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.tomo-parameter-item {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
}

.tomo-parameter-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
    display: block;
}

.tomo-parameter-value {
    color: #212529;
    font-size: 14px;
}

/* Notification Styles */
.tomo-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 500px;
    z-index: 9999;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease;
}

.tomo-notification-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.tomo-notification-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.tomo-notification-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.tomo-notification-info {
    background-color: #cce5ff;
    border: 1px solid #b3d7ff;
    color: #0056b3;
}

.notification-content {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    margin-left: auto;
    padding: 4px;
    border-radius: 2px;
    opacity: 0.7;
}

.notification-close:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
}

/* File Selection Styles */
.selected-file {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background-color: #e3f2fd;
    border: 1px solid #90caf9;
    border-radius: 4px;
    margin-bottom: 16px;
}

.selected-file .fa {
    color: #1976d2;
    font-size: 18px;
}

.filename {
    font-weight: 500;
    color: #1976d2;
}

.filesize {
    color: #666;
    font-size: 12px;
}

.tomo-upload-zone.file-selected {
    border-color: #28a745;
    background-color: #d4edda;
}

/* Animation Keyframes */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
