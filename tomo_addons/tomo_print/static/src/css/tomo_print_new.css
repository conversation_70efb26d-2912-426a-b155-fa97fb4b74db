/* TomoPrint New Job Form Styles */

.tomo_print_new_form {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.tomo_print_new_form .o_form_sheet {
    background-color: white;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 900px;
    margin: 20px auto;
    padding: 30px;
}

/* Header Styles */
.tomo_header {
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.tomo_title {
    font-size: 48px;
    font-weight: bold;
    color: #212529;
    margin-bottom: 5px;
    letter-spacing: -1px;
}

.tomo_subtitle {
    font-size: 16px;
    color: #6c757d;
    margin: 0;
    font-weight: 400;
}

/* Main Content Layout */
.tomo_main_content {
    margin-top: 30px;
}

/* File Upload Section */
.tomo_upload_section {
    padding-right: 20px;
}

.tomo_file_upload_area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tomo_file_upload_area:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.tomo_file_input {
    width: 100%;
}

.tomo_file_input .o_field_widget {
    border: none !important;
    background: transparent !important;
}

/* Advanced Section */
.tomo_advanced_section {
    padding: 15px 0;
}

.tomo_advanced_section .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tomo_advanced_section .form-check-label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.tomo_advanced_section .form-check-label i {
    color: #6c757d;
}

/* Email Section */
.tomo_email_section {
    margin-top: 20px;
}

.tomo_email_input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

.tomo_email_input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Options Section */
.tomo_options_section {
    padding-left: 20px;
    border-left: 1px solid #dee2e6;
}

.tomo_option_group {
    margin-bottom: 20px;
}

.tomo_option_row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.tomo_option_label {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0;
    flex-shrink: 0;
    min-width: 120px;
}

.tomo_input_group {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: flex-end;
}

.tomo_number_input {
    width: 80px;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    text-align: center;
}

/* For fields without input group wrapper */
.tomo_option_row > .tomo_number_input {
    width: 120px;
    margin-left: auto;
}

.tomo_number_input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.tomo_input_suffix {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.tomo_range_info {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    text-align: right;
    padding-right: 20px;
}

/* Reorientate Display */
.tomo_option_group .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tomo_option_group .form-check-input-wrapper {
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 5px;
}

.tomo_option_group .form-check-input-wrapper i {
    color: #6c757d;
}

.tomo_option_group .form-check-input-wrapper .text-success {
    color: #28a745 !important;
}

/* Status Section */
.tomo_status_section {
    border-top: 1px solid #dee2e6;
    padding-top: 30px;
}

.tomo_status_message .alert {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 500;
}

.tomo_status_message .alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.tomo_status_message .alert i {
    margin-right: 8px;
    font-size: 18px;
}

/* Progress Section */
.tomo_progress_section {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

/* Button Styles */
.tomo_print_new_form .o_form_button_box {
    text-align: center;
    margin-bottom: 20px;
}

.tomo_print_new_form .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    min-width: 150px;
}

.tomo_print_new_form .btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tomo_print_new_form .o_form_sheet {
        margin: 10px;
        padding: 20px;
    }
    
    .tomo_title {
        font-size: 36px;
    }
    
    .tomo_upload_section,
    .tomo_options_section {
        padding: 0;
        border: none;
    }
    
    .tomo_options_section {
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid #dee2e6;
    }
    
    .tomo_file_upload_area {
        padding: 30px 15px;
        min-height: 100px;
    }
}

/* Form Field Overrides */
.tomo_print_new_form .o_field_widget {
    margin-bottom: 0;
}

.tomo_print_new_form .o_field_boolean_toggle {
    margin: 0;
}

.tomo_print_new_form .o_field_boolean_toggle .o_boolean_toggle {
    margin: 0;
}

/* Hide default form elements */
.tomo_print_new_form .o_form_label {
    display: none;
}

.tomo_print_new_form .o_group {
    margin-bottom: 0;
}

/* Custom placeholder styling */
.tomo_email_input::placeholder,
.tomo_number_input::placeholder {
    color: #adb5bd;
    font-style: italic;
}

/* Animation for status changes */
.tomo_status_section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Notification Styles for New Form */
.tomo_notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 500px;
    z-index: 9999;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease;
}

.tomo_notification_success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.tomo_notification_error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.tomo_notification_warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.tomo_notification_info {
    background-color: #cce5ff;
    border: 1px solid #b3d7ff;
    color: #0056b3;
}

.notification_content {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification_close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    margin-left: auto;
    padding: 4px;
    border-radius: 2px;
    opacity: 0.7;
}

.notification_close:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
}

/* File Selected State */
.tomo_file_selected {
    text-align: center;
    padding: 20px;
}

.tomo_file_selected .file-name {
    font-weight: 500;
    color: #155724;
    margin-bottom: 4px;
}

.tomo_file_selected .file-size {
    color: #6c757d;
    font-size: 12px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
